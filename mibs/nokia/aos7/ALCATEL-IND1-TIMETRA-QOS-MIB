ALCATEL-IND1-TIMETRA-QOS-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE,
    Unsigned32, <PERSON>p<PERSON><PERSON><PERSON>, Integer32
                                                     FROM SNMPv2-SMI
    MODULE-COMPLIANCE, OBJECT-GROUP
                                                     FROM SNMPv2-CONF
    TEXTUAL-CONVENTION, RowStatus,
    MacAddress, TruthValue, 
    StorageType, TimeStamp
                                                     FROM SNMPv2-TC
    AtmServiceCategory, AtmTrafficDescrParamIndex
                                                     FROM ATM-TC-MIB
    InetAddress, InetAddressType,                                                 
    InetAddressIPv6, InetAddressPrefixLength
                                                     FROM INET-ADDRESS-MIB

    timetraSRMIBModules, tmnxSRObjs,
    tmnxSRNotifyPrefix, tmnxSRConfs
                                                     FROM ALCATEL-IND1-TIMETRA-GLOBAL-MIB
    Dot1<PERSON><PERSON>rity, Ip<PERSON><PERSON><PERSON><PERSON>refixLength,
    ServiceAccessPoint,TItemDescription,
    T<PERSON>amed<PERSON><PERSON>, TNamedItemOrEmpty,
    TDSCPValue,  TDSCPName, TDSCPNameOrEmpty,
    TIpProtocol, TTcpUdpPort,
    TTcpUdpPortOperator, TFrameType, TFCName, TFCNameOrEmpty,
    TLspExpValue, TQueueId, TIngressQueueId,
    TEgressQueueId, TPortSchedulerPIR, 
    TPortSchedulerCIR,
    TWeight, TCIRRate, TPIRRate, TPIRRateOrZero,
    TmnxEnabledDisabled, TPolicyID,
    TSapIngressPolicyID, TSapEgressPolicyID
                                                     FROM ALCATEL-IND1-TIMETRA-TC-MIB
    ;

timetraQosMIBModule   MODULE-IDENTITY
        LAST-UPDATED    "0801010000Z"
        ORGANIZATION    "Alcatel"
        CONTACT-INFO
            "Alcatel 7x50 Support
             Web: http://www.alcatel.com/comps/pages/carrier_support.jhtml"
        DESCRIPTION
            "This document is the SNMP MIB module to manage and provision
             Quality of Service features on Alcatel 7x50 systems.

             Copyright 2003-2008 Alcatel-Lucent. All rights reserved.
             Reproduction of this document is authorized on the condition that
             the foregoing copyright notice is included.

             This SNMP MIB module (Specification) embodies Alcatel's
             proprietary intellectual property.  Alcatel retains
             all title and ownership in the Specification, including any
             revisions.

             Alcatel grants all interested parties a non-exclusive
             license to use and distribute an unmodified copy of this
             Specification in connection with management of Alcatel
             products, and without fee, provided this copyright notice and
             license appear on all copies.

             This Specification is supplied 'as is', and Alcatel
             makes no warranty, either express or implied, as to the use,
             operation, condition, or performance of the Specification."

--
--  Revision History
--
        REVISION        "0801010000Z"
        DESCRIPTION     "Rev 6.0                01 Jan 2008 00:00
                         6.0 release of the TIMETRA-QOS-MIB."

        REVISION        "0701010000Z"
        DESCRIPTION     "Rev 5.0                01 Jan 2007 00:00
                         5.0 release of the TIMETRA-QOS-MIB."

        REVISION        "0602280000Z"
        DESCRIPTION     "Rev 4.0                28 Feb 2006 00:00
                         4.0 release of the TIMETRA-QOS-MIB."

        REVISION        "0508310000Z"
        DESCRIPTION     "Rev 3.0                31 Aug 2005 00:00
                         3.0 release of the TIMETRA-QOS-MIB."

        REVISION        "0501240000Z"
        DESCRIPTION     "Rev 2.1                24 Jan 2005 00:00
                         2.1 release of the TIMETRA-QOS-MIB."

        REVISION        "0401150000Z"
        DESCRIPTION     "Rev 2.0                    15 Jan 2004 00:00
                         2.0 release of the TIMETRA-QOS-MIB."

        REVISION        "0308150000Z"
        DESCRIPTION     "Rev 1.2                    15 Aug 2003 00:00
                         1.2 release of the TIMETRA-QOS-MIB."

        REVISION        "200301200000Z"
        DESCRIPTION     "Rev 1.0                    20 Jan 2003 00:00
                         1.0 Release of the TIMETRA-QOS-MIB."

        REVISION        "200105290000Z"
        DESCRIPTION     "Rev 0.1                    29 May 2001 00:00
                         Initial version of the TIMETRA-QOS-MIB."

        ::= { timetraSRMIBModules 16 }

tQosObjects                    OBJECT IDENTIFIER ::= { tmnxSRObjs 16 }
tQosNotifyPrefix              OBJECT IDENTIFIER ::= { tmnxSRNotifyPrefix 16}
    tQosNotifications           OBJECT IDENTIFIER ::= { tQosNotifyPrefix 0}
tmnxQosConformance             OBJECT IDENTIFIER ::= { tmnxSRConfs 16 }

tDSCPObjects                   OBJECT IDENTIFIER ::= { tQosObjects 1 }
tFCObjects                     OBJECT IDENTIFIER ::= { tQosObjects 2 }
tSapIngressObjects             OBJECT IDENTIFIER ::= { tQosObjects 3 }
tSapEgressObjects              OBJECT IDENTIFIER ::= { tQosObjects 4 }
tNetworkObjects                OBJECT IDENTIFIER ::= { tQosObjects 5 }
tNetworkQueueObjects           OBJECT IDENTIFIER ::= { tQosObjects 6 }
tSharedQueueObjects            OBJECT IDENTIFIER ::= { tQosObjects 7 }
tSlopeObjects                  OBJECT IDENTIFIER ::= { tQosObjects 10 }
tSchedulerObjects              OBJECT IDENTIFIER ::= { tQosObjects 12 }
tQosTimeStampObjects           OBJECT IDENTIFIER ::= { tQosObjects 20 }
tAtmTdpObjects                 OBJECT IDENTIFIER ::= { tQosObjects 21 }
tPoolObjects                   OBJECT IDENTIFIER ::= { tQosObjects 22 }

--
-- TEXTUAL-CONVENTIONs for Alcatel 7x50 SR series QoS Elements
--

TNetworkPolicyID ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "the identification number of a network policy."
    SYNTAX Unsigned32 (1..65535)

TItemScope ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "This textual-convention determines some aspects of an item's
        behavior regarding creation and use, unused entry garbage collection,
        and automated promulgation by Element Management System to
        other systems in the service domain.

        TItemScope applies to SAP-ingress, SAP-egress, and Network policies,
        and to IP filters and MAC filters.

        exclusive:

            When the scope of an item is defined as exclusive, the item can
            only be applied once, for example to a single SAP.  Attempting
            to assign the policy to a second SAP is not allowed and will
            result in an error.  If the item is removed from the exclusive
            SAP, it will become available for assignment to another
            exclusive SAP.

            A non-applied exclusive scope policy is a candidate to be removed
            from the system by a TBD garbage collection command.

            The system default policies cannot be put into the exclusive scope.
            An error will be generated if scope exclusive is executed in
            any policies with a policy-id equal to 1.

        template:

            When the scope of an item is defined as template, the item can be
            applied any number of times.  Policies with template scope
            will not be considered for deletion by a TBD garbage collection
            command; all items of scope 'template' must be deleted explicitly.

            The system default policies will always be scope template.
            An error will occur if a policy-id 1 is attempted to be
            set to scope exclusive."
    SYNTAX INTEGER { exclusive(1), template(2) }

TItemMatch  ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "when set to off, the item is not matched.
         when set to false, packets without the item match the filter.
         when set to true, packets with the item match the filter."
    SYNTAX INTEGER { off(1), false(2), true(3) }

TPriority ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "the priority to apply to a packet"
    SYNTAX INTEGER { low(1), high(2) }

TPriorityOrDefault ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "the priority to apply to a packet.
         when set to default(3), the priority from the default-action is used."
    SYNTAX INTEGER { low(1), high(2), default(3) }

TProfile ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "the profile marking of a packet at the ingress."
    SYNTAX INTEGER { in(1), out(2) }

TDEProfile ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "This textual-convention specifies the profile marking of a packet.

        Value of 'in' specifies the in-profile marking.

        Value of 'out' specifies the out-profile marking.
        
        Value of 'de' specifies that the profile marking will be based on the DE
        (Drop-Eligible) bit.  DE bit-value of '0' specifies in-profile and DE
        bit value of '1' specifies out-profile marking."
    SYNTAX INTEGER { in (1), out (2), de (3) }

TProfileOrNone ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "Profile marking of a packet."
    SYNTAX INTEGER { none (0), in(1), out(2) }

TAdaptationRule ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The adaptation rule to be applied to calcluate the operational values
         for the specified entity."
    SYNTAX       INTEGER {
                     max    (1),
                     min    (2),
                     closest(3)
                 }

TRemarkType ::=  TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The remarking to be used."
    SYNTAX       INTEGER {
                     none       (1),
                     dscp       (2),
                     precedence (3)
                 }

TPrecValue ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The precedence bits as used in the IPv4 header. This constitutes of
         3 bits and hence can hold the values from 0 to 7."
    SYNTAX       Integer32 (0..7)

TPrecValueOrNone ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The precedence bits as used in the IPv4 header. This constitutes of
         3 bits and hence can hold the values from 0 to 7. The value '-1'
         specifies that the precedence value is undefined/unused."
    SYNTAX      Integer32 (-1 | 0..7)

TBurstSize ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The amount of buffer space (in kbytes) assigned to a queue.
         The value -1 means that the actual value is derived from the
         corresponding buffer policy's default value."
    SYNTAX       Integer32 (-1 | 0..131072)

TBurstPercent ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The percentage of buffer space assigned to a queue that is
         reserved for some purpose."
    SYNTAX       Integer32 (0..100)

TBurstHundredthsOfPercent ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The percentage of buffer space assigned to a queue that is
         reserved for some purpose, defined to two decimal places."
    SYNTAX      Integer32 (0..10000)

TBurstPercentOrDefault ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The percentage of buffer space assigned to a queue that is
         reserved for some purpose.
         The value -1 means that the actual value is derived from the
         corresponding buffer policy's default value."
    SYNTAX       Integer32 (-1 | 0..100)

TRatePercent ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The percentage of maximum rate allowed."
    SYNTAX       Integer32 (0..100)

TPIRRatePercent ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The percentage of maximum PIR rate allowed. A value of 0 is
         no acceptable, so the range begins at 1."
    SYNTAX       Integer32 (1..100)

TLevel ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The level of the specified entity while feeding into the parent."
    SYNTAX      Integer32 (1..8)

TLevelOrDefault ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The level of the specified entity while feeding into the parent.
         The value 0 is used to denote a default value."
    SYNTAX       Integer32 (0|1..8)

TQueueMode ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The mode in which the queue is operating.

         If the queue is operating in the 'priority' mode, it is
         capable of handling traffic differently with two distinct
         priorities. These priorities are assigned by the stages
         preceding the queueing framework in the system.

         When the queue is operating in the 'profile' mode, in other
         words the color aware mode, the queue tries to provide the
         appropriate bandwidth to the packets with different profiles.

         The profiles are assigned according to the configuration of the
         forwarding class or the sub-forwarding class.

         In 'priority' mode, the queue does not have the functionality
         to support the profiled traffic and in such cases the queue
         will have a degraded performance. However, the converse is not
         valid and a queue in 'profile' mode should be capable of
         supporting the different priorities of traffic."
    SYNTAX       INTEGER {
                     priority(1),
                     profile (2)
                 }

TEntryIndicator ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "Uniquely identifies an entry in a policy or filter table. The
         value 0 is not a valid entry-id. When used as insertion point
         the value 0 indicates that entries must be inserted at the very
         beginning, i.e.before the first entry defined."
    SYNTAX Unsigned32 (0..65535)

TEntryId ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "uniquely identifies an entry in a policy or filter table.
         to facilitate insertion of entries in the tables, we recommend
         assigning entry IDs by 10s:  10, 20, 30, etc. "
    SYNTAX Unsigned32 (1..65535)

TMatchCriteria ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "determines whether the entry matches traffic using IP match
         entries or MAC match entries."
    SYNTAX  INTEGER { ip(1), mac(2), none(3) }

TAtmTdpDescrType ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "The TAtmTdpDescrType is an enumerated integer whose value
         indicates the types of cell loss priority to be used in
         conjunction with traffic parameters.

        The following values are outlined:
          Integer Value               Interpretation
          -------------               ------------------------
          clp0And1pcr                 PCR applies to CLP 0 and
                                      CLP 1 cell flows
          clp0And1pcrPlusClp0And1scr  PCR applies to CLP 0 and
                                      CLP 1 cell flows.
                                      SCR applies to CLP 0 and
                                      CLP 1 cell flows.
          clp0And1pcrPlusClp0scr      PCR applies to CLP 0 and
                                      CLP 1 cell flows.
                                      SCR applies to CLP 0 cell flows.
          clp0And1pcrPlusClp0scrTag   PCR applies to CLP 0 and
                                      CLP 1 cell flows.
                                      SCR applies to CLP 0 cell flows. "
    SYNTAX INTEGER {
               clp0And1pcr(0),
               clp0And1pcrPlusClp0And1scr(1),
               clp0And1pcrPlusClp0scr(2),
               clp0And1pcrPlusClp0scrTag(3)
           }

TDEValue ::= TEXTUAL-CONVENTION
    STATUS       current
    DESCRIPTION
        "This textual-convention specifies the DE (Drop Eligible) bit value.
        The value of '-1' means DE value is not specified."
    SYNTAX Integer32 (-1 | 0..1)

--
--
-- mib objects
--

--
-- DSCP Name -> DSCP value mapping table
--

tDSCPNameTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TDSCPNameEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "A list of all DSCP Names.  DSCP Name entries must
         exist here and be active before they can be referenced
         elsewhere (e.g. IP filter)."
    ::= { tDSCPObjects 1 }

tDSCPNameEntry OBJECT-TYPE
    SYNTAX       TDSCPNameEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about a particular DSCP Name, particularly
         the DSCP value. It maps a DSCP Name into a DSCP value.

         Some default entries are created by the agent.
         Default entries have tDSCPNameStorageType permanent,
         and can not be deleted.

         Entries cannot be created by user but may be supported later.

         An attempt to create a row in this table would return no creation."
    INDEX { tDSCPName }
    ::= { tDSCPNameTable 1 }

TDSCPNameEntry ::= SEQUENCE
    {
        tDSCPName            TDSCPName,
        tDSCPNameRowStatus   RowStatus,
        tDSCPNameStorageType StorageType,
        tDSCPNameDscpValue   TDSCPValue,
        tDSCPNameLastChanged TimeStamp
    }

tDSCPName OBJECT-TYPE
    SYNTAX       TDSCPName
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "The name assigned to the DSCP Value."
    REFERENCE
        ""
    ::= { tDSCPNameEntry 1 }

tDSCPNameRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "Row Status of the entry."
    ::= { tDSCPNameEntry 2 }

tDSCPNameStorageType OBJECT-TYPE
    SYNTAX       StorageType
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The storage type of this row in the table."
    DEFVAL { nonVolatile }
    ::= { tDSCPNameEntry 3 }

tDSCPNameDscpValue OBJECT-TYPE
    SYNTAX       TDSCPValue
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The DSCP Value."
    REFERENCE
        ""
    DEFVAL { 0 }
    ::= { tDSCPNameEntry 4 }

tDSCPNameLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "timestamp of last change to this row in tDSCPNameTable."
    ::= { tDSCPNameEntry 5 }

--
-- Forwarding Class name table
--

tFCNameTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TFCNameEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "A list of all Forwarding Class Names.  Forwarding Class Name entries
        must exist here and be active before they can be referenced
        elsewhere (e.g. qos queue)."
    ::= { tFCObjects 1 }

tFCNameEntry OBJECT-TYPE
    SYNTAX       TFCNameEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about a particular Forwarding Class Name, particularly
         the Forwarding Class value.

         Some default entries are created by the agent.
         Default entries have tFCStorageType permanent,
         and can not be deleted.

         Entries cannot be created by user but maybe supported in future.

         An attempt to create a row in this table would return no creation."
    INDEX { tFCName }
    ::= { tFCNameTable 1 }

TFCNameEntry ::= SEQUENCE
    {
        tFCName               TFCName,
        tFCRowStatus          RowStatus,
        tFCStorageType        StorageType,
        tFCValue              Integer32,
        tFCNameLastChanged    TimeStamp
    }

tFCName OBJECT-TYPE
    SYNTAX       TFCName
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION "Forwarding class name."
    REFERENCE ""
    ::= { tFCNameEntry 1 }

tFCRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION "Row Status of the entry."
    ::= { tFCNameEntry 2 }

tFCStorageType OBJECT-TYPE
    SYNTAX       StorageType
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION "The storage type of this row in the table."
    DEFVAL { nonVolatile }
    ::= { tFCNameEntry 3 }

tFCValue OBJECT-TYPE
    SYNTAX       Integer32 (0..7)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION "FC Value."
    REFERENCE ""
    DEFVAL { 0 }
    ::= { tFCNameEntry 4 }

tFCNameLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "timestamp of last change to this row in tFCNameTable."
    ::= { tFCNameEntry 5 }

--
-- sap-ingress policy table
--

tSapIngressTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TSapIngressEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION  "  "
    ::= { tSapIngressObjects 1 }

tSapIngressEntry OBJECT-TYPE
    SYNTAX       TSapIngressEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about a particular Sap Ingress Policy.

         sap-ingress policy (1) is the default entry.

         Default Entry is created by the agent, other entries are
         created by user.

         Default Entry cannot be modified/deleted. The other
         entries are deleted by user.

         There is no StorageType object, entries have a presumed
         StorageType of nonVolatile.
        "
    INDEX { tSapIngressIndex }
    ::= { tSapIngressTable 1 }

TSapIngressEntry ::= SEQUENCE
    {
        tSapIngressIndex                        TSapIngressPolicyID,
        tSapIngressRowStatus                    RowStatus,
        tSapIngressScope                        TItemScope,
        tSapIngressDescription                  TItemDescription,
        tSapIngressDefaultFC                    TNamedItem,
        tSapIngressDefaultFCPriority            TPriority,
        tSapIngressMatchCriteria                TMatchCriteria,
        tSapIngressLastChanged                  TimeStamp
    }

tSapIngressIndex OBJECT-TYPE
    SYNTAX       TSapIngressPolicyID (1..65535)
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "the policy ID is the index to this table."
    ::= { tSapIngressEntry 1 }

tSapIngressRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "Row Status for the policy. The deletion of this row has
         an action of removing the dependent rows in the following
         tables :
           - tSapIngressQueueTable
           - tSapIngressDSCPTable
           - tSapIngressDot1pTable
        "
    ::= { tSapIngressEntry 2 }

tSapIngressScope OBJECT-TYPE
    SYNTAX       TItemScope
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "Scope of the policy. If the scope is 'exclusive', it can only be
         instantiated once whereas it could have multiple instances if defined
         as 'exclusive'."
    DEFVAL { template }
    ::= { tSapIngressEntry 3 }

tSapIngressDescription OBJECT-TYPE
    SYNTAX       TItemDescription
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "user-provided description of the policy."
    DEFVAL { ''H }
    ::= { tSapIngressEntry 4 }

tSapIngressDefaultFC OBJECT-TYPE
    SYNTAX       TNamedItem
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSapIngressDefaultFC specifies the forwarding class or the
         sub forwarding class which should be used for all traffic that
         does not match a specific classification specified in this
         SAP ingress QoS policy.

         Specification of a sub forwarding class is also allowed. The
         format of a sub forwarding class is the concatenation of the
         base forwarding class and a sub class identifier in the form

             base-fc-name[.sub-fc-name]

         To reference a sub forwarding class, the mapping of the sub
         forwarding class should be existing in this QoS policy.

         If not specified explicitly, all traffic will be classified
         as best-effort 'be'."
    DEFVAL { 'be'H }
    ::= { tSapIngressEntry 5 }

tSapIngressDefaultFCPriority OBJECT-TYPE
    SYNTAX       TPriority
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "the priority to assign to traffic ingressing as the default
         action queue."
    DEFVAL { low }
    ::= { tSapIngressEntry 6 }

tSapIngressMatchCriteria OBJECT-TYPE
    SYNTAX       TMatchCriteria
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "indicates which type of match criteria the policy should use.
         when set to ip(1), the policy looks for match entries in
         tSapIngressIPCriteriaTable or tSapIngressIPv6CriteriaTable.
         When set to mac(2), the policy looks for match entries in 
         tSapIngressMacCriteriaTable.

         the value of tSapIngressMatchCriteria is set when the first
         match criteria table entry is created.  the value is set to
         none(3) when the last criteria table entry is removed.

         when tSapIngressMatchCriteria is set to ip(1), no
         tSapIngressMacCriteriaTable entries can be created.
         when set to mac(2), no tSapIngressIPCriteriaTable or 
         tSapIngressIPv6CriteriaTable entries can be created.
        "
    ::= { tSapIngressEntry 7 }

tSapIngressLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "timestamp of last change to this row in tSapIngressTable."
    ::= { tSapIngressEntry 8 }

--
-- sap-ingress policy queue table
--

tSapIngressQueueTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TSapIngressQueueEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "information about all sap-ingress policies' queues."
    ::= { tSapIngressObjects 2 }

tSapIngressQueueEntry OBJECT-TYPE
    SYNTAX       TSapIngressQueueEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about a particular queue for a sap-ingress policy.

         Queue (1) is the used as a default for unicast and queue (11) is
         used as a default for multicast. Both these are created by the
         agent and can be edited but not removed from the system.

         Other entries are created by user."
    INDEX { tSapIngressIndex, tSapIngressQueue }
    ::= { tSapIngressQueueTable 1 }

TSapIngressQueueEntry ::= SEQUENCE
    {
        tSapIngressQueue                 TIngressQueueId,
        tSapIngressQueueRowStatus        RowStatus,
        tSapIngressQueueParent           TNamedItemOrEmpty,
        tSapIngressQueueLevel            TLevel,
        tSapIngressQueueWeight           TWeight,
        tSapIngressQueueCIRLevel         TLevelOrDefault,
        tSapIngressQueueCIRWeight        TWeight,
        tSapIngressQueueMCast            TruthValue,
        tSapIngressQueueExpedite         INTEGER,
        tSapIngressQueueCBS              TBurstSize,
        tSapIngressQueueMBS              TBurstSize,
        tSapIngressQueueHiPrioOnly       TBurstPercentOrDefault,
        tSapIngressQueuePIRAdaptation    TAdaptationRule,
        tSapIngressQueueCIRAdaptation    TAdaptationRule,
        tSapIngressQueueAdminPIR         TPIRRate,
        tSapIngressQueueAdminCIR         TCIRRate,
        tSapIngressQueueOperPIR          TPIRRate,
        tSapIngressQueueOperCIR          TCIRRate,
        tSapIngressQueueLastChanged      TimeStamp,
        tSapIngressQueuePoliced          TruthValue,
        tSapIngressQueueMode             TQueueMode,
        tSapIngressQueuePoolName         TNamedItemOrEmpty
    }

tSapIngressQueue OBJECT-TYPE
    SYNTAX       TIngressQueueId (1..32)
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "The queue ID is used as the secondary index to the table entry."
    ::= { tSapIngressQueueEntry 1 }

tSapIngressQueueRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "Row Status of the entry. This allows creation/deletion of rows in this
         table."
    ::= { tSapIngressQueueEntry 2 }

tSapIngressQueueParent OBJECT-TYPE
    SYNTAX       TNamedItemOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The scheduler to which this queue would be feeding to."
    DEFVAL { ''H }
    ::= { tSapIngressQueueEntry 3 }

tSapIngressQueueLevel OBJECT-TYPE
    SYNTAX       TLevel
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "This specifies the level of priority while feeding to the parent."
    DEFVAL { 1 }
    ::= { tSapIngressQueueEntry 4 }

tSapIngressQueueWeight OBJECT-TYPE
    SYNTAX       TWeight
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The weight that needs to be used by the scheduler to which this queue
         would be feeding to."
    DEFVAL { 1 }
    ::= { tSapIngressQueueEntry 5 }

tSapIngressQueueCIRLevel OBJECT-TYPE
    SYNTAX       TLevelOrDefault
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "This specifies the level of priority while feeding to the parent.
         The level '0' means treat all offered load for this queue as for
         the above CIR traffic."
    DEFVAL { 0 }
    ::= { tSapIngressQueueEntry 6 }

tSapIngressQueueCIRWeight OBJECT-TYPE
    SYNTAX       TWeight
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The weight that needs to be used until the committed rate by the
         scheduler to which this queue would be feeding to."
    DEFVAL { 1 }
    ::= { tSapIngressQueueEntry 7 }

tSapIngressQueueMCast OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "This object specifies if this is a multicast queue or not."
    DEFVAL { false }
    ::= { tSapIngressQueueEntry 8 }

tSapIngressQueueExpedite OBJECT-TYPE
    SYNTAX       INTEGER {
                    expedited     (1),
                    auto-expedited(2),
                    non-expedited (3)
                 }
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSapIngressQueueExpedite specifies the priority that this queue should
         be assigned by the hardware level schedulers.

         The value 'auto-expedited' implies that this attribute should be
         dynamically updated by looking at the forwarding classes mapping
         into this queue. In such a case the queue is treated as
         'non-expedited' if there is even a single non-expedited forwarding
         class using this queue.

         This attribute is associated with the queue at the time of creation and
         cannot be modified thereafter."
    DEFVAL { auto-expedited }
    ::= { tSapIngressQueueEntry 9 }

tSapIngressQueueCBS OBJECT-TYPE
    SYNTAX       TBurstSize
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The amount of reserved buffer space (in kilo bytes) for the queue."
    DEFVAL { -1 }
    ::= { tSapIngressQueueEntry 10 }

tSapIngressQueueMBS OBJECT-TYPE
    SYNTAX       TBurstSize
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The maximum amount of buffer space (in kilo bytes) allowed for the
         queue."
    DEFVAL { -1 }
    ::= { tSapIngressQueueEntry 11 }

tSapIngressQueueHiPrioOnly OBJECT-TYPE
    SYNTAX       TBurstPercentOrDefault
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The percentage of buffer space for the queue, used
         exclusively by high priority packets."
    DEFVAL { -1 }
    ::= { tSapIngressQueueEntry 12 }

tSapIngressQueuePIRAdaptation OBJECT-TYPE
    SYNTAX       TAdaptationRule
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The adaptation rule to be used while computing the operational PIR
         value. The adaptation rule specifies the rules to compute the
         operational values while maintaining minimum offset."
    DEFVAL { closest }
    ::= { tSapIngressQueueEntry 13 }

tSapIngressQueueCIRAdaptation OBJECT-TYPE
    SYNTAX       TAdaptationRule
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The adaptation rule to be used while computing the operational CIR
         value. The adaptation rule specifies the rules to compute the
         operational values while maintaining minimum offset."
    DEFVAL { closest }
    ::= { tSapIngressQueueEntry 14 }

tSapIngressQueueAdminPIR OBJECT-TYPE
    SYNTAX       TPIRRate
    UNITS        "kbps"
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The administrative PIR specified by the user."
    DEFVAL { -1 }
    ::= { tSapIngressQueueEntry 15 }

tSapIngressQueueAdminCIR OBJECT-TYPE
    SYNTAX       TCIRRate
    UNITS        "kbps"
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The administrative CIR specified by the user."
    DEFVAL { 0 }
    ::= { tSapIngressQueueEntry 16 }

tSapIngressQueueOperPIR OBJECT-TYPE
    SYNTAX       TPIRRate
    UNITS        "kbps"
    MAX-ACCESS   read-only
    STATUS       obsolete
    DESCRIPTION
        "The operating PIR."
    ::= { tSapIngressQueueEntry 17 }

tSapIngressQueueOperCIR OBJECT-TYPE
    SYNTAX       TCIRRate
    UNITS        "kbps"
    MAX-ACCESS   read-only
    STATUS       obsolete
    DESCRIPTION
        "The operational value derived by computing the CIR value from
         the administrative CIR and PIR values and their corresponding
         adaptation rules."
    ::= { tSapIngressQueueEntry 18 }

tSapIngressQueueLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "timestamp of last change to this row in tSapIngressQueueTable."
    ::= { tSapIngressQueueEntry 19 }

tSapIngressQueuePoliced OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSapIngressQueuePoliced specifies that the out of profile
         traffic feeding into the physical queue instance should be
         dropped. tSapIngressQueuePoliced overrides the bandwidth
         specified by the object tSapIngressQueueAdminCIR."
    DEFVAL { false }
    ::= { tSapIngressQueueEntry 20 }

tSapIngressQueueMode OBJECT-TYPE
    SYNTAX       TQueueMode
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSapIngressQueueMode specifies the mode in which the queue
         is operating. This attribute is associated with the queue
         at the time of creation and cannot be modified thereafter."
    DEFVAL { priority }
    ::= { tSapIngressQueueEntry 21 }

tSapIngressQueuePoolName OBJECT-TYPE   
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tSapIngressQueuePoolName specifies the name of 
         the pool to be applied for SAP ingress queue."
    DEFVAL { ''H }    
    ::= { tSapIngressQueueEntry 22 }

--
-- sap-ingress policy dscp table
--

tSapIngressDSCPTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TSapIngressDSCPEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "a list of all DSCP entries for all sap-ingress policies."
    ::= { tSapIngressObjects 3 }

tSapIngressDSCPEntry OBJECT-TYPE
    SYNTAX       TSapIngressDSCPEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about a particular DSCP entry for a sap-ingress policy.

        Entries are created by user.
        Entries are deleted by user.

        There is no StorageType object, entries have a presumed
        StorageType of nonVolatile.

        in the event that one policy has multiple entries, and
        the tSapIngressDSCP values are the same, only one queue
        will be created (the queue for the lexicographically
        first tSapIngressDSCP).
        "
    INDEX { tSapIngressIndex, tSapIngressDSCP }
    ::= { tSapIngressDSCPTable 1 }

TSapIngressDSCPEntry ::= SEQUENCE
    {
        tSapIngressDSCP                    TDSCPName,
        tSapIngressDSCPRowStatus           RowStatus,
        tSapIngressDSCPFC                  TNamedItemOrEmpty,
        tSapIngressDSCPPriority            TPriorityOrDefault,
        tSapIngressDSCPLastChanged         TimeStamp
    }

tSapIngressDSCP OBJECT-TYPE
    SYNTAX       TDSCPName
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        ""
    ::= { tSapIngressDSCPEntry 1 }

tSapIngressDSCPRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "This object allows creation/deletion of rows in this table."
    ::= { tSapIngressDSCPEntry 2 }

tSapIngressDSCPFC OBJECT-TYPE
    SYNTAX       TNamedItemOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSapIngressDSCPFC specifies the forwarding class or the sub
         forwarding class to be used to classify all the traffic that
         matches the specific DSCP i.e. tSapIngressDSCP.

         Specification of a sub forwarding class is also allowed. The
         format of a sub forwarding class is the concatenation of the
         base forwarding class and a sub class identifier in the form

             base-fc-name[.sub-fc-name]

         To reference a sub forwarding class, the mapping of the sub
         forwarding class should be existing in this QoS policy.

         Unless tSapIngressDSCPFC is specified explicitly, this match
         will be ignored for classification purposes."
    DEFVAL { ''H }
    ::= { tSapIngressDSCPEntry 3 }

tSapIngressDSCPPriority OBJECT-TYPE
    SYNTAX       TPriorityOrDefault
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        ""
    DEFVAL { default }
    ::= { tSapIngressDSCPEntry 4 }

tSapIngressDSCPLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "timestamp of last change to this row in tSapIngressDSCPTable."
    ::= { tSapIngressDSCPEntry 5 }

--
-- sap-ingress policy dot1p table
--

tSapIngressDot1pTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TSapIngressDot1pEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "a list of all dot1p entries for all sap-ingress policies."
    ::= { tSapIngressObjects 4 }

tSapIngressDot1pEntry OBJECT-TYPE
    SYNTAX       TSapIngressDot1pEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about a particular dot1p match for a sap-ingress policy.
         Entries are created by user.
         Entries are deleted by user.

         There is no StorageType object, entries have a presumed
         StorageType of nonVolatile.
        "
    INDEX { tSapIngressIndex, tSapIngressDot1pValue }
    ::= { tSapIngressDot1pTable 1 }

TSapIngressDot1pEntry ::= SEQUENCE
    {
        tSapIngressDot1pValue               Dot1PPriority,
        tSapIngressDot1pRowStatus           RowStatus,
        tSapIngressDot1pFC                  TNamedItemOrEmpty,
        tSapIngressDot1pPriority            TPriorityOrDefault,
        tSapIngressDot1pLastChanged         TimeStamp
    }

tSapIngressDot1pValue OBJECT-TYPE
    SYNTAX       Dot1PPriority (0..7)
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "dot1p value to match in the packet.  this value is
        also used as an index so that for any dot1p value there
        is only one possible disposition queue and priority.
        "
    ::= { tSapIngressDot1pEntry 1 }

tSapIngressDot1pRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "Row Status for this sap-ingress policy's dot1p entry."
    ::= { tSapIngressDot1pEntry 2 }

tSapIngressDot1pFC OBJECT-TYPE
    SYNTAX       TNamedItemOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSapIngressDot1pFC specifies the forwarding class or the sub
         forwarding class to be used to classify all the traffic that
         matches the specific Dot1p i.e. tSapIngressDot1pValue.

         Specification of a sub forwarding class is also allowed. The
         format of a sub forwarding class is the concatenation of the
         base forwarding class and a sub class identifier in the form

             base-fc-name[.sub-fc-name]

         To reference a sub forwarding class, the mapping of the sub
         forwarding class should be existing in this QoS policy.

         Unless tSapIngressDot1pFC is specified explicitly, this match
         will be ignored for classification purposes."
    DEFVAL { ''H }
    ::= { tSapIngressDot1pEntry 3 }

tSapIngressDot1pPriority OBJECT-TYPE
    SYNTAX       TPriorityOrDefault
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "priority to use for packets that match the dot1p value
        tSapIngressDot1pValue."
    DEFVAL { default }
    ::= { tSapIngressDot1pEntry 4 }

tSapIngressDot1pLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "timestamp of last change to this row in tSapIngressDot1pTable."
    ::= { tSapIngressDot1pEntry 5 }

--
-- sap-ingress policy ip criteria table
--

tSapIngressIPCriteriaTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TSapIngressIPCriteriaEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "information about all sap-ingress policies' ip-criteria entries."
    ::= { tSapIngressObjects 5 }

tSapIngressIPCriteriaEntry OBJECT-TYPE
    SYNTAX       TSapIngressIPCriteriaEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about a particular ip-criteria entry for a sap-ingress
         policy.
         Entries are created by user.
         Entries are deleted by user.
         There is no StorageType object, entries have a presumed
         StorageType of nonVolatile.
        "
    INDEX { tSapIngressIndex, tSapIngressIPCriteriaIndex }
    ::= { tSapIngressIPCriteriaTable 1 }

TSapIngressIPCriteriaEntry ::= SEQUENCE
    {
        tSapIngressIPCriteriaIndex               TEntryId,
        tSapIngressIPCriteriaRowStatus           RowStatus,
        tSapIngressIPCriteriaDescription         TItemDescription,
        tSapIngressIPCriteriaActionFC            TNamedItemOrEmpty,
        tSapIngressIPCriteriaActionPriority      TPriorityOrDefault,
        tSapIngressIPCriteriaSourceIpAddr        IpAddress,
        tSapIngressIPCriteriaSourceIpMask        IpAddressPrefixLength,
        tSapIngressIPCriteriaDestIpAddr          IpAddress,
        tSapIngressIPCriteriaDestIpMask          IpAddressPrefixLength,
        tSapIngressIPCriteriaProtocol            TIpProtocol,
        tSapIngressIPCriteriaSourcePortValue1    TTcpUdpPort,
        tSapIngressIPCriteriaSourcePortValue2    TTcpUdpPort,
        tSapIngressIPCriteriaSourcePortOperator  TTcpUdpPortOperator,
        tSapIngressIPCriteriaDestPortValue1      TTcpUdpPort,
        tSapIngressIPCriteriaDestPortValue2      TTcpUdpPort,
        tSapIngressIPCriteriaDestPortOperator    TTcpUdpPortOperator,
        tSapIngressIPCriteriaDSCP                TDSCPNameOrEmpty,
        tSapIngressIPCriteriaFragment            TItemMatch,
        tSapIngressIPCriteriaLastChanged         TimeStamp
    }

tSapIngressIPCriteriaIndex OBJECT-TYPE
    SYNTAX       TEntryId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "unique identifier separates this entry from others in this
         policy."
    ::= { tSapIngressIPCriteriaEntry 1 }

tSapIngressIPCriteriaRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "Row Status for the IP Criteria entry."
    ::= { tSapIngressIPCriteriaEntry 2 }

tSapIngressIPCriteriaDescription OBJECT-TYPE
    SYNTAX       TItemDescription
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "description of this IP criteria entry."
    DEFVAL { ''H }
    ::= { tSapIngressIPCriteriaEntry 3 }

tSapIngressIPCriteriaActionFC OBJECT-TYPE
    SYNTAX       TNamedItemOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSapIngressIPCriteriaActionFC specifies the forwarding class
         or the sub forwarding class to be used to classify all the
         traffic that matches the specific IP criteria.

         Specification of a sub forwarding class is also allowed. The
         format of a sub forwarding class is the concatenation of the
         base forwarding class and a sub class identifier in the form

             base-fc-name[.sub-fc-name]

         To reference a sub forwarding class, the mapping of the sub
         forwarding class should be existing in this QoS policy.

         Unless tSapIngressIPCriteriaActionFC is specified explicitly,
         this match will be ignored for classification purposes."
    DEFVAL { ''H }
    ::= { tSapIngressIPCriteriaEntry 4 }

tSapIngressIPCriteriaActionPriority OBJECT-TYPE
    SYNTAX       TPriorityOrDefault
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
       "priority for packets which match this entry's criteria.

        when set to default, the packet is sent to the queue
        from DSCP/dot1p/FC which matched the packet, using the
        priority from the DSCP/dot1p/FC which matched the packet."
    DEFVAL { default }
    ::= { tSapIngressIPCriteriaEntry 5 }

tSapIngressIPCriteriaSourceIpAddr OBJECT-TYPE
    SYNTAX       IpAddress
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "IP address to match with source-IP of the packet."
    DEFVAL { '00000000'H }
    ::= { tSapIngressIPCriteriaEntry 6 }

tSapIngressIPCriteriaSourceIpMask OBJECT-TYPE
    SYNTAX       IpAddressPrefixLength
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "the prefix is the number of bits of the source IP address to match."
    DEFVAL { 0 }
    ::= { tSapIngressIPCriteriaEntry 7 }

tSapIngressIPCriteriaDestIpAddr OBJECT-TYPE
    SYNTAX       IpAddress
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "IP address to match with destination-IP of the packet."
    DEFVAL { '00000000'H }
    ::= { tSapIngressIPCriteriaEntry 8 }

tSapIngressIPCriteriaDestIpMask OBJECT-TYPE
    SYNTAX       IpAddressPrefixLength
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "the prefix is the number of bits of the destination IP address to
         match."
    DEFVAL { 0 }
    ::= { tSapIngressIPCriteriaEntry 9 }

tSapIngressIPCriteriaProtocol OBJECT-TYPE
    SYNTAX       TIpProtocol
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "IP protocol to match.  use -1 to disable matching by IP protocol."
    DEFVAL { -1 }
    ::= { tSapIngressIPCriteriaEntry 10 }

tSapIngressIPCriteriaSourcePortValue1 OBJECT-TYPE
    SYNTAX       TTcpUdpPort
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "TCP/UDP port value1. The value of this object is used according
         to the description for tSapIngressIPCriteriaSourcePortOperator."
    DEFVAL { 0 }
    ::= { tSapIngressIPCriteriaEntry 11 }

tSapIngressIPCriteriaSourcePortValue2 OBJECT-TYPE
    SYNTAX       TTcpUdpPort
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "TCP/UDP port value2. The value of this object is used according
         to the description for tSapIngressIPCriteriaSourcePortOperator."
    DEFVAL { 0 }
    ::= { tSapIngressIPCriteriaEntry 12 }

tSapIngressIPCriteriaSourcePortOperator OBJECT-TYPE
    SYNTAX       TTcpUdpPortOperator
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The operator specifies the manner in which
         tSapIngressIPCriteriaSourcePortValue1 and 
         tSapIngressIPCriteriaSourcePortValue2 are to be used. The value 
         of these latter 2 objects and 
         tSapIngressIPCriteriaSourcePortOperator is used as described in 
         TTcpUdpPortOperator."
    DEFVAL { none }
    ::= { tSapIngressIPCriteriaEntry 13 }

tSapIngressIPCriteriaDestPortValue1 OBJECT-TYPE
    SYNTAX       TTcpUdpPort
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "TCP/UDP port value1. The value of this object is used according
         to the description for tSapIngressIPCriteriaDestPortOperator."
    DEFVAL { 0 }
    ::= { tSapIngressIPCriteriaEntry 14 }

tSapIngressIPCriteriaDestPortValue2 OBJECT-TYPE
    SYNTAX       TTcpUdpPort
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "TCP/UDP port value2. The value of this object is used according
         to the description for tSapIngressIPCriteriaDestPortOperator."
    DEFVAL { 0 }
    ::= { tSapIngressIPCriteriaEntry 15 }

tSapIngressIPCriteriaDestPortOperator OBJECT-TYPE
    SYNTAX       TTcpUdpPortOperator
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The operator specifies the manner in which
         tSapIngressIPCriteriaDestPortValue1 and 
         tSapIngressIPCriteriaDestPortValue2 are to be used. The value 
         of these latter 2 objects and 
         tSapIngressIPCriteriaDestPortOperator is used as described in 
         TTcpUdpPortOperator."
    DEFVAL { none }
    ::= { tSapIngressIPCriteriaEntry 16 }

tSapIngressIPCriteriaDSCP OBJECT-TYPE
    SYNTAX       TDSCPNameOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "DSCP value to match in the packet"
    DEFVAL { ''H }
    ::= { tSapIngressIPCriteriaEntry 17 }

tSapIngressIPCriteriaFragment OBJECT-TYPE
    SYNTAX       TItemMatch
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "matches when the packet is a fragment (true)
         or when the packet is not a fragment (false)
         or matches all packets fragmented or not (off)."
    DEFVAL { off }
    ::= { tSapIngressIPCriteriaEntry 19 }

tSapIngressIPCriteriaLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "timestamp of last change to this row in tSapIngressIPCriteriaTable."
    ::= { tSapIngressIPCriteriaEntry 20 }

--
-- sap-ingress policy mac criteria table
--

tSapIngressMacCriteriaTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TSapIngressMacCriteriaEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "information about all sap-ingress policies' mac-criteria entries."
    ::= { tSapIngressObjects 6 }

tSapIngressMacCriteriaEntry OBJECT-TYPE
    SYNTAX       TSapIngressMacCriteriaEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about a particular mac-criteria entry
         for a sap-ingress policy.
         Entries are created by user.
         Entries are deleted by user.
         There is no StorageType object, entries have a presumed
         StorageType of nonVolatile."
    INDEX { tSapIngressIndex, tSapIngressMacCriteriaIndex }
    ::= { tSapIngressMacCriteriaTable 1 }

TSapIngressMacCriteriaEntry ::= SEQUENCE
    {
        tSapIngressMacCriteriaIndex          TEntryId,
        tSapIngressMacCriteriaRowStatus      RowStatus,
        tSapIngressMacCriteriaDescription    TItemDescription,
        tSapIngressMacCriteriaActionFC       TNamedItemOrEmpty,
        tSapIngressMacCriteriaActionPriority TPriorityOrDefault,
        tSapIngressMacCriteriaFrameType      TFrameType,
        tSapIngressMacCriteriaSrcMacAddr     MacAddress,
        tSapIngressMacCriteriaSrcMacMask     MacAddress,
        tSapIngressMacCriteriaDstMacAddr     MacAddress,
        tSapIngressMacCriteriaDstMacMask     MacAddress,
        tSapIngressMacCriteriaDot1PValue     Dot1PPriority,
        tSapIngressMacCriteriaDot1PMask      Dot1PPriority,
        tSapIngressMacCriteriaEthernetType   Integer32,
        tSapIngressMacCriteriaDSAP           ServiceAccessPoint,
        tSapIngressMacCriteriaDSAPMask       ServiceAccessPoint,
        tSapIngressMacCriteriaSSAP           ServiceAccessPoint,
        tSapIngressMacCriteriaSSAPMask       ServiceAccessPoint,
        tSapIngressMacCriteriaSnapPid        Integer32,
        tSapIngressMacCriteriaSnapOui        INTEGER,
        tSapIngressMacCriteriaLastChanged    TimeStamp
    }

tSapIngressMacCriteriaIndex OBJECT-TYPE
    SYNTAX       TEntryId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION ""
    ::= { tSapIngressMacCriteriaEntry 1 }

tSapIngressMacCriteriaRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION ""
    ::= { tSapIngressMacCriteriaEntry 2 }

tSapIngressMacCriteriaDescription OBJECT-TYPE
    SYNTAX       TItemDescription
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION ""
    DEFVAL { ''H }
    ::= { tSapIngressMacCriteriaEntry 3 }

tSapIngressMacCriteriaActionFC OBJECT-TYPE
    SYNTAX       TNamedItemOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSapIngressMacCriteriaActionFC specifies the forwarding class
         or the sub forwarding class to be used to classify all the
         traffic that matches the specific Mac criteria.

         Specification of a sub forwarding class is also allowed. The
         format of a sub forwarding class is the concatenation of the
         base forwarding class and a sub class identifier in the form

             base-fc-name[.sub-fc-name]

         To reference a sub forwarding class, the mapping of the sub
         forwarding class should be existing in this QoS policy.

         Unless tSapIngressMacCriteriaActionFC is specified explicitly,
         this match will be ignored for classification purposes."
    ::= { tSapIngressMacCriteriaEntry 4 }

tSapIngressMacCriteriaActionPriority OBJECT-TYPE
    SYNTAX       TPriorityOrDefault
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION ""
    DEFVAL { default }
    ::= { tSapIngressMacCriteriaEntry 5 }

tSapIngressMacCriteriaFrameType OBJECT-TYPE
    SYNTAX       TFrameType
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The type of mac frame for which we are defining this match criteria."
    DEFVAL { e802dot3 }
    ::= { tSapIngressMacCriteriaEntry 6 }

tSapIngressMacCriteriaSrcMacAddr OBJECT-TYPE
    SYNTAX       MacAddress
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION ""
    DEFVAL { '000000000000'H }
    ::= { tSapIngressMacCriteriaEntry 8 }

tSapIngressMacCriteriaSrcMacMask OBJECT-TYPE
    SYNTAX       MacAddress
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION ""
    DEFVAL { '000000000000'H }
    ::= { tSapIngressMacCriteriaEntry 9 }

tSapIngressMacCriteriaDstMacAddr OBJECT-TYPE
    SYNTAX       MacAddress
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION ""
    DEFVAL { '000000000000'H }
    ::= { tSapIngressMacCriteriaEntry 10 }

tSapIngressMacCriteriaDstMacMask OBJECT-TYPE
    SYNTAX       MacAddress
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION ""
    DEFVAL { '000000000000'H }
    ::= { tSapIngressMacCriteriaEntry 11 }

tSapIngressMacCriteriaDot1PValue OBJECT-TYPE
    SYNTAX       Dot1PPriority
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION ""
    DEFVAL { -1 }
    ::= { tSapIngressMacCriteriaEntry 12 }

tSapIngressMacCriteriaDot1PMask OBJECT-TYPE
    SYNTAX       Dot1PPriority
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION ""
    DEFVAL { 0 }
    ::= { tSapIngressMacCriteriaEntry 13 }

tSapIngressMacCriteriaEthernetType OBJECT-TYPE
    SYNTAX       Integer32 (-1 | 1536..65535)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "the ethernet type, ssap/dsap, and snap-pid match criteria
         are mutually exclusive and only one can be set per entry."
    DEFVAL { -1 }
    ::= { tSapIngressMacCriteriaEntry 14 }

tSapIngressMacCriteriaDSAP OBJECT-TYPE
    SYNTAX       ServiceAccessPoint
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "the ethernet type, ssap/dsap, and snap-pid match criteria
         are mutually exclusive and only one can be set per entry."
    DEFVAL { -1 }
    ::= { tSapIngressMacCriteriaEntry 15 }

tSapIngressMacCriteriaDSAPMask OBJECT-TYPE
    SYNTAX       ServiceAccessPoint
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "the ethernet type, ssap/dsap, and snap-pid match criteria
         are mutually exclusive and only one can be set per entry."
    DEFVAL { -1 }
    ::= { tSapIngressMacCriteriaEntry 16 }

tSapIngressMacCriteriaSSAP OBJECT-TYPE
    SYNTAX       ServiceAccessPoint
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "the ethernet type, ssap/dsap, and snap-pid match criteria
         are mutually exclusive and only one can be set per entry."
    DEFVAL { -1 }
    ::= { tSapIngressMacCriteriaEntry 17 }

tSapIngressMacCriteriaSSAPMask OBJECT-TYPE
    SYNTAX       ServiceAccessPoint
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "the ethernet type, ssap/dsap, and snap-pid match criteria
         are mutually exclusive and only one can be set per entry."
    DEFVAL { -1 }
    ::= { tSapIngressMacCriteriaEntry 18 }

tSapIngressMacCriteriaSnapPid OBJECT-TYPE
    SYNTAX       Integer32 (-1 | 0..65535)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "the ethernet type, ssap/dsap, and snap-pid match criteria
         are mutually exclusive and only one can be set per entry."
    DEFVAL { -1 }
    ::= { tSapIngressMacCriteriaEntry 19 }

tSapIngressMacCriteriaSnapOui OBJECT-TYPE
    SYNTAX          INTEGER { off(1), zero(2), nonZero(3) }
    MAX-ACCESS      read-create
    STATUS          current
    DESCRIPTION     "whether to match snap-oui, and what to match."
    DEFVAL { off }
    ::= { tSapIngressMacCriteriaEntry 20 }

tSapIngressMacCriteriaLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "timestamp of last change to this row in tSapIngressMacCriteriaTable."
    ::= { tSapIngressMacCriteriaEntry 21 }

--
-- sap-ingress policy fc table
--

tSapIngressFCTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TSapIngressFCEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "This table maintains the mapping of a particular
         forwarding class traffic into the specified queue."
    ::= { tSapIngressObjects 7 }

tSapIngressFCEntry OBJECT-TYPE
    SYNTAX       TSapIngressFCEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about a particular sap-ingress policy's
         forwarding class mappings to queues.

         Entries are created and deleted by user. They have
         a presumed StorageType of nonVolatile."
    INDEX { tSapIngressIndex, tSapIngressFCName }
    ::= { tSapIngressFCTable 1 }

TSapIngressFCEntry ::= SEQUENCE
    {
        tSapIngressFCName             TNamedItem,
        tSapIngressFCRowStatus        RowStatus,
        tSapIngressFCQueue            TIngressQueueId,
        tSapIngressFCMCastQueue       TIngressQueueId,
        tSapIngressFCBCastQueue       TIngressQueueId,
        tSapIngressFCUnknownQueue     TIngressQueueId,
        tSapIngressFCLastChanged      TimeStamp,
        tSapIngressFCInProfRemark     TRemarkType,
        tSapIngressFCInProfDscp       TNamedItemOrEmpty,
        tSapIngressFCInProfPrec       TPrecValueOrNone,
        tSapIngressFCOutProfRemark    TRemarkType,
        tSapIngressFCOutProfDscp      TNamedItemOrEmpty,
        tSapIngressFCOutProfPrec      TPrecValueOrNone,
        tSapIngressFCProfile          TProfileOrNone,
        tSapIngressFCDE1OutOfProfile  TruthValue
    }

tSapIngressFCName OBJECT-TYPE
    SYNTAX       TNamedItem
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "tSapIngressFCName specifies the forwarding class or the sub
         forwarding class for which this mapping is defined.

         A sub forwarding class mapping can also be configured. The
         format of a sub forwarding class is the concatenation of the
         base forwarding class and a sub class identifier in the form

             base-fc-name[.sub-fc-name]

         A sub forwarding class mapping should exist in this table if
         any explicit match criteria in this SAP ingress QoS policy
         try to use it. However, it is not the same for the base
         forwarding classes."
    ::= { tSapIngressFCEntry 1 }

tSapIngressFCRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "Row Status for this queue."
    ::= { tSapIngressFCEntry 2 }

tSapIngressFCQueue OBJECT-TYPE
    SYNTAX       TIngressQueueId
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The specific queue to be used for packets in this forwarding class.
         A value of zero implies that the default queues should be used."
    ::= { tSapIngressFCEntry 3 }

tSapIngressFCMCastQueue OBJECT-TYPE
    SYNTAX       TIngressQueueId
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The specific multicast queue to be used for packets in this forwarding
         class. The queue is used only for specific entities and will be
         ignored wherever it is irrelevant.

         A value of zero implies that the default queues should be used."
    ::= { tSapIngressFCEntry 4 }

tSapIngressFCBCastQueue OBJECT-TYPE
    SYNTAX       TIngressQueueId
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The specific broadcast queue to be used for packets in this forwarding
         class. The queue is used only for specific entities and will be
         ignored wherever it is irrelevant.
         A value of zero implies that the default queues should be used."
    ::= { tSapIngressFCEntry 5 }

tSapIngressFCUnknownQueue OBJECT-TYPE
    SYNTAX       TIngressQueueId
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The specific unknown destination queue to be used for packets in this
         forwarding class. The queue is used only for specific entities and
         will be ignored wherever it is irrelevant.
         A value of zero implies that the default queues should be used."
    ::= { tSapIngressFCEntry 6 }

tSapIngressFCLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tSapIngressFCLastChanged indicates the value of sysUpTime
         when the forwarding class entry was last modified."
    ::= { tSapIngressFCEntry 7 }

tSapIngressFCInProfRemark OBJECT-TYPE
    SYNTAX       TRemarkType
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSapIngressFCInProfRemark specifies the remarking type to
         be used for the in profile packets feeding into the queues."
    DEFVAL { none }
    ::= { tSapIngressFCEntry 8 }

tSapIngressFCInProfDscp OBJECT-TYPE
    SYNTAX       TNamedItemOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSapIngressFCInProfDscp specifies the DSCP to be used while
         remarking the in profile packets when the in-profile
         remarking type, tSapIngressFCInProfRemark is specified to be
         'dscp'."
    DEFVAL { ''H }
    ::= { tSapIngressFCEntry 9 }

tSapIngressFCInProfPrec OBJECT-TYPE
    SYNTAX       TPrecValueOrNone
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSapIngressFCInProfPrec specifies the precedence value to be used
         while remarking the in profile packets when the in-profile
         remarking type, tSapIngressFCInProfRemark is specified to be
         'precedence'."
    DEFVAL { -1 }
    ::= { tSapIngressFCEntry 10 }

tSapIngressFCOutProfRemark OBJECT-TYPE
    SYNTAX       TRemarkType
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSapIngressFCOutProfRemark specifies the remarking type to
         be used for the in profile packets feeding into the queues."
    DEFVAL { none }
    ::= { tSapIngressFCEntry 11 }

tSapIngressFCOutProfDscp OBJECT-TYPE
    SYNTAX       TNamedItemOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSapIngressFCOutProfDscp specifies the DSCP to be used while
         remarking the in profile packets when the in-profile
         remarking type, tSapIngressFCOutProfRemark is specified to be
         'dscp'."
    DEFVAL { ''H }
    ::= { tSapIngressFCEntry 12 }

tSapIngressFCOutProfPrec OBJECT-TYPE
    SYNTAX       TPrecValueOrNone
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSapIngressFCOutProfPrec specifies the precedence value to be used
         while remarking the in profile packets when the in-profile
         remarking type, tSapIngressFCOutProfRemark is specified to be
         'precedence'."
    DEFVAL { -1 }
    ::= { tSapIngressFCEntry 13 }

tSapIngressFCProfile OBJECT-TYPE
    SYNTAX       TProfileOrNone
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSapIngressFCProfile specifies the profile of the packets
         associated with this forwarding class.

         This object can not be set to anything but 'none' if the
         queues being used by this mapping do not have their modes,
         tSapIngressQueueMode set to 'profile'."
    DEFVAL { none }
    ::= { tSapIngressFCEntry 14 }

tSapIngressFCDE1OutOfProfile OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of tSapIngressFCDE1OutOfProfile specifies whether frames
        with DE value of '1' are to be treated as out-of-profile (as if
        tSapIngressFCProfile was set to 'out'). 
        
        When tSapIngressFCDE1OutOfProfile is set to 'true', frames with DE value
        of '1' are autmatically classified as out-of-profile.

        Frames with DE value of '0' will continue to be policed based on the
        value of tSapIngressQueueAdminCIR.  That is, if the dynamic rate of
        ingress queue is within CIR, frames with DE=0 will be treated as
        in-profile (as if tSapIngressFCProfile was set to 'in') otherwise these
        frames are treated as out-of-profile.
        
        When tSapIngressFCDE1OutOfProfile is set to 'true', it supercedes action
        specified by the value of tSapIngressFCProfile."
    DEFVAL { false }
    ::= { tSapIngressFCEntry 18 }

--
-- sap-ingress policy precedence table
--

tSapIngressPrecTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TSapIngressPrecEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION  "This table maintains the mapping of a particular
                  precedence value into a forwarding class"
    ::= { tSapIngressObjects 8 }

tSapIngressPrecEntry OBJECT-TYPE
    SYNTAX       TSapIngressPrecEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about a particular sap-ingress policy's
         IP precedence values to forwarding class mappings.

         Entries are created and deleted by user. They have
         a presumed StorageType of nonVolatile."
    INDEX { tSapIngressIndex, tSapIngressPrecValue }
    ::= { tSapIngressPrecTable 1 }

TSapIngressPrecEntry ::= SEQUENCE
    {
        tSapIngressPrecValue               TPrecValue,
        tSapIngressPrecRowStatus           RowStatus,
        tSapIngressPrecFC                  TNamedItemOrEmpty,
        tSapIngressPrecFCPriority          TPriorityOrDefault,
        tSapIngressPrecLastChanged         TimeStamp
    }

tSapIngressPrecValue OBJECT-TYPE
    SYNTAX       TPrecValue
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Precedence value for which the mapping is done."
    ::= { tSapIngressPrecEntry 1 }

tSapIngressPrecRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "Row Status for this mapping."
    ::= { tSapIngressPrecEntry 2 }

tSapIngressPrecFC OBJECT-TYPE
    SYNTAX       TNamedItemOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSapIngressPrecFC specifies the forwarding class or the sub
         forwarding class to be used to classify all the traffic that
         matches the specific precedence value i.e. tSapIngressPrecValue.

         Specification of a sub forwarding class is also allowed. The
         format of a sub forwarding class is the concatenation of the
         base forwarding class and a sub class identifier in the form

             base-fc-name[.sub-fc-name]

         To reference a sub forwarding class, the mapping of the sub
         forwarding class should be existing in this QoS policy.

         Unless tSapIngressPrecFC is specified explicitly, this match
         will be ignored for classification purposes."
    DEFVAL { ''H }
    ::= { tSapIngressPrecEntry 3 }

tSapIngressPrecFCPriority OBJECT-TYPE
    SYNTAX       TPriorityOrDefault
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The priority to be assigned to the matching traffic."
    DEFVAL { default }
    ::= { tSapIngressPrecEntry 4 }

tSapIngressPrecLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "timestamp of last change to this row in tSapIngressPrecTable."
    ::= { tSapIngressPrecEntry 5 }

--
-- sap-ingress policy ipv6 criteria table
--

tSapIngressIPv6CriteriaTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TSapIngressIPv6CriteriaEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about all sap-ingress policies' ipv6-criteria entries."
    ::= { tSapIngressObjects 9 }

tSapIngressIPv6CriteriaEntry OBJECT-TYPE
    SYNTAX       TSapIngressIPv6CriteriaEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about a particular ipv6-criteria entry for a sap-ingress
         policy.
         Entries are created/deleted by user.
         There is no StorageType object, entries have a presumed
         StorageType of nonVolatile."
    INDEX { tSapIngressIndex, tSapIngressIPv6CriteriaIndex }
    ::= { tSapIngressIPv6CriteriaTable 1 }

TSapIngressIPv6CriteriaEntry ::= SEQUENCE
    {
        tSapIngressIPv6CriteriaIndex               TEntryId,
        tSapIngressIPv6CriteriaRowStatus           RowStatus,
        tSapIngressIPv6CriteriaDescription         TItemDescription,
        tSapIngressIPv6CriteriaActionFC            TNamedItemOrEmpty,
        tSapIngressIPv6CriteriaActionPriority      TPriorityOrDefault,
        tSapIngressIPv6CriteriaSourceIpAddr        InetAddressIPv6,
        tSapIngressIPv6CriteriaSourceIpMask        InetAddressPrefixLength,
        tSapIngressIPv6CriteriaDestIpAddr          InetAddressIPv6,
        tSapIngressIPv6CriteriaDestIpMask          InetAddressPrefixLength,
        tSapIngressIPv6CriteriaNextHeader          TIpProtocol,
        tSapIngressIPv6CriteriaSourcePortValue1    TTcpUdpPort,
        tSapIngressIPv6CriteriaSourcePortValue2    TTcpUdpPort,
        tSapIngressIPv6CriteriaSourcePortOperator  TTcpUdpPortOperator,
        tSapIngressIPv6CriteriaDestPortValue1      TTcpUdpPort,
        tSapIngressIPv6CriteriaDestPortValue2      TTcpUdpPort,
        tSapIngressIPv6CriteriaDestPortOperator    TTcpUdpPortOperator,
        tSapIngressIPv6CriteriaDSCP                TDSCPNameOrEmpty,
        tSapIngressIPv6CriteriaLastChanged         TimeStamp
    }

tSapIngressIPv6CriteriaIndex OBJECT-TYPE
    SYNTAX       TEntryId
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "The value of the object tSapIngressIPv6CriteriaIndex specifies
         uniquely each entry in the policy."
    ::= { tSapIngressIPv6CriteriaEntry 1 }

tSapIngressIPv6CriteriaRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of the object tSapIngressIPv6CriteriaRowStatus specifies the
         Row Status for the IPv6 Criteria entry."
    ::= { tSapIngressIPv6CriteriaEntry 2 }

tSapIngressIPv6CriteriaDescription OBJECT-TYPE
    SYNTAX       TItemDescription
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of the object tSapIngressIPv6CriteriaDescription specifies
         an optional user provided description of this IPv6 criteria entry."
    DEFVAL { ''H }
    ::= { tSapIngressIPv6CriteriaEntry 3 }

tSapIngressIPv6CriteriaActionFC OBJECT-TYPE
    SYNTAX       TNamedItemOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of the object tSapIngressIPv6CriteriaActionFC specifies the
         forwarding class or the sub forwarding class to be used to classify
         all the traffic that matches the specific IPv6 criteria.

         Specification of a sub forwarding class is also allowed. The
         format of a sub forwarding class is the concatenation of the
         base forwarding class and a sub class identifier in the form

             base-fc-name[.sub-fc-name]

         To reference a sub forwarding class, the mapping of the sub
         forwarding class should be existing in this QoS policy.

         Unless tSapIngressIPv6CriteriaActionFC is specified explicitly,
         this match will be ignored for classification purposes."
    DEFVAL { ''H }
    ::= { tSapIngressIPv6CriteriaEntry 4 }

tSapIngressIPv6CriteriaActionPriority OBJECT-TYPE
    SYNTAX       TPriorityOrDefault
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
       "The value of the object tSapIngressIPv6CriteriaActionPriority specifies
        the priority for packets which match this entry's criteria.
        When set to 'default', the packet is sent to the queue
        from DSCP/dot1p/FC which matched the packet, using the
        priority from the DSCP/dot1p/FC which matched the packet."
    DEFVAL { default }
    ::= { tSapIngressIPv6CriteriaEntry 5 }

tSapIngressIPv6CriteriaSourceIpAddr OBJECT-TYPE
    SYNTAX       InetAddressIPv6
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of the object tSapIngressIPv6CriteriaSourceIpAddr specifies
         the IPv6 address to match with source-IPv6 of the packet."
    DEFVAL { '00000000000000000000000000000000'H }
    ::= { tSapIngressIPv6CriteriaEntry 6 }

tSapIngressIPv6CriteriaSourceIpMask OBJECT-TYPE
    SYNTAX       InetAddressPrefixLength (0..128)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of the object tSapIngressIPv6CriteriaSourceIpMask specifies
         the prefix is the number of bits of the source IPv6 address to match."
    DEFVAL { 0 }
    ::= { tSapIngressIPv6CriteriaEntry 7 }

tSapIngressIPv6CriteriaDestIpAddr OBJECT-TYPE
    SYNTAX       InetAddressIPv6
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of the object tSapIngressIPv6CriteriaDestIpAddr specifies
         the IPv6 address to match with destination-IPv6 of the packet."
    DEFVAL { '00000000000000000000000000000000'H }
    ::= { tSapIngressIPv6CriteriaEntry 8 }

tSapIngressIPv6CriteriaDestIpMask OBJECT-TYPE
    SYNTAX       InetAddressPrefixLength (0..128)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of the object tSapIngressIPv6CriteriaDestIpMask specifies
         the prefix is the number of bits of the destination IPv6 address to
         match."
    DEFVAL { 0 }
    ::= { tSapIngressIPv6CriteriaEntry 9 }

tSapIngressIPv6CriteriaNextHeader OBJECT-TYPE
    SYNTAX       TIpProtocol
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of the object tSapIngressIPv6CriteriaNextHeader specifies
         the Next Header to match.  use -1 to disable matching by Next Header.
         Because the match criteria only pertains to the last next-header, the
         following values are not accepted: 0, 43, 44, 50, 51, and 60."
    DEFVAL { -1 }
    ::= { tSapIngressIPv6CriteriaEntry 10 }

tSapIngressIPv6CriteriaSourcePortValue1 OBJECT-TYPE
    SYNTAX       TTcpUdpPort
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of the object tSapIngressIPv6CriteriaSourcePortValue1
         specifies the TCP/UDP port value1. The value of this object is used
         according to the description for
         tSapIngressIPv6CriteriaSourcePortOperator."
    DEFVAL { 0 }
    ::= { tSapIngressIPv6CriteriaEntry 11 }

tSapIngressIPv6CriteriaSourcePortValue2 OBJECT-TYPE
    SYNTAX       TTcpUdpPort
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of the object tSapIngressIPv6CriteriaSourcePortValue2
         specifies TCP/UDP port value2. The value of this object is used
         according to the description for
         tSapIngressIPv6CriteriaSourcePortOperator."
    DEFVAL { 0 }
    ::= { tSapIngressIPv6CriteriaEntry 12 }

tSapIngressIPv6CriteriaSourcePortOperator OBJECT-TYPE
    SYNTAX       TTcpUdpPortOperator
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of the object tSapIngressIPv6CriteriaSourcePortOperator
         specifies The operator specifies the manner in which
         tSapIngressIPv6CriteriaSourcePortValue1 and
         tSapIngressIPv6CriteriaSourcePortValue2 are to be used."
    DEFVAL { none }
    ::= { tSapIngressIPv6CriteriaEntry 13 }

tSapIngressIPv6CriteriaDestPortValue1 OBJECT-TYPE
    SYNTAX       TTcpUdpPort
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of the object tSapIngressIPv6CriteriaDestPortValue1
         specifies the TCP/UDP port value1. The value of this object is used
         according to the description for
         tSapIngressIPv6CriteriaDestPortOperator."
    DEFVAL { 0 }
    ::= { tSapIngressIPv6CriteriaEntry 14 }

tSapIngressIPv6CriteriaDestPortValue2 OBJECT-TYPE
    SYNTAX       TTcpUdpPort
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of the object tSapIngressIPv6CriteriaDestPortValue2
         specifies the TCP/UDP port value2. The value of this object is used
         according to the description for
         tSapIngressIPv6CriteriaDestPortOperator."
    DEFVAL { 0 }
    ::= { tSapIngressIPv6CriteriaEntry 15 }

tSapIngressIPv6CriteriaDestPortOperator OBJECT-TYPE
    SYNTAX       TTcpUdpPortOperator
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of the object tSapIngressIPv6CriteriaDestPortOperator
         specifies the manner in which
         tSapIngressIPv6CriteriaDestPortValue1 and
         tSapIngressIPv6CriteriaDestPortValue2 are to be used."
    DEFVAL { none }
    ::= { tSapIngressIPv6CriteriaEntry 16 }

tSapIngressIPv6CriteriaDSCP OBJECT-TYPE
    SYNTAX       TDSCPNameOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of the object tSapIngressIPv6CriteriaDSCP specifies the
         DSCP value to match in the packet"
    DEFVAL { ''H }
    ::= { tSapIngressIPv6CriteriaEntry 17 }

tSapIngressIPv6CriteriaLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "The value of the object indicates timestamp of last change to this
         row in tSapIngressIPv6CriteriaTable."
    ::= { tSapIngressIPv6CriteriaEntry 20 }

--
-- sap-egress policy table
--
tSapEgressTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TSapEgressEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "information about all sap-egress policies."
    ::= { tSapEgressObjects 1 }

tSapEgressEntry OBJECT-TYPE
    SYNTAX       TSapEgressEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about a particular sap-egress policy.
         Entries are created by user.
         Entries are deleted by user.
         There is no StorageType object, entries have a presumed
         StorageType of nonVolatile.

         the default sap-egress policy (1) cannot be changed or
         destroyed.
        "
    INDEX { tSapEgressIndex }
    ::= { tSapEgressTable 1 }

TSapEgressEntry ::= SEQUENCE
    {
        tSapEgressIndex              TSapEgressPolicyID,
        tSapEgressRowStatus          RowStatus,
        tSapEgressScope              TItemScope,
        tSapEgressDescription        TItemDescription,
        tSapEgressLastChanged        TimeStamp
    }

tSapEgressIndex OBJECT-TYPE
    SYNTAX       TSapEgressPolicyID
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "the sap-egress policy ID is used as index for the table."
    ::= { tSapEgressEntry 1 }

tSapEgressRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "Row Status of this policy's entry."
    ::= { tSapEgressEntry 2 }

tSapEgressScope OBJECT-TYPE
    SYNTAX       TItemScope
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "scope of the sap-egress policy."
    DEFVAL { template }
    ::= { tSapEgressEntry 3 }

tSapEgressDescription OBJECT-TYPE
    SYNTAX       TItemDescription
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "user-supplied description of this sap-egress policy."
    DEFVAL { ''H }
    ::= { tSapEgressEntry 4 }

tSapEgressLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "timestamp of last change to this row in tSapEgressTable."
    ::= { tSapEgressEntry 5 }

--
-- sap-egress policy queue table
--

tSapEgressQueueTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TSapEgressQueueEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION  "information about all sap-egress policies' queues."
    ::= { tSapEgressObjects 2 }

tSapEgressQueueEntry OBJECT-TYPE
    SYNTAX       TSapEgressQueueEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about a particular sap-egress queue.
         Entries are created/deleted by user.
         There is no StorageType object, entries have a presumed
         StorageType of nonVolatile."
    INDEX { tSapEgressIndex, tSapEgressQueueIndex }
    ::= { tSapEgressQueueTable 1 }

TSapEgressQueueEntry ::= SEQUENCE
    {
        tSapEgressQueueIndex             TEgressQueueId,
        tSapEgressQueueRowStatus         RowStatus,
        tSapEgressQueueParent            TNamedItemOrEmpty,
        tSapEgressQueueLevel             TLevel,
        tSapEgressQueueWeight            TWeight,
        tSapEgressQueueCIRLevel          TLevelOrDefault,
        tSapEgressQueueCIRWeight         TWeight,
        tSapEgressQueueExpedite          INTEGER,
        tSapEgressQueueCBS               TBurstSize,
        tSapEgressQueueMBS               TBurstSize,
        tSapEgressQueueHiPrioOnly        TBurstPercentOrDefault,
        tSapEgressQueuePIRAdaptation     TAdaptationRule,
        tSapEgressQueueCIRAdaptation     TAdaptationRule,
        tSapEgressQueueAdminPIR          TPIRRate,
        tSapEgressQueueAdminCIR          TCIRRate,
        tSapEgressQueueOperPIR           TPIRRate,
        tSapEgressQueueOperCIR           TCIRRate,
        tSapEgressQueueLastChanged       TimeStamp,
        tSapEgressQueueUsePortParent     TruthValue,
        tSapEgressQueuePortLvl           TLevel,
        tSapEgressQueuePortWght          TWeight,
        tSapEgressQueuePortCIRLvl        TLevelOrDefault,
        tSapEgressQueuePortCIRWght       TWeight,
        tSapEgressQueuePortAvgOverhead   Unsigned32,
        tSapEgressQueuePoolName          TNamedItemOrEmpty
    }

tSapEgressQueueIndex OBJECT-TYPE
    SYNTAX       TEgressQueueId (1..8)
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "the number of a queue.  zero is not allowed.
         for sap-egress, only three queues are allowed,
         so the queue index range is restricted."
    ::= { tSapEgressQueueEntry 1 }

tSapEgressQueueRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "Row Status of the entry."
    ::= { tSapEgressQueueEntry 2 }

tSapEgressQueueParent OBJECT-TYPE
    SYNTAX       TNamedItemOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The scheduler to which this queue would be feeding to."
    DEFVAL { ''H }
    ::= { tSapEgressQueueEntry 3 }

tSapEgressQueueLevel OBJECT-TYPE
    SYNTAX       TLevel
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "This specifies the level of priority while feeding to the parent."
    DEFVAL { 1 }
    ::= { tSapEgressQueueEntry 4 }

tSapEgressQueueWeight OBJECT-TYPE
    SYNTAX       TWeight
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The weight that needs to be used by the scheduler to which this queue
         would be feeding to."
    DEFVAL { 1 }
    ::= { tSapEgressQueueEntry 5 }

tSapEgressQueueCIRLevel OBJECT-TYPE
    SYNTAX       TLevelOrDefault
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "This specifies the level of priority while feeding to the parent.
         The level '0' means treat all offered load for this queue as for
         the above CIR traffic."
    DEFVAL { 0 }
    ::= { tSapEgressQueueEntry 6 }

tSapEgressQueueCIRWeight OBJECT-TYPE
    SYNTAX       TWeight
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The weight that needs to be used until the committed rate by the
         scheduler to which this queue would be feeding to."
    DEFVAL { 1 }
    ::= { tSapEgressQueueEntry 7 }

tSapEgressQueueExpedite OBJECT-TYPE
    SYNTAX       INTEGER {
                    expedited     (1),
                    auto-expedited(2),
                    non-expedited (3)
                 }
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSapEgressQueueExpedite specifies the priority that this queue should
         be assigned by the hardware level schedulers.

         The value 'auto-expedited' implies that this attribute should be
         dynamically updated by looking at the forwarding classes mapping
         into this queue. In such a case the queue is treated as
         'non-expedited' if there is even a single non-expedited forwarding
         class using this queue.

         This attribute is associated with the queue at the time of creation
         and cannot be modified thereafter."
    DEFVAL { auto-expedited }
    ::= { tSapEgressQueueEntry 8 }

tSapEgressQueueCBS OBJECT-TYPE
    SYNTAX       TBurstSize
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The amount of reserved buffer space (in kilo bytes) for the queue."
    DEFVAL { -1 }
    ::= { tSapEgressQueueEntry 9 }

tSapEgressQueueMBS OBJECT-TYPE
    SYNTAX       TBurstSize
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The maximum amount of buffer space (in kilo bytes) allowed for the
        queue."
    DEFVAL { -1 }
    ::= { tSapEgressQueueEntry 10 }

tSapEgressQueueHiPrioOnly OBJECT-TYPE
    SYNTAX       TBurstPercentOrDefault
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The percentage of buffer space for the queue, used
         exclusively by high priority packets."
    DEFVAL { -1 }
    ::= { tSapEgressQueueEntry 11 }

tSapEgressQueuePIRAdaptation OBJECT-TYPE
    SYNTAX       TAdaptationRule
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The adaptation rule to be used while computing the operational PIR
         value. The adaptation rule specifies the rules to compute the
         operational values while maintaining minimum offset."
    DEFVAL { closest }
    ::= { tSapEgressQueueEntry 12 }

tSapEgressQueueCIRAdaptation OBJECT-TYPE
    SYNTAX       TAdaptationRule
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The adaptation rule to be used while computing the operational CIR
         value. The adaptation rule specifies the rules to compute the
         operational values while maintaining minimum offset."
    DEFVAL { closest }
    ::= { tSapEgressQueueEntry 13 }

tSapEgressQueueAdminPIR OBJECT-TYPE
    SYNTAX       TPIRRate
    UNITS        "kbps"
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The administrative PIR specified by the user."
    DEFVAL { -1 }
    ::= { tSapEgressQueueEntry 14 }

tSapEgressQueueAdminCIR OBJECT-TYPE
    SYNTAX       TCIRRate
    UNITS        "kbps"
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The administrative CIR specified by the user."
    DEFVAL { 0 }
    ::= { tSapEgressQueueEntry 15 }

tSapEgressQueueOperPIR OBJECT-TYPE
    SYNTAX       TPIRRate
    UNITS        "kbps"
    MAX-ACCESS   read-only
    STATUS       obsolete
    DESCRIPTION
        "The administrative PIR specified by the user."
    ::= { tSapEgressQueueEntry 16 }

tSapEgressQueueOperCIR OBJECT-TYPE
    SYNTAX       TCIRRate
    UNITS        "kbps"
    MAX-ACCESS   read-only
    STATUS       obsolete
    DESCRIPTION
        "The operational value derived by computing the CIR value from
         the administrative CIR and PIR values and their corresponding
         adaptation rules."
    ::= { tSapEgressQueueEntry 17 }

tSapEgressQueueLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "timestamp of last change to this row in tSapEgressQueueTable."
    ::= { tSapEgressQueueEntry 18 }

tSapEgressQueueUsePortParent  OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of the object tSapEgressQueueUsePortParent specifies
         whether this queue is parented by a port-level scheduler. When
         the value is 'true', this SAP egress queue is parented by a 
         port-level scheduler. This object is mutually exclusive with 
         tSapEgressQueueParent. Only one kind of parent is allowed."
    DEFVAL      { false }
    ::= { tSapEgressQueueEntry 19 }

tSapEgressQueuePortLvl        OBJECT-TYPE
    SYNTAX       TLevel
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of the object tSapEgressQueuePortLvl specifies the
         port priority this queue will use to receive bandwidth from
         the port-level scheduler for its above-cir offered load."
    DEFVAL      { 1 }
    ::= { tSapEgressQueueEntry 20 }

tSapEgressQueuePortWght       OBJECT-TYPE
    SYNTAX      TWeight
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of the object tSapEgressQueuePortWght specifies the
         weight this queue will use to receive bandwidth from the
         port-level scheduler for its above-cir offered load."
    DEFVAL      { 1 }
    ::= { tSapEgressQueueEntry 21 }

tSapEgressQueuePortCIRLvl        OBJECT-TYPE
    SYNTAX      TLevelOrDefault
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of the object tSapEgressQueuePortCIRLvl specifies the
         port priority this queue will use to receive bandwidth from
         the port-level scheduler for its within-cir offered load."
    DEFVAL      { 0 }
    ::= { tSapEgressQueueEntry 22 }

tSapEgressQueuePortCIRWght       OBJECT-TYPE
    SYNTAX      TWeight
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of the object tSapEgressQueuePortCIRWght specifies the
         weight this queue will use to receive bandwidth from the
         port-level scheduler for its within-cir offered load."
    DEFVAL      { 0 }
    ::= { tSapEgressQueueEntry 23 }

tSapEgressQueuePortAvgOverhead  OBJECT-TYPE
    SYNTAX      Unsigned32 (0..10000)
    UNITS       "Hundredths of a percent"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tSapEgressQueuePortAvgOverhead specifies the 
         encapsulation overhead, in hundredths of a percent,  used to 
         translate packet-based rate to frame-based rate and vice versa."
    DEFVAL      { 0 }
    ::= { tSapEgressQueueEntry 24 }

tSapEgressQueuePoolName OBJECT-TYPE   
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tSapEgressQueuePoolName specifies the name of 
         the pool to be applied for SAP egress queue."
    DEFVAL { ''H }    
    ::= { tSapEgressQueueEntry 25 }

--
-- sap-egress policy fc table
--

tSapEgressFCTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TSapEgressFCEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION  "  "
    ::= { tSapEgressObjects 3 }

tSapEgressFCEntry OBJECT-TYPE
    SYNTAX       TSapEgressFCEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about a particular sap-egress policy's
         forwarding class mappings to queues and dot1p values.
         Entries are created/deleted by user.
         There is no StorageType object, entries have a presumed
         StorageType of nonVolatile.
        "
    INDEX { tSapEgressIndex, tSapEgressFCName }
    ::= { tSapEgressFCTable 1 }

TSapEgressFCEntry ::= SEQUENCE
    {
        tSapEgressFCName               TFCName,
        tSapEgressFCRowStatus          RowStatus,
        tSapEgressFCQueue              TEgressQueueId,
        tSapEgressFCDot1PValue         Dot1PPriority,
        tSapEgressFCLastChanged        TimeStamp,
        tSapEgressFCDot1PInProfile     Dot1PPriority,
        tSapEgressFCDot1POutProfile    Dot1PPriority,
        tSapEgressFCForceDEValue       TDEValue,
        tSapEgressFCDEMark             TruthValue,
        tSapEgressFCInProfDscp         TNamedItemOrEmpty,
        tSapEgressFCOutProfDscp        TNamedItemOrEmpty,
        tSapEgressFCInProfPrec         TPrecValueOrNone,
        tSapEgressFCOutProfPrec        TPrecValueOrNone
    }

tSapEgressFCName OBJECT-TYPE
    SYNTAX       TFCName
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "forwarding class name."
    ::= { tSapEgressFCEntry 1 }

tSapEgressFCRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "Row Status for this queue."
    ::= { tSapEgressFCEntry 2 }

tSapEgressFCQueue OBJECT-TYPE
    SYNTAX       TEgressQueueId
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The queue to use for packets in this forwarding class.
         No FC can map to the same queue being used by 'premium' class.
         You must specify a valid queue-id before setting the row status
         to active."
    ::= { tSapEgressFCEntry 3 }

tSapEgressFCDot1PValue OBJECT-TYPE
    SYNTAX       Dot1PPriority
    MAX-ACCESS   read-create
    STATUS       obsolete
    DESCRIPTION
        "The value of tSapEgressFCDot1PValue specifies 802.1p value to use for
        packets in this forwarding class.

        This object has been replaced by tSapEgressFCDot1PInProfile and
        tSapEgressFCDot1POutProfile objects."
    DEFVAL { -1 }
    ::= { tSapEgressFCEntry 4 }

tSapEgressFCLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "timestamp of last change to this row in tSapEgressFCTable."
    ::= { tSapEgressFCEntry 5 }

tSapEgressFCDot1PInProfile OBJECT-TYPE
    SYNTAX       Dot1PPriority
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
       "The value of tSapEgressFCDot1PInProfile specifies 802.1p value to set
       for in-profile frames in this forwarding class.

       An inconsistentValue error is returned if this object is set to any value
       other than '-1', when tSapEgressFCDot1POutProfile object is set to '-1'."
    DEFVAL { -1 }
    ::= { tSapEgressFCEntry 8 }

tSapEgressFCDot1POutProfile OBJECT-TYPE
    SYNTAX       Dot1PPriority
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
       "The value of tSapEgressFCDot1POutProfile specifies 802.1p value to set
       for out-profile frames in this forwarding class.
        
       An inconsistentValue error is returned if this object is set to any value
       other than '-1', when tSapEgressFCDot1PInProfile object is set to '-1'."
    DEFVAL { -1 }
    ::= { tSapEgressFCEntry 9 }

tSapEgressFCForceDEValue OBJECT-TYPE
    SYNTAX       TDEValue
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
       "The value of tSapEgressFCForceDEValue specifies the DE value to set
       for all the frames of this forwarding class regardless of frame's profile
       status.

       An inconsistentValue error is returned if this object is set to any value
       other than '-1', when tSapEgressFCDEMark object is set to 'false'."
    DEFVAL { -1 }
    ::= { tSapEgressFCEntry 10 }

tSapEgressFCDEMark OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
       "The value of tSapEgressFCDEMark specifies whether to set DE value in the
       frames of this forwarding class.

       An inconsistentValue error is returned if this object is set to 'false'
       when tSapEgressFCForceDEValue object is not set to '-1'."
    DEFVAL { false }
    ::= { tSapEgressFCEntry 11 }

tSapEgressFCInProfDscp OBJECT-TYPE
    SYNTAX       TNamedItemOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
       "The value of tSapEgressFCInProfDscp specifies the DSCP name to set
       for in-profile frames in this forwarding class.

       The values of tSapEgressFCInProfDscp and tSapEgressFCOutProfDscp
       must be set to either the default or non-default values for both.
       An inconsistentValue error is returned if one object is set to the
       default and other is set to a non-default value.

       The values of tSapEgressFCInProfPrec and tSapEgressFCOutProfPrec are
       reset to default values when tSapEgressFCInProfDscp is set to any value
       other than default.

       An inconsistentValue error is returned if this object is set to any value
       other than its default value when setting tSapEgressFCInProfPrec object
       or tSapEgressFCOutProfPrec to a value other than their default values."
    DEFVAL { ''H }
    ::= { tSapEgressFCEntry 12 }

tSapEgressFCOutProfDscp OBJECT-TYPE
    SYNTAX       TNamedItemOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
       "The value of tSapEgressFCOutProfDscp specifies the DSCP name to set
       for out-profile frames in this forwarding class.

       The values of tSapEgressFCInProfDscp and tSapEgressFCOutProfDscp
       must be set to either the default or non-default values for both.
       An inconsistentValue error is returned if one object is set to the
       default and other is set to a non-default value.

       The values of tSapEgressFCInProfPrec and tSapEgressFCOutProfPrec are
       reset to default values when tSapEgressFCOutProfDscp is set to any value
       other than default.
       
       An inconsistentValue error is returned if this object is set to any value
       other than its default value when setting tSapEgressFCInProfPrec object
       or tSapEgressFCOutProfPrec to a value other than their default values."
    DEFVAL { ''H }
    ::= { tSapEgressFCEntry 13 }

tSapEgressFCInProfPrec OBJECT-TYPE
    SYNTAX       TPrecValueOrNone
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
       "The value of tSapEgressFCInProfPrec specifies the precedence value to
       set for in-profile frames in this forwarding class.

       The values of tSapEgressFCInProfPrec and tSapEgressFCOutProfPrec
       must be set to either the default or non-default values for both.
       An inconsistentValue error is returned if one object is set to the
       default and other is set to a non-default value.

       The values of tSapEgressFCInProfDscp and tSapEgressFCOutProfDscp are
       reset to default values when tSapEgressFCOutProfPrec is set to any value
       other than default.

       An inconsistentValue error is returned if this object is set to any value
       other than its default value when setting tSapEgressFCInProfDscp object
       or tSapEgressFCOutProfDscp to a value other than their default values."
    DEFVAL { -1 }
    ::= { tSapEgressFCEntry 14 }

tSapEgressFCOutProfPrec OBJECT-TYPE
    SYNTAX       TPrecValueOrNone
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
       "The value of tSapEgressFCOutProfPrec specifies the precedence value to
       set for out-profile frames in this forwarding class.

       The values of tSapEgressFCInProfPrec and tSapEgressFCOutProfPrec must be
       be set to either the default or non-default values for both.
       An inconsistentValue error is returned if one object is set to the
       default and other is set to a non-default value.

       The values of tSapEgressFCInProfDscp and tSapEgressFCOutProfDscp are
       reset to default values when tSapEgressFCOutProfPrec is set to any value
       other than default.
       
       An inconsistentValue error is returned if this object is set to any value
       other than its default value when setting tSapEgressFCInProfDscp object
       or tSapEgressFCOutProfDscp to a value other than their default values."
    DEFVAL { -1 }
    ::= { tSapEgressFCEntry 15 }

--
-- network policy table
--

tNetworkPolicyTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TNetworkPolicyEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION  "  "
    ::= { tNetworkObjects 1 }

tNetworkPolicyEntry OBJECT-TYPE
    SYNTAX       TNetworkPolicyEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about a particular Network Policy.

         network policy (1) is the default entry.

         Default Entry is created by the agent, other entries are
         created by user.

         Default Entry cannot be modified/deleted. The other
         entries are deleted by user.

         There is no StorageType object, entries have a presumed
         StorageType of nonVolatile.

         The default Egress FC entries are created as an action
         of setting the rowstatus."
    INDEX { tNetworkPolicyIndex }
    ::= { tNetworkPolicyTable 1 }

TNetworkPolicyEntry ::= SEQUENCE
    {
        tNetworkPolicyIndex                       TNetworkPolicyID,
        tNetworkPolicyRowStatus                   RowStatus,
        tNetworkPolicyScope                       TItemScope,
        tNetworkPolicyDescription                 TItemDescription,
        tNetworkPolicyIngressDefaultActionFC      TFCName,
        tNetworkPolicyIngressDefaultActionProfile TProfile,
        tNetworkPolicyEgressRemark                TruthValue,
        tNetworkPolicyLastChanged                 TimeStamp,
        tNetworkPolicyIngressLerUseDscp           TruthValue
    }

tNetworkPolicyIndex OBJECT-TYPE
    SYNTAX       TNetworkPolicyID
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION  ""
    REFERENCE    ""
    ::= { tNetworkPolicyEntry 1 }

tNetworkPolicyRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION  ""
    ::= { tNetworkPolicyEntry 2 }

tNetworkPolicyScope OBJECT-TYPE
    SYNTAX       TItemScope
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION  ""
    DEFVAL { template }
    ::= { tNetworkPolicyEntry 5 }

tNetworkPolicyDescription OBJECT-TYPE
    SYNTAX       TItemDescription
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tNetworkPolicyDescription holds the description for this policy."
    DEFVAL { ''H }
    ::= { tNetworkPolicyEntry 6 }

tNetworkPolicyIngressDefaultActionFC OBJECT-TYPE
    SYNTAX       TFCName
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tNetworkPolicyIngressDefaultActionFC specifies the default
         forwarding class to be used while classifying the ingress traffic."
    DEFVAL { 'be'H }
    ::= { tNetworkPolicyEntry 7 }

tNetworkPolicyIngressDefaultActionProfile OBJECT-TYPE
    SYNTAX       TProfile
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tNetworkPolicyIngressDefaultActionProfile specifies the
         default profile to be used for the ingressing traffic."
    DEFVAL { out }
    ::= { tNetworkPolicyEntry 8 }

tNetworkPolicyEgressRemark OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The object tNetworkPolicyEgressRemark specifies if the system
         will remark the egress packets or not."
    DEFVAL { false }
    ::= { tNetworkPolicyEntry 9 }

tNetworkPolicyLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Timestamp of last change to this row in tNetworkPolicyTable."
    ::= { tNetworkPolicyEntry 10 }

tNetworkPolicyIngressLerUseDscp OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The object has significance only for the label edge routers
         which should honor the DSCP markings instead of the LSPEXP bits."
    DEFVAL { false }
    ::= { tNetworkPolicyEntry 11 }

--
-- network ingress policy dscp table
--

tNetworkIngressDSCPTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TNetworkIngressDSCPEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION  "  "
    ::= { tNetworkObjects 2 }

tNetworkIngressDSCPEntry OBJECT-TYPE
    SYNTAX       TNetworkIngressDSCPEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about a dscp map for a network policy.

         Some default entries are created by the agent for the
         default network policy (1). These cannot be deleted or
         modified. Other entries are created/deleted by user.

         There is no StorageType object, entries have a presumed
         StorageType of nonVolatile.
        "
    INDEX { tNetworkPolicyIndex, tNetworkIngressDSCP }
    ::= { tNetworkIngressDSCPTable 1 }

TNetworkIngressDSCPEntry ::= SEQUENCE
    {
        tNetworkIngressDSCP            TDSCPName,
        tNetworkIngressDSCPRowStatus   RowStatus,
        tNetworkIngressDSCPFC          TFCNameOrEmpty,
        tNetworkIngressDSCPProfile     TProfile,
        tNetworkIngressDSCPLastChanged TimeStamp
    }

tNetworkIngressDSCP OBJECT-TYPE
    SYNTAX       TDSCPName
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION  ""
    ::= { tNetworkIngressDSCPEntry 1 }

tNetworkIngressDSCPRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION ""
    ::= { tNetworkIngressDSCPEntry 2 }

tNetworkIngressDSCPFC OBJECT-TYPE
    SYNTAX       TFCNameOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION  ""
    DEFVAL { ''H }
    ::= { tNetworkIngressDSCPEntry 3 }

tNetworkIngressDSCPProfile OBJECT-TYPE
    SYNTAX       TProfile
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "there is no DEFVAL for this object.  it must be deliberately
         set before activating the row."
    ::= { tNetworkIngressDSCPEntry 4 }

tNetworkIngressDSCPLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "timestamp of last change to this row in tNetworkIngressDSCPTable."
    ::= { tNetworkIngressDSCPEntry 5 }

--
-- network ingress policy dot1p table
--

tNetworkIngressDot1pTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TNetworkIngressDot1pEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION  " "
    ::= { tNetworkObjects 3 }

tNetworkIngressDot1pEntry OBJECT-TYPE
    SYNTAX       TNetworkIngressDot1pEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about a dot1p map for a network policy.

         Some default entries are created by the agent for the
         default network policy (1). These cannot be deleted or
         modified. Other entries are created/deleted by user.

         There is no StorageType object, entries have a presumed
         StorageType of nonVolatile.
        "
    INDEX { tNetworkPolicyIndex, tNetworkIngressDot1pValue }
    ::= { tNetworkIngressDot1pTable 1 }

TNetworkIngressDot1pEntry ::= SEQUENCE
    {
        tNetworkIngressDot1pValue       Dot1PPriority,
        tNetworkIngressDot1pRowStatus   RowStatus,
        tNetworkIngressDot1pFC          TFCNameOrEmpty,
        tNetworkIngressDot1pProfile     TDEProfile,
        tNetworkIngressDot1pLastChanged TimeStamp
    }

tNetworkIngressDot1pValue OBJECT-TYPE
    SYNTAX       Dot1PPriority (0..7)
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION  ""
    ::= { tNetworkIngressDot1pEntry 1 }

tNetworkIngressDot1pRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION  ""
    ::= { tNetworkIngressDot1pEntry 2 }

tNetworkIngressDot1pFC OBJECT-TYPE
    SYNTAX       TFCNameOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION  ""
    DEFVAL { ''H }
    ::= { tNetworkIngressDot1pEntry 3 }

tNetworkIngressDot1pProfile OBJECT-TYPE
    SYNTAX       TDEProfile
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "there is no DEFVAL for this object. It must be deliberately
         set before activating the row."
    ::= { tNetworkIngressDot1pEntry 4 }

tNetworkIngressDot1pLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "timestamp of last change to this row in tNetworkIngressDot1pTable."
    ::= { tNetworkIngressDot1pEntry 5 }

--
-- network ingress policy lspexp table
--

tNetworkIngressLSPEXPTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TNetworkIngressLSPEXPEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION  "  "
    ::= { tNetworkObjects 4 }

tNetworkIngressLSPEXPEntry OBJECT-TYPE
    SYNTAX       TNetworkIngressLSPEXPEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about LSP EXP mapping for a network policy.

         Some default entries are created by the agent for the
         default network policy (1). These cannot be deleted or
         modified. Other entries are created/deleted by user.

         There is no StorageType object, entries have a presumed
         StorageType of nonVolatile."
    INDEX { tNetworkPolicyIndex, tNetworkIngressLSPEXP }
    ::= { tNetworkIngressLSPEXPTable 1 }

TNetworkIngressLSPEXPEntry ::= SEQUENCE
    {
        tNetworkIngressLSPEXP            TLspExpValue,
        tNetworkIngressLSPEXPRowStatus   RowStatus,
        tNetworkIngressLSPEXPFC          TFCNameOrEmpty,
        tNetworkIngressLSPEXPProfile     TProfile,
        tNetworkIngressLSPEXPLastChanged TimeStamp
    }

tNetworkIngressLSPEXP OBJECT-TYPE
    SYNTAX       TLspExpValue (0..7)
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION  ""
    ::= { tNetworkIngressLSPEXPEntry 1 }

tNetworkIngressLSPEXPRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION  ""
    ::= { tNetworkIngressLSPEXPEntry 2 }

tNetworkIngressLSPEXPFC OBJECT-TYPE
    SYNTAX       TFCNameOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION  ""
    DEFVAL { ''H }
    ::= { tNetworkIngressLSPEXPEntry 3 }

tNetworkIngressLSPEXPProfile OBJECT-TYPE
    SYNTAX       TProfile
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "there is no DEFVAL for this object.  it must be deliberately
        set before activating the row."
    ::= { tNetworkIngressLSPEXPEntry 4 }

tNetworkIngressLSPEXPLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "timestamp of last change to this row in tNetworkIngressLSPEXPTable."
    ::= { tNetworkIngressLSPEXPEntry 5 }

--
-- network egress fc table
--

tNetworkEgressFCTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TNetworkEgressFCEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about all network policies' egress FC traffic disposition."
    ::= { tNetworkObjects 7 }

tNetworkEgressFCEntry OBJECT-TYPE
    SYNTAX       TNetworkEgressFCEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about a particular network policy's egress
         per-forwarding-class traffic disposition.

         Entries are created/deleted as an action of creating/deleting
         a row in the tNetworkPolicyTable.

         There is no StorageType object, entries have a presumed
         StorageType of nonVolatile."
    INDEX { tNetworkPolicyIndex, tNetworkEgressFCName }
    ::= { tNetworkEgressFCTable 1 }

TNetworkEgressFCEntry ::= SEQUENCE
    {
        tNetworkEgressFCName              TFCName,
        tNetworkEgressFCDSCPInProfile     TDSCPNameOrEmpty,
        tNetworkEgressFCDSCPOutProfile    TDSCPNameOrEmpty,
        tNetworkEgressFCLspExpInProfile   TLspExpValue,
        tNetworkEgressFCLspExpOutProfile  TLspExpValue,
        tNetworkEgressFCDot1pInProfile    Dot1PPriority,
        tNetworkEgressFCDot1pOutProfile   Dot1PPriority,
        tNetworkEgressFCLastChanged       TimeStamp,
        tNetworkEgressFCForceDEValue      TDEValue,
        tNetworkEgressFCDEMark            TruthValue
    }

tNetworkEgressFCName OBJECT-TYPE
    SYNTAX       TFCName
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "the name of the forwarding class (FC)."
    ::= { tNetworkEgressFCEntry 1 }

tNetworkEgressFCDSCPInProfile OBJECT-TYPE
    SYNTAX       TDSCPNameOrEmpty
    MAX-ACCESS   read-write
    STATUS       current
    DESCRIPTION
        "the DSCP to use for in-profile traffic."
    ::= { tNetworkEgressFCEntry 2 }

tNetworkEgressFCDSCPOutProfile OBJECT-TYPE
    SYNTAX       TDSCPNameOrEmpty
    MAX-ACCESS   read-write
    STATUS       current
    DESCRIPTION
        "the DSCP to use for out-of-profile traffic."
    ::= { tNetworkEgressFCEntry 3 }

tNetworkEgressFCLspExpInProfile OBJECT-TYPE
    SYNTAX       TLspExpValue
    MAX-ACCESS   read-write
    STATUS       current
    DESCRIPTION
        "the lsp-exp value to use for in-profile traffic."
    ::= { tNetworkEgressFCEntry 4 }

tNetworkEgressFCLspExpOutProfile OBJECT-TYPE
    SYNTAX       TLspExpValue
    MAX-ACCESS   read-write
    STATUS       current
    DESCRIPTION
        "the lsp-exp value to use for out-of-profile traffic."
    ::= { tNetworkEgressFCEntry 5 }

tNetworkEgressFCDot1pInProfile OBJECT-TYPE
    SYNTAX       Dot1PPriority
    MAX-ACCESS   read-write
    STATUS       current
    DESCRIPTION
        "the Dot1p to use for in-profile traffic."
    ::= { tNetworkEgressFCEntry 6 }

tNetworkEgressFCDot1pOutProfile OBJECT-TYPE
    SYNTAX       Dot1PPriority
    MAX-ACCESS   read-write
    STATUS       current
    DESCRIPTION
        "the Dot1p to use for out-of-profile traffic."
    ::= { tNetworkEgressFCEntry 7 }

tNetworkEgressFCLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "timestamp of last change to this row in tNetworkEgressFCTable."
    ::= { tNetworkEgressFCEntry 8 }

tNetworkEgressFCForceDEValue OBJECT-TYPE
    SYNTAX       TDEValue
    MAX-ACCESS   read-write
    STATUS       current
    DESCRIPTION
        "The value of tNetworkEgressFCForceDEValue specifies the DE value to set
        for network egress packets in this forwarding class regardless of
        profile status of the frames.

       An inconsistentValue error is returned if this object is set to any value
       other than '-1', when tNetworkEgressFCDEMark object is set to
       'false'."
    DEFVAL { -1 }
    ::= { tNetworkEgressFCEntry 9 }

tNetworkEgressFCDEMark OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-write
    STATUS       current
    DESCRIPTION
       "The value of tNetworkEgressFCDEMark specifies whether to set DE value
       in the frames of this forwarding class.

       An inconsistentValue error is returned if this object is set to 'false'
       when tNetworkEgressFCForceDEValue object is not set to '-1'."
    DEFVAL { false }
    ::= { tNetworkEgressFCEntry 10 }

--
-- network-queue policy table
--

tNetworkQueuePolicyTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TNetworkQueuePolicyEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION  "  "
    ::= { tNetworkQueueObjects 1 }

tNetworkQueuePolicyEntry OBJECT-TYPE
    SYNTAX       TNetworkQueuePolicyEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION  ""
    INDEX { tNetworkQueuePolicy }
    ::= { tNetworkQueuePolicyTable 1 }

TNetworkQueuePolicyEntry ::= SEQUENCE
    {
        tNetworkQueuePolicy                TNamedItem,
        tNetworkQueuePolicyRowStatus       RowStatus,
        tNetworkQueuePolicyDescription     TItemDescription,
        tNetworkQueuePolicyLastChanged     TimeStamp
    }

tNetworkQueuePolicy OBJECT-TYPE
    SYNTAX       TNamedItem
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION  ""
    ::= { tNetworkQueuePolicyEntry 1 }

tNetworkQueuePolicyRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION  ""
    ::= { tNetworkQueuePolicyEntry 2 }

tNetworkQueuePolicyDescription OBJECT-TYPE
    SYNTAX       TItemDescription
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION  ""
    DEFVAL { ''H }
    ::= { tNetworkQueuePolicyEntry 3 }

tNetworkQueuePolicyLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  ""
    ::= { tNetworkQueuePolicyEntry 8 }

--
-- network-queue policy queue table
--

tNetworkQueueTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TNetworkQueueEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION  ""
    ::= { tNetworkQueueObjects 2 }

tNetworkQueueEntry OBJECT-TYPE
    SYNTAX       TNetworkQueueEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION  ""
    INDEX { tNetworkQueuePolicy, tNetworkQueue }
    ::= { tNetworkQueueTable 1 }

TNetworkQueueEntry ::= SEQUENCE
    {
        tNetworkQueue                 TQueueId,
        tNetworkQueueRowStatus        RowStatus,
        tNetworkQueuePoolName         TNamedItemOrEmpty,
        tNetworkQueueParent           TNamedItemOrEmpty,
        tNetworkQueueLevel            TLevel,
        tNetworkQueueWeight           TWeight,
        tNetworkQueueCIRLevel         TLevelOrDefault,
        tNetworkQueueCIRWeight        TWeight,
        tNetworkQueueMCast            TruthValue,
        tNetworkQueueExpedite         INTEGER,
        tNetworkQueueCIR              TRatePercent,
        tNetworkQueuePIR              TPIRRatePercent,
        tNetworkQueueCBS              TBurstHundredthsOfPercent,
        tNetworkQueueMBS              TBurstHundredthsOfPercent,
        tNetworkQueueHiPrioOnly       TBurstPercentOrDefault,
        tNetworkQueueLastChanged      TimeStamp,
        tNetworkQueueUsePortParent    TruthValue,
        tNetworkQueuePortLvl          TLevel,
        tNetworkQueuePortWght         TWeight,
        tNetworkQueuePortCIRLvl       TLevelOrDefault,
        tNetworkQueuePortCIRWght      TWeight,
        tNetworkQueuePortAvgOverhead  Unsigned32,
        tNetworkQueueCIRAdaptation    TAdaptationRule,
        tNetworkQueuePIRAdaptation    TAdaptationRule
    }

tNetworkQueue OBJECT-TYPE
    SYNTAX       TQueueId (1..16)
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "The queue ID is used as the secondary index to the table entry."
    ::= { tNetworkQueueEntry 1 }

tNetworkQueueRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "Row Status of the entry. This allows creation/deletion of rows in this
         table."
    ::= { tNetworkQueueEntry 2 }

tNetworkQueuePoolName OBJECT-TYPE
    SYNTAX       TNamedItemOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The tNetworkQueuePoolName specifies the pool that is associated
         with this network queue. If tNetworkQueuePoolName is empty, no pool
         has been associated with the queue."
    DEFVAL { ''H }
    ::= { tNetworkQueueEntry 3 }

tNetworkQueueParent OBJECT-TYPE
    SYNTAX       TNamedItemOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The scheduler to which this queue would be feeding to."
    DEFVAL { ''H }
    ::= { tNetworkQueueEntry 4 }

tNetworkQueueLevel OBJECT-TYPE
    SYNTAX       TLevel
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "This specifies the level of priority while feeding to the parent."
    DEFVAL { 1 }
    ::= { tNetworkQueueEntry 5 }

tNetworkQueueWeight OBJECT-TYPE
    SYNTAX       TWeight
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The weight that needs to be used by the scheduler to which this queue
         would be feeding to."
    DEFVAL { 1 }
    ::= { tNetworkQueueEntry 6 }

tNetworkQueueCIRLevel OBJECT-TYPE
    SYNTAX       TLevelOrDefault
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "This specifies the level of priority while feeding to the parent.
         The level '0' means treat all offered load for this queue as for
         the above CIR traffic."
    DEFVAL { 0 }
    ::= { tNetworkQueueEntry 7 }

tNetworkQueueCIRWeight OBJECT-TYPE
    SYNTAX       TWeight
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The weight that needs to be used until the committed rate by the
         scheduler to which this queue would be feeding to."
    DEFVAL { 1 }
    ::= { tNetworkQueueEntry 8 }

tNetworkQueueMCast OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "This object specifies if this is a multicast queue or not."
    DEFVAL { false }
    ::= { tNetworkQueueEntry 9 }

tNetworkQueueExpedite OBJECT-TYPE
    SYNTAX       INTEGER {
                     expedited     (1),
                     auto-expedited(2),
                     non-expedited (3)
                 }
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tNetworkQueueExpedite specifies the priority that this queue should
         be assigned by the hardware level schedulers.

         The value 'auto-expedited' implies that this attribute should be
         dynamically updated by looking at the forwarding classes mapping
         into this queue. In such a case the queue is treated as
         'non-expedited' if there is even a single non-expedited forwarding
         class using this queue.

         This attribute is associated with the queue at the time of creation
         and cannot be modified thereafter."
    DEFVAL { auto-expedited }
    ::= { tNetworkQueueEntry 10 }

tNetworkQueueCIR OBJECT-TYPE
    SYNTAX       TRatePercent
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The percentage of max rate allowed."
    DEFVAL { 0 }
    ::= { tNetworkQueueEntry 11 }

tNetworkQueuePIR OBJECT-TYPE
    SYNTAX       TPIRRatePercent
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The percentage of max rate allowed."
    DEFVAL { 100 }
    ::= { tNetworkQueueEntry 12 }

tNetworkQueueCBS OBJECT-TYPE
    SYNTAX       TBurstHundredthsOfPercent
    UNITS        "Hundredths of a percent"
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The percentage of reserved buffer space for the queue, 
         defined to two decimal places."
    DEFVAL { 0 }
    ::= { tNetworkQueueEntry 13 }

tNetworkQueueMBS OBJECT-TYPE
    SYNTAX       TBurstHundredthsOfPercent
    UNITS        "Hundredths of a percent"
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The percentage of reserved buffer space for the queue, 
         defined to two decimal places."
    DEFVAL { 10000 }
    ::= { tNetworkQueueEntry 14 }

tNetworkQueueHiPrioOnly OBJECT-TYPE
    SYNTAX       TBurstPercentOrDefault
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The percentage of buffer space for the queue, used
         exclusively by high priority packets."
    DEFVAL { -1 }
    ::= { tNetworkQueueEntry 15 }

tNetworkQueueLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "The value of the object tNetworkQueueLastChanged indicates the 
         timestamp of last change to this row in tNetworkQueueTable."
    ::= { tNetworkQueueEntry 16 }

tNetworkQueueUsePortParent  OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of the object tNetworkQueueUsePortParent specifies
         whether this queue is parented by a port-level scheduler. When
         the value is 'true', this network queue is parented by a 
         port-level scheduler."
    DEFVAL      { false }
    ::= { tNetworkQueueEntry 17 }

tNetworkQueuePortLvl        OBJECT-TYPE
    SYNTAX       TLevel
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of the object tNetworkQueuePortLvl specifies the
         port priority this queue will use to receive bandwidth from
         the port-level scheduler for its above-cir offered load."
    DEFVAL      { 1 }
    ::= { tNetworkQueueEntry 18 }

tNetworkQueuePortWght       OBJECT-TYPE
    SYNTAX      TWeight
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of the object tNetworkQueuePortWght specifies the
         weight this queue will use to receive bandwidth from the
         port-level scheduler for its above-cir offered load."
    DEFVAL      { 1 }
    ::= { tNetworkQueueEntry 19 }

tNetworkQueuePortCIRLvl        OBJECT-TYPE
    SYNTAX      TLevelOrDefault
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of the object tNetworkQueuePortCIRLvl specifies the
         port priority this queue will use to receive bandwidth from
         the port-level scheduler for its within-cir offered load."
    DEFVAL      { 0 }
    ::= { tNetworkQueueEntry 20 }

tNetworkQueuePortCIRWght       OBJECT-TYPE
    SYNTAX      TWeight
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of the object tNetworkQueuePortCIRWght specifies the
         weight this queue will use to receive bandwidth from the
         port-level scheduler for its within-cir offered load."
    DEFVAL      { 0 }
    ::= { tNetworkQueueEntry 21 }

tNetworkQueuePortAvgOverhead    OBJECT-TYPE
    SYNTAX      Unsigned32 (0..10000)
    UNITS       "Hundredths of a percent"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tNetworkQueuePortAvgOverhead specifies the 
         encapsulation overhead, in hundredths of a percent,  used to 
         translate packet-based rate to frame-based rate and vice versa."
    DEFVAL      { 0 }
    ::= { tNetworkQueueEntry 22 }

tNetworkQueueCIRAdaptation       OBJECT-TYPE
    SYNTAX      TAdaptationRule
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tNetworkQueueCIRAdaptation specifies the adaptation rule
         to be used while computing the operational CIR value. The adaptation
         rule specifies the rules to compute the operational value while
         maintaining the minimum offset."
    DEFVAL      { closest }
    ::= { tNetworkQueueEntry 23 }

tNetworkQueuePIRAdaptation       OBJECT-TYPE
    SYNTAX      TAdaptationRule
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tNetworkQueuePIRAdaptation specifies the adaptation rule
         to be used while computing the operational PIR value. The adaptation
         rule specifies the rules to compute the operational value while
         maintaining the minimum offset."
    DEFVAL      { closest }
    ::= { tNetworkQueueEntry 24 }

--
-- network-queue policy fc table
--

tNetworkQueueFCTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TNetworkQueueFCEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION  ""
    ::= { tNetworkQueueObjects 3 }

tNetworkQueueFCEntry OBJECT-TYPE
    SYNTAX       TNetworkQueueFCEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION  ""
    INDEX { tNetworkQueuePolicy, tNetworkQueueFCName }
    ::= { tNetworkQueueFCTable 1 }

TNetworkQueueFCEntry ::= SEQUENCE
    {
        tNetworkQueueFCName          TFCName,
        tNetworkQueueFCRowStatus     RowStatus,
        tNetworkQueueFC              TQueueId,
        tNetworkQueueFCMCast         TQueueId,
        tNetworkQueueFCLastChanged   TimeStamp
    }

tNetworkQueueFCName OBJECT-TYPE
    SYNTAX       TFCName
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Forwarding class name."
    ::= { tNetworkQueueFCEntry 1 }

tNetworkQueueFCRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "Row Status for this queue."
    ::= { tNetworkQueueFCEntry 2 }

tNetworkQueueFC OBJECT-TYPE
    SYNTAX       TQueueId (1..16)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The specific queue to be used for packets in this forwarding class."
    DEFVAL { 1 }
    ::= { tNetworkQueueFCEntry 3 }

tNetworkQueueFCMCast OBJECT-TYPE
    SYNTAX       TQueueId (1..16)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The specific multicast queue to be used for packets in this forwarding
         class. The queue is used only for specific entities and will be
         ignored wherever it is irrelevant."
    DEFVAL { 9 }
    ::= { tNetworkQueueFCEntry 4 }

tNetworkQueueFCLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "timestamp of last change to this row in tNetworkQueueFCTable."
    ::= { tNetworkQueueFCEntry 7 }

--
-- shared queue policy table
--

tSharedQueuePolicyTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TSharedQueuePolicyEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Defines the Alcatel 7x50 SR series Shared Queue Policy Table
         for providing, via SNMP, the capability of defining the queue
         policies.

         tSharedQueuePolicyTable holds a list of all shared queue
         policies in the managed system. Shared queue policies should
         exist in this table before being referenced elsewhere.

         There is a default queue policy 'default' created by the
         agent and can not be deleted."
    ::= { tSharedQueueObjects 1 }

tSharedQueuePolicyEntry OBJECT-TYPE
    SYNTAX       TSharedQueuePolicyEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Defines an entry in the tSharedQueuePolicyTable. Entries are
         created and deleted in this table by SNMP sets to
         tSharedQueuePolicyRowStatus."
    INDEX { tSharedQueuePolicy }
    ::= { tSharedQueuePolicyTable 1 }

TSharedQueuePolicyEntry ::= SEQUENCE
    {
        tSharedQueuePolicy                TNamedItem,
        tSharedQueuePolicyRowStatus       RowStatus,
        tSharedQueuePolicyLastChanged     TimeStamp,
        tSharedQueuePolicyDescription     TItemDescription
    }

tSharedQueuePolicy OBJECT-TYPE
    SYNTAX       TNamedItem
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "tSharedQueuePolicy serves as an index and uniquely identifies
         a queue policy in the managed system."
    ::= { tSharedQueuePolicyEntry 1 }

tSharedQueuePolicyRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSharedQueuePolicyRowStatus allows entries to be created and
         deleted in the tSharedQueuePolicyTable. The deletion of an
         entry in this table will fail if the action is performed on the
         default policy or the policy is being used within the system."
    REFERENCE
        "See definition of RowStatus in RFC 2579, 'Textual Conventions
         for SMIv2.'"
    ::= { tSharedQueuePolicyEntry 2 }

tSharedQueuePolicyLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tSharedQueuePolicyLastChanged indicates the value of sysUpTime
         when the queue policy entry was last modified."
    ::= { tSharedQueuePolicyEntry 3 }

tSharedQueuePolicyDescription OBJECT-TYPE
    SYNTAX       TItemDescription
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSharedQueuePolicyDescription specifies the user provided
         description string for this queue policy entry. It can consist
         of only the printable, seven-bit ASCII characters."
    DEFVAL { ''H }
    ::= { tSharedQueuePolicyEntry 4 }

--
-- queue policy queue table
--

tSharedQueueTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TSharedQueueEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Defines the Alcatel 7x50 SR series Shared Queue Table for
         providing, via SNMP, the capability of defining the queues for
         the shared queue policies existing in the tSharedQueuePolicyTable.

         tSharedQueueTable holds a list of all the shared queues in the
         managed system defined for the queue policies. Queues should exist
         in this table before being referenced by the forwarding class
         mappings within the queue policy as defined in the
         tSharedQueueFCTable.

         There is a default queue with the queue identifier '1' which
         gets created automatically when a new queue policy is created."
    ::= { tSharedQueueObjects 2 }

tSharedQueueEntry OBJECT-TYPE
    SYNTAX       TSharedQueueEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Defines an entry in the tSharedQueueTable.

         Entries are created/deleted in this table by SNMP sets to
         tSharedQueueRowStatus.

         An exception to this rule is the default queue. The default queue
         is created/deleted as a result of the creation/deletion of a new
         queue policy in the tSharedQueuePolicyTable."
    INDEX { tSharedQueuePolicy, tSharedQueueId }
    ::= { tSharedQueueTable 1 }

TSharedQueueEntry ::= SEQUENCE
    {
        tSharedQueueId               TQueueId,
        tSharedQueueRowStatus        RowStatus,
        tSharedQueueLastChanged      TimeStamp, 
        tSharedQueuePoolName         TNamedItemOrEmpty,
        tSharedQueueParent           TNamedItemOrEmpty,
        tSharedQueueLevel            TLevel,
        tSharedQueueWeight           TWeight,
        tSharedQueueCIRLevel         TLevelOrDefault,
        tSharedQueueCIRWeight        TWeight,
        tSharedQueueExpedite         INTEGER,
        tSharedQueueCIR              TRatePercent,
        tSharedQueuePIR              TRatePercent,
        tSharedQueueCBS              TBurstPercent,
        tSharedQueueMBS              TBurstPercent,
        tSharedQueueHiPrioOnly       TBurstPercentOrDefault,
        tSharedQueueIsMultipoint     TruthValue
    }

tSharedQueueId OBJECT-TYPE
    SYNTAX       TQueueId (1..32)
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "tSharedQueueId serves as the secondary index. When used in
         conjunction with tSharedQueuePolicy, it uniquely identifies
         a shared queue in the managed system."
    ::= { tSharedQueueEntry 1 }

tSharedQueueRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSharedQueueRowStatus allows entries to be created and deleted
         in the tSharedQueueTable. The deletion of an entry in this table
         will fail if the action is performed on the default queue or if
         the queue is being referenced by a forwarding class mapping in
         the queue policy."
    REFERENCE
        "See definition of RowStatus in RFC 2579, 'Textual Conventions for
         SMIv2.'"
    ::= { tSharedQueueEntry 2 }

tSharedQueueLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tSharedQueueLastChanged indicates the value of sysUpTime when
         the queue entry was last modified."
    ::= { tSharedQueueEntry 3 }

tSharedQueuePoolName OBJECT-TYPE
    SYNTAX       TNamedItemOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSharedQueuePoolName specifies the pool from which the queue
         should be allocating its buffer. By default it will share the
         default pool being used by the entity where the queue is
         instantiated."
    DEFVAL { ''H }
    ::= { tSharedQueueEntry 4 }

tSharedQueueParent OBJECT-TYPE
    SYNTAX       TNamedItemOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSharedQueueParent specifies the scheduler to which this queue
         should be feeding into. By default there would be no parent for
         the queue and it will be controlled by the entity where the
         queue is instantiated."
    DEFVAL { ''H }
    ::= { tSharedQueueEntry 5 }

tSharedQueueLevel OBJECT-TYPE
    SYNTAX       TLevel
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSharedQueueLevel specifies the level of priority to be used
         by the parent entity to compute the priority of this queue
         when the traffic is exceeding the committed rate."
    DEFVAL { 1 }
    ::= { tSharedQueueEntry 6 }

tSharedQueueWeight OBJECT-TYPE
    SYNTAX       TWeight
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSharedQueueWeight specifies the weight that this queue should
         be assigned by the parent among all the entities feeding into
         the parent with the same level when the traffic is exceeding the
         committed rate."
    DEFVAL { 1 }
    ::= { tSharedQueueEntry 7 }

tSharedQueueCIRLevel OBJECT-TYPE
    SYNTAX       TLevelOrDefault
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSharedQueueCIRLevel specifies the level of priority to be used
         by the parent entity to compute the priority of this queue when
         the traffic is conforming to the committed rate.

         The level '0' means treat all offered load for this queue same
         as the load which is exceeding the committed rate."
    DEFVAL { 0 }
    ::= { tSharedQueueEntry 8 }

tSharedQueueCIRWeight OBJECT-TYPE
    SYNTAX       TWeight
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSharedQueueCIRWeight specifies the weight that this queue should
         be assigned by the parent among all the entities feeding into the
         parent with the same level when the traffic is conforming to the
         committed rate."
    DEFVAL { 1 }
    ::= { tSharedQueueEntry 9 }

tSharedQueueExpedite OBJECT-TYPE
    SYNTAX       INTEGER {
                     expedited     (1),
                     auto-expedited(2),
                     non-expedited (3)
                 }
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSharedQueueExpedite specifies the priority that this queue should
         be assigned by the hardware level schedulers.

         The value 'auto-expedited' implies that this attribute should be
         dynamically updated by looking at the forwarding classes mapping
         into this queue. In such a case the queue is treated as
         'non-expedited' if there is even a single non-expedited forwarding
         class using this queue."
    DEFVAL { auto-expedited }
    ::= { tSharedQueueEntry 10 }

tSharedQueueCIR OBJECT-TYPE
    SYNTAX       TRatePercent
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSharedQueueCIR specifies the committed rate as a percentage of
         maximum rate for this queue."
    DEFVAL { 0 }
    ::= { tSharedQueueEntry 11 }

tSharedQueuePIR OBJECT-TYPE
    SYNTAX       TRatePercent
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSharedQueuePIR specifies the peak rate as a percentage of
         maximum rate for this queue."
    DEFVAL { 100 }
    ::= { tSharedQueueEntry 12 }

tSharedQueueCBS OBJECT-TYPE
    SYNTAX       TBurstPercent
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSharedQueueCBS specifies the committed burst size as a percentage
         of maximum reserved buffer space for the queue."
    DEFVAL { 0 }
    ::= { tSharedQueueEntry 13 }

tSharedQueueMBS OBJECT-TYPE
    SYNTAX       TBurstPercent
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSharedQueueMBS specifies the maximum burst size as a percentage
         of maximum reserved buffer space for the queue."
    DEFVAL { 100 }
    ::= { tSharedQueueEntry 14 }

tSharedQueueHiPrioOnly OBJECT-TYPE
    SYNTAX       TBurstPercentOrDefault
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSharedQueueHiPrioOnly specifies the percentage of buffer space
         to be exclusively by high priority packets as a percentage of
         maximum reserved buffer space for the queue."
    DEFVAL { -1 }
    ::= { tSharedQueueEntry 15 }

tSharedQueueIsMultipoint        OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tSharedQueueIsMultipoint specifies whether this 
         shared queue is a multipoint-shared queue. This object can only
         be set at row creation time."
    DEFVAL { false }
    ::= { tSharedQueueEntry 16 }

--
-- queue policy fc table
--

tSharedQueueFCTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TSharedQueueFCEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Defines the Alcatel 7x50 SR series forwarding class to
         shared queue mapping table for providing, via SNMP, the
         capability of defining the forwarding class to shared queue
         mapping for the queue policies existing in the
         tSharedQueuePolicyTable.

         tSharedQueueFCTable holds a list of forwarding class to
         queue mappings managed system defined for the queue policies.
         The forwarding classes which are not existing in this table
         should use the default shared queue for this policy."
    ::= { tSharedQueueObjects 3 }

tSharedQueueFCEntry OBJECT-TYPE
    SYNTAX       TSharedQueueFCEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Defines an entry in the tSharedQueueFCTable.

         Entries are created/deleted in this table by SNMP sets to
         tSharedQueueFCRowStatus."
    INDEX { tSharedQueuePolicy, tSharedQueueFCName }
    ::= { tSharedQueueFCTable 1 }

TSharedQueueFCEntry ::= SEQUENCE
    {
        tSharedQueueFCName          TFCName,
        tSharedQueueFCRowStatus     RowStatus,
        tSharedQueueFCLastChanged   TimeStamp,
        tSharedQueueFCQueue         TQueueId,
        tSharedQueueFCMCastQueue    TQueueId,
        tSharedQueueFCBCastQueue    TQueueId,
        tSharedQueueFCUnknownQueue  TQueueId
    }

tSharedQueueFCName OBJECT-TYPE
    SYNTAX       TFCName
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "tSharedQueueFCName serves as the secondary index. When used in
         conjunction with tSharedQueuePolicy, it uniquely identifies a
         forwarding class to queue mapping in the managed system."
    ::= { tSharedQueueFCEntry 1 }

tSharedQueueFCRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSharedQueueFCRowStatus allows entries to be created and
         deleted in the tSharedQueueFCTable. Deletion of an entry from
         this table will cause the forwarding class to use the default
         mapping."
    REFERENCE
        "See definition of RowStatus in RFC 2579, 'Textual Conventions
         for SMIv2.'"
    ::= { tSharedQueueFCEntry 2 }

tSharedQueueFCLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tSharedQueueFCLastChanged indicates the value of sysUpTime
         when the forwarding class to queue mapping entry was last
         modified."
    ::= { tSharedQueueFCEntry 3 }

tSharedQueueFCQueue OBJECT-TYPE
    SYNTAX       TQueueId (1..8)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSharedQueueFCQueue specifies the logical queue that the
         traffic classified into this forwarding class should use."
    DEFVAL { 1 }
    ::= { tSharedQueueFCEntry 4 }

tSharedQueueFCMCastQueue OBJECT-TYPE
    SYNTAX       TQueueId (9..16)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of tSharedQueueFCMCastQueue specifies the logical 
         queue that mutlticast traffic classified into this forwarding 
         class should use."
    DEFVAL { 9 }
    ::= { tSharedQueueFCEntry 5 }

tSharedQueueFCBCastQueue OBJECT-TYPE
    SYNTAX       TQueueId (17..24)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of tSharedQueueFCBCastQueue specifies the logical 
         queue that broadscast traffic classified into this forwarding 
         class should use."
    DEFVAL { 17 }
    ::= { tSharedQueueFCEntry 6 }

tSharedQueueFCUnknownQueue OBJECT-TYPE
    SYNTAX       TQueueId (25..32)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of tSharedQueueFCUnknownQueue specifies the logical 
         queue that unknown traffic classified into this forwarding 
         class should use."
    DEFVAL { 25 }
    ::= { tSharedQueueFCEntry 7 }

--
-- Slope policy table
--

tSlopePolicyTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TSlopePolicyEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Slope Policy Table. Each entry in this table defines the RED
        slopes for the high and the low priority traffic."
    ::= { tSlopeObjects 1 }

tSlopePolicyEntry OBJECT-TYPE
    SYNTAX       TSlopePolicyEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Slope Policy Entry."
    INDEX { tSlopePolicy }
    ::= { tSlopePolicyTable 1 }

TSlopePolicyEntry ::= SEQUENCE
    {
        tSlopePolicy            TNamedItem,
        tSlopeRowStatus         RowStatus,
        tSlopeDescription       TItemDescription,
        tSlopeHiAdminStatus     INTEGER,
        tSlopeHiStartAverage    Unsigned32,
        tSlopeHiMaxAverage      Unsigned32,
        tSlopeHiMaxProbability  Unsigned32,
        tSlopeLoAdminStatus     INTEGER,
        tSlopeLoStartAverage    Unsigned32,
        tSlopeLoMaxAverage      Unsigned32,
        tSlopeLoMaxProbability  Unsigned32,
        tSlopeTimeAvgFactor     Unsigned32,
        tSlopeLastChanged       TimeStamp
    }

tSlopePolicy OBJECT-TYPE
    SYNTAX       TNamedItem
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "tSlopePolicy maintains the name of the slope policy
        identifying the policy."
    ::= { tSlopePolicyEntry 1 }

tSlopeRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "Row Status of the entry. This allows creation/deletion of rows in this
         table."
    ::= { tSlopePolicyEntry 2 }

tSlopeDescription OBJECT-TYPE
    SYNTAX       TItemDescription
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "User-provided description of the policy."
    DEFVAL { ''H }
    ::= { tSlopePolicyEntry 3 }

tSlopeHiAdminStatus OBJECT-TYPE
    SYNTAX       INTEGER {
                     up  (1),
                     down(2)
                 }
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSlopeHiAdminStatus specifies the admin state for the high priority RED
         slope. If the tSlopeHiAdminStatus is set to 'down', the RED slope is
         inactive."
    DEFVAL { down }
    ::= { tSlopePolicyEntry 4 }

tSlopeHiStartAverage OBJECT-TYPE
    SYNTAX       Unsigned32 (0..100)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The percentage of the buffer utilized after which the drop probability
         starts to rise above 0."
    DEFVAL { 70 }
    ::= { tSlopePolicyEntry 5 }

tSlopeHiMaxAverage OBJECT-TYPE
    SYNTAX       Unsigned32 (0..100)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The percentage of the buffer utilized after which the drop probability
         is 100 percent. This implies that all packets beyond this point will be
         dropped."
    DEFVAL { 90 }
    ::= { tSlopePolicyEntry 6 }

tSlopeHiMaxProbability OBJECT-TYPE
    SYNTAX       Unsigned32 (0..100)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The drop probability increases steadily from 0 at the
         tSlopeHiStartAverage upto the tSlopeHiMaxProbability at the
         tSlopeHiMaxAverage."
    DEFVAL { 80 }
    ::= { tSlopePolicyEntry 7 }

tSlopeLoAdminStatus OBJECT-TYPE
    SYNTAX       INTEGER {
                     up(1),
                     down(2)
                 }
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tSlopeLoAdminStatus specifies the admin state for the high priority RED
        slope. If the tSlopeLoAdminStatus is set to 'down', the RED slope is
        inactive."
    DEFVAL { down }
    ::= { tSlopePolicyEntry 8 }

tSlopeLoStartAverage OBJECT-TYPE
    SYNTAX       Unsigned32 (0..100)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The percentage of the buffer utilized after which the drop probability
        starts to rise above 0."
    DEFVAL { 50 }
    ::= { tSlopePolicyEntry 9 }

tSlopeLoMaxAverage OBJECT-TYPE
    SYNTAX       Unsigned32 (0..100)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The percentage of the buffer utilized after which the drop probability
        is 100 percent. This implies that all packets beyond this point will be
        dropped."
    DEFVAL { 75 }
    ::= { tSlopePolicyEntry 10 }

tSlopeLoMaxProbability OBJECT-TYPE
    SYNTAX       Unsigned32 (0..100)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The drop probability increases steadily from 0 at the
        tSlopeLoStartAverage upto the tSlopeLoMaxProbability at the
        tSlopeLoMaxAverage."
    DEFVAL { 80 }
    ::= { tSlopePolicyEntry 11 }

tSlopeTimeAvgFactor OBJECT-TYPE
    SYNTAX       Unsigned32 (0..15)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The time average factor describes the weighting between the previous
        shared buffer average utilization result and the new shared buffer
        utilization in determining the new shared buffer average utilization."
    DEFVAL { 7 }
    ::= { tSlopePolicyEntry 12 }

tSlopeLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "Timestamp of when this entry was last changed."
    ::= { tSlopePolicyEntry 13 }

--
-- Scheduler policy table
--

tSchedulerPolicyTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TSchedulerPolicyEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        " "
    ::= { tSchedulerObjects 1 }

tSchedulerPolicyEntry OBJECT-TYPE
    SYNTAX       TSchedulerPolicyEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about a particular Scheduler Policy."
    INDEX { IMPLIED tSchedulerPolicyName }
    ::= { tSchedulerPolicyTable 1 }

TSchedulerPolicyEntry ::= SEQUENCE
    {
        tSchedulerPolicyName            TNamedItem,
        tSchedulerPolicyRowStatus       RowStatus,
        tSchedulerPolicyDescription     TItemDescription,
        tSchedulerPolicyLastChanged     TimeStamp,
        tSchedulerPolicyFrameBasedAccnt TruthValue
    }

tSchedulerPolicyName OBJECT-TYPE
    SYNTAX       TNamedItem
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Name of the scheduler policy."
    ::= { tSchedulerPolicyEntry 1 }

tSchedulerPolicyRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "Row Status of the entry."
    ::= { tSchedulerPolicyEntry 2 }

tSchedulerPolicyDescription OBJECT-TYPE
    SYNTAX      TItemDescription
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "Description for the scheduler policy"
    DEFVAL { ''H }
    ::= { tSchedulerPolicyEntry 3 }

tSchedulerPolicyLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "timestamp of last change to this row in tSchedulerPolicyTable."
    ::= { tSchedulerPolicyEntry 4 }

tSchedulerPolicyFrameBasedAccnt OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of tSchedulerPolicyFrameBasedAccnt specifies whether
         to use frame-based accounting for the schedulers within the
         scheduler policy and the children queues parented to the
         scheduling policy.

         If the value is 'false', the default packet-based accounting
         method will be used."
    DEFVAL { false }
    ::= { tSchedulerPolicyEntry 5 }

--
-- Virtual Scheduler table
--

tVirtualSchedulerTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TVirtualSchedulerEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        " "
    ::= { tSchedulerObjects 2 }

tVirtualSchedulerEntry OBJECT-TYPE
    SYNTAX       TVirtualSchedulerEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Information about a particular Scheduler belonging to a Scheduler
        Policy."
    INDEX { tSchedulerPolicyName, tVirtualSchedulerTier,
            IMPLIED tVirtualSchedulerName }
    ::= { tVirtualSchedulerTable 1 }

TVirtualSchedulerEntry ::= SEQUENCE
    {
        tVirtualSchedulerTier          INTEGER,
        tVirtualSchedulerName          TNamedItem,
        tVirtualSchedulerRowStatus     RowStatus,
        tVirtualSchedulerDescription   TItemDescription,
        tVirtualSchedulerParent        TNamedItemOrEmpty,
        tVirtualSchedulerLevel         TLevel,
        tVirtualSchedulerWeight        TWeight,
        tVirtualSchedulerCIRLevel      TLevelOrDefault,
        tVirtualSchedulerCIRWeight     TWeight,
        tVirtualSchedulerPIR           TPIRRate,
        tVirtualSchedulerCIR           TCIRRate,
        tVirtualSchedulerSummedCIR     TruthValue,
        tVirtualSchedulerLastChanged   TimeStamp,
        tVirtualSchedulerUsePortParent TruthValue,
        tVirtualSchedulerPortLvl       TLevel,
        tVirtualSchedulerPortWght      TWeight,
        tVirtualSchedulerPortCIRLvl    TLevelOrDefault,
        tVirtualSchedulerPortCIRWght   TWeight
    }

tVirtualSchedulerTier OBJECT-TYPE
    SYNTAX       INTEGER (1..3)
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Tier to which this scheduler belongs to."
    ::= { tVirtualSchedulerEntry 1 }

tVirtualSchedulerName OBJECT-TYPE
    SYNTAX       TNamedItem
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "Name of the scheduler policy."
    ::= { tVirtualSchedulerEntry 2 }

tVirtualSchedulerRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "Row Status of the entry."
    ::= { tVirtualSchedulerEntry 3 }

tVirtualSchedulerDescription OBJECT-TYPE
    SYNTAX       TItemDescription
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "Description for the scheduler policy"
    DEFVAL { ''H }
    ::= { tVirtualSchedulerEntry 4 }

tVirtualSchedulerParent OBJECT-TYPE
    SYNTAX       TNamedItemOrEmpty
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The scheduler to which this scheduler would be feeding to."
    DEFVAL { ''H }
    ::= { tVirtualSchedulerEntry 5 }

tVirtualSchedulerLevel OBJECT-TYPE
    SYNTAX  TLevel
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "This specifies the level of priority while feeding to the parent."
    DEFVAL { 1 }
    ::= { tVirtualSchedulerEntry 6 }

tVirtualSchedulerWeight OBJECT-TYPE
    SYNTAX       TWeight
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The weight that needs to be used by the scheduler to which this queue
         would be feeding to."
    DEFVAL { 1 }
    ::= { tVirtualSchedulerEntry 7 }

tVirtualSchedulerCIRLevel OBJECT-TYPE
    SYNTAX  TLevelOrDefault
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "This specifies the level of priority while feeding to the parent.
         The level '0' means treat all offered load for this queue as for
         the above CIR traffic."
    DEFVAL { 0 }
    ::= { tVirtualSchedulerEntry 8 }

tVirtualSchedulerCIRWeight OBJECT-TYPE
    SYNTAX       TWeight
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The weight that needs to be used until the committed rate by the
         scheduler to which this queue would be feeding to."
    DEFVAL { 1 }
    ::= { tVirtualSchedulerEntry 9 }

tVirtualSchedulerPIR OBJECT-TYPE
    SYNTAX       TPIRRate
    UNITS        "kbps"
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The rate allowed for this scheduler. A value of -1 implies
         maximum."
    DEFVAL { -1 }
    ::= { tVirtualSchedulerEntry 10 }

tVirtualSchedulerCIR OBJECT-TYPE
    SYNTAX       TCIRRate
    UNITS        "kbps"
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The rate allowed for this scheduler. A value of -1 implies
         maximum."
    DEFVAL { 0 }
    ::= { tVirtualSchedulerEntry 11 }

tVirtualSchedulerSummedCIR OBJECT-TYPE
    SYNTAX       TruthValue
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tVirtualSchedulerSummedCIR specifies if the CIR should be
         used as the summed value of the children. If set to 'true',
         tVirtualSchedulerCIR loses its meaning."
    DEFVAL { true }
    ::= { tVirtualSchedulerEntry 12 }

tVirtualSchedulerLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "The value of the object tVirtualSchedulerLastChanged indicates the
         timestamp of last change to this row in tVirtualSchedulerTable."
    ::= { tVirtualSchedulerEntry 13 }

tVirtualSchedulerUsePortParent  OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of the object tVirtualSchedulerUsePortParent specifies
         whether this scheduler is parented by a port-level scheduler. When
         the value is 'true', this virtual scheduler is parented by a 
         port-level scheduler. This object is mutually exclusive with 
         tVirtualSchedulerParent. Only one kind of parent is allowed."
    DEFVAL      { false }
    ::= { tVirtualSchedulerEntry 14 }

tVirtualSchedulerPortLvl        OBJECT-TYPE
    SYNTAX       TLevel
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of the object tVirtualSchedulerPortLvl specifies the
         port priority this scheduler will use to receive bandwidth from
         the port-level scheduler for its above-cir offered load."
    DEFVAL      { 1 }
    ::= { tVirtualSchedulerEntry 15 }

tVirtualSchedulerPortWght       OBJECT-TYPE
    SYNTAX      TWeight
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of the object tVirtualSchedulerPortWght specifies the
         weight this scheduler will use to receive bandwidth from the
         port-level scheduler for its above-cir offered load."
    DEFVAL      { 1 }
    ::= { tVirtualSchedulerEntry 16 }

tVirtualSchedulerPortCIRLvl     OBJECT-TYPE
    SYNTAX      TLevelOrDefault
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of the object tVirtualSchedulerPortCIRLvl specifies the
         port priority this scheduler will use to receive bandwidth from
         the port-level scheduler for its within-cir offered load."
    DEFVAL      { 0 }
    ::= { tVirtualSchedulerEntry 17 }

tVirtualSchedulerPortCIRWght    OBJECT-TYPE
    SYNTAX      TWeight
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of the object tVirtualSchedulerPortCIRWght specifies the
         weight this scheduler will use to receive bandwidth from the
         port-level scheduler for its within-cir offered load."
    DEFVAL      { 0 }
    ::= { tVirtualSchedulerEntry 18 }

--
--  Port Scheduler plcy table
--

tPortSchedulerPlcyTable       OBJECT-TYPE
    SYNTAX      SEQUENCE OF TPortSchedulerPlcyEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The tPortSchedulerPlcyTable has an entry for each port scheduler
         configured on this system."
    ::= { tSchedulerObjects 3 }

tPortSchedulerPlcyEntry       OBJECT-TYPE
    SYNTAX      TPortSchedulerPlcyEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row represents a particular port scheduler. Entries are created
         and deleted by the user."
    INDEX { IMPLIED tPortSchedulerPlcyName }
    ::= { tPortSchedulerPlcyTable 1 }

TPortSchedulerPlcyEntry ::= SEQUENCE
    {
        tPortSchedulerPlcyName                  TNamedItem,
        tPortSchedulerPlcyRowStatus             RowStatus,
        tPortSchedulerPlcyDescription           TItemDescription,
        tPortSchedulerPlcyLastChanged           TimeStamp,
        tPortSchedulerPlcyMaxRate               TPortSchedulerPIR,
        tPortSchedulerPlcyLvl1PIR               TPortSchedulerPIR,
        tPortSchedulerPlcyLvl1CIR               TPortSchedulerCIR,
        tPortSchedulerPlcyLvl2PIR               TPortSchedulerPIR,
        tPortSchedulerPlcyLvl2CIR               TPortSchedulerCIR,
        tPortSchedulerPlcyLvl3PIR               TPortSchedulerPIR,
        tPortSchedulerPlcyLvl3CIR               TPortSchedulerCIR,
        tPortSchedulerPlcyLvl4PIR               TPortSchedulerPIR,
        tPortSchedulerPlcyLvl4CIR               TPortSchedulerCIR,
        tPortSchedulerPlcyLvl5PIR               TPortSchedulerPIR,
        tPortSchedulerPlcyLvl5CIR               TPortSchedulerCIR,
        tPortSchedulerPlcyLvl6PIR               TPortSchedulerPIR,
        tPortSchedulerPlcyLvl6CIR               TPortSchedulerCIR,
        tPortSchedulerPlcyLvl7PIR               TPortSchedulerPIR,
        tPortSchedulerPlcyLvl7CIR               TPortSchedulerCIR,
        tPortSchedulerPlcyLvl8PIR               TPortSchedulerPIR,
        tPortSchedulerPlcyLvl8CIR               TPortSchedulerCIR,
        tPortSchedulerPlcyOrphanLvl             TLevel,
        tPortSchedulerPlcyOrphanWeight          TWeight,
        tPortSchedulerPlcyOrphanCIRLvl          TLevelOrDefault,
        tPortSchedulerPlcyOrphanCIRWght         TWeight
    }

tPortSchedulerPlcyName  OBJECT-TYPE
    SYNTAX      TNamedItem
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyName specifies the name of the port
         scheduler."
    ::= { tPortSchedulerPlcyEntry 1 } 

tPortSchedulerPlcyRowStatus     OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyRowStatus is used for the creation
         and deletion of port scheduler policies."
    ::= { tPortSchedulerPlcyEntry 2 } 
        
tPortSchedulerPlcyDescription   OBJECT-TYPE
    SYNTAX      TItemDescription
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyDescription specifies the description
         of this port scheduler."
    DEFVAL      { "" }
    ::= { tPortSchedulerPlcyEntry 3 } 

tPortSchedulerPlcyLastChanged        OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyLastChanged indicates the value of 
         sysUpTime at the time of the most recent management change to this
         row."
    ::= { tPortSchedulerPlcyEntry 4 }

tPortSchedulerPlcyMaxRate        OBJECT-TYPE
    SYNTAX      TPortSchedulerPIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyMaxRate specifies the explicit maximum
         frame based bandwidth limit of this port scheduler."
    DEFVAL      { -1 }
    ::= { tPortSchedulerPlcyEntry 5 }

tPortSchedulerPlcyLvl1PIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerPIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyLvl1PIR specifies the total bandwidth
         limit, PIR, for priority level 1."
    DEFVAL      { -1 }
    ::= { tPortSchedulerPlcyEntry 6 }

tPortSchedulerPlcyLvl1CIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerCIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyLvl1CIR specifies the within-cir
         bandwidth limit for priority level 1."
    DEFVAL      { -1 }
    ::= { tPortSchedulerPlcyEntry 7 }

tPortSchedulerPlcyLvl2PIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerPIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyLvl2PIR specifies the total bandwidth
         limit, PIR, for priority level 2."
    DEFVAL      { -1 }
    ::= { tPortSchedulerPlcyEntry 8 }

tPortSchedulerPlcyLvl2CIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerCIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyLvl2CIR specifies the within-cir
         bandwidth limit for priority level 2."
    DEFVAL      { -1 }
    ::= { tPortSchedulerPlcyEntry 9 }

tPortSchedulerPlcyLvl3PIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerPIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyLvl3PIR specifies the total bandwidth
         limit, PIR, for priority level 3."
    DEFVAL      { -1 }
    ::= { tPortSchedulerPlcyEntry 10 }

tPortSchedulerPlcyLvl3CIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerCIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyLvl3CIR specifies the within-cir
         bandwidth limit for priority level 3."
    DEFVAL      { -1 }
    ::= { tPortSchedulerPlcyEntry 11 }

tPortSchedulerPlcyLvl4PIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerPIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyLvl4PIR specifies the total bandwidth
         limit, PIR, for priority level 4."
    DEFVAL      { -1 }
    ::= { tPortSchedulerPlcyEntry 12 }

tPortSchedulerPlcyLvl4CIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerCIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyLvl4CIR specifies the within-cir
         bandwidth limit for priority level 4."
    DEFVAL      { -1 }
    ::= { tPortSchedulerPlcyEntry 13 }

tPortSchedulerPlcyLvl5PIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerPIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyLvl5PIR specifies the total bandwidth
         limit, PIR, for priority level 5."
    DEFVAL      { -1 }
    ::= { tPortSchedulerPlcyEntry 14 }

tPortSchedulerPlcyLvl5CIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerCIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyLvl5CIR specifies the within-cir
         bandwidth limit for priority level 5."
    DEFVAL      { -1 }
    ::= { tPortSchedulerPlcyEntry 15 }

tPortSchedulerPlcyLvl6PIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerPIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyLvl6PIR specifies the total bandwidth
         limit, PIR, for priority level 6."
    DEFVAL      { -1 }
    ::= { tPortSchedulerPlcyEntry 16 }

tPortSchedulerPlcyLvl6CIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerCIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyLvl6CIR specifies the within-cir
         bandwidth limit for priority level 6."
    DEFVAL      { -1 }
    ::= { tPortSchedulerPlcyEntry 17 }

tPortSchedulerPlcyLvl7PIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerPIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyLvl7PIR specifies the total bandwidth
         limit, PIR, for priority level 7."
    DEFVAL      { -1 }
    ::= { tPortSchedulerPlcyEntry 18 }

tPortSchedulerPlcyLvl7CIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerCIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyLvl7CIR specifies the within-cir
         bandwidth limit for priority level 7."
    DEFVAL      { -1 }
    ::= { tPortSchedulerPlcyEntry 19 }

tPortSchedulerPlcyLvl8PIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerPIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyLvl8PIR specifies the total bandwidth
         limit, PIR, for priority level 8."
    DEFVAL      { -1 }
    ::= { tPortSchedulerPlcyEntry 20 }

tPortSchedulerPlcyLvl8CIR        OBJECT-TYPE
    SYNTAX      TPortSchedulerCIR
    UNITS       "kbps"
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyLvl8CIR specifies the within-cir
         bandwidth limit for priority level 8."
    DEFVAL      { -1 }
    ::= { tPortSchedulerPlcyEntry 21 }

tPortSchedulerPlcyOrphanLvl        OBJECT-TYPE
    SYNTAX      TLevel
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyOrphanLvl specifies the port priority
         of orphaned queues and schedulers that are above-cir."
    DEFVAL      { 1 }
    ::= { tPortSchedulerPlcyEntry 22 }

tPortSchedulerPlcyOrphanWeight        OBJECT-TYPE
    SYNTAX      TWeight
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyOrphanWeight specifies the weight of
         orphaned queues and schedulers that are above-cir."
    DEFVAL      { 0 } 
    ::= { tPortSchedulerPlcyEntry 23 }

tPortSchedulerPlcyOrphanCIRLvl        OBJECT-TYPE
    SYNTAX      TLevelOrDefault
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyOrphanCIRLvl specifies the port priority
         of orphaned queues and schedulers that are within-cir."
    DEFVAL      { 0 }
    ::= { tPortSchedulerPlcyEntry 24 }

tPortSchedulerPlcyOrphanCIRWght        OBJECT-TYPE
    SYNTAX      TWeight
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tPortSchedulerPlcyOrphanCIRWght specifies the weight of
         orphaned queues and schedulers that are within-cir."
    DEFVAL      { 0 }
    ::= { tPortSchedulerPlcyEntry 25 }

--
-- Qos Domain Time Stamps
--
tQosDomainLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "The value of the object tQosDomainLastChanged indicates the
         timestamp of last change to the QoS Domain."
    ::= { tQosTimeStampObjects 1 }

tDSCPNameTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "The value of the object tDSCPNameTableLastChanged indicates the
         timestamp of last change  to the DSCP Name Table."
    ::= { tQosTimeStampObjects 5 }

tFCNameTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "The value of the object tFCNameTableLastChanged indicates the
         timestamp of last change to the FC Name Table."
    ::= { tQosTimeStampObjects 10 }

tSapIngressTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "The value of the object tSapIngressTableLastChanged indicates the
         timestamp of last change  to the Sap Ingress Policy Table."
    ::= { tQosTimeStampObjects 20 }

tSapIngressQueueTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tSapIngressQueueTableLastChanged maintains the timestamp of
        the last change made to the Sap Ingress Queue Table."
    ::= { tQosTimeStampObjects 21 }

tSapIngressDSCPTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tSapIngressDSCPTableLastChanged maintains the timestamp of the
        last change made to the Sap Ingress DSCP Mapping Table."
    ::= { tQosTimeStampObjects 22 }

tSapIngressDot1pTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tSapIngressDot1pTableLastChanged maintains the timestamp of the
        last change made to the Sap Ingress Dot1p Table."
    ::= { tQosTimeStampObjects 23 }

tSapIngressIPCriteriaTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tSapIngressIPCriteriaTableLastChanged maintains the timestamp of
        the last change made to the Sap Ingress IP Criteria Table."
    ::= { tQosTimeStampObjects 24 }

tSapIngressMacCriteriaTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tSapIngressMacCriteriaTableLastChanged maintains the timestamp
        of the last change made to the Sap Ingress Mac Criteria Table."
    ::= { tQosTimeStampObjects 25 }

tSapIngressFCTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tSapIngressFCTableLastChanged maintains the timestamp of the
        last change made to the Sap Ingress FC Table."
    ::= { tQosTimeStampObjects 26 }

tSapIngressPrecTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tSapIngressPrecTableLastChanged maintains the timestamp of the
        last change made to the Sap Ingress Precedence Table."
    ::= { tQosTimeStampObjects 27 }

tSapEgressTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tSapEgressTableLastChanged maintains the timestamp of the
        last change made to the Sap Egress Policy Table."
    ::= { tQosTimeStampObjects 30 }

tSapEgressQueueTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tSapEgressQueueTableLastChanged maintains the timestamp of
        the last change made to the Sap Egress Queue Table."
    ::= { tQosTimeStampObjects 31 }

tSapEgressFCTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tSapEgressFCTableLastChanged maintains the timestamp of the
        last change made to the Sap Egress FC Table."
    ::= { tQosTimeStampObjects 32 }

tNetworkPolicyTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tNetworkPolicyTableLastChanged maintains the timestamp of the
        last change made to the Network Policy Table."
    ::= { tQosTimeStampObjects 40 }

tNetworkIngressDSCPTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tNetworkIngressDSCPTableLastChanged maintains the timestamp of
        the last change made to the Network Ingress DSCP Mapping Table."
    ::= { tQosTimeStampObjects 41 }

tNetworkIngressLSPEXPTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tNetworkIngressLSPEXPTableLastChanged maintains the timestamp
        of the last change made to the Network Ingress LSPEXP Table."
    ::= { tQosTimeStampObjects 42 }

tNetworkEgressFCTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tNetworkEgressFCTableLastChanged maintains the timestamp of
        the last change made to the Network Egress FC Table."
    ::= { tQosTimeStampObjects 43 }

tNetworkIngressDot1pTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tNetworkIngressDot1pTableLastChanged maintains the timestamp
        of the last change made to the Network Ingress Dot1p Table."
    ::= { tQosTimeStampObjects 44 }

tNetworkQueuePolicyTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tNetworkQueuePolicyTableLastChanged maintains the timestamp of
        the last change made to the Network Queue Policy Table."
    ::= { tQosTimeStampObjects 50 }

tNetworkQueueTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tNetworkQueueTableLastChanged maintains the timestamp of the
        last change made to the Network Queue Table."
    ::= { tQosTimeStampObjects 51 }

tNetworkQueueFCTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tNetworkQueueFCTableLastChanged maintains the timestamp of the
        last change made to the Network Queue FC Table."
    ::= { tQosTimeStampObjects 52 }

tSlopePolicyTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tSlopePolicyTableLastChanged maintains the timestamp of the
        last change made to the Slope Policy Table."
    ::= { tQosTimeStampObjects 55 }

tSchedulerPolicyTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tSchedulerPolicyTableLastChanged maintains the timestamp of
        the last change made to the Scheduler Policy Table."
    ::= { tQosTimeStampObjects 60 }

tVirtualSchedulerTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tVirtualSchedulerTableLastChanged maintains the timestamp of
        the last change made to the Virtual Scheduler Table."
    ::= { tQosTimeStampObjects 61 }

tAtmTdpTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tAtmTdpTableLastChanged maintains the timestamp of
         the last change made to the Traffic Descriptor Profile Table."
    ::= { tQosTimeStampObjects 62 }

tSharedQueuePolicyTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tSharedQueuePolicyTableLastChanged maintains the timestamp of
        the last change made to the Queue Policy Table."
    ::= { tQosTimeStampObjects 63 }

tSharedQueueTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tSharedQueueTableLastChanged maintains the timestamp of the
        last change made to the Queue Table."
    ::= { tQosTimeStampObjects 64 }

tSharedQueueFCTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tSharedQueueFCTableLastChanged maintains the timestamp of the
        last change made to the Queue FC Table."
    ::= { tQosTimeStampObjects 65 }


tSapIngressIPv6CriteriaTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "The value of the object tSapIngressIPv6CriteriaTableLastChanged
         indicates the timestamp of the last change made to the Sap Ingress
         IPv6 Criteria Table."
    ::= { tQosTimeStampObjects 66 }

tNamedPoolPolicyTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "The value of tNamedPoolPolicyTableLastChanged indicates the sysUpTime
         at the time of the last modification of an entry in the
         tNamedPoolPolicyTable.

         If no changes were made to the table since the last re-initialization
         of the local network management subsystem, then this object
         contains a zero value."
    ::= { tQosTimeStampObjects 73 }

tQ1NamedPoolTableLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "The value of tQ1NamedPoolTableLastChanged indicates the sysUpTime
         at the time of the last modification of an entry in the
         tQ1NamedPoolTable.

         If no changes were made to the table since the last re-initialization
         of the local network management subsystem, then this object
         contains a zero value."
    ::= { tQosTimeStampObjects 74 }


--
-- Atm Traffic Descriptor Table
--

tAtmTdpTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TAtmTdpEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "This table contains information on ATM traffic
         descriptors for controlling ATM traffic
         management capabilities."
    ::= { tAtmTdpObjects 1}

tAtmTdpEntry OBJECT-TYPE
    SYNTAX       TAtmTdpEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "This list contains ATM traffic descriptor
         objects and the associated parameters. All objects that
         do not apply to a given service category are given default
         values of zero."
    INDEX  {tAtmTdpIndex}
    ::= { tAtmTdpTable 1}

TAtmTdpEntry ::= SEQUENCE
    {
        tAtmTdpIndex        AtmTrafficDescrParamIndex,
        tAtmTdpSir          Unsigned32,
        tAtmTdpPir          Unsigned32,
        tAtmTdpMbs          Unsigned32,
        tAtmTdpMir          Unsigned32,
        tAtmTdpShaping      INTEGER,
        tAtmTdpServCat      AtmServiceCategory,
        tAtmTdpDescription  TItemDescription,
        tAtmTdpLastChanged  TimeStamp,
        tAtmTdpRowStatus    RowStatus,
        tAtmTdpDescrType    TAtmTdpDescrType,
        tAtmTdpCdvt         Unsigned32,
        tAtmTdpPolicing     INTEGER,
        tAtmTdpCLPTagging   INTEGER
    }

tAtmTdpIndex OBJECT-TYPE
    SYNTAX       AtmTrafficDescrParamIndex (1..1000)
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "This object is used to identify an entity
         inside the tAtmTdpTable.
         When creating a new row in the table
         the value of this index may be obtained
         by retrieving the value of tAtmTdpIndexNext."
    ::= { tAtmTdpEntry 1 }

tAtmTdpSir OBJECT-TYPE
    SYNTAX       Unsigned32 (0..4294967295)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tAtmTdpSir identifies the sustained information rate
         in kilobits per second.
         The default Sir is determined by the Service Category.
         The following default applies to Sir that is modifiable
         depending upon a given service category:

           Applicable Service Category  Default Sir Value
                   RT-VBR                       0
                  NRT-VBR                       0

         Note that for the service categories given above,
         the value of Sir must be less than or equal to
         the value of Pir."
    ::= { tAtmTdpEntry 2 }

tAtmTdpPir OBJECT-TYPE
    SYNTAX       Unsigned32 (0..4294967295)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tAtmTdpPir identifies the peak information rate
         in kilobits per second.
         The default Pir is determined by the Service Category.
         The following default applies to Pir that is modifiable
         depending upon a given service category:

           Applicable Service Category   Default Pir Value
                UBR (with/without Mir)          4294967295
                CBR                             0
             RT-VBR                             0
            NRT-VBR                             0"
    ::= { tAtmTdpEntry 3 }

tAtmTdpMbs OBJECT-TYPE
    SYNTAX       Unsigned32 (0..4294967295)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tAtmTdpMbs identifies the maximum burst size
         in cells.
         The default Mbs is determined by the Service Category.
         The following default applies to Mbs that is modifiable
         depending upon a given service category:

           Applicable Service Category  Default Mbs Value
                    RT-VBR                     32
                   NRT-VBR                     32"
    ::= { tAtmTdpEntry 4 }

tAtmTdpMir OBJECT-TYPE
    SYNTAX       Unsigned32 (0..4294967295)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tAtmTdpMir identifies the Minimum Information Rate
         in kilobits per second.
         The default Mir is determined by the Service Category.
         The following default applies for Mir that is modifiable
         depending upon a given service category:

           Applicable Service Category  Default Mir Value
             UBR (with/without Mir)             0

         Note that for the service categories given above,
         the value of Mir must be less than or equal to
         the value of Pir."
    ::= { tAtmTdpEntry 5 }

tAtmTdpShaping OBJECT-TYPE
    SYNTAX       INTEGER {
                     disabled(0),
                     enabled(1)
                 }
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tAtmTdpShaping determines whether egress shaping should occur.
         The default tAtmTdpShaping is determined by the Service Category.
         The following default applies for tAtmTdpShaping
         depending upon a given service category:

          Applicable Service Category  Default Shaping Value
                UBR                         disabled
                CBR                         enabled
             RT-VBR                         enabled
            NRT-VBR                         enabled

         For a Service Category of UBR, tAtmTdpShaping cannot be enabled.
        
         For CBR and RT-VBR service cateories disabling shaping is 
         MDA-dependent.

         Shaping is only applied in the egress direction."
    ::= { tAtmTdpEntry 6 }

tAtmTdpServCat OBJECT-TYPE
    SYNTAX       AtmServiceCategory
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tAtmTdpServCat determines the ATM service category."
    DEFVAL { ubr }
    ::= { tAtmTdpEntry 7 }

tAtmTdpDescription OBJECT-TYPE
    SYNTAX       TItemDescription
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "The value of tAtmTdpDescription specifies an ASCII string used
         to describe the ATM traffic descriptor."
    ::= { tAtmTdpEntry 8 }

tAtmTdpLastChanged OBJECT-TYPE
    SYNTAX       TimeStamp
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "tAtmTdpLastChanged maintains the timestamp of the last change
         made to a row in the Traffic Descriptor Profile Table."
    ::= { tAtmTdpEntry 9 }

tAtmTdpRowStatus OBJECT-TYPE
    SYNTAX       RowStatus
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tAtmTdpRowStatus is used to create/modify/delete a new row in
         this table. Only createAndGo/Active/Destroy are supported"
    ::= { tAtmTdpEntry 10 }

tAtmTdpDescrType OBJECT-TYPE
    SYNTAX       TAtmTdpDescrType
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tAtmTdpDescrType identifies the types of ATM traffic descriptor.
         The default is determined by the value of tAtmTdpServCat.
         The following default rules apply:

         Applicable Service Category       tAtmTdpDescrType
                UBR                          clp0And1pcr
                CBR                          clp0And1pcr
             RT-VBR                    clp0And1pcrPlusClp0And1scr
            NRT-VBR                    clp0And1pcrPlusClp0And1scr"
    ::= { tAtmTdpEntry 11 }

tAtmTdpCdvt OBJECT-TYPE
    SYNTAX       Unsigned32 (0..4294967295)
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tAtmTdpCdvt identifies the Cell Delay Variation
         Tolerance in microseconds.
         The following default applies for Cdvt that is modifiable
         depending upon a given service category:

           Applicable Service Category  Default Cdvt Value
             CBR/RT-VBR/NRT-VBR/UBR             250 "

    ::= { tAtmTdpEntry 12 }

tAtmTdpPolicing OBJECT-TYPE
    SYNTAX       INTEGER {
                     disabled(0),
                     enabled(1)
                 }
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tAtmTdpPolicing determines whether ingress traffic is policed.
         Policing by default is disabled. Policing is valid for CBR, UBR
         RT-VBR and NRT-VBR. This is cell-based policing.

          tAtmTdpDescrType           Policing Scheme    Applicable Serv Cat
           clp0And1pcr                 CBR.1             CBR and UBR
           clp0And1pcrPlusClp0And1scr  VBR.1             RT/NRT-VBR
           clp0And1pcrPlusClp0scr      VBR.2             RT/NRT-VBR
           clp0And1pcrPlusClp0scrTag   VBR.3             RT/NRT-VBR"
    ::= { tAtmTdpEntry 13 }

tAtmTdpCLPTagging OBJECT-TYPE
    SYNTAX       INTEGER {
                     disabled(0),
                     enabled(1)
                 }
    MAX-ACCESS   read-create
    STATUS       current
    DESCRIPTION
        "tAtmTdpCLPTagging controls the setting of the CLP bit in the 
         ATM cell  header for egress traffic on an IES or VPRN SAP. 
         If disabled (the default), all traffic has the CLP bit set 
         to zero. If enabled, traffic queued on expedited queues has 
         the CLP bit set to zero, while traffic on non-expedited queues 
         has the CLP bit set to one."
    DEFVAL { disabled }
    ::= { tAtmTdpEntry 14 }



--
-- Named Pool Policy Table
--

tNamedPoolPolicyTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TNamedPoolPolicyEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "This table contains information on Named buffer pools policy."
    ::= { tPoolObjects 1 }

tNamedPoolPolicyEntry OBJECT-TYPE
    SYNTAX       TNamedPoolPolicyEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "This list contains Named buffer pools policy related information."
    INDEX  {tNamedPoolPolicyName}
    ::= { tNamedPoolPolicyTable 1}

TNamedPoolPolicyEntry ::= SEQUENCE
    {
       tNamedPoolPolicyName               TNamedItem,
       tNamedPoolPolicyRowStatus          RowStatus,
       tNamedPoolPolicyLastChanged        TimeStamp,
       tNamedPoolPolicyDescription        TItemDescription,
       tNamedPoolPolicyQ1DefaultWeight    Unsigned32,
       tNamedPoolPolicyQ1MdaWeight        Unsigned32,
       tNamedPoolPolicyQ1PortWeight       Unsigned32
    }

tNamedPoolPolicyName OBJECT-TYPE
    SYNTAX      TNamedItem
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The value of tNamedPoolPolicyName indicates the name of 
         Named pool policy."
    ::= { tNamedPoolPolicyEntry 1 }

tNamedPoolPolicyRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tNamedPoolPolicyRowStatus is used for 
         creation or deletion of named pool policies."
    ::= { tNamedPoolPolicyEntry 2 }

tNamedPoolPolicyLastChanged OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tNamedPoolPolicyLastChanged indicates the 
         sysUpTime when this row was last modified."
    ::= { tNamedPoolPolicyEntry 3 }

tNamedPoolPolicyDescription OBJECT-TYPE
    SYNTAX      TItemDescription
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tNamedPoolPolicyDescription specifies the 
         description for a specific named pool policy."
    DEFVAL { "" }
    ::= { tNamedPoolPolicyEntry 4 }

tNamedPoolPolicyQ1DefaultWeight OBJECT-TYPE
    SYNTAX      Unsigned32 (0..100)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tNamedPoolPolicyQ1DefaultWeight specifies 
         the port allocation weight given to the default pools."
    DEFVAL { 50 }
    ::= { tNamedPoolPolicyEntry 5 }

tNamedPoolPolicyQ1MdaWeight OBJECT-TYPE
    SYNTAX      Unsigned32 (0..100)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tNamedPoolPolicyQ1MdaWeight specifies 
         the port allocation weight given to the MDA named pools."
    DEFVAL { 50 }
    ::= { tNamedPoolPolicyEntry 6 }

tNamedPoolPolicyQ1PortWeight OBJECT-TYPE
    SYNTAX      Unsigned32 (0..100)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tNamedPoolPolicyQ1PortWeight specifies 
         the port allocation weight given to the local port 
         named pools."
    DEFVAL { 50 }
    ::= { tNamedPoolPolicyEntry 7 }



--
-- Q1 Named Pool Table
--

tQ1NamedPoolTable OBJECT-TYPE
    SYNTAX       SEQUENCE OF TQ1NamedPoolEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "This table contains information on Q1 pools policy."
    ::= { tPoolObjects 2 }

tQ1NamedPoolEntry OBJECT-TYPE
    SYNTAX       TQ1NamedPoolEntry
    MAX-ACCESS   not-accessible
    STATUS       current
    DESCRIPTION
        "This list contains Q1 pools policy related information."
    INDEX  {tQ1NamedPoolPolicyName, 
            tQ1NamedPoolName}
    ::= { tQ1NamedPoolTable 1}

TQ1NamedPoolEntry ::= SEQUENCE
    {
       tQ1NamedPoolPolicyName               TNamedItem,
       tQ1NamedPoolName                     TNamedItem,
       tQ1NamedPoolRowStatus                RowStatus,
       tQ1NamedPoolLastChanged              TimeStamp,
       tQ1NamedPoolDescription              TItemDescription,
       tQ1NamedPoolNetworkAllocWeight       Unsigned32,
       tQ1NamedPoolAccessAllocWeight        Unsigned32,
       tQ1NamedPoolSlopePolicy              TNamedItemOrEmpty,
       tQ1NamedPoolReservedCbs              Integer32
    }

tQ1NamedPoolPolicyName OBJECT-TYPE
    SYNTAX      TNamedItem
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The value of tQ1NamedPoolPolicyName indicates the name of 
         the existing Q1 named pool policy."
    ::= { tQ1NamedPoolEntry 1 }

tQ1NamedPoolName OBJECT-TYPE
    SYNTAX      TNamedItem
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The value of tQ1NamedPoolName indicates the name of 
         the existing pool within the policy."
    ::= { tQ1NamedPoolEntry 2 }

tQ1NamedPoolRowStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tQ1NamedPoolRowStatus is used for 
         creation or deletion of Q1 named pools."       
    ::= { tQ1NamedPoolEntry 3 }

tQ1NamedPoolLastChanged OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of tQ1NamedPoolLastChanged indicates the 
         sysUpTime when this row was last modified. "
    ::= { tQ1NamedPoolEntry 4 }

tQ1NamedPoolDescription OBJECT-TYPE
    SYNTAX      TItemDescription
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tQ1NamedPoolDescription specifies the 
         description for a specific Q1 named pool."
    DEFVAL { "" }
    ::= { tQ1NamedPoolEntry 5 }

tQ1NamedPoolNetworkAllocWeight OBJECT-TYPE
    SYNTAX      Unsigned32 (0..100)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tQ1NamedPoolNetworkAllocWeight specifies 
         the weight used to divide network associated buffer 
         space between named pools."
    DEFVAL { 50 }
    ::= { tQ1NamedPoolEntry 6 }


tQ1NamedPoolAccessAllocWeight OBJECT-TYPE
    SYNTAX      Unsigned32 (0..100)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tQ1NamedPoolAccessAllocWeight specifies 
         the weight used to divide access associated buffer 
         space between named pools."
    DEFVAL { 50 }
    ::= { tQ1NamedPoolEntry 7 }

tQ1NamedPoolSlopePolicy OBJECT-TYPE
    SYNTAX      TNamedItemOrEmpty
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tQ1NamedPoolSlopePolicy specifies the 
         name of the slop-policy which is used to override 
         the default slope-policy for the named buffer pool."
    DEFVAL { ''H }
    ::= { tQ1NamedPoolEntry 8 }

tQ1NamedPoolReservedCbs OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The value of tQ1NamedPoolReservedCbs specifies the 
         reserved CBS size of the pool. The reserved CBS 
         (Committed Burst Size) defines the amount of buffer 
         space within the pool that is not considered shared."
    DEFVAL { 30 }
    ::= { tQ1NamedPoolEntry 9 }

--
-- The following object may be used in conjunction with
-- the atmTrafficDescrParamTable for the creation of
-- new table entries.
--

tAtmTdpIndexNext OBJECT-TYPE
    SYNTAX       Integer32 (0..1000)
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "This object contains an appropriate value to be used for
         tAtmTdpIndex when creating entries in the tAtmTdpTable.
         The value 0 indicates that no unassigned entries are
         available. To obtain the tAtmTdpIndex value for a new
         entry, the manager issues a management protocol
         retrieval operation to obtain the current value of this
         object.  After each retrieval, the agent should modify the
         value to the next unassigned index.
         After a manager retrieves a value the agent will determine
         through its local policy when this index value will be made
         available for reuse."
    ::= { tAtmTdpObjects 2 }

---
--- The following objects are to be used in
--- conjunction with the tAtmTdpTable
--- for information on profile limits and consumed
--- profiles.
---

tAtmTdpsMaxSupported OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "This object contains the maximum number of ATM
         Traffic Descriptor Profiles that can be configured
         on this system."
    ::= { tAtmTdpObjects 3 }

tAtmTdpsCurrentlyConfigured OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION
        "This object contains the number of currently configured
         ATM Traffic Descriptor Profiles on this system."
    ::= { tAtmTdpObjects 4 }

--
-- Conformance Information
--

tmnxQosCompliances OBJECT IDENTIFIER ::= { tmnxQosConformance 1 }
tmnxQosGroups      OBJECT IDENTIFIER ::= { tmnxQosConformance 2 }

--
-- compliance statements
--

-- tmnxQosCompliance   MODULE-COMPLIANCE
--    ::= { tmnxQosCompliances 1 }

-- tmnxQosR2r1Compliance   MODULE-COMPLIANCE
--    ::= { tmnxQosCompliances 2 }

-- tmnxQosV3v0Compliance   MODULE-COMPLIANCE
--    ::= { tmnxQosCompliances 3 }

tmnxQos7450V4v0Compliance   MODULE-COMPLIANCE
    STATUS   obsolete
    DESCRIPTION
        "The compliance statement for management of QOS features
         on Alcatel 7450 ESS series systems release R4.0."
    MODULE  -- this module
        MANDATORY-GROUPS {
            tmnxQosGlobalGroup,
            tmnxQosDSCPGroup,
            tmnxQosFCGroup,
            tmnxQosSapIngressV4v0Group,
            tmnxQosSapEgressR2r1Group,
            tmnxQosNetworkR2r1Group,
            tmnxQosSlopeGroup,
            tmnxQosSchedulerGroup,
            -- tmnxQosAtmTdpV3v0Group,
            tmnxQosQueueV4v0Group
            -- tmnxQosSapIpv6FilterR4r0Group
        }
    ::= { tmnxQosCompliances 4 }

tmnxQos7750V4v0Compliance   MODULE-COMPLIANCE
    STATUS   obsolete
    DESCRIPTION
        "The compliance statement for management of QOS features
         on Alcatel 7750 SR series systems release R4.0."
    MODULE  -- this module
        MANDATORY-GROUPS {
            tmnxQosGlobalGroup,
            tmnxQosDSCPGroup,
            tmnxQosFCGroup,
            tmnxQosSapIngressV4v0Group,
            tmnxQosSapEgressR2r1Group,
            tmnxQosNetworkR2r1Group,
            tmnxQosSlopeGroup,
            tmnxQosSchedulerGroup,
            tmnxQosAtmTdpV3v0Group,
            tmnxQosQueueV4v0Group,
            tmnxQosSapIpv6FilterR4r0Group
        }
    ::= { tmnxQosCompliances 5 }

tmnxQos7450V5v0Compliance   MODULE-COMPLIANCE
    STATUS   obsolete
    DESCRIPTION
        "The compliance statement for management of QOS features
         on Alcatel 7450 ESS series systems release R5.0."
    MODULE  -- this module
        MANDATORY-GROUPS {
            tmnxQosGlobalGroup,
            tmnxQosDSCPGroup,
            tmnxQosFCGroup,
            tmnxQosSapIngressV4v0Group,
            tmnxQosSapEgressV5v0Group,
            tmnxQosNetworkV5v0Group,
            tmnxQosSlopeGroup,
            tmnxQosSchedulerV5v0Group,
            -- tmnxQosAtmTdpV5v0Group,
            tmnxQosQueueV4v0Group
            -- tmnxQosSapIpv6FilterR4r0Group
        }
    ::= { tmnxQosCompliances 6 }

tmnxQos7750V5v0Compliance   MODULE-COMPLIANCE
    STATUS   obsolete
    DESCRIPTION
        "The compliance statement for management of QOS features
         on Alcatel 7750 SR series systems release R5.0."
    MODULE  -- this module
        MANDATORY-GROUPS {
            tmnxQosGlobalGroup,
            tmnxQosDSCPGroup,
            tmnxQosFCGroup,
            tmnxQosSapIngressV4v0Group,
            tmnxQosSapEgressV5v0Group,
            tmnxQosNetworkV5v0Group,
            tmnxQosSlopeGroup,
            tmnxQosSchedulerV5v0Group,
            tmnxQosAtmTdpV5v0Group,
            tmnxQosQueueV4v0Group,
            tmnxQosSapIpv6FilterR4r0Group
        }
    ::= { tmnxQosCompliances 7 }

tmnxQos7450V6v0Compliance   MODULE-COMPLIANCE
    STATUS   current
    DESCRIPTION
        "The compliance statement for management of QOS features
         on Alcatel 7450 ESS series systems release R6.0."
    MODULE  -- this module
        MANDATORY-GROUPS {
            tmnxQosGlobalGroup,
            tmnxQosDSCPGroup,
            tmnxQosFCGroup,
            tmnxQosSapIngressV6v0Group,
            tmnxQosSapEgressV6v0Group,
            tmnxQosNetworkV6v0Group,
            tmnxQosSlopeGroup,
            tmnxQosSchedulerV5v0Group,
            -- tmnxQosAtmTdpV5v0Group,
            tmnxQosQueueV4v0Group,
            -- tmnxQosSapIpv6FilterR4r0Group
            tmnxQosFrameBasedV6v0Group,
            tmnxQosNamedPoolPolicyV6v0Group
        }
    ::= { tmnxQosCompliances 8 }

tmnxQos7750V6v0Compliance   MODULE-COMPLIANCE
    STATUS   current
    DESCRIPTION
        "The compliance statement for management of QOS features
         on Alcatel 7750 SR series systems release R6.0."
    MODULE  -- this module
        MANDATORY-GROUPS {
            tmnxQosGlobalGroup,
            tmnxQosDSCPGroup,
            tmnxQosFCGroup,
            tmnxQosSapIngressV6v0Group,
            tmnxQosSapEgressV6v0Group,
            tmnxQosNetworkV6v0Group,
            tmnxQosSlopeGroup,
            tmnxQosSchedulerV5v0Group,
            tmnxQosAtmTdpV5v0Group,
            tmnxQosQueueV4v0Group,
            tmnxQosSapIpv6FilterR4r0Group,
            tmnxQosFrameBasedV6v0Group,
            tmnxQosNamedPoolPolicyV6v0Group
        }
    ::= { tmnxQosCompliances 9 }
--
-- units of conformance
--
tmnxQosGlobalGroup OBJECT-GROUP
    OBJECTS {
        tQosDomainLastChanged
    }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting general management of QOS
         features on Alcatel 7x50 SR series systems."
    ::= { tmnxQosGroups 1 }

tmnxQosDSCPGroup OBJECT-GROUP
    OBJECTS {
        tDSCPNameRowStatus,
        tDSCPNameStorageType,
        tDSCPNameDscpValue,
        tDSCPNameLastChanged,
        tDSCPNameTableLastChanged
    }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of Differential Services
         Code Points on Alcatel 7x50 SR series systems."
    ::= { tmnxQosGroups 2 }

tmnxQosFCGroup OBJECT-GROUP
    OBJECTS {
        tFCRowStatus,
        tFCStorageType,
        tFCValue,
        tFCNameLastChanged,
        tFCNameTableLastChanged
    }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of Forwarding Classes
         on Alcatel 7x50 SR series systems."
    ::= { tmnxQosGroups 3 }

-- tmnxQosSapIngressGroup OBJECT-GROUP
--    ::= { tmnxQosGroups 4 }

-- tmnxQosSapEgressGroup OBJECT-GROUP
--    ::= { tmnxQosGroups 5 }

-- tmnxQosNetworkGroup OBJECT-GROUP
--    ::= { tmnxQosGroups 6 }

tmnxQosSlopeGroup OBJECT-GROUP
    OBJECTS {
        tSlopeRowStatus,
        tSlopeDescription,
        tSlopeHiAdminStatus,
        tSlopeHiStartAverage,
        tSlopeHiMaxAverage,
        tSlopeHiMaxProbability,
        tSlopeLoAdminStatus,
        tSlopeLoStartAverage,
        tSlopeLoMaxAverage,
        tSlopeLoMaxProbability,
        tSlopeTimeAvgFactor,
        tSlopeLastChanged,
        tSlopePolicyTableLastChanged
    }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of Slope policies
         on Alcatel 7x50 SR series systems."
    ::= { tmnxQosGroups 7 }

tmnxQosSchedulerGroup OBJECT-GROUP
    OBJECTS {
        tSchedulerPolicyRowStatus,
        tSchedulerPolicyDescription,
        tSchedulerPolicyLastChanged,
        tSchedulerPolicyTableLastChanged,
        tVirtualSchedulerRowStatus,
        tVirtualSchedulerDescription,
        tVirtualSchedulerParent,
        tVirtualSchedulerLevel,
        tVirtualSchedulerWeight,
        tVirtualSchedulerCIRLevel,
        tVirtualSchedulerCIRWeight,
        tVirtualSchedulerPIR,
        tVirtualSchedulerCIR,
        tVirtualSchedulerSummedCIR,
        tVirtualSchedulerLastChanged,
        tVirtualSchedulerTableLastChanged
    }
    STATUS      obsolete
    DESCRIPTION
        "The group of objects supporting management of Scheduler policies
         on Alcatel 7x50 SR series systems."
    ::= { tmnxQosGroups 8 }

-- tmnxQosAtmTdpR2r1Group OBJECT-GROUP
--    ::= { tmnxQosGroups 9 }

tQosObsoleteObjectsGroup    OBJECT-GROUP
    OBJECTS {
        tSapIngressQueueOperPIR,
        tSapIngressQueueOperCIR,
        tSapEgressQueueOperPIR,
        tSapEgressQueueOperCIR
    }
    STATUS    current
    DESCRIPTION
        "The group of obsolete objects in the ALCATEL-IND1-TIMETRA-QOS-MIB."
    ::= { tmnxQosGroups 10 }

-- tmnxQosSapIngressR2r1Group OBJECT-GROUP
--    ::= { tmnxQosGroups 11 }

tmnxQosSapEgressR2r1Group OBJECT-GROUP
    OBJECTS {
        tSapEgressRowStatus,
        tSapEgressScope,
        tSapEgressDescription,
        tSapEgressLastChanged,
        tSapEgressTableLastChanged,
        tSapEgressQueueRowStatus,
        tSapEgressQueueParent,
        tSapEgressQueueLevel,
        tSapEgressQueueWeight,
        tSapEgressQueueCIRLevel,
        tSapEgressQueueCIRWeight,
        tSapEgressQueueExpedite,
        tSapEgressQueueCBS,
        tSapEgressQueueMBS,
        tSapEgressQueueHiPrioOnly,
        tSapEgressQueueCIRAdaptation,
        tSapEgressQueuePIRAdaptation,
        tSapEgressQueueAdminPIR,
        tSapEgressQueueAdminCIR,
        tSapEgressQueueLastChanged,
        tSapEgressQueueTableLastChanged,
        tSapEgressFCRowStatus,
        tSapEgressFCQueue,
        tSapEgressFCDot1PValue,
        tSapEgressFCLastChanged,
        tSapEgressFCTableLastChanged
    }
    STATUS      obsolete
    DESCRIPTION
        "The group of objects supporting management of SAP egress QoS
         policies on Alcatel 7x50 SR series systems."
    ::= { tmnxQosGroups 12 }

tmnxQosNetworkR2r1Group OBJECT-GROUP
    OBJECTS {
        tNetworkPolicyRowStatus,
        tNetworkPolicyScope,
        tNetworkPolicyDescription,
        tNetworkPolicyIngressDefaultActionFC,
        tNetworkPolicyIngressDefaultActionProfile,
        tNetworkPolicyEgressRemark,
        tNetworkPolicyLastChanged,
        tNetworkPolicyIngressLerUseDscp,
        tNetworkPolicyTableLastChanged,
        tNetworkIngressDSCPRowStatus,
        tNetworkIngressDSCPFC,
        tNetworkIngressDSCPProfile,
        tNetworkIngressDSCPLastChanged,
        tNetworkIngressDSCPTableLastChanged,
        tNetworkIngressDot1pRowStatus,
        tNetworkIngressDot1pFC,
        tNetworkIngressDot1pProfile,
        tNetworkIngressDot1pLastChanged,
        tNetworkIngressDot1pTableLastChanged,
        tNetworkIngressLSPEXPRowStatus,
        tNetworkIngressLSPEXPFC,
        tNetworkIngressLSPEXPProfile,
        tNetworkIngressLSPEXPLastChanged,
        tNetworkIngressLSPEXPTableLastChanged,
        tNetworkEgressFCDSCPInProfile,
        tNetworkEgressFCDSCPOutProfile,
        tNetworkEgressFCLspExpInProfile,
        tNetworkEgressFCLspExpOutProfile,
        tNetworkEgressFCDot1pInProfile,
        tNetworkEgressFCDot1pOutProfile,
        tNetworkEgressFCLastChanged,
        tNetworkEgressFCTableLastChanged,
        tNetworkQueuePolicyRowStatus,
        tNetworkQueuePolicyDescription,
        tNetworkQueuePolicyLastChanged,
        tNetworkQueuePolicyTableLastChanged,
        tNetworkQueueRowStatus,
        tNetworkQueuePoolName,
        tNetworkQueueParent,
        tNetworkQueueLevel,
        tNetworkQueueWeight,
        tNetworkQueueCIRLevel,
        tNetworkQueueCIRWeight,
        tNetworkQueueMCast,
        tNetworkQueueExpedite,
        tNetworkQueueCIR,
        tNetworkQueuePIR,
        tNetworkQueueCBS,
        tNetworkQueueMBS,
        tNetworkQueueHiPrioOnly,
        tNetworkQueueLastChanged ,
        tNetworkQueueTableLastChanged,
        tNetworkQueueFCRowStatus,
        tNetworkQueueFC,
        tNetworkQueueFCMCast,
        tNetworkQueueFCLastChanged,
        tNetworkQueueFCTableLastChanged
    }
    STATUS      obsolete
    DESCRIPTION
        "The group of objects supporting management of Network QoS policies
         on Alcatel 7x50 SR series systems."
    ::= { tmnxQosGroups 13 }

-- tmnxQosQueueR2r1Group OBJECT-GROUP
--    ::= { tmnxQosGroups 14 }

tmnxQosAtmTdpV3v0Group OBJECT-GROUP
    OBJECTS   {
        tAtmTdpSir,
        tAtmTdpPir,
        tAtmTdpMbs,
        tAtmTdpMir,
        tAtmTdpShaping,
        tAtmTdpServCat,
        tAtmTdpLastChanged,
        tAtmTdpDescription,
        tAtmTdpRowStatus,
        tAtmTdpDescrType,
        tAtmTdpCdvt,
        tAtmTdpPolicing,
        tAtmTdpIndexNext,
        tAtmTdpsMaxSupported,
        tAtmTdpsCurrentlyConfigured,
        tAtmTdpTableLastChanged
    }
    STATUS    obsolete
    DESCRIPTION
        "The group of objects for ATM Traffic Descriptor Profiles
         on Alcatel 7x50 SR series systems release 3.0."
    ::= { tmnxQosGroups 15 }


tmnxQosSapIpv6FilterR4r0Group OBJECT-GROUP
    OBJECTS {
        tSapIngressIPv6CriteriaRowStatus,
        tSapIngressIPv6CriteriaDescription,
        tSapIngressIPv6CriteriaActionFC,
        tSapIngressIPv6CriteriaActionPriority,
        tSapIngressIPv6CriteriaSourceIpAddr,
        tSapIngressIPv6CriteriaSourceIpMask,
        tSapIngressIPv6CriteriaDestIpAddr,
        tSapIngressIPv6CriteriaDestIpMask,
        tSapIngressIPv6CriteriaNextHeader,
        tSapIngressIPv6CriteriaSourcePortValue1,
        tSapIngressIPv6CriteriaSourcePortValue2,
        tSapIngressIPv6CriteriaSourcePortOperator,
        tSapIngressIPv6CriteriaDestPortValue1,
        tSapIngressIPv6CriteriaDestPortValue2,
        tSapIngressIPv6CriteriaDestPortOperator,
        tSapIngressIPv6CriteriaDSCP,
        tSapIngressIPv6CriteriaLastChanged,
        tSapIngressIPv6CriteriaTableLastChanged
    }
    STATUS    current
    DESCRIPTION
        "The group of objects for Qos Policies related to IPv6
         on Alcatel 7x50 SR series systems release 4.0."
    ::= { tmnxQosGroups 16 }

tmnxQosQueueV4v0Group OBJECT-GROUP
    OBJECTS {
        tSharedQueuePolicyRowStatus,
        tSharedQueuePolicyDescription,
        tSharedQueuePolicyLastChanged,
        tSharedQueuePolicyTableLastChanged,
        tSharedQueueRowStatus,
        tSharedQueuePoolName,
        tSharedQueueParent,
        tSharedQueueLevel,
        tSharedQueueWeight,
        tSharedQueueCIRLevel,
        tSharedQueueCIRWeight,
        tSharedQueueExpedite,
        tSharedQueueCIR,
        tSharedQueuePIR,
        tSharedQueueCBS,
        tSharedQueueMBS,
        tSharedQueueHiPrioOnly,
        tSharedQueueLastChanged,
        tSharedQueueIsMultipoint,
        tSharedQueueTableLastChanged,
        tSharedQueueFCRowStatus,
        tSharedQueueFCQueue,
        tSharedQueueFCLastChanged,
        tSharedQueueFCTableLastChanged,
        tSharedQueueFCMCastQueue,
        tSharedQueueFCBCastQueue,
        tSharedQueueFCUnknownQueue
    }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of QoS queue policies
         on Alcatel 7x50 SR series systems release 4.0."
    ::= { tmnxQosGroups 18 }

tmnxQosSapIngressV4v0Group OBJECT-GROUP
    OBJECTS {
        tSapIngressRowStatus,
        tSapIngressScope,
        tSapIngressDescription,
        tSapIngressDefaultFC,
        tSapIngressDefaultFCPriority,
        tSapIngressMatchCriteria,
        tSapIngressLastChanged,
        tSapIngressTableLastChanged,
        tSapIngressQueueRowStatus,
        tSapIngressQueueParent,
        tSapIngressQueueLevel,
        tSapIngressQueueWeight,
        tSapIngressQueueCIRLevel,
        tSapIngressQueueCIRWeight,
        tSapIngressQueueMCast,
        tSapIngressQueueExpedite,
        tSapIngressQueueCBS,
        tSapIngressQueueMBS,
        tSapIngressQueueHiPrioOnly,
        tSapIngressQueueCIRAdaptation,
        tSapIngressQueuePIRAdaptation,
        tSapIngressQueueAdminPIR,
        tSapIngressQueueAdminCIR,
        tSapIngressQueueLastChanged,
        tSapIngressQueueMode,
        tSapIngressQueueTableLastChanged,
        tSapIngressQueuePoliced,
        tSapIngressDSCPRowStatus,
        tSapIngressDSCPFC,
        tSapIngressDSCPPriority,
        tSapIngressDSCPLastChanged,
        tSapIngressDSCPTableLastChanged,
        tSapIngressDot1pRowStatus,
        tSapIngressDot1pFC,
        tSapIngressDot1pPriority,
        tSapIngressDot1pLastChanged,
        tSapIngressDot1pTableLastChanged,
        tSapIngressIPCriteriaRowStatus,
        tSapIngressIPCriteriaDescription,
        tSapIngressIPCriteriaActionFC,
        tSapIngressIPCriteriaActionPriority,
        tSapIngressIPCriteriaSourceIpAddr,
        tSapIngressIPCriteriaSourceIpMask,
        tSapIngressIPCriteriaDestIpAddr,
        tSapIngressIPCriteriaDestIpMask,
        tSapIngressIPCriteriaProtocol,
        tSapIngressIPCriteriaSourcePortValue1,
        tSapIngressIPCriteriaSourcePortValue2,
        tSapIngressIPCriteriaSourcePortOperator,
        tSapIngressIPCriteriaDestPortValue1,
        tSapIngressIPCriteriaDestPortValue2,
        tSapIngressIPCriteriaDestPortOperator,
        tSapIngressIPCriteriaDSCP,
        tSapIngressIPCriteriaFragment,
        tSapIngressIPCriteriaLastChanged,
        tSapIngressIPCriteriaTableLastChanged,
        tSapIngressMacCriteriaRowStatus,
        tSapIngressMacCriteriaDescription,
        tSapIngressMacCriteriaActionFC,
        tSapIngressMacCriteriaActionPriority,
        tSapIngressMacCriteriaFrameType,
        tSapIngressMacCriteriaSrcMacAddr,
        tSapIngressMacCriteriaSrcMacMask,
        tSapIngressMacCriteriaDstMacAddr,
        tSapIngressMacCriteriaDstMacMask,
        tSapIngressMacCriteriaDot1PValue,
        tSapIngressMacCriteriaDot1PMask,
        tSapIngressMacCriteriaEthernetType,
        tSapIngressMacCriteriaDSAP,
        tSapIngressMacCriteriaDSAPMask,
        tSapIngressMacCriteriaSSAP,
        tSapIngressMacCriteriaSSAPMask,
        tSapIngressMacCriteriaSnapPid,
        tSapIngressMacCriteriaSnapOui,
        tSapIngressMacCriteriaLastChanged,
        tSapIngressMacCriteriaTableLastChanged,
        tSapIngressFCRowStatus,
        tSapIngressFCQueue,
        tSapIngressFCMCastQueue,
        tSapIngressFCBCastQueue,
        tSapIngressFCUnknownQueue,
        tSapIngressFCLastChanged,
        tSapIngressFCInProfRemark,
        tSapIngressFCInProfDscp,
        tSapIngressFCInProfPrec,
        tSapIngressFCOutProfRemark,
        tSapIngressFCOutProfDscp,
        tSapIngressFCOutProfPrec,
        tSapIngressFCProfile,
        tSapIngressFCTableLastChanged,
        tSapIngressPrecRowStatus,
        tSapIngressPrecFC,
        tSapIngressPrecFCPriority,
        tSapIngressPrecLastChanged,
        tSapIngressPrecTableLastChanged
    }
    STATUS      obsolete
    DESCRIPTION
        "The group of objects supporting management of SAP ingress QoS
         policies on Alcatel 7x50 SR series systems release 4.0."
    ::= { tmnxQosGroups 19 }

tmnxQosSchedulerV5v0Group OBJECT-GROUP
    OBJECTS {
        tSchedulerPolicyRowStatus,
        tSchedulerPolicyDescription,
        tSchedulerPolicyLastChanged,
        tSchedulerPolicyTableLastChanged,
        tVirtualSchedulerRowStatus,
        tVirtualSchedulerDescription,
        tVirtualSchedulerParent,
        tVirtualSchedulerLevel,
        tVirtualSchedulerWeight,
        tVirtualSchedulerCIRLevel,
        tVirtualSchedulerCIRWeight,
        tVirtualSchedulerPIR,
        tVirtualSchedulerCIR,
        tVirtualSchedulerSummedCIR,
        tVirtualSchedulerLastChanged,
        tVirtualSchedulerUsePortParent,
        tVirtualSchedulerPortLvl,
        tVirtualSchedulerPortWght,
        tVirtualSchedulerPortCIRLvl,
        tVirtualSchedulerPortCIRWght,
        tVirtualSchedulerTableLastChanged,
        tPortSchedulerPlcyRowStatus,
        tPortSchedulerPlcyDescription,
        tPortSchedulerPlcyLastChanged,
        tPortSchedulerPlcyMaxRate,
        tPortSchedulerPlcyLvl1PIR,
        tPortSchedulerPlcyLvl1CIR,
        tPortSchedulerPlcyLvl2PIR,
        tPortSchedulerPlcyLvl2CIR,
        tPortSchedulerPlcyLvl3PIR,
        tPortSchedulerPlcyLvl3CIR,
        tPortSchedulerPlcyLvl4PIR,
        tPortSchedulerPlcyLvl4CIR,
        tPortSchedulerPlcyLvl5PIR,
        tPortSchedulerPlcyLvl5CIR,
        tPortSchedulerPlcyLvl6PIR,
        tPortSchedulerPlcyLvl6CIR,
        tPortSchedulerPlcyLvl7PIR,
        tPortSchedulerPlcyLvl7CIR,
        tPortSchedulerPlcyLvl8PIR,
        tPortSchedulerPlcyLvl8CIR,
        tPortSchedulerPlcyOrphanLvl,
        tPortSchedulerPlcyOrphanWeight,
        tPortSchedulerPlcyOrphanCIRLvl,
        tPortSchedulerPlcyOrphanCIRWght
    }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of Scheduler policies
         on Alcatel 7x50 SR series systems."
    ::= { tmnxQosGroups 20 }

tmnxQosSapEgressV5v0Group OBJECT-GROUP
    OBJECTS {
        tSapEgressRowStatus,
        tSapEgressScope,
        tSapEgressDescription,
        tSapEgressLastChanged,
        tSapEgressTableLastChanged,
        tSapEgressQueueRowStatus,
        tSapEgressQueueParent,
        tSapEgressQueueLevel,
        tSapEgressQueueWeight,
        tSapEgressQueueCIRLevel,
        tSapEgressQueueCIRWeight,
        tSapEgressQueueExpedite,
        tSapEgressQueueCBS,
        tSapEgressQueueMBS,
        tSapEgressQueueHiPrioOnly,
        tSapEgressQueueCIRAdaptation,
        tSapEgressQueuePIRAdaptation,
        tSapEgressQueueAdminPIR,
        tSapEgressQueueAdminCIR,
        tSapEgressQueueLastChanged,
        tSapEgressQueueTableLastChanged,
        tSapEgressFCRowStatus,
        tSapEgressFCQueue,
        tSapEgressFCDot1PValue,
        tSapEgressFCLastChanged,
        tSapEgressFCTableLastChanged,
        tSapEgressQueueUsePortParent,
        tSapEgressQueuePortLvl,
        tSapEgressQueuePortWght,
        tSapEgressQueuePortCIRLvl,
        tSapEgressQueuePortCIRWght,
        tSapEgressQueuePortAvgOverhead
    }
    STATUS      obsolete
    DESCRIPTION
        "The group of objects supporting management of SAP egress QoS
         policies on Alcatel 7x50 SR series systems."
    ::= { tmnxQosGroups 21 }

tmnxQosNetworkV5v0Group OBJECT-GROUP
    OBJECTS {
        tNetworkPolicyRowStatus,
        tNetworkPolicyScope,
        tNetworkPolicyDescription,
        tNetworkPolicyIngressDefaultActionFC,
        tNetworkPolicyIngressDefaultActionProfile,
        tNetworkPolicyEgressRemark,
        tNetworkPolicyLastChanged,
        tNetworkPolicyIngressLerUseDscp,
        tNetworkPolicyTableLastChanged,
        tNetworkIngressDSCPRowStatus,
        tNetworkIngressDSCPFC,
        tNetworkIngressDSCPProfile,
        tNetworkIngressDSCPLastChanged,
        tNetworkIngressDSCPTableLastChanged,
        tNetworkIngressDot1pRowStatus,
        tNetworkIngressDot1pFC,
        tNetworkIngressDot1pProfile,
        tNetworkIngressDot1pLastChanged,
        tNetworkIngressDot1pTableLastChanged,
        tNetworkIngressLSPEXPRowStatus,
        tNetworkIngressLSPEXPFC,
        tNetworkIngressLSPEXPProfile,
        tNetworkIngressLSPEXPLastChanged,
        tNetworkIngressLSPEXPTableLastChanged,
        tNetworkEgressFCDSCPInProfile,
        tNetworkEgressFCDSCPOutProfile,
        tNetworkEgressFCLspExpInProfile,
        tNetworkEgressFCLspExpOutProfile,
        tNetworkEgressFCDot1pInProfile,
        tNetworkEgressFCDot1pOutProfile,
        tNetworkEgressFCLastChanged,
        tNetworkEgressFCTableLastChanged,
        tNetworkQueuePolicyRowStatus,
        tNetworkQueuePolicyDescription,
        tNetworkQueuePolicyLastChanged,
        tNetworkQueuePolicyTableLastChanged,
        tNetworkQueueRowStatus,
        tNetworkQueuePoolName,
        tNetworkQueueParent,
        tNetworkQueueLevel,
        tNetworkQueueWeight,
        tNetworkQueueCIRLevel,
        tNetworkQueueCIRWeight,
        tNetworkQueueMCast,
        tNetworkQueueExpedite,
        tNetworkQueueCIR,
        tNetworkQueuePIR,
        tNetworkQueueCBS,
        tNetworkQueueMBS,
        tNetworkQueueHiPrioOnly,
        tNetworkQueueLastChanged,
        tNetworkQueueUsePortParent,
        tNetworkQueuePortLvl,
        tNetworkQueuePortWght,
        tNetworkQueuePortCIRLvl,
        tNetworkQueuePortCIRWght,
        tNetworkQueuePortAvgOverhead,
        tNetworkQueuePIRAdaptation,       
        tNetworkQueueCIRAdaptation,
        tNetworkQueueTableLastChanged,
        tNetworkQueueFCRowStatus,
        tNetworkQueueFC,
        tNetworkQueueFCMCast,
        tNetworkQueueFCLastChanged,
        tNetworkQueueFCTableLastChanged
    }
    STATUS      obsolete
    DESCRIPTION
        "The group of objects supporting management of Network QoS policies
         on Alcatel 7x50 SR series systems."
    ::= { tmnxQosGroups 22 }

tmnxQosAtmTdpV5v0Group OBJECT-GROUP
    OBJECTS   {
        tAtmTdpSir,
        tAtmTdpPir,
        tAtmTdpMbs,
        tAtmTdpMir,
        tAtmTdpShaping,
        tAtmTdpServCat,
        tAtmTdpLastChanged,
        tAtmTdpDescription,
        tAtmTdpRowStatus,
        tAtmTdpDescrType,
        tAtmTdpCdvt,
        tAtmTdpPolicing,
        tAtmTdpCLPTagging,
        tAtmTdpIndexNext,
        tAtmTdpsMaxSupported,
        tAtmTdpsCurrentlyConfigured,
        tAtmTdpTableLastChanged
    }
    STATUS    current
    DESCRIPTION
        "The group of objects for ATM Traffic Descriptor Profiles
         on Alcatel 7x50 SR series systems release 5.0."
    ::= { tmnxQosGroups 24 }

tmnxQosSapIngressV6v0Group OBJECT-GROUP
    OBJECTS {
        tSapIngressRowStatus,
        tSapIngressScope,
        tSapIngressDescription,
        tSapIngressDefaultFC,
        tSapIngressDefaultFCPriority,
        tSapIngressMatchCriteria,
        tSapIngressLastChanged,
        tSapIngressTableLastChanged,
        tSapIngressQueueRowStatus,
        tSapIngressQueueParent,
        tSapIngressQueueLevel,
        tSapIngressQueueWeight,
        tSapIngressQueueCIRLevel,
        tSapIngressQueueCIRWeight,
        tSapIngressQueueMCast,
        tSapIngressQueueExpedite,
        tSapIngressQueueCBS,
        tSapIngressQueueMBS,
        tSapIngressQueueHiPrioOnly,
        tSapIngressQueueCIRAdaptation,
        tSapIngressQueuePIRAdaptation,
        tSapIngressQueueAdminPIR,
        tSapIngressQueueAdminCIR,
        tSapIngressQueueLastChanged,
        tSapIngressQueueMode,
        tSapIngressQueuePoolName,
        tSapIngressQueueTableLastChanged,
        tSapIngressQueuePoliced,
        tSapIngressDSCPRowStatus,
        tSapIngressDSCPFC,
        tSapIngressDSCPPriority,
        tSapIngressDSCPLastChanged,
        tSapIngressDSCPTableLastChanged,
        tSapIngressDot1pRowStatus,
        tSapIngressDot1pFC,
        tSapIngressDot1pPriority,
        tSapIngressDot1pLastChanged,
        tSapIngressDot1pTableLastChanged,
        tSapIngressIPCriteriaRowStatus,
        tSapIngressIPCriteriaDescription,
        tSapIngressIPCriteriaActionFC,
        tSapIngressIPCriteriaActionPriority,
        tSapIngressIPCriteriaSourceIpAddr,
        tSapIngressIPCriteriaSourceIpMask,
        tSapIngressIPCriteriaDestIpAddr,
        tSapIngressIPCriteriaDestIpMask,
        tSapIngressIPCriteriaProtocol,
        tSapIngressIPCriteriaSourcePortValue1,
        tSapIngressIPCriteriaSourcePortValue2,
        tSapIngressIPCriteriaSourcePortOperator,
        tSapIngressIPCriteriaDestPortValue1,
        tSapIngressIPCriteriaDestPortValue2,
        tSapIngressIPCriteriaDestPortOperator,
        tSapIngressIPCriteriaDSCP,
        tSapIngressIPCriteriaFragment,
        tSapIngressIPCriteriaLastChanged,
        tSapIngressIPCriteriaTableLastChanged,
        tSapIngressMacCriteriaRowStatus,
        tSapIngressMacCriteriaDescription,
        tSapIngressMacCriteriaActionFC,
        tSapIngressMacCriteriaActionPriority,
        tSapIngressMacCriteriaFrameType,
        tSapIngressMacCriteriaSrcMacAddr,
        tSapIngressMacCriteriaSrcMacMask,
        tSapIngressMacCriteriaDstMacAddr,
        tSapIngressMacCriteriaDstMacMask,
        tSapIngressMacCriteriaDot1PValue,
        tSapIngressMacCriteriaDot1PMask,
        tSapIngressMacCriteriaEthernetType,
        tSapIngressMacCriteriaDSAP,
        tSapIngressMacCriteriaDSAPMask,
        tSapIngressMacCriteriaSSAP,
        tSapIngressMacCriteriaSSAPMask,
        tSapIngressMacCriteriaSnapPid,
        tSapIngressMacCriteriaSnapOui,
        tSapIngressMacCriteriaLastChanged,
        tSapIngressMacCriteriaTableLastChanged,
        tSapIngressFCRowStatus,
        tSapIngressFCQueue,
        tSapIngressFCMCastQueue,
        tSapIngressFCBCastQueue,
        tSapIngressFCUnknownQueue,
        tSapIngressFCLastChanged,
        tSapIngressFCInProfRemark,
        tSapIngressFCInProfDscp,
        tSapIngressFCInProfPrec,
        tSapIngressFCOutProfRemark,
        tSapIngressFCOutProfDscp,
        tSapIngressFCOutProfPrec,
        tSapIngressFCProfile,
        tSapIngressFCTableLastChanged,
        tSapIngressPrecRowStatus,
        tSapIngressPrecFC,
        tSapIngressPrecFCPriority,
        tSapIngressPrecLastChanged,
        tSapIngressPrecTableLastChanged,
        tSapIngressFCDE1OutOfProfile
    }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of SAP ingress QoS
         policies on Alcatel 7x50 SR series systems release 6.0."
    ::= { tmnxQosGroups 25 }

tmnxQosSapEgressV6v0Group OBJECT-GROUP
    OBJECTS {
        tSapEgressRowStatus,
        tSapEgressScope,
        tSapEgressDescription,
        tSapEgressLastChanged,
        tSapEgressTableLastChanged,
        tSapEgressQueueRowStatus,
        tSapEgressQueueParent,
        tSapEgressQueueLevel,
        tSapEgressQueueWeight,
        tSapEgressQueueCIRLevel,
        tSapEgressQueueCIRWeight,
        tSapEgressQueueExpedite,
        tSapEgressQueueCBS,
        tSapEgressQueueMBS,
        tSapEgressQueueHiPrioOnly,
        tSapEgressQueueCIRAdaptation,
        tSapEgressQueuePIRAdaptation,
        tSapEgressQueueAdminPIR,
        tSapEgressQueueAdminCIR,
        tSapEgressQueueLastChanged,
        tSapEgressQueueTableLastChanged,
        tSapEgressFCRowStatus,
        tSapEgressFCQueue,
        tSapEgressFCLastChanged,
        tSapEgressFCTableLastChanged,
        tSapEgressFCDot1PInProfile,
        tSapEgressFCDot1POutProfile,
        tSapEgressFCForceDEValue,
        tSapEgressFCDEMark,
        tSapEgressFCInProfDscp,
        tSapEgressFCOutProfDscp,
        tSapEgressFCInProfPrec,
        tSapEgressFCOutProfPrec,
        tSapEgressQueueUsePortParent,
        tSapEgressQueuePortLvl,
        tSapEgressQueuePortWght,
        tSapEgressQueuePortCIRLvl,
        tSapEgressQueuePortCIRWght,
        tSapEgressQueuePortAvgOverhead,
        tSapEgressQueuePoolName
    }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of SAP egress QoS
         policies on Alcatel 7x50 SR series systems."
    ::= { tmnxQosGroups 26 }

tmnxQosNetworkV6v0Group OBJECT-GROUP
    OBJECTS {
        tNetworkPolicyRowStatus,
        tNetworkPolicyScope,
        tNetworkPolicyDescription,
        tNetworkPolicyIngressDefaultActionFC,
        tNetworkPolicyIngressDefaultActionProfile,
        tNetworkPolicyEgressRemark,
        tNetworkPolicyLastChanged,
        tNetworkPolicyIngressLerUseDscp,
        tNetworkPolicyTableLastChanged,
        tNetworkIngressDSCPRowStatus,
        tNetworkIngressDSCPFC,
        tNetworkIngressDSCPProfile,
        tNetworkIngressDSCPLastChanged,
        tNetworkIngressDSCPTableLastChanged,
        tNetworkIngressDot1pRowStatus,
        tNetworkIngressDot1pFC,
        tNetworkIngressDot1pProfile,
        tNetworkIngressDot1pLastChanged,
        tNetworkIngressDot1pTableLastChanged,
        tNetworkIngressLSPEXPRowStatus,
        tNetworkIngressLSPEXPFC,
        tNetworkIngressLSPEXPProfile,
        tNetworkIngressLSPEXPLastChanged,
        tNetworkIngressLSPEXPTableLastChanged,
        tNetworkEgressFCDSCPInProfile,
        tNetworkEgressFCDSCPOutProfile,
        tNetworkEgressFCLspExpInProfile,
        tNetworkEgressFCLspExpOutProfile,
        tNetworkEgressFCDot1pInProfile,
        tNetworkEgressFCDot1pOutProfile,
        tNetworkEgressFCLastChanged,
        tNetworkEgressFCForceDEValue,
        tNetworkEgressFCDEMark,
        tNetworkEgressFCTableLastChanged,
        tNetworkQueuePolicyRowStatus,
        tNetworkQueuePolicyDescription,
        tNetworkQueuePolicyLastChanged,
        tNetworkQueuePolicyTableLastChanged,
        tNetworkQueueRowStatus,
        tNetworkQueuePoolName,
        tNetworkQueueParent,
        tNetworkQueueLevel,
        tNetworkQueueWeight,
        tNetworkQueueCIRLevel,
        tNetworkQueueCIRWeight,
        tNetworkQueueMCast,
        tNetworkQueueExpedite,
        tNetworkQueueCIR,
        tNetworkQueuePIR,
        tNetworkQueueCBS,
        tNetworkQueueMBS,
        tNetworkQueueHiPrioOnly,
        tNetworkQueueLastChanged,
        tNetworkQueueUsePortParent,
        tNetworkQueuePortLvl,
        tNetworkQueuePortWght,
        tNetworkQueuePortCIRLvl,
        tNetworkQueuePortCIRWght,
        tNetworkQueuePortAvgOverhead,
        tNetworkQueuePIRAdaptation,       
        tNetworkQueueCIRAdaptation,
        tNetworkQueueTableLastChanged,
        tNetworkQueueFCRowStatus,
        tNetworkQueueFC,
        tNetworkQueueFCMCast,
        tNetworkQueueFCLastChanged,
        tNetworkQueueFCTableLastChanged
    }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of Network QoS policies
         on Alcatel 7x50 SR series systems."
    ::= { tmnxQosGroups 27 }

tmnxQosFrameBasedV6v0Group OBJECT-GROUP
    OBJECTS {
        tSchedulerPolicyFrameBasedAccnt
   }
    STATUS      current
    DESCRIPTION
        "The group of objects supporting management of frame-based queue
         accounting on Alcatel 7x50 SR series systems release 6.0."
    ::= { tmnxQosGroups 28 }

tmnxQosObsoletedV6v0Group OBJECT-GROUP
    OBJECTS {
        tSapEgressFCDot1PValue
    }
    STATUS      current
    DESCRIPTION
        "The group of obsolete objects for the QoS on
         Alcatel 7x50 SR series systems."
    ::= { tmnxQosGroups 29 }

tmnxQosNamedPoolPolicyV6v0Group OBJECT-GROUP
    OBJECTS {
        tNamedPoolPolicyTableLastChanged,
        tNamedPoolPolicyRowStatus,
        tNamedPoolPolicyLastChanged,
        tNamedPoolPolicyDescription,
        tNamedPoolPolicyQ1DefaultWeight,
        tNamedPoolPolicyQ1MdaWeight,
        tNamedPoolPolicyQ1PortWeight,
        tQ1NamedPoolTableLastChanged,
        tQ1NamedPoolRowStatus,
        tQ1NamedPoolLastChanged,
        tQ1NamedPoolDescription,
        tQ1NamedPoolNetworkAllocWeight,
        tQ1NamedPoolAccessAllocWeight,
        tQ1NamedPoolSlopePolicy,
        tQ1NamedPoolReservedCbs
    }
    STATUS      current
    DESCRIPTION
        "The group of objects for the named pool policy on
         Alcatel 7x50 SR series systems."
    ::= { tmnxQosGroups 30 }

--
-- tmnxQosNotificationsGroup NOTIFICATION-GROUP
--     NOTIFICATIONS {
--         xxxx
--      }
--     STATUS current
--     DESCRIPTION
--         "the group of notifications providing for xxxx"
--     ::= { tmnxQosGroups X }
--


END
