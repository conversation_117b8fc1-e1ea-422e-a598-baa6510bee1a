-- =================================================================
-- Copyright (C) 2003 by HUAWEI TECHNOLOGIES. All rights reserved.
--
-- Description: Huawei PING extend MIB File(extended from DISMAN-PING-MIB RFC2925) 
-- Reference:   
-- Version:     V1.2
-- History:
--              Lv <PERSON><PERSON><PERSON>, 2003.08, Initial Version
--              Gao Long, 2004-12-16, Added 5 field in hwPingJitterStatsTable
--              Pei <PERSON>, 2005-03-16, added the hwpingUdpServerVPNInstance
-- =================================================================

HUAWEI-DISMAN-PING-MIB DEFINITIONS ::= BEGIN

    IMPORTS
        MODULE-IDENTITY, OBJECT-TYPE, mib-2
            FROM SNMPv2-SMI
        TEXTUAL-CONVENTION, DateAndTime,DisplayString,TimeStamp,Unsigned32,RowStatus
            FROM SNMPv2-TC
        pingCtlEntry,pingCtlOwnerIndex,pingCtlTestName
            FROM DISMAN-PING-MIB
        hwDatacomm
            FROM HUAWEI-MIB;

    hwDismanPing MODULE-IDENTITY
        LAST-UPDATED "200308130000Z"
        ORGANIZATION "Huawei Technologies co.,Ltd."
        CONTACT-INFO 
            "   R&D BeiJing, Huawei Technologies co.,Ltd.
                Huawei Bld.,NO.3 Xinxi Rd., 
                Shang-Di Information Industry Base,
                Hai-Dian District Beijing P.R. China
                Zip:100085 
                Http://www.huawei.com                                       
                E-mail:<EMAIL> 
            "
        DESCRIPTION 
            "This private mib file includes the test information of the device.
            It is supplementary for DISMAN-PING-MIB(rfc2925). By setting more 
            parameters you can finish more types test such as tcp, udp, dlsw, 
            dhcp, ftp, http and jitter .
            This mib contains 5 tables:
                hwPingCtlTable,
                hwpingResultsTable,
                hwPingJitterStatsTable,
                hwPingTcpServerTable,
                hwPingUdpServerTable,
            and 3 nodes:
                hwPingMIBVersion,
                hwPingAgentEnable,
                hwPingServerEnable.
            "
        ::= { hwDatacomm 28 }
                   
                                  
    hwPingObjects OBJECT IDENTIFIER ::= { hwDismanPing 1 }

    -- Textual Conventions

    InetAddress ::= TEXTUAL-CONVENTION
        STATUS       current
        DESCRIPTION
            "Denotes a generic Internet address.
        
             An InetAddress value is always interpreted within the
             context of an InetAddressType value. The InetAddressType
             object which defines the context must be registered
             immediately before the object which uses the InetAddress
             textual convention. In other words, the object identifiers
             for the InetAddressType object and the InetAddress object
             MUST have the same length and the last sub-identifier of
             the InetAddressType object MUST be 1 less than the last
             sub-identifier of the InetAddress object.
        
             When this textual convention is used as the syntax of an
             index object, there may be issues with the limit of 128
             sub-identifiers specified in SMIv2, STD 58. In this case,
             the OBJECT-TYPE declaration MUST include a 'SIZE' clause
             to limit the number of potential instance sub-identifiers."
        SYNTAX      OCTET STRING (SIZE (0..255))


    -- The registration node (point) for huawei ping implementation types
    
    hwPingImplementationTypeDomains  OBJECT IDENTIFIER ::= { hwDismanPing 2 }
     
    hwpingUdpEcho OBJECT-IDENTITY
        STATUS      current
        DESCRIPTION
            "Indicates that an implementation is using the UDP based echo server. 
            Notes: The hwpingCtlTargetPort and hwpingCtlSourcePort need to be set."
        ::= { hwPingImplementationTypeDomains 1 }
    
    hwpingTcpconnect OBJECT-IDENTITY
        STATUS      current
        DESCRIPTION
            "Indicates that an implementation is attempting to
            connect to a TCP port in order to calculate a round
            trip time.
            Notes: The hwpingCtlTargetPort and hwpingCtlSourcePort need to be set."
        ::= { hwPingImplementationTypeDomains 2 }

    hwpingjitter OBJECT-IDENTITY
        STATUS      current
        DESCRIPTION
            "Indicates that an implementation is using udp for packet transfers. 
            'jitter' will cause the PING application to perform delay variance analysis."
        ::= { hwPingImplementationTypeDomains 3 }

    hwpingHttp OBJECT-IDENTITY
        STATUS      current
        DESCRIPTION
            "Indicates that an implementation is using udp for name resolution, 
            tcp connect and tcp data transfer mechanisms for HTTP data download 
            from a particular HTTP Server.
            it cause the PING application to perform a download of the object specified in the URL"
        ::= { hwPingImplementationTypeDomains 4 }


    hwpingdlsw OBJECT-IDENTITY
        STATUS      current
        DESCRIPTION
            "Indicates that an implementation is using tcp for sending keepalives. 
            'dlsw' will cause the PING application to perform a keepalive operation 
            to measure the response time of a DLSw peer."
        ::= { hwPingImplementationTypeDomains 5 }

    hwpingdhcp OBJECT-IDENTITY
        STATUS      current
        DESCRIPTION
            "Indicates that an implementation is using udp for sending dhcp requests. 
             'dhcp' will cause the PING application
             to perform an IP Address lease request/teardown operation."
        ::= { hwPingImplementationTypeDomains 6 }

    hwpingftp OBJECT-IDENTITY
        STATUS      current
        DESCRIPTION
            "Indicates that an implementation is using tcp for connect & data transfer. "
        ::= { hwPingImplementationTypeDomains 7 }


    -- MIB contains three groups      

    hwPingMIBVersion OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "Huawei PING MIB version string.
            The format will be:
            'Version.Release.Patch: Textual-Description'
            For example:  '1.0.0: Initial Ping MIB'"
        ::= { hwPingObjects 1 }    
                                                                       
    hwPingCtlTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF HWPingCtlEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Defines the ping Control Extend Table for providing, via SNMP,
            the capability of performing ping operations at
            a remote host.  "
        ::= { hwPingObjects 2 }

    hwPingCtlEntry OBJECT-TYPE
        SYNTAX      HWPingCtlEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Defines an entry in the hwpingCtlTable. The operation of this 
            table is same as that of pingResultsTable."
        AUGMENTS    { pingCtlEntry }
        ::= { hwPingCtlTable 1 }    

    HWPingCtlEntry ::=
        SEQUENCE {
            hwpingCtlTargetPort             Integer32,
            hwpingCtlSourcePort             Integer32,
            hwpingCtlTTL                    Integer32,     
            hwpingCtlJitterAdminInterval    Integer32,
            hwpingCtlJitterAdminNumPackets  Integer32,
            hwpingCtlHttpOperationType      INTEGER,
            hwpingCtlHttpOperationString    DisplayString,
            hwpingCtlFtpOperationType       INTEGER,
            hwpingCtlFtpUsername            DisplayString,
            hwpingCtlFtpPassword            DisplayString,
            hwpingCtlFtpOperationString     DisplayString,
            hwpingCtlVPNInstance            DisplayString
            }
    
    hwpingCtlTargetPort          OBJECT-TYPE
        SYNTAX      Integer32 (0..65535)
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "This object represents the target's port number. This
            object is applicable to udpEcho, tcpConnect and jitter probes."
        DEFVAL      { 0 }
        ::= { hwPingCtlEntry 1 }

    hwpingCtlSourcePort          OBJECT-TYPE     
        SYNTAX      Integer32 (0..65535)
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "This object represents the source's port number. If this 
            object is not specified, the application will get a 
            port allocated by the system. This object is applicable 
            to all probes except dns, dlsw."
        DEFVAL      { 0 }
        ::= { hwPingCtlEntry 2 }        
    
    hwpingCtlTTL OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "Specifies the value to store in the TTL Field in the IP packet used to
            encapsulate the ping probe."
        DEFVAL      { 20 }
        ::= { hwPingCtlEntry 3 }

    hwpingCtlJitterAdminInterval OBJECT-TYPE
        SYNTAX      Integer32 
        UNITS       "milliseconds"
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "This value represents the inter-packet delay between packets
            and is in milliseconds. This value is currently used for 
            Jitter probe. This object is applicable to jitter probe only."
        DEFVAL      { 20 }
        ::= { hwPingCtlEntry 4 }

    hwpingCtlJitterAdminNumPackets OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "This value represents the number of packets that need to be 
            transmitted. This value is currently used for Jitter probe. 
            This object is applicable to jitter probe only."
        DEFVAL      { 10 }
        ::= { hwPingCtlEntry 5 }  

    hwpingCtlHttpOperationType OBJECT-TYPE
        SYNTAX      INTEGER{
            get(1),
            post(2)
            }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "Http operation type:get or post."
        DEFVAL      { get }
        ::= { hwPingCtlEntry 6 }  

    hwpingCtlHttpOperationString OBJECT-TYPE
        SYNTAX      DisplayString(SIZE(0..240))
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "Http operation string.for example,if you choose get operation type,the string will be
            url , home page and http version."
        DEFVAL      { "" }
        ::= { hwPingCtlEntry 7 }

    hwpingCtlFtpOperationType OBJECT-TYPE
        SYNTAX      INTEGER{
            get(1),
            put(2)
            }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "Ftp operation type:get or put"
        DEFVAL      { get }
        ::= { hwPingCtlEntry 8 }  

    hwpingCtlFtpUsername OBJECT-TYPE
        SYNTAX      DisplayString(SIZE(0..32))
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "Ftp username"
        DEFVAL      { "" }
        ::= { hwPingCtlEntry 9 }

    hwpingCtlFtpPassword OBJECT-TYPE
        SYNTAX      DisplayString(SIZE(0..32))
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "Ftp password."
        DEFVAL      { "" }
        ::= { hwPingCtlEntry 10 }  

    hwpingCtlFtpOperationString OBJECT-TYPE
        SYNTAX      DisplayString(SIZE(0..240))
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "Ftp operation string.If the ftp operation type is get,the string will be file name 
            that you will get from ftp server."
        DEFVAL      { "" }
        ::= { hwPingCtlEntry 11 }   

    hwpingCtlVPNInstance OBJECT-TYPE
        SYNTAX      DisplayString(SIZE(0..31))
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "This field is used to specify the VPN name in which the PING operation will be userd.
            The agent will use the field to identify the VPN routing Table for this operation."
        DEFVAL      { "" }
        ::= { hwPingCtlEntry 12 }


    -- HUAWEI Ping Results Table

    hwpingResultsTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF HWPingResultsEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Defines the Ping Results Extend Table for providing
            the capability of performing ping operations at
            a remote host.  The results of these operations are
            stored in the hwpingResultsTable . The operation of this 
            table is same as that of pingResultsTable."
        ::= { hwPingObjects 3 }

    hwpingResultsEntry OBJECT-TYPE
        SYNTAX      HWPingResultsEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "The operation of this table is same as that of pingResultsTable."
        INDEX {
            pingCtlOwnerIndex,
            pingCtlTestName
            }
        ::= { hwpingResultsTable 1 }

    HWPingResultsEntry ::=
        SEQUENCE {
            hwpingResultsRttNumDisconnects   Gauge32,
            hwpingResultsRttTimeouts         Gauge32,
            hwpingResultsRttBusies           Gauge32,
            hwpingResultsRttNoConnections    Gauge32,
            hwpingResultsRttDrops            Gauge32,
            hwpingResultsRttSequenceErrors   Gauge32,
            hwpingResultsRttStatsErrors         Gauge32,
            hwpingResultsMaxDelaySD          Gauge32,
            hwpingResultsMaxDelayDS          Gauge32,
            hwpingResultsLostPacketRatio     Gauge32
            }

    hwpingResultsRttNumDisconnects OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "When the pingCtlType is 'icmpecho' , this 
            object represents the number of times that the target or 
            hop along the path to a target became disconnected.  For
            all other values of pingCtlType, this object will
            remain zero.
            
            For connectionless protocols this has no meaning,
            and will consequently remain 0.  
            Since this error does not indicate any information about
            the failure of an PING operation, no response time 
            information for this instance will be recorded in the 
            appropriate objects."
        ::= { hwpingResultsEntry 1 }

    hwpingResultsRttTimeouts OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of occasions when a PING operation was not
            completed before a timeout occurred, i.e.
            pingCtlTimeOut was exceeded.
            
            Since the PING operation was never completed, the 
            completion time of these operations are not accumulated."
        ::= { hwpingResultsEntry 2 }

    hwpingResultsRttBusies OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of occasions when a PING operation could not
            be initiated because a previous PING operation has not 
            been completed.
            
            When the pingCtlType is 'pathEcho' this can occur for
            both connection oriented protocols and connectionless
            protocols.
            
            When the initiation of a new operation cannot be started,
            this object will be incremented and the operation will be
            omitted.  (The next operation will start at the next 
            Frequency).  "
        ::= { hwpingResultsEntry 3 }

    hwpingResultsRttNoConnections OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "When the pingCtlType is 'icmpecho'  this is
            the number of occasions when a PING operation could not be
            initiated because the connection to the target has not 
            been established.  For all other pingCtlTypes this
            object will remain zero."
        ::= { hwpingResultsEntry 4 }

    hwpingResultsRttDrops OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of occasions when a PING operation could not 
            be initiated because some necessary internal resource 
            (for example memory) was not available,or the operation 
            completion could not be recognized."
        ::= { hwpingResultsEntry 5 }

    hwpingResultsRttSequenceErrors OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "When the pingCtlType is 'icmpecho' this is 
            the number of PING operation completions received with 
            an unexpected sequence identifier.  For all other values
            of pingCtlType this object will remain zero.
            
            When this has occurred some of the possible reasons may
            be:  
              - a duplicate packet was received
              - a response was received after it had timed-out
              - a corrupted packet was received and was not detected
            
            "
        ::= { hwpingResultsEntry 6 }

    hwpingResultsRttStatsErrors OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of some other errors" 
        ::= { hwpingResultsEntry 7 }

    hwpingResultsMaxDelaySD OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "milliseconds"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            " The maximal delay from source device to destination device. 
            Currently this value is only used for JITTER test, and this 
            value must be 0 in other type test. In JITTER test, if 
            destination system time is not valid, the SD delay may be set 0."
        ::= { hwpingResultsEntry 8 }
    
    hwpingResultsMaxDelayDS OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "milliseconds"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            " The maximal delay from destination device to source device. 
            Currently this value is only used for JITTER test, and this 
            value is 0 in other type test. In JITTER test, if destination
            system time is not valid, the DS delay may be set 0."
        ::= { hwpingResultsEntry 9 }
        
    hwpingResultsLostPacketRatio OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "milliseconds"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The value of this object reflects the drop rate of HWPing test.
             For example, this value is 30 that means source device only 
             had received 70% packets of total sent packets."
        ::= { hwpingResultsEntry 10 }

    -- =================================================================
    --    HUAWEI Jitter Statistics Table
    -- =================================================================

    hwPingJitterStatsTable OBJECT-TYPE
        SYNTAX SEQUENCE OF HWPingJitterStatsEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A table which contains the status of latest Jitter 
            operation."
        ::= { hwPingObjects 4 }

    hwPingJitterStatsEntry OBJECT-TYPE
        SYNTAX      HWPingJitterStatsEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A table which contains the status of latest Jitter 
            operation.
            
            This entry is created only if the pingCtlType 
            is hwpingjitter. "
           
        INDEX { 
            pingCtlOwnerIndex,
            pingCtlTestName      
            }
        ::= { hwPingJitterStatsTable 1 }

    HWPingJitterStatsEntry  ::= SEQUENCE
        {
        hwPingJitterStatsNumOfRTT                Counter32,
        hwPingJitterStatsMinOfPositivesSD        Gauge32,
        hwPingJitterStatsMaxOfPositivesSD        Gauge32,
        hwPingJitterStatsNumOfPositivesSD        Gauge32,
        hwPingJitterStatsSumOfPositivesSD        Gauge32,
        hwPingJitterStatsSum2PositivesSD         Gauge32,
        hwPingJitterStatsMinOfNegativesSD        Gauge32,
        hwPingJitterStatsMaxOfNegativesSD        Gauge32,
        hwPingJitterStatsNumOfNegativesSD        Gauge32,
        hwPingJitterStatsSumOfNegativesSD        Gauge32,
        hwPingJitterStatsSum2NegativesSD         Gauge32,
        hwPingJitterStatsMinOfPositivesDS        Gauge32,
        hwPingJitterStatsMaxOfPositivesDS        Gauge32,
        hwPingJitterStatsNumOfPositivesDS        Gauge32,
        hwPingJitterStatsSumOfPositivesDS        Gauge32,
        hwPingJitterStatsSum2PositivesDS         Gauge32,
        hwPingJitterStatsMinOfNegativesDS        Gauge32,
        hwPingJitterStatsMaxOfNegativesDS        Gauge32,
        hwPingJitterStatsNumOfNegativesDS        Gauge32,
        hwPingJitterStatsSumOfNegativesDS        Gauge32,
        hwPingJitterStatsSum2NegativesDS         Gauge32,
        hwPingJitterStatsPacketLossSD            Gauge32,
        hwPingJitterStatsPacketLossDS            Gauge32,
        hwPingJitterStatsAvePositivesSD          Gauge32,
        hwPingJitterStatsAveNegativesSD          Gauge32,
        hwPingJitterStatsAvePositivesDS          Gauge32,
        hwPingJitterStatsAveNegativesDS          Gauge32,
        hwPingJitterStatsPktLossUnknown          Gauge32
        }

    hwPingJitterStatsNumOfRTT OBJECT-TYPE
        SYNTAX      Counter32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of Jitter's that are successfully measured."
        ::= { hwPingJitterStatsEntry 1 }

    hwPingJitterStatsMinOfPositivesSD OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The minimum of all positive jitter values from packets sent
            from source to destination."
        ::= { hwPingJitterStatsEntry 2 }

    hwPingJitterStatsMaxOfPositivesSD OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The maximum of all positive jitter values from packets sent
            from source to destination."
        ::= { hwPingJitterStatsEntry 3 }

    hwPingJitterStatsNumOfPositivesSD OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The sum of number of all positive jitter values from packets
            sent from source to destination."
        ::= { hwPingJitterStatsEntry 4 }

    hwPingJitterStatsSumOfPositivesSD OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The sum of RTT's of all positive jitter values from packets
            sent from source to destination."
        ::= { hwPingJitterStatsEntry 5 }

    hwPingJitterStatsSum2PositivesSD OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The sum of square of RTT's of all positive jitter values from
            packets sent from source to destination."
        ::= { hwPingJitterStatsEntry 6 }

    hwPingJitterStatsMinOfNegativesSD OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The minimum of absolute values of all negative jitter values 
            from packets sent from source to destination."
        ::= { hwPingJitterStatsEntry 7 }

    hwPingJitterStatsMaxOfNegativesSD OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The maximum of absolute values of all negative jitter values 
            from packets sent from source to destination."
        ::= { hwPingJitterStatsEntry 8 }

    hwPingJitterStatsNumOfNegativesSD OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The sum of number of all negative jitter values from packets
            sent from source to destination."
        ::= { hwPingJitterStatsEntry 9 }

    hwPingJitterStatsSumOfNegativesSD OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The sum of all negative jitter values from packets
            sent from source to destination."
        ::= { hwPingJitterStatsEntry 10 }

    hwPingJitterStatsSum2NegativesSD OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The sum of square of RTT's of all negative jitter values from
            packets sent from source to destination."
        ::= { hwPingJitterStatsEntry 11 }

    hwPingJitterStatsMinOfPositivesDS OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The minimum of all positive jitter values from packets sent
            from destination to source."
        ::= { hwPingJitterStatsEntry 12 }

    hwPingJitterStatsMaxOfPositivesDS OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The maximum of all positive jitter values from packets sent
            from destination to source."
        ::= { hwPingJitterStatsEntry 13 }

    hwPingJitterStatsNumOfPositivesDS OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The sum of number of all positive jitter values from packets
            sent from destination to source."
        ::= { hwPingJitterStatsEntry 14 }

    hwPingJitterStatsSumOfPositivesDS OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The sum of RTT's of all positive jitter values from packets
            sent from destination to source."
        ::= { hwPingJitterStatsEntry 15 }

    hwPingJitterStatsSum2PositivesDS OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The sum of squares of RTT's of all positive jitter values from
            packets sent from destination to source."
        ::= { hwPingJitterStatsEntry 16 }

    hwPingJitterStatsMinOfNegativesDS OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The minimum of all negative jitter values from packets sent
            from destination to source."
        ::= { hwPingJitterStatsEntry 17 }

    hwPingJitterStatsMaxOfNegativesDS OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The maximum of all negative jitter values from packets sent
            from destination to source."
        ::= { hwPingJitterStatsEntry 18 }

    hwPingJitterStatsNumOfNegativesDS OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The sum of number of all negative jitter values from packets
            sent from destination to source."
        ::= { hwPingJitterStatsEntry 19 }

    hwPingJitterStatsSumOfNegativesDS OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The sum of RTT's of all negative jitter values from packets
            sent from destination to source."
        ::= { hwPingJitterStatsEntry 20 }

    hwPingJitterStatsSum2NegativesDS OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The sum of squares of RTT's of all negative jitter values from
            packets sent from destination to source."
        ::= { hwPingJitterStatsEntry 21 }

    hwPingJitterStatsPacketLossSD OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of packets lost when sent from source to destination."
        ::= { hwPingJitterStatsEntry 22 }

    hwPingJitterStatsPacketLossDS OBJECT-TYPE
        SYNTAX      Gauge32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The number of packets lost when sent from destination to source."
        ::= { hwPingJitterStatsEntry 23 }

    hwPingJitterStatsAvePositivesSD OBJECT-TYPE
      SYNTAX      Gauge32
      MAX-ACCESS  read-only
      STATUS      current
      DESCRIPTION
           "The average of RTT's of all positive jitter values from packets
           sent from source to destination."
      ::= { hwPingJitterStatsEntry 24 }
    
    hwPingJitterStatsAveNegativesSD OBJECT-TYPE
      SYNTAX      Gauge32
      MAX-ACCESS  read-only
      STATUS      current
      DESCRIPTION
           "The average of all negative jitter values from packets
           sent from source to destination."
      ::= { hwPingJitterStatsEntry 25 }
      
    hwPingJitterStatsAvePositivesDS OBJECT-TYPE
      SYNTAX      Gauge32
      MAX-ACCESS  read-only
      STATUS      current
      DESCRIPTION
           "The average of RTT's of all positive jitter values from packets
           sent from destination to source."
      ::= { hwPingJitterStatsEntry 26 }
      
    hwPingJitterStatsAveNegativesDS OBJECT-TYPE
      SYNTAX      Gauge32
      MAX-ACCESS  read-only
      STATUS      current
      DESCRIPTION
           "The average of RTT's of all negative jitter values from packets
           sent from destination to source."
      ::= { hwPingJitterStatsEntry 27 }
    
    hwPingJitterStatsPktLossUnknown OBJECT-TYPE
        SYNTAX      Gauge32
        UNITS       "milliseconds"
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
             " The number of packets which lost but not knowing result ."
        ::= { hwPingJitterStatsEntry 28 }
      
    hwPingAgentEnable OBJECT-TYPE
        SYNTAX      INTEGER{
            enable(1),
            disable(2) 
            }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "Enable Huawei PING agent function"
        DEFVAL { disable }     
        ::= { hwPingObjects 5 }   
  
    -- =================================================================
    --    HUAWEI tcp server configuration information Statistics Table
    -- =================================================================

    hwPingTcpServerTable OBJECT-TYPE
        SYNTAX SEQUENCE OF HWPingTcpServerEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A table which contains the status of hwping tcp server 
            configuration information."
        ::= { hwPingObjects 6 }

    hwPingTcpServerEntry OBJECT-TYPE
        SYNTAX      HWPingTcpServerEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A table which contains the status of hwping tcp server 
            configuration information.
            
            This entry is includeed not only the tcp server ip address 
            but also the tcp server udp port. If used for VPN it is also 
            included the VPN name."
        INDEX { 
            hwPingTcpServerIpAddress,
            hwPingTcpServerPort,
            hwpingTcpServerVPNInstance    
            }
        ::= { hwPingTcpServerTable 1 }

    HWPingTcpServerEntry  ::= SEQUENCE
        {
        hwPingTcpServerIpAddress                InetAddress,
        hwPingTcpServerPort                     Integer32,
        hwpingTcpServerVPNInstance              DisplayString,
        hwpingTcpServerRowStatus                RowStatus
        }

    hwPingTcpServerIpAddress          OBJECT-TYPE     
        SYNTAX      InetAddress
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "This object represents the tcp server's ip address. 
            This object and hwpingTcpServerPort is hwPingTcpServerTable
            index."
        DEFVAL { ''H }
        ::= { hwPingTcpServerEntry 1 }        

    hwPingTcpServerPort          OBJECT-TYPE     
        SYNTAX      Integer32 (0..65535)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "This object represents the tcp server's port number. 
            This object and hwPingTcpServerIpAddress is hwPingTcpServerTable
            index."
        DEFVAL      { 0 }
        ::= { hwPingTcpServerEntry 2 }   

    hwpingTcpServerVPNInstance          OBJECT-TYPE
        SYNTAX      DisplayString(SIZE(1..31))
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "This field is used to specify the VPN name in which the PING operation will be userd.
            The TCP server will use the field to identify the VPN routing Table for this operation."
        DEFVAL      { "" }
        ::= { hwPingTcpServerEntry 3 }

    hwpingTcpServerRowStatus OBJECT-TYPE
        SYNTAX      RowStatus
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "This object allows entries to be created and deleted
            in the hwPingTcpServerTable. 
            
            A value MUST be specified for hwPingTcpServerIpAddress
            and hwPingTcpServerPort prior to a transition to 
            active(1) state being accepted."
            
        REFERENCE
            "See definition of RowStatus in RFC 2579, 'Textual
            Conventions for SMIv2.'"
        ::= { hwPingTcpServerEntry 4 }   

    hwPingUdpServerTable OBJECT-TYPE
        SYNTAX SEQUENCE OF HWPingUdpServerEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A table which contains the status of hwping udp server 
            configuration information."
        ::= { hwPingObjects 7 }

    hwPingUdpServerEntry OBJECT-TYPE
        SYNTAX      HWPingUdpServerEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A table which contains the status of hwping udp server 
            configuration information.
           
            This entry is includeed not only the udp server ip address 
            but also the udp server udp port.If used for VPN it is also 
            included the VPN name."
           
        INDEX { 
            hwPingUdpServerIpAddress,
            hwPingUdpServerPort, 
            hwpingUdpServerVPNInstance  
            }
        ::= { hwPingUdpServerTable 1 }

    HWPingUdpServerEntry  ::= SEQUENCE
        {
        hwPingUdpServerIpAddress                InetAddress,
        hwPingUdpServerPort                     Integer32,
        hwpingUdpServerVPNInstance              DisplayString,
        hwpingUdpServerRowStatus                RowStatus
        }

    hwPingUdpServerIpAddress          OBJECT-TYPE     
        SYNTAX      InetAddress
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "This object represents the udp server's ip address. 
            This object and hwpingUdpServerPort is hwPingUdpServerEntry
            index."
        DEFVAL { ''H }
        ::= { hwPingUdpServerEntry 1 }        

    hwPingUdpServerPort          OBJECT-TYPE     
        SYNTAX      Integer32 (0..65535)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "This object represents the udp server's port number. 
            This object and hwPingUdpServerIpAddress is hwPingUdpServerEntry
            index."
        DEFVAL      { 0 }
        ::= { hwPingUdpServerEntry 2 }  

    hwpingUdpServerVPNInstance          OBJECT-TYPE
        SYNTAX      DisplayString(SIZE(1..31))
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "This field is used to specify the VPN name in which the PING operation will be userd.
            The UDP server will use the field to identify the VPN routing Table for this operation."
        DEFVAL      { "" }
        ::= { hwPingUdpServerEntry 3 }

    hwpingUdpServerRowStatus OBJECT-TYPE
        SYNTAX      RowStatus
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "This object allows entries to be created and deleted
            in the hwPingUdpServerTable. 
            
            A value MUST be specified for hwPingUdpServerIpAddress
            and hwPingUdpServerPort prior to a transition to 
            active(1) state being accepted."
            
        REFERENCE
            "See definition of RowStatus in RFC 2579, 'Textual
            Conventions for SMIv2.'"
        ::= { hwPingUdpServerEntry 4 }   

    hwPingServerEnable OBJECT-TYPE
        SYNTAX      INTEGER{
            enable(1),
            disable(2) 
            }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
            "Enable Huawei PING server function"
        DEFVAL { disable }     
        ::= { hwPingObjects 8 }   

END
