-- This file was included in Ciena MIB release MIBS-CIENA-CES-08-07-00-024
 --
 -- CIENA-CES-CHASSIS-MIB.my
 --

 CIENA-CES-CHASSIS-MIB DEFINITIONS ::= BEGIN

 IMPORTS
   Integer32, Unsigned32, NOTIFICATION-TYPE, OBJECT-TYPE, MODULE-IDENTITY
       FROM SNMPv2-SMI
   TruthValue, DisplayString, <PERSON><PERSON><PERSON><PERSON>, DateAndTime
       FROM SNMPv2-TC
   OBJECT-GROUP, NOTIFICATION-GROUP
       FROM SNMPv2-CONF
    cienaGlobalSeverity
       FROM CIENA-GLOBAL-MIB
   cienaCesNotifications, cienaCesConfig
       FROM CIENA-SMI
   TceHealthStatus, TceHealthCategory
       FROM CIENA-CES-MODULE-MIB;

 cienaCesChassisMIB MODULE-IDENTITY
              LAST-UPDATED "201810170000Z"
              ORGANIZATION "Ciena Corp."
              CONTACT-INFO
              "   Mib Meister
                  7035 Ridge Road
                  Hanover, Maryland 21076
                  USA
                  Phone:  +1 ************
                  Email:  <EMAIL>"
              DESCRIPTION
                       "This module defines the chassis configuration objects and also the objects required for
                       any chassis related notifications."

              REVISION "201810170000Z"
              DESCRIPTION "Augmented cienaCesChassisPowerSupplyType with following enumeration: unknown(99)."
                           
              REVISION "201801310000Z"
              DESCRIPTION "Added cienaCesChassisMefSourceMacAddress."

              REVISION "201706070000Z"
              DESCRIPTION "Updated contact info."

              REVISION "201705310000Z"
              DESCRIPTION "Corrected chassisIomNotifGroup macro from OBJECT-GROUP to NOTIFICATION-GROUP."

              REVISION "201703240000Z"
              DESCRIPTION "Added aisSession to cienaCesChassisResourceHealthSubCategory."

              REVISION "201702090000Z"
              DESCRIPTION "Added the cienaCesChassisDyingGaspNotification"

              REVISION "201610200000Z"
              DESCRIPTION "Added cienaCesChassisRedundancyGroupsMax and cienaCesChassisLinksPerRedundancyGroupMax to
                           cienaCesChassisPlatform."

              REVISION "201609200000Z"
              DESCRIPTION "Added cienaCesChassisIOMSerialNumber to cienaCesChassisIOM.
                           Added cienaCesChassisFanTraySerialNumber to cienaCesChassisFanTrayEntry.
                           Augmented cienaCesChassisIOMStateChangeNotification, cienaCesChassisIOMBuzzerEnableChangeNotification,
                            cienaCesChassisIOMBuzzerStateChangeNotification, cienaCesChassisIOMAlarmOutputStateChangeNotification, 
                            cienaCesChassisIOMAlarmInputStateChangeNotification with cienaCesChassisIOMSerialNumber.
                           Augmented cienaCesChassisFanTrayRemoveNotification, cienaCesChassisFanTrayInsertNotification, 
                            cienaCesChassisFanTrayStatusFaultedNotification, cienaCesChassisFanTrayStatusOkNotification
                            with cienaCesChassisFanTraySerialNumber."

              REVISION "201603280000Z"
              DESCRIPTION "Changed the description for cienaCesChassisRebootNotification to correctly reflect behavior."

              REVISION "201603160000Z"
              DESCRIPTION "Added cienaCesChassisIOMname, cienaCesChassisIOMChassisIndx, cienaCesChassisIOMShelfIndex,
                            and cienaCesChassisIOMSlotIndx to cienaCesChassisIOM. 
                           Added cienaCesChassisPowerSupplyChassisIndx, cienaCesChassisPowerSupplyShelfIndx and 
                            cienaCesChassisPowerSupplySlotIndx to cienaCesChassisPowerSupplyEntry.
                           Added cienaCesChassisFanName, cienaCesChassisFanChassisIndx and cienaCesChassisFanShelfIndx,
                            to cienaCesChassisFanEntry.
                           Added cienaCesChassisFanTrayName, cienaCesChassisFanTrayChassisIndx, cienaCesChassisFanTrayShelfIndx,
                            and cienaCesChassisFanTraySlotIndx to cienaCesChassisFanTrayEntry.
                           Added cienaCesChassisFanTempName, cienaCesChassisFanTempChassisIndx and cienaCesChassisFanTempShelfIndx,
                            to cienaCesChassisFanTempEntry.
                           Augmented cienaCesChassisPowerSupplyFaultedNotification, cienaCesChassisPowerSupplyOnlineNotification
                            with cienaCesChassisPowerSupplySlotName, cienaCesChassisPowerSupplyChassisIndx, cienaCesChassisPowerSupplyShelfIndx
                            and cienaCesChassisPowerSupplySlotIndx objects.
                           Augmented cienaCesChassisFanHiTempNotification, cienaCesChassisFanNormalTempNotification,
                            cienaCesChassisFanLoTempNotification with cienaCesChassisFanTempName, cienaCesChassisFanTempChassisIndx
                            and cienaCesChassisFanTempShelfIndx objects.
                           Augmented cienaCesChassisFanSpeedMinThresholdNotification, cienaCesChassisFanSpeedNormalRangeNotification
                            with cienaCesChassisFanName, cienaCesChassisFanChassisIndx and cienaCesChassisFanShelfIndx objects.
                           Augmented cienaCesChassisFanTrayRemoveNotification, cienaCesChassisFanTrayInsertNotification, 
                            cienaCesChassisFanTrayStatusFaultedNotification, cienaCesChassisFanTrayStatusOkNotification
                            with cienaCesChassisFanTrayName, cienaCesChassisFanTrayChassisIndx, cienaCesChassisFanTrayShelfIndx,
                            and cienaCesChassisFanTraySlotIndx objects.
                           Augmented cienaCesChassisIOMStateChangeNotification, cienaCesChassisIOMBuzzerEnableChangeNotification,
                            cienaCesChassisIOMBuzzerStateChangeNotification, cienaCesChassisIOMAlarmOutputStateChangeNotification, 
                            cienaCesChassisIOMAlarmInputStateChangeNotification with cienaCesChassisIOMname, cienaCesChassisIOMChassisIndx,
                            cienaCesChassisIOMShelfIndex and cienaCesChassisIOMSlotIndx objects."

              REVISION "201507060000Z"
              DESCRIPTION "Added cienaCesChassisRestart to cienaCesChassisGlobal.
                           Added cienaCesChassisPowerSupplyRevInfo to cienaCesChassisPowerSupplyEntry."

              REVISION "201506020000Z"
              DESCRIPTION "Added cienaCesChassisFileDescriptorHealthTable at cienaCesChassisHealth 40.
                           Added cienaCesChassisProcessHealthTable at cienaCesChassisHealth 41.
                           Added cienaCesChassisThreadHealthTable at cienaCesChassisHealth 42."

              REVISION "201505070000Z"
              DESCRIPTION "Added cienaCesChassisAlarmCutoffNotification at cienaCesChassisMIBNotifications 27.
                           Added cienaCesChassisAlarmCutoffOrigin at cienaCesChassisGlobal 12."

              REVISION "201503020000Z"
              DESCRIPTION "Added cienaCesChassisPowerSupplyFRU to the cienaCesChassisPowerSupplyEntry.
                           Added cienaCesChassisPowerSupplySlotName to the cienaCesChassisPowerSupplyEntry.
                           Clarified the DESCRIPTION clause of cienaCesChassisPowerSupplyManufacturer."

              REVISION "201502250000Z"
              DESCRIPTION "Added cienaCesChassisModemTempHealthTable at cienaCesChassisHealth 38 and all encompassed objects.
                           Added cienaCesChassisModemWatermarkHealthTable at cienaCesChassisHealth 39 and all encompassed objects."

              REVISION "201411100000Z"
              DESCRIPTION "Changed Power Supply Index from 1..2 to 1..8."

              REVISION "201411010000Z"
              DESCRIPTION "Removed references to 5410 platform, fixed miscellaneous spelling."

              REVISION "201402250000Z"
              DESCRIPTION "Added cienaCesChassisAirFilterServiceNotification at cienaCesChassisMIBNotifications 26."

              REVISION "201401230000Z"
              DESCRIPTION "Added cienaCesChassisPowerParamsHealthTable at cienaCesChassisHealth 35.
                           Added cienaCesChassisPowerOutputVoltageHealthTable at cienaCesChassisHealth 36.
                           Added cienaCesChassisUsbFlashEnabledNotification at cienaCesChassisMIBNotifications 24.
                           Added cienaCesChassisUsbFlashDisabledNotification at cienaCesChassisMIBNotifications 25."

              REVISION "201312180000Z"
              DESCRIPTION "Defined cienaCesChassisIOM at cienaCesChassis 6 and all encompassing objects.
                           Defined cienaCesChassisIOMStateChangeNotification at cienaCesChassisMIBNotifications 19.
                           Defined cienaCesChassisIOMBuzzerEnableChangeNotification at cienaCesChassisMIBNotifications 20.
                           Defined cienaCesChassisIOMBuzzerStateChangeNotification at cienaCesChassisMIBNotifications 21.
                           Defined cienaCesChassisIOMAlarmOutputStateChangeNotification at cienaCesChassisMIBNotifications 22.
                           Defined cienaCesChassisIOMAlarmInputStateChangeNotification at cienaCesChassisMIBNotifications 23.
                           Defined chassisIomStateGroup at cienaCesChassisMIBGroups 9.
                           Defined chassisIomNotifGroup at cienaCesChassisMIBGroups 10.
                           Added TruthValue to IMPORTS clause."

              REVISION "201312050000Z"
              DESCRIPTION "Revised DESCRIPTION clause of cienaCesChassisHealthSubCategory to reflect current MIB object definitions.
                           Changed MAX-ACCESS clause of the following table index objects from read-only to not-accessible:
                            cienaCesChassisCPUHealthSubCategory, cienaCesChassisDatapathHealthSubCategory, cienaCesChassisControlPlaneHealthSubCategory,
                            cienaCesChassisFabricHealthSubCategory, cienaCesChassisSMHealthSubCategory, cienaCesChassisSMTempHealthSubCategory,
                            cienaCesChassisSMSamplesHealthSubCategory, cienaCesChassisDiskHealthSubCategory, cienaCesChassisModuleTempHealthSubCategory,
                            cienaCesChassisModuleSamplesHealthSubCategory, cienaCesChassisFanTrayHealthSubCategory, cienaCesChassisFanTraySpeedMismatchHealthSubCategory,
                            cienaCesChassisFanSpeedMismatchHealthSubCategory, cienaCesChassisFanTempHealthSubCategory, cienaCesChassisFanSamplesHealthSubCategory,
                            cienaCesChassisFanRPMHealthSubCategory, cienaCesChassisPowerHealthSubCategory, cienaCesChassisFeedPowerHealthSubCategory,
                            cienaCesChassisResourceHealthSubCategory, cienaCesChassisMemoryHealthSubCategory, cienaCesChassisMACHealthSubCategory,
                            cienaCesChassisI2CHealthSubCategory, cienaCesChassisFlashDriverHealthSubCategory, cienaCesChassisXcvrHealthSubCategory,
                            cienaCesChassisPortLinkHealthSubCategory, cienaCesChassisIOMStatusHealthSubCategory.
                           Augmented cienaCesChassisDatapathHealthSubCategory with the following enumerations: dataPlane(2), system(3), controlPlane(4).
                           Corrected cienaCesChassisFanRPMHealthSubCategory enumeration from: minSpeed(1) to maxSpeed(1) and minSpeed(2).
                           Corrected cienaCesChassisFanRPMHealthMinMeasurement and cienaCesChassisFanRPMHealthMinThreshold DESCRIPTION clauses.
                           Corrected cienaCesChassisResourceHealthSubCategory enumeration pltfLocalBridgeMacs(91) to pltfmLocalBridgeMacs(91)
                           Augmented cienaCesChassisResourceHealthSubCategory with the following enumerations:
                            pltfmPpRif(92), pltfmLmPowerBudget(93), pltfmPpIngressL2Xform(94), pltfmPpEgressL2xform(95), pltfmPpInternalTcam(96),
                            pltfmPpFECPointer(97), ethLpTable(98), pltfmPpFECPointerVRing(99), l2CftProfile(100), pfgProfile(101), pltfmNpMaintPoint(102),
                            pltfmNpMaintPointSession(103), pltfmNpFastTimer300Hz(104), pltfmNpFastTimer10msec(105), pltfmNpFastTimer100msec(106),
                            pltfmNpFastTimer1sec(107), pltfmNpSlowTimer(108), pltfmNpWatchdogTimer(109), pltfmNpProtectionGroup(110), benchmarkReflectorProfile(111).
                           Augmented cienaCesChassisI2CHealthSubCategory with the following enumerations: pduA1(10), pduB1(11), pduA2(12), pduB2(13), pduA3(14), pduB3(15),
                            pduA4(16), pduB4(17), cfu1(18), cfu2(19), cfu3(20), cfu4(21), pslm1(22), pslm2(23), pslm3(24), pslm4(25), pslm5(26), pslm6(27), pslm7(28),
                            pslm8(29), pslm9(30), pslm10(31), pslm11(32), pslm12(33), pslm13(34), pslm14(35), pslm15(36), pslm16(37), pslm17(38), pslm18(39), pslm19(40),
                            pslm20(41), sm1(42), sm2(43), sm3(44), sm4(45), sm5(46), io(47).
                           Augmented cienaCesChassisXcvrHealthSubCategory with the following enumerations: bias(4), vcc(5), rxPowerLane1(6), rxPowerLane2(7), rxPowerLane3(8),
                            rxPowerLane4(9), rxPowerLane5(10), rxPowerLane6(11), rxPowerLane7(12), rxPowerLane8(13), rxPowerLane9(14), rxPowerLane10(15), rxPowerLane11(16),
                            rxPowerLane12(17), rxPowerLane13(18), rxPowerLane14(19), rxPowerLane15(20), rxPowerLane16(21), txPowerLane1(22), txPowerLane2(23), txPowerLane3(24),
                            txPowerLane4(25), txPowerLane5(26), txPowerLane6(27), txPowerLane7(28), txPowerLane8(29), txPowerLane9(30), txPowerLane10(31), txPowerLane11(32),
                            txPowerLane12(33), txPowerLane13(34), txPowerLane14(35), txPowerLane15(36), txPowerLane16(37).
                           Augmented cienaCesChassisMemoryHealthSubcategory with the following enumeration: heap(6).
                           Corrected cienaCesChassisIOMStatusHealthSubCategory enumeration portLink-State(1) changed to state(1).
                           Added cienaCesChassisSMFabricHealthTable at cienaCesChassisHealth 32.
                           Added cienaCesChassisSPIHealthTable at cienaCesChassisHealth 33.
                           Added cienaCesChassisUsbFlashHealthTable at cienaCesChassisHealth 34.
                           Added cienaCesChassisIomTempHealthTable at cienaCesChassisHealth 35."

              REVISION "201303280000Z"
              DESCRIPTION
                       "Added cienaCesChassisIDP objects."

              REVISION "201303070000Z"
              DESCRIPTION
                       "Changed cienaCesChassisHealthStatusNormalNotification to cienaCesChassisHealthStatusGoodNotification."

              REVISION "201302060000Z"
              DESCRIPTION
                       "Added sensor26 and sensor27 to CienaCesChassisModuleTempHealthEntry, and sensor26Invalid and
                       sensor27Invalid to CienaCesChassisModuleSamplesHealthEntry for PSLM400 support."

              REVISION "201206280000Z"
              DESCRIPTION
                       "Added pltfmLocalDestIndex, pltfmBscp, pltfmHighRateTokenBucket, pltfmLowRateTokenBucket, pltfmParentMeter,
                        pltfmChildMeter, pltfmL2UserTypes, and pltfLocalBridgeMacs to cienaCesChassisResourceHealthSubCategory."

              REVISION "201206040000Z"
              DESCRIPTION
                       "Added cienaCesChassisLinxStatHealthTable. Modified cienaCesChassisDiskHealthSubCategory by 1) removing flash1,
                        sys0, sys1, and cf0, and 2) adding usb."

              REVISION "201003280000Z"
              DESCRIPTION
                       "Initial creation."
          ::= { cienaCesConfig 5 }

 --
 -- Node definitions
 --

 cienaCesChassisMIBObjects OBJECT IDENTIFIER ::= { cienaCesChassisMIB 1 }

 --
 --
 cienaCesChassisGlobal OBJECT IDENTIFIER ::= { cienaCesChassisMIBObjects 1 }
 cienaCesChassisObjects       OBJECT IDENTIFIER ::= { cienaCesChassisMIBObjects 2 }
 cienaCesChassis       OBJECT IDENTIFIER ::= { cienaCesChassisObjects 1 }
 cienaCesChassisPlatform       OBJECT IDENTIFIER ::= { cienaCesChassisObjects 2 }

 -- the Power Supply module group.

 cienaCesChassisPowerModule OBJECT IDENTIFIER ::= { cienaCesChassis 1 }

 -- the Fan module group.

 cienaCesChassisFanModule  OBJECT IDENTIFIER ::= { cienaCesChassis 2 }

  -- the Temp Module

 cienaCesChassisFanModuleTemp OBJECT IDENTIFIER ::= { cienaCesChassis 3 }

 -- the Health Mgr report

 cienaCesChassisHealth OBJECT IDENTIFIER ::= { cienaCesChassis 4 }

 -- Chassis IDP contents

 cienaCesChassisIDP                     OBJECT IDENTIFIER ::= { cienaCesChassis 5 }

 -- Chassis IOM information

 cienaCesChassisIOM  OBJECT IDENTIFIER ::= { cienaCesChassis 6 }


 cienaCesChassisMIBNotificationPrefix  OBJECT IDENTIFIER ::= { cienaCesNotifications 4 }
 cienaCesChassisMIBNotifications       OBJECT IDENTIFIER ::=
                       { cienaCesChassisMIBNotificationPrefix 0 }

 -- Conformance information

 cienaCesChassisMIBConformance OBJECT IDENTIFIER ::= { cienaCesChassisMIB 2 }
 cienaCesChassisMIBCompliances OBJECT IDENTIFIER ::= { cienaCesChassisMIBConformance 1 }
 cienaCesChassisMIBGroups      OBJECT IDENTIFIER ::= { cienaCesChassisMIBConformance 2 }

 --
 -- Global stuff
 --

  cienaCesChassisMacAddress    OBJECT-TYPE
     SYNTAX           MacAddress
     MAX-ACCESS       read-only
     STATUS           current
     DESCRIPTION
             "This object indicates the chassis MAC address."
     ::= { cienaCesChassisGlobal 1 }

 cienaCesChassisDeviceId    OBJECT-TYPE
     SYNTAX           DisplayString
     MAX-ACCESS       read-only
     STATUS           current
     DESCRIPTION
             "This object indicates the chassis device ID."
     ::= { cienaCesChassisGlobal 2 }

 cienaCesChassisPartNumber    OBJECT-TYPE
     SYNTAX           DisplayString
     MAX-ACCESS       read-only
     STATUS           current
     DESCRIPTION
             "This object indicates the chassis part number."
     ::= { cienaCesChassisGlobal 3 }

  cienaCesChassisSerialNumber    OBJECT-TYPE
     SYNTAX           DisplayString
     MAX-ACCESS       read-only
     STATUS           current
     DESCRIPTION
             "This object indicates the chassis serial number."
     ::= { cienaCesChassisGlobal 4 }

  cienaCesChassisMfgDate    OBJECT-TYPE
     SYNTAX           DateAndTime
     MAX-ACCESS       read-only
     STATUS           current
     DESCRIPTION
             "This object indicates the chassis manufacturing date."
     ::= { cienaCesChassisGlobal 5 }

  cienaCesChassisParamVersion    OBJECT-TYPE
     SYNTAX           DisplayString
     MAX-ACCESS       read-only
     STATUS           current
     DESCRIPTION
             "This object indicates the chassis param version."
     ::= { cienaCesChassisGlobal 6 }

 cienaCesChassisSystemDateAndTime    OBJECT-TYPE
     SYNTAX           DisplayString
     MAX-ACCESS       read-only
     STATUS           current
     DESCRIPTION
             "This object displays local system date and time."
     ::= { cienaCesChassisGlobal 7 }

 cienaCesChassisSystemUTCDateAndTime    OBJECT-TYPE
     SYNTAX           DisplayString
     MAX-ACCESS       read-only
     STATUS           current
     DESCRIPTION
             "This object displays coordinated UTC system date and time."
     ::= { cienaCesChassisGlobal 8 }

  cienaCesChassisSystemTimeOffset    OBJECT-TYPE
     SYNTAX           Integer32
     MAX-ACCESS       read-only
     STATUS           current
     DESCRIPTION
             "This object displays the time offset in seconds.
              Valid range is -43200 .. 50400.
              This range corresponds to -12Hr, +14Hr consistent with the maximum time zone spacing"
     ::= { cienaCesChassisGlobal 9 }

   cienaCesChassisRebootReasonErrorType OBJECT-TYPE
     SYNTAX      INTEGER {
                                unknown(1),
                                user(2),
                                powerFailure(3),
                                upgrade(4),
                                resetButton(5),
                                coldFailover(6),
                                faultManager(7),
                                communicationFailure(8),
                                autoRevert(9),
                                unprotectedFailure(10),
                                bootFailure(11),
                                softwareRevert(12),
                                snmp(13),
                                appLoad(14),
                                errorHandler(15),
                                watchdog(16)
                         }
     MAX-ACCESS  accessible-for-notify
     STATUS      current
     DESCRIPTION
             "This variable indicates the type of reboot reasons
              included in the cienaCesChassisRebootNotification trap."
    ::= { cienaCesChassisGlobal 10 }

 cienaCesChassisSystemId    OBJECT-TYPE
     SYNTAX           DisplayString
     MAX-ACCESS       accessible-for-notify
     STATUS           current
     DESCRIPTION
             "This object indicates the system identification (SID) as provisioned by the operator through TL1."
     ::= { cienaCesChassisGlobal 11 }

  cienaCesChassisAlarmCutoffOrigin OBJECT-TYPE
     SYNTAX      INTEGER {
                           cli(1),
                           snmp(2),
                           primaryCm(3),
                           secondaryCm(4)
                         }
     MAX-ACCESS  accessible-for-notify
     STATUS      current
     DESCRIPTION
             "This object indicates the origin of the alarm cutoff command
              included in the cienaCesChassisAlarmCutoffNotification."
    ::= { cienaCesChassisGlobal 12 }


  cienaCesChassisRestart OBJECT-TYPE
     SYNTAX      TruthValue
     MAX-ACCESS  read-write
     STATUS      current
     DESCRIPTION
             "This object restarts the chassis. Setting the value of this object 
	      to true will restart the chassis immediately. When read, this 
	      object will always return false."
    DEFVAL { false }
    ::= { cienaCesChassisGlobal 13 }

  cienaCesChassisMefSourceMacAddress    OBJECT-TYPE
     SYNTAX           MacAddress
     MAX-ACCESS       read-only
     STATUS           current
     DESCRIPTION
             "This object indicates the chassis MEF source MAC address."
     ::= { cienaCesChassisGlobal 14 }

 --
 -- Power Module
 --
 cienaCesChassisPowerTable  OBJECT-TYPE
     SYNTAX      SEQUENCE OF CienaCesChassisPowerEntry
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
             "A list of power supply entries."
     ::= { cienaCesChassisPowerModule 1 }

 cienaCesChassisPowerEntry     OBJECT-TYPE
     SYNTAX         CienaCesChassisPowerEntry
     MAX-ACCESS     not-accessible
     STATUS         current
     DESCRIPTION
             "An entry in the power table providing objects for a
             power supply."
     INDEX  { cienaCesChassisPowerSupplyIndx }
     ::= { cienaCesChassisPowerTable 1 }

 CienaCesChassisPowerEntry ::=  SEQUENCE  {
     cienaCesChassisPowerSupplyIndx         Integer32,
     cienaCesChassisPowerSupplyState            INTEGER,
     cienaCesChassisPowerSupplyType                     INTEGER,
     cienaCesChassisPowerSupplyManufacturer     DisplayString,
     cienaCesChassisPowerSupplySerialNumber     DisplayString,
     cienaCesChassisPowerSupplyPartNum          DisplayString,
     cienaCesChassisPowerSupplyNotifIndx        Integer32,
     cienaCesChassisPowerSupplyFRU              TruthValue,
     cienaCesChassisPowerSupplySlotName         DisplayString,
     cienaCesChassisPowerSupplyRevInfo          DisplayString,
     cienaCesChassisPowerSupplyChassisIndx      Unsigned32,
     cienaCesChassisPowerSupplyShelfIndx        Unsigned32,
     cienaCesChassisPowerSupplySlotIndx         Unsigned32
 }

 cienaCesChassisPowerSupplyIndx  OBJECT-TYPE
     SYNTAX            Integer32 (1..8)
     MAX-ACCESS        not-accessible
     STATUS            current
     DESCRIPTION
             "This object indicates the unique index of the table."
     ::= { cienaCesChassisPowerEntry 1 }

 cienaCesChassisPowerSupplyState  OBJECT-TYPE
     SYNTAX  INTEGER  {
                  online(1),
                  faulted(2),
                  offline(3),
                  uninstalled(4)
              }
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
             "Specifies the state of the power supply."
     ::= { cienaCesChassisPowerEntry 2 }

  cienaCesChassisPowerSupplyType  OBJECT-TYPE
     SYNTAX       INTEGER  {
                       ac(1),
                       dc(2),
                       unequipped(3),
                       unknown(99)
                  }
      MAX-ACCESS  read-only
      STATUS      current
      DESCRIPTION
              "Specifies the power supply type."
      ::= { cienaCesChassisPowerEntry 3 }

  cienaCesChassisPowerSupplyManufacturer  OBJECT-TYPE
     SYNTAX       DisplayString
      MAX-ACCESS  read-only
      STATUS      current
      DESCRIPTION
              "Specifies the power supply manufacturer or the manufacturer
               specific model name of the power supply."
      ::= { cienaCesChassisPowerEntry 4 }

  cienaCesChassisPowerSupplySerialNumber  OBJECT-TYPE
     SYNTAX       DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
              "Specifies the power supply serial number."
      ::= { cienaCesChassisPowerEntry 5 }

  cienaCesChassisPowerSupplyPartNum  OBJECT-TYPE
     SYNTAX       DisplayString
      MAX-ACCESS  read-only
      STATUS      current
      DESCRIPTION
              "Specifies the power supply part number."
      ::= { cienaCesChassisPowerEntry 6 }

  cienaCesChassisPowerSupplyNotifIndx  OBJECT-TYPE
     SYNTAX            Integer32 (1..2)
     MAX-ACCESS        accessible-for-notify
     STATUS            current
     DESCRIPTION
             "This object indicates the unique index of the table that is used in the
             definition of power-supply traps."
     ::= { cienaCesChassisPowerEntry 7 }

  cienaCesChassisPowerSupplyFRU  OBJECT-TYPE
     SYNTAX            TruthValue
     MAX-ACCESS        read-only
     STATUS            current
     DESCRIPTION
             "This object indicates whether or not the power supply is a field replaceable unit (FRU).  It returns true if the unit is an FRU."
     ::= { cienaCesChassisPowerEntry 8 }

  cienaCesChassisPowerSupplySlotName  OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
              "Specifies the name of the chassis slot in which the power supply is
               installed."
      ::= { cienaCesChassisPowerEntry 9 }

  cienaCesChassisPowerSupplyRevInfo  OBJECT-TYPE
     SYNTAX      DisplayString
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
              "Specifies the revision information of the power supply."
     ::= { cienaCesChassisPowerEntry 10 }

  cienaCesChassisPowerSupplyChassisIndx  OBJECT-TYPE
     SYNTAX             Unsigned32 (1..1)
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
             "This object represents the chassis index."
     ::= { cienaCesChassisPowerEntry 11 }

  cienaCesChassisPowerSupplyShelfIndx  OBJECT-TYPE
     SYNTAX             Unsigned32 (0..992)
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
             "This object represents the chassis shelf index."
     ::= { cienaCesChassisPowerEntry 12 }

  cienaCesChassisPowerSupplySlotIndx  OBJECT-TYPE
     SYNTAX             Unsigned32 (1..38)
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
             "This object represents the power supply chassis slot index."
     ::= { cienaCesChassisPowerEntry 13 }

 --
 --  The Fan module group
 --
 cienaCesChassisFanTable  OBJECT-TYPE
     SYNTAX      SEQUENCE OF CienaCesChassisFanEntry
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
             "A list of fan module entries."
     ::= { cienaCesChassisFanModule 1 }

 cienaCesChassisFanEntry  OBJECT-TYPE
     SYNTAX         CienaCesChassisFanEntry
     MAX-ACCESS     not-accessible
     STATUS         current
     DESCRIPTION
             "An entry in the fan module Table providing objects for a
             fan module."
     INDEX  { cienaCesChassisFanTrayIndx, cienaCesChassisFanIndx }
     ::= { cienaCesChassisFanTable 1 }

 CienaCesChassisFanEntry ::=  SEQUENCE  {
     cienaCesChassisFanTrayIndx           Integer32,
     cienaCesChassisFanIndx               Integer32,
     cienaCesChassisFanStatus             INTEGER,
     cienaCesChassisFanAvgSpeed           Integer32,
     cienaCesChassisFanCurrentSpeed       Integer32,
     cienaCesChassisFanMinSpeed           Integer32,
     cienaCesChassisFanTrayNotifIndex     Integer32,
     cienaCesChassisFanNotifIndex         Integer32,
     cienaCesChassisFanName               DisplayString,
     cienaCesChassisFanChassisIndx        Unsigned32,
     cienaCesChassisFanShelfIndx          Unsigned32
 }

 cienaCesChassisFanTrayIndx  OBJECT-TYPE
     SYNTAX        Integer32 (1..4)
     MAX-ACCESS    not-accessible
     STATUS        current
     DESCRIPTION
             "This object indicates the fan tray and also represents the unique index in the table."
     ::= { cienaCesChassisFanEntry 1 }

 cienaCesChassisFanIndx  OBJECT-TYPE
     SYNTAX        Integer32 (1..65535)
     MAX-ACCESS    not-accessible
     STATUS        current
     DESCRIPTION
             "This object identifies a fan in the fan tray and is a unique index in the table."
     ::= { cienaCesChassisFanEntry 2 }

 cienaCesChassisFanStatus OBJECT-TYPE
     SYNTAX        INTEGER  {
                        ok(1),
                        pending(2),
                        rpm-warning(3),
                        uninstalled(4),
                        unknown(99)
                   }
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "Denotes the fan module status as follows:
              'ok' means fan is operational,
              'pending' means fan is installed but statistics
              are not yet available,
              'rpm_warning' means fan is not working.
              'uninstalled' means fan not found."
     ::= { cienaCesChassisFanEntry 3 }

 cienaCesChassisFanAvgSpeed OBJECT-TYPE
     SYNTAX        Integer32
     UNITS         "rpm"
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "The average speed of the fan in RPM unit.  The average is calculated
             on a continuous basis from system startup."
     ::= { cienaCesChassisFanEntry 4 }

 cienaCesChassisFanCurrentSpeed OBJECT-TYPE
     SYNTAX        Integer32
     UNITS         "rpm"
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "The current speed of the fan in RPM."
     ::= { cienaCesChassisFanEntry 5 }

 cienaCesChassisFanMinSpeed OBJECT-TYPE
     SYNTAX        Integer32
     UNITS         "rpm"
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "The minimum speed of the fan in RPM."
     ::= { cienaCesChassisFanEntry 6 }

  cienaCesChassisFanTrayNotifIndex  OBJECT-TYPE
     SYNTAX        Integer32 (1..4)
     MAX-ACCESS    accessible-for-notify
     STATUS        current
     DESCRIPTION
             "This object indicates the fan tray and also represents the unique index
             used in the definition of fan traps."
     ::= { cienaCesChassisFanEntry 7 }

 cienaCesChassisFanNotifIndex  OBJECT-TYPE
     SYNTAX        Integer32 (1..65535)
     MAX-ACCESS    accessible-for-notify
     STATUS        current
     DESCRIPTION
             "This object identifies a fan in the fan tray and is a unique index
             used in the definition of fan traps."
     ::= { cienaCesChassisFanEntry 8 }

 cienaCesChassisFanName OBJECT-TYPE
     SYNTAX        DisplayString
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object identifies a fan name."
     ::= { cienaCesChassisFanEntry 9 }

  cienaCesChassisFanChassisIndx  OBJECT-TYPE
     SYNTAX             Unsigned32 (1..1)
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
             "This object represents the chassis index."
     ::= { cienaCesChassisFanEntry 10 }

  cienaCesChassisFanShelfIndx  OBJECT-TYPE
     SYNTAX             Unsigned32 (0..992)
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
             "This object represents the chassis shelf index."
     ::= { cienaCesChassisFanEntry 11 }

 --
 -- Fan Tray Info
 --
 cienaCesChassisFanTrayTable  OBJECT-TYPE
     SYNTAX      SEQUENCE OF CienaCesChassisFanTrayEntry
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
             "A list of fan tray module entries."
     ::= { cienaCesChassisFanModule 2 }

 cienaCesChassisFanTrayEntry  OBJECT-TYPE
     SYNTAX         CienaCesChassisFanTrayEntry
     MAX-ACCESS     not-accessible
     STATUS         current
     DESCRIPTION
             "An entry in the fan tray module table providing objects related to
              fan tray."
     INDEX  { cienaCesChassisFanTrayIndx }
     ::= { cienaCesChassisFanTrayTable 1 }

 CienaCesChassisFanTrayEntry ::=  SEQUENCE  {
         cienaCesChassisFanTrayStatus           INTEGER,
         cienaCesChassisFanTrayType             INTEGER,
         cienaCesChassisFanTrayMode             INTEGER,
         cienaCesChassisFanTrayNumFans          Integer32,
         cienaCesChassisFanTrayName             DisplayString,
         cienaCesChassisFanTrayChassisIndx      Unsigned32,
         cienaCesChassisFanTrayShelfIndx        Unsigned32,
         cienaCesChassisFanTraySlotIndx         Unsigned32,
         cienaCesChassisFanTraySerialNumber     DisplayString
 }

 cienaCesChassisFanTrayStatus  OBJECT-TYPE
     SYNTAX        INTEGER  {
                        ok(1),
                        pending(2),
                        rpm-warning(3),
                        uninstalled(4),
                        unknown(99)
                   }
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the status of the fan tray."
     ::= { cienaCesChassisFanTrayEntry 1 }

 cienaCesChassisFanTrayType  OBJECT-TYPE
     SYNTAX        INTEGER  {
                        fixed(1),
                        hotSwappable(2),
                        unequipped(3)
                   }
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the type of fan tray."
     ::= { cienaCesChassisFanTrayEntry 2 }

 cienaCesChassisFanTrayMode  OBJECT-TYPE
     SYNTAX        INTEGER {
                                        invalid(1),
                                        full(2),
                                        auto(3)
                                }
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the operational mode of the fan tray."
     ::= { cienaCesChassisFanTrayEntry 3 }

 cienaCesChassisFanTrayNumFans  OBJECT-TYPE
     SYNTAX        Integer32
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the number of fans on the tray."
     ::= { cienaCesChassisFanTrayEntry 4 }

 cienaCesChassisFanTrayName  OBJECT-TYPE
     SYNTAX        DisplayString
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the fan tray name."
     ::= { cienaCesChassisFanTrayEntry 5 }

  cienaCesChassisFanTrayChassisIndx  OBJECT-TYPE
     SYNTAX             Unsigned32 (1..1)
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
             "This object represents the chassis index."
     ::= { cienaCesChassisFanTrayEntry 6 }

  cienaCesChassisFanTrayShelfIndx  OBJECT-TYPE
     SYNTAX             Unsigned32 (0..992)
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
             "This object represents the chassis shelf index."
     ::= { cienaCesChassisFanTrayEntry 7 }

  cienaCesChassisFanTraySlotIndx  OBJECT-TYPE
     SYNTAX             Unsigned32 (1..38)
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
             "This object represents the fan tray chassis slot index."
     ::= { cienaCesChassisFanTrayEntry 8 }

 cienaCesChassisFanTraySerialNumber  OBJECT-TYPE
     SYNTAX        DisplayString
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the fan tray serial number."
     ::= { cienaCesChassisFanTrayEntry 9 }


 --
 --  The Fan temperature group
 --
 cienaCesChassisFanTempTable  OBJECT-TYPE
     SYNTAX      SEQUENCE OF CienaCesChassisFanTempEntry
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
             "A list of fan temperature module entries."
     ::= { cienaCesChassisFanModuleTemp 2 }

 cienaCesChassisFanTempEntry  OBJECT-TYPE
     SYNTAX         CienaCesChassisFanTempEntry
     MAX-ACCESS     not-accessible
     STATUS         current
     DESCRIPTION
             "An entry in the fan temperature module table providing objects for a
             fan module."
     INDEX  { cienaCesChassisFanTempTrayIndx, cienaCesChassisFanTempId }
     ::= { cienaCesChassisFanTempTable 1 }

 CienaCesChassisFanTempEntry ::=  SEQUENCE  {
         cienaCesChassisFanTempTrayIndx             Integer32,
     cienaCesChassisFanTempId                       Integer32,
         cienaCesChassisFanTempDesc                     DisplayString,
     cienaCesChassisFanTemp                     Integer32,
     cienaCesChassisFanTempHigh                 Integer32,
     cienaCesChassisFanTempLow              Integer32,
     cienaCesChassisFanTempLoThreshold      Integer32,
     cienaCesChassisFanTempHiThreshold      Integer32,
     cienaCesChassisFanTempTrayNotifIndx        Integer32,
     cienaCesChassisFanTempNotifId          Integer32,
     cienaCesChassisFanTempName             DisplayString,
     cienaCesChassisFanTempChassisIndx      Unsigned32,
     cienaCesChassisFanTempShelfIndx        Unsigned32
 }

 cienaCesChassisFanTempTrayIndx  OBJECT-TYPE
     SYNTAX        Integer32 (1..65535)
     MAX-ACCESS    not-accessible
     STATUS        current
     DESCRIPTION
             "This object indicates the unique index in the table."
     ::= { cienaCesChassisFanTempEntry 1 }

 cienaCesChassisFanTempId  OBJECT-TYPE
     SYNTAX        Integer32 (1..65535)
     MAX-ACCESS    not-accessible
     STATUS        current
     DESCRIPTION
             "This object indicates the unique index in the table."
     ::= { cienaCesChassisFanTempEntry 2 }

 cienaCesChassisFanTempDesc  OBJECT-TYPE
     SYNTAX        DisplayString
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the description of the fan temperature sensor."
     ::= { cienaCesChassisFanTempEntry 3 }

 cienaCesChassisFanTemp  OBJECT-TYPE
     SYNTAX        Integer32
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the current chassis fan temperature."
     ::= { cienaCesChassisFanTempEntry 4 }

  cienaCesChassisFanTempHigh  OBJECT-TYPE
     SYNTAX        Integer32
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the highest temperature this chassis fan reached so far."
     ::= { cienaCesChassisFanTempEntry 5 }

  cienaCesChassisFanTempLow  OBJECT-TYPE
     SYNTAX        Integer32
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the lowest temperature this chassis fan reached so far."
     ::= { cienaCesChassisFanTempEntry 6 }

  cienaCesChassisFanTempLoThreshold  OBJECT-TYPE
     SYNTAX            Integer32 (0..50)
     MAX-ACCESS        read-only
     STATUS            current
     DESCRIPTION
             "This object indicates the low threshold value for the fan temperature sensor."
     ::= { cienaCesChassisFanTempEntry 7 }

 cienaCesChassisFanTempHiThreshold  OBJECT-TYPE
     SYNTAX            Integer32 (0..50)
     MAX-ACCESS        read-only
     STATUS            current
     DESCRIPTION
             "This object indicates the high threshold value for the fan temperature sensor."
     ::= { cienaCesChassisFanTempEntry 8 }

 cienaCesChassisFanTempTrayNotifIndx  OBJECT-TYPE
     SYNTAX        Integer32 (1..65535)
     MAX-ACCESS    accessible-for-notify
     STATUS        current
     DESCRIPTION
             "This object indicates the unique index used in fan temperature trap definition."
     ::= { cienaCesChassisFanTempEntry 9 }

 cienaCesChassisFanTempNotifId  OBJECT-TYPE
     SYNTAX        Integer32 (1..65535)
     MAX-ACCESS    accessible-for-notify
     STATUS        current
     DESCRIPTION
             "This object indicates the unique index used in fan temperature trap definition."
     ::= { cienaCesChassisFanTempEntry 10 }

 cienaCesChassisFanTempName  OBJECT-TYPE
     SYNTAX        DisplayString
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the fan temperature sensor name."
     ::= { cienaCesChassisFanTempEntry 11 } 

  cienaCesChassisFanTempChassisIndx  OBJECT-TYPE
     SYNTAX             Unsigned32 (1..1)
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
             "This object represents the chassis index."
     ::= { cienaCesChassisFanTempEntry 12 }

  cienaCesChassisFanTempShelfIndx  OBJECT-TYPE
     SYNTAX             Unsigned32 (0..992)
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
             "This object represents the chassis shelf index."
     ::= { cienaCesChassisFanTempEntry 13 }

 --
 -- Health
 --

  cienaCesChassisHealthCategory  OBJECT-TYPE
     SYNTAX           TceHealthCategory
     MAX-ACCESS       accessible-for-notify
     STATUS           current
     DESCRIPTION
             "This object indicates the health category that the Health Manager
          monitors on the chassis."
     ::= { cienaCesChassisHealth 1 }


  cienaCesChassisHealthSubCategory  OBJECT-TYPE
     SYNTAX           Unsigned32
     MAX-ACCESS       accessible-for-notify
     STATUS           current
     DESCRIPTION
          "This object indicates the sub-category that has meaning specific to the value
          of the accompanying cienaCesChassisHealthCategory object.  Refer to the specific
          subCategory object that corresponds to the cienaCesChassisHealthCategory
          for details on the particular subCategory as referenced below:

          TceHealthCategory value,              corresponding subCategory object
          ----------------------------------------------------------------------
          unknown(1)
          cpu(2)                                cienaCesChassisCPUHealthSubCategory
          datapath(3)                           cienaCesChassisDatapathHealthSubCategory
          ethernet(4)                           cienaCesChassisControlPlaneHealthSubCategory
          fabric(5)                             cienaCesChassisFabricHealthSubCategory
          sm(6)                                 cienaCesChassisSMHealthSubCategory
          tempSm(7)                             cienaCesChassisSMTempHealthSubCategory
          samplesSm(8)                          cienaCesChassisSMSamplesHealthSubCategory
          disk(9)                               cienaCesChassisDiskHealthSubCategory
          tempModule(10)                        cienaCesChassisModuleTempHealthSubCategory
          samplesModule(11)                     cienaCesChassisModuleSamplesHealthSubCategory
          fanTray(12)                           cienaCesChassisFanTrayHealthSubCategory
          fanTraySpeedMismatch(13)              cienaCesChassisFanTraySpeedMismatchHealthSubCategory
          fanSpeedMismatch(14)                  cienaCesChassisFanSpeedMismatchHealthSubCategory
          tempFan(15)                           cienaCesChassisFanTempHealthSubCategory
          samplesFan(16)                        cienaCesChassisFanSamplesHealthSubCategory
          fanRpm(17)                            cienaCesChassisFanRPMHealthSubCategory
          power(18)                             cienaCesChassisPowerHealthSubCategory
          feedPower(19)                         cienaCesChassisFeedPowerHealthSubCategory
          systemResource(20)                    cienaCesChassisResourceHealthSubCategory
          memory(21)                            cienaCesChassisMemoryHealthSubCategory
          mac(22)                               cienaCesChassisMACHealthSubCategory
          i2c(23)                               cienaCesChassisI2CHealthSubCategory
          flash(24)                             cienaCesChassisFlashDriverHealthSubCategory
          transceiver(25)                       cienaCesChassisXcvrHealthSubCategory
          link(26)                              cienaCesChassisPortLinkHealthSubCategory
          iomStatus(27)                         cienaCesChassisIOMStatusHealthSubCategory
          usbFlash(28)                          cienaCesChassisUsbFlashHealthSubCategory
          linxstats(29)                         cienaCesChassisLinxStatHealthSubCategory
          smFabric(30)                          cienaCesChassisSMFabricHealthSubCategory
          spi(31)                               cienaCesChassisSPIHealthSubCategory
          slotResource(32)                      cienaCesModuleResourceHealthSubCategory(cienaCesModuleMIB)
          tempIom(33)                           cienaCesChassisIomTempHealthSubCategory
          powerParams(34)                       cienaCesChassisPowerParamsHealthSubCategory
          powerOutputVoltage(35)                cienaCesChassisPowerOutputVoltageHealthSubCategory
          tempModem(36)                         cienaCesChassisModemTempHealthSubCategory
          watermarkModem(37)                    cienaCesChassisModemWatermarkHealthSubCategory
          fileDescriptor(38)                    cienaCesChassisFileDescriptorHealthSubCategory
          process(39)                           cienaCesChassisProcessHealthSubCategory
          thread(40)                            cienaCesChassisThreadHealthSubCategory"

           ::= { cienaCesChassisHealth 2 }

  cienaCesChassisHealthStatus  OBJECT-TYPE
     SYNTAX           TceHealthStatus
     MAX-ACCESS       accessible-for-notify
     STATUS           current
     DESCRIPTION
             "This object indicates the current health status of health category and
          its sub-category being monitored on the chassis."
     ::= { cienaCesChassisHealth 3 }

  cienaCesChassisHealthStatusLast  OBJECT-TYPE
     SYNTAX           TceHealthStatus
     MAX-ACCESS       accessible-for-notify
     STATUS           current
     DESCRIPTION
             "This object indicates the last known health status of health category and
          its sub-category being monitored on the chassis."
          ::= { cienaCesChassisHealth 4 }

 cienaCesChassisCPUHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisCPUHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of CPU resources."
        ::= { cienaCesChassisHealth 5 }

 cienaCesChassisCPUHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisCPUHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisCPUHealthTable."
        INDEX { cienaCesChassisCPUHealthSubCategory, cienaCesChassisCPUHealthOriginIndex }
        ::= { cienaCesChassisCPUHealthTable 1 }

 CienaCesChassisCPUHealthEntry ::= SEQUENCE {
        cienaCesChassisCPUHealthSubCategory                     INTEGER,
        cienaCesChassisCPUHealthOriginIndex                 Unsigned32,
        cienaCesChassisCPUHealthState                           TceHealthStatus,
        cienaCesChassisCPUHealthCurrMeasurement                 Unsigned32,
        cienaCesChassisCPUHealthMaxMeasurement  Unsigned32,
        cienaCesChassisCPUHealthMaxThreshold            Unsigned32
 }

 cienaCesChassisCPUHealthSubCategory            OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         cpu-Usage(1)
                                                        }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "CPU sub-category being monitored.
                         - 'none' is an enumeration  placeholder "
                ::= { cienaCesChassisCPUHealthEntry 1 }

 cienaCesChassisCPUHealthOriginIndex                    OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisCPUHealthEntry 2}

 cienaCesChassisCPUHealthState                          OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health state of the cpu sub-category being monitored."
                ::= { cienaCesChassisCPUHealthEntry 3}

 cienaCesChassisCPUHealthCurrMeasurement                OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "percent"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current value of the cpu utilization for the slot being monitored."
                ::= { cienaCesChassisCPUHealthEntry 4 }

 cienaCesChassisCPUHealthMaxMeasurement         OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "percent"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum recorded cpu utilization value for the slot being monitored."
                ::= { cienaCesChassisCPUHealthEntry 5 }

 cienaCesChassisCPUHealthMaxThreshold   OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "percent"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum possible value of cpu utilization."
                ::= { cienaCesChassisCPUHealthEntry 6 }

 cienaCesChassisDatapathHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisDatapathHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of datapath resources."
        ::= { cienaCesChassisHealth 6 }

 cienaCesChassisDatapathHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisDatapathHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisDatapathHealthTable."
        INDEX { cienaCesChassisDatapathHealthSubCategory, cienaCesChassisDatapathHealthOriginIndex }
        ::= { cienaCesChassisDatapathHealthTable 1 }

 CienaCesChassisDatapathHealthEntry ::= SEQUENCE {
        cienaCesChassisDatapathHealthSubCategory                        INTEGER,
        cienaCesChassisDatapathHealthOriginIndex                    Unsigned32,
        cienaCesChassisDatapathHealthState                              TceHealthStatus
 }

 cienaCesChassisDatapathHealthSubCategory               OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         status(1),
                                                         dataPlane(2),
                                                         system(3),
                                                         controlPlane(4)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the health-item being monitored.
                        - 'none' is an enumeration  placeholder "
                ::= { cienaCesChassisDatapathHealthEntry 1 }

 cienaCesChassisDatapathHealthOriginIndex                       OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisDatapathHealthEntry 2}

 cienaCesChassisDatapathHealthState                             OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Status of the data-path for the slot being monitored."
                ::= { cienaCesChassisDatapathHealthEntry 3}


 cienaCesChassisControlPlaneHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisControlPlaneHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of control plane resources."
        ::= { cienaCesChassisHealth 7 }

 cienaCesChassisControlPlaneHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisControlPlaneHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisControlPlaneHealthTable."
        INDEX { cienaCesChassisControlPlaneHealthSubCategory, cienaCesChassisControlPlaneHealthOriginIndex }
        ::= { cienaCesChassisControlPlaneHealthTable 1 }

 CienaCesChassisControlPlaneHealthEntry ::= SEQUENCE {
        cienaCesChassisControlPlaneHealthSubCategory                    INTEGER,
        cienaCesChassisControlPlaneHealthOriginIndex                Unsigned32,
        cienaCesChassisControlPlaneHealthState                          TceHealthStatus,
        cienaCesChassisControlPlaneHealthCurrMeasurement                Unsigned32,
        cienaCesChassisControlPlaneHealthMaxMeasurement         Unsigned32,
        cienaCesChassisControlPlaneHealthMaxThreshold           Unsigned32
 }

 cienaCesChassisControlPlaneHealthSubCategory           OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         droppedPackets(1),
                                                         cRCErrors(2),
                                                         errorPackets(3)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the control backplane item being monitored.
                        - 'none' is an enumeration  placeholder "
                ::= { cienaCesChassisControlPlaneHealthEntry 1 }

 cienaCesChassisControlPlaneHealthOriginIndex                   OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisControlPlaneHealthEntry 2}

 cienaCesChassisControlPlaneHealthState                                 OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Status of the control backplane item for the slot being monitored."
                ::= { cienaCesChassisControlPlaneHealthEntry 3}

 cienaCesChassisControlPlaneHealthCurrMeasurement               OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "percent"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current value of the control backplane item for the slot being monitored."
                ::= { cienaCesChassisControlPlaneHealthEntry 4 }

 cienaCesChassisControlPlaneHealthMaxMeasurement        OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "percent"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum recorded value of the control plane item for the slot being monitored."
                ::= { cienaCesChassisControlPlaneHealthEntry 5 }

 cienaCesChassisControlPlaneHealthMaxThreshold          OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "percent"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum possible value for the control plane item."
                ::= { cienaCesChassisControlPlaneHealthEntry 6 }


 cienaCesChassisFabricHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisFabricHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of fabric resources."
        ::= { cienaCesChassisHealth 8 }

 cienaCesChassisFabricHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisFabricHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisFabricHealthTable."
        INDEX { cienaCesChassisFabricHealthSubCategory, cienaCesChassisFabricHealthOriginIndex }
        ::= { cienaCesChassisFabricHealthTable 1 }

 CienaCesChassisFabricHealthEntry ::= SEQUENCE {
        cienaCesChassisFabricHealthSubCategory                  INTEGER,
        cienaCesChassisFabricHealthOriginIndex              Unsigned32,
        cienaCesChassisFabricHealthState                                TceHealthStatus
 }

 cienaCesChassisFabricHealthSubCategory                 OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         status(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the fabric being monitored.
                        - 'none' is an enumeration  placeholder "
                ::= { cienaCesChassisFabricHealthEntry 1 }

 cienaCesChassisFabricHealthOriginIndex                         OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisFabricHealthEntry 2}

 cienaCesChassisFabricHealthState                               OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Fabric state of the slot."
                ::= { cienaCesChassisFabricHealthEntry 4}
--
--SM
--
 cienaCesChassisSMHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisSMHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of SM."
        ::= { cienaCesChassisHealth 9 }

 cienaCesChassisSMHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisSMHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisSMHealthTable."
        INDEX { cienaCesChassisSMHealthSubCategory, cienaCesChassisSMHealthOriginIndex }
        ::= { cienaCesChassisSMHealthTable 1 }

 CienaCesChassisSMHealthEntry ::= SEQUENCE {
        cienaCesChassisSMHealthSubCategory                      INTEGER,
        cienaCesChassisSMHealthOriginIndex                  Unsigned32,
        cienaCesChassisSMHealthState                            TceHealthStatus
 }

 cienaCesChassisSMHealthSubCategory             OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         status(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the SM being monitored.
                        - 'none' is an enumeration  placeholder "
                ::= { cienaCesChassisSMHealthEntry 1 }

 cienaCesChassisSMHealthOriginIndex                     OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisSMHealthEntry 2}

 cienaCesChassisSMHealthState                           OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "SM state of the slot."
                ::= { cienaCesChassisSMHealthEntry 4}

 --
 -- SM temp health
 --
  cienaCesChassisSMTempHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisSMTempHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of SM temperature resources."
        ::= { cienaCesChassisHealth 10 }

 cienaCesChassisSMTempHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisSMTempHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisSMTempHealthTable."
        INDEX { cienaCesChassisSMTempHealthSubCategory, cienaCesChassisSMTempHealthOriginIndex }
        ::= { cienaCesChassisSMTempHealthTable 1 }

 CienaCesChassisSMTempHealthEntry ::= SEQUENCE {
        cienaCesChassisSMTempHealthSubCategory                  INTEGER,
        cienaCesChassisSMTempHealthOriginIndex              Unsigned32,
        cienaCesChassisSMTempHealthState                                TceHealthStatus,
        cienaCesChassisSMTempHealthCurrMeasurement              Unsigned32,
        cienaCesChassisSMTempHealthMinMeasurement               Unsigned32,
        cienaCesChassisSMTempHealthMaxMeasurement               Unsigned32,
        cienaCesChassisSMTempHealthMinThreshold                 Unsigned32,
                cienaCesChassisSMTempHealthMaxThreshold                 Unsigned32
 }

 cienaCesChassisSMTempHealthSubCategory                 OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         temperature(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the SM temperature being monitored.
                        - 'none' is an enumeration  placeholder "
                ::= { cienaCesChassisSMTempHealthEntry 1 }


 cienaCesChassisSMTempHealthOriginIndex                 OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisSMTempHealthEntry 2}

 cienaCesChassisSMTempHealthState                               OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Temperature state of the module temperature sub-category."
                ::= { cienaCesChassisSMTempHealthEntry 3}

 cienaCesChassisSMTempHealthCurrMeasurement             OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current temperature of the SM temperature sub-category."
                ::= { cienaCesChassisSMTempHealthEntry 4 }

 cienaCesChassisSMTempHealthMinMeasurement      OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The minimum recorded temperature reached by the SM temperature sub-category."
                ::= { cienaCesChassisSMTempHealthEntry 5 }

 cienaCesChassisSMTempHealthMaxMeasurement      OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum recorded temperature reached by the SM temperature sub-category."
                ::= { cienaCesChassisSMTempHealthEntry 6 }


 cienaCesChassisSMTempHealthMinThreshold        OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The minimum operational temperature of the SM temperature sub-category."
                ::= { cienaCesChassisSMTempHealthEntry 7 }

 cienaCesChassisSMTempHealthMaxThreshold                        OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum operational temperature of the SM temperature sub-category."
                ::= { cienaCesChassisSMTempHealthEntry 8 }

 --
 -- SM samples
 --
 cienaCesChassisSMSamplesHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisSMSamplesHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of SM."
        ::= { cienaCesChassisHealth 11 }

 cienaCesChassisSMSamplesHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisSMSamplesHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisSMHealthTable."
        INDEX { cienaCesChassisSMSamplesHealthSubCategory, cienaCesChassisSMSamplesHealthOriginIndex }
        ::= { cienaCesChassisSMSamplesHealthTable 1 }

 CienaCesChassisSMSamplesHealthEntry ::= SEQUENCE {
        cienaCesChassisSMSamplesHealthSubCategory                       INTEGER,
        cienaCesChassisSMSamplesHealthOriginIndex                   Unsigned32,
        cienaCesChassisSMSamplesHealthState                             TceHealthStatus,
        cienaCesChassisSMSamplesHealthCurrMeasurement           INTEGER,
        cienaCesChassisSMSamplesHealthMaxMeasurement            INTEGER
 }

 cienaCesChassisSMSamplesHealthSubCategory              OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         invalid(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the SM being monitored.
                        - 'none' is an enumeration  placeholder
                        - 'invalid' indicates the number of invalid sensor reads
                           if the 'cienaCesChassisSMSamplesHealthCurrMeasurement' associated with this instance
                           is a huge value ,it means something is broken."
                ::= { cienaCesChassisSMSamplesHealthEntry 1 }

 cienaCesChassisSMSamplesHealthOriginIndex                      OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisSMSamplesHealthEntry 2}

 cienaCesChassisSMSamplesHealthState                            OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "SM state of the slot."
                ::= { cienaCesChassisSMSamplesHealthEntry 3}

 cienaCesChassisSMSamplesHealthCurrMeasurement                          OBJECT-TYPE
                SYNTAX          INTEGER
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "SM state of the slot."
                ::= { cienaCesChassisSMSamplesHealthEntry 4}

 cienaCesChassisSMSamplesHealthMaxMeasurement                           OBJECT-TYPE
                SYNTAX          INTEGER
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "SM state of the slot."
                ::= { cienaCesChassisSMSamplesHealthEntry 5}


 cienaCesChassisDiskHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisDiskHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of disk resources."
        ::= { cienaCesChassisHealth 12 }

 cienaCesChassisDiskHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisDiskHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisDiskHealthTable."
        INDEX { cienaCesChassisDiskHealthSubCategory, cienaCesChassisDiskHealthOriginIndex }
        ::= { cienaCesChassisDiskHealthTable 1 }

 CienaCesChassisDiskHealthEntry ::= SEQUENCE {
        cienaCesChassisDiskHealthSubCategory                    INTEGER,
        cienaCesChassisDiskHealthOriginIndex                Unsigned32,
        cienaCesChassisDiskHealthState                          TceHealthStatus,
        cienaCesChassisDiskHealthCurrMeasurement                Unsigned32,
        cienaCesChassisDiskHealthMaxMeasurement         Unsigned32,
        cienaCesChassisDiskHealthMaxThreshold           Unsigned32
 }

 cienaCesChassisDiskHealthSubCategory           OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         flash0(1),
                                                         ram(2),
                                                         usb(3)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the health-item being monitored.
                        - 'none' is an enumeration  placeholder"
                ::= { cienaCesChassisDiskHealthEntry 1 }

 cienaCesChassisDiskHealthOriginIndex                   OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisDiskHealthEntry 2}

 cienaCesChassisDiskHealthState                                 OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Disk sub-category utilization state of the slot."
                ::= { cienaCesChassisDiskHealthEntry 3}

 cienaCesChassisDiskHealthCurrMeasurement               OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "KB"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current value of disk sub-category used for the slot."
                ::= { cienaCesChassisDiskHealthEntry 4 }

 cienaCesChassisDiskHealthMaxMeasurement        OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "KB"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum recorded value of disk sub-category used by the slot."
                ::= { cienaCesChassisDiskHealthEntry 5 }

 cienaCesChassisDiskHealthMaxThreshold          OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "KB"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum size of the disk sub-category."
                ::= { cienaCesChassisDiskHealthEntry 6 }


 cienaCesChassisModuleTempHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisModuleTempHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of module temperature resources."
        ::= { cienaCesChassisHealth 13 }

 cienaCesChassisModuleTempHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisModuleTempHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisModuleTempHealthTable."
        INDEX { cienaCesChassisModuleTempHealthSubCategory, cienaCesChassisModuleTempHealthOriginIndex }
        ::= { cienaCesChassisModuleTempHealthTable 1 }

 CienaCesChassisModuleTempHealthEntry ::= SEQUENCE {
        cienaCesChassisModuleTempHealthSubCategory                      INTEGER,
        cienaCesChassisModuleTempHealthOriginIndex                  Unsigned32,
        cienaCesChassisModuleTempHealthState                            TceHealthStatus,
        cienaCesChassisModuleTempHealthCurrMeasurement                  Unsigned32,
        cienaCesChassisModuleTempHealthMinMeasurement           Unsigned32,
        cienaCesChassisModuleTempHealthMaxMeasurement           Unsigned32,
        cienaCesChassisModuleTempHealthMinThreshold             Unsigned32,
                cienaCesChassisModuleTempHealthMaxThreshold             Unsigned32
 }

 cienaCesChassisModuleTempHealthSubCategory             OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         sensor1(1),
                                                         sensor2(2),
                                                         sensor3(3),
                                                         sensor4(4),
                                                         sensor5(5),
                                                         sensor6(6),
                                                         sensor7(7),
                                                         sensor8(8),
                                                         sensor9(9),
                                                         sensor10(10),
                                                         sensor11(11),
                                                         sensor12(12),
                                                         sensor13(13),
                                                         sensor14(14),
                                                         sensor15(15),
                                                         sensor16(16),
                                                         sensor17(17),
                                                         sensor18(18),
                                                         sensor19(19),
                                                         sensor20(20),
                                                         sensor21(21),
                                                         sensor22(22),
                                                         sensor23(23),
                                                         sensor24(24),
                                                         sensor25(25),
                                                         sensor26(26),
                                                         sensor27(27)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the module temperature being monitored."
                ::= { cienaCesChassisModuleTempHealthEntry 1 }


 cienaCesChassisModuleTempHealthOriginIndex             OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisModuleTempHealthEntry 2}

 cienaCesChassisModuleTempHealthState                           OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Temperature state of the module temperature sub-category."
                ::= { cienaCesChassisModuleTempHealthEntry 3}

 cienaCesChassisModuleTempHealthCurrMeasurement                 OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current temperature of the module temperature sub-category."
                ::= { cienaCesChassisModuleTempHealthEntry 4 }

 cienaCesChassisModuleTempHealthMinMeasurement  OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The minimum recorded temperature reached by the module temperature sub-category."
                ::= { cienaCesChassisModuleTempHealthEntry 5 }

 cienaCesChassisModuleTempHealthMaxMeasurement  OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum recorded temperature reached by the module temperature sub-category."
                ::= { cienaCesChassisModuleTempHealthEntry 6 }


 cienaCesChassisModuleTempHealthMinThreshold    OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The minimum operational temperature of the module temperature sub-category."
                ::= { cienaCesChassisModuleTempHealthEntry 7 }

 cienaCesChassisModuleTempHealthMaxThreshold                    OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum operational temperature of the module temperature sub-category."
                ::= { cienaCesChassisModuleTempHealthEntry 8 }

 --
 -- Module samples
 --
 cienaCesChassisModuleSamplesHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisModuleSamplesHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of SM."
        ::= { cienaCesChassisHealth 14 }

 cienaCesChassisModuleSamplesHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisModuleSamplesHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisModuleSamplesHealthTable."
        INDEX { cienaCesChassisModuleSamplesHealthSubCategory, cienaCesChassisModuleSamplesHealthOriginIndex }
        ::= { cienaCesChassisModuleSamplesHealthTable 1 }

 CienaCesChassisModuleSamplesHealthEntry ::= SEQUENCE {
        cienaCesChassisModuleSamplesHealthSubCategory                   INTEGER,
        cienaCesChassisModuleSamplesHealthOriginIndex               Unsigned32,
        cienaCesChassisModuleSamplesHealthState                                 TceHealthStatus,
        cienaCesChassisModuleSamplesHealthCurrMeasurement               INTEGER,
        cienaCesChassisModuleSamplesHealthMaxMeasurement                INTEGER
 }

 cienaCesChassisModuleSamplesHealthSubCategory          OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         sensor1Invalid(1),
                                                         sensor2Invalid(2),
                                                         sensor3Invalid(3),
                                                         sensor4Invalid(4),
                                                         sensor5Invalid(5),
                                                         sensor6Invalid(6),
                                                         sensor7Invalid(7),
                                                         sensor8Invalid(8),
                                                         sensor9Invalid(9),
                                                         sensor10Invalid(10),
                                                         sensor11Invalid(11),
                                                         sensor12Invalid(12),
                                                         sensor13Invalid(13),
                                                         sensor14Invalid(14),
                                                         sensor15Invalid(15),
                                                         sensor16Invalid(16),
                                                         sensor17Invalid(17),
                                                         sensor18Invalid(18),
                                                         sensor19Invalid(19),
                                                         sensor20Invalid(20),
                                                         sensor21Invalid(21),
                                                         sensor22Invalid(22),
                                                         sensor23Invalid(23),
                                                         sensor24Invalid(24),
                                                         sensor25Invalid(25),
                                                         sensor26Invalid(26),
                                                         sensor27Invalid(27)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the module samples being monitored.
                        - 'none' is an enumeration  placeholder
                        - 'sensorXXInvalid' indicates the number of invalid sensor reads
                           if the 'cienaCesChassisModuleSamplesHealthCurrMeasurement' associated with this instance
                           is a huge value ,it means something is broken."
                ::= { cienaCesChassisModuleSamplesHealthEntry 1 }

 cienaCesChassisModuleSamplesHealthOriginIndex                  OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisModuleSamplesHealthEntry 2}

 cienaCesChassisModuleSamplesHealthState                                OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "module samples state of the slot."
                ::= { cienaCesChassisModuleSamplesHealthEntry 3}

  cienaCesChassisModuleSamplesHealthCurrMeasurement                             OBJECT-TYPE
                SYNTAX          INTEGER
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current measurement of module samples state of the slot."
                ::= { cienaCesChassisModuleSamplesHealthEntry 4}

 cienaCesChassisModuleSamplesHealthMaxMeasurement                               OBJECT-TYPE
                SYNTAX          INTEGER
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Maximum measurement of module samples of the slot."
                ::= { cienaCesChassisModuleSamplesHealthEntry 5}



 cienaCesChassisFanTrayHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisFanTrayHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of fan tray resources."
        ::= { cienaCesChassisHealth 15 }

 cienaCesChassisFanTrayHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisFanTrayHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisFanTrayHealthTable."
        INDEX { cienaCesChassisFanTrayHealthSubCategory, cienaCesChassisFanTrayHealthOriginIndex}
        ::= { cienaCesChassisFanTrayHealthTable 1 }

 CienaCesChassisFanTrayHealthEntry ::= SEQUENCE {
        cienaCesChassisFanTrayHealthSubCategory                         INTEGER,
        cienaCesChassisFanTrayHealthOriginIndex                         Unsigned32,
        cienaCesChassisFanTrayHealthState                               TceHealthStatus
 }

 cienaCesChassisFanTrayHealthSubCategory                OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         status(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the fan tray being monitored.
                        - 'none' is an enumeration  placeholder         "
                ::= { cienaCesChassisFanTrayHealthEntry 1 }

 cienaCesChassisFanTrayHealthOriginIndex OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the fan-tray being monitored."
                ::= { cienaCesChassisFanTrayHealthEntry 2 }

 cienaCesChassisFanTrayHealthState                              OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health Status of the fan-tray being monitored."
                ::= { cienaCesChassisFanTrayHealthEntry 3}

 --
 --FanTraySpeedMismatch
 --

 cienaCesChassisFanTraySpeedMismatchHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisFanTraySpeedMismatchHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of fan tray speed mismatch count."
        ::= { cienaCesChassisHealth 16 }

 cienaCesChassisFanTraySpeedMismatchHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisFanTraySpeedMismatchHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisFanTraySpeedMisMatchHealthTable."
        INDEX { cienaCesChassisFanTraySpeedMismatchHealthSubCategory, cienaCesChassisFanTraySpeedMismatchHealthOriginIndex}
        ::= { cienaCesChassisFanTraySpeedMismatchHealthTable 1 }

 CienaCesChassisFanTraySpeedMismatchHealthEntry ::= SEQUENCE {
        cienaCesChassisFanTraySpeedMismatchHealthSubCategory                    INTEGER,
        cienaCesChassisFanTraySpeedMismatchHealthOriginIndex                    Unsigned32,
        cienaCesChassisFanTraySpeedMismatchHealthState                          TceHealthStatus,
        cienaCesChassisFanTraySpeedMismatchHealthCurrMeasurement   INTEGER,
        cienaCesChassisFanTraySpeedMismatchHealthMaxMeasurement         INTEGER,
        cienaCesChassisFanTraySpeedMismatchHealthMaxThreshold           INTEGER
 }

 cienaCesChassisFanTraySpeedMismatchHealthSubCategory           OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         count(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the fan tray speed mismatch monitored.
                        - 'none' is an enumeration  placeholder"
                ::= { cienaCesChassisFanTraySpeedMismatchHealthEntry 1 }

 cienaCesChassisFanTraySpeedMismatchHealthOriginIndex OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the fan-tray being monitored."
                ::= { cienaCesChassisFanTraySpeedMismatchHealthEntry 2 }

 cienaCesChassisFanTraySpeedMismatchHealthState                                 OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health Status of the fan-tray being monitored."
                ::= { cienaCesChassisFanTraySpeedMismatchHealthEntry 3}

 cienaCesChassisFanTraySpeedMismatchHealthCurrMeasurement                               OBJECT-TYPE
                SYNTAX          INTEGER
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current measurement of the fan-tray mismatch speed."
                ::= { cienaCesChassisFanTraySpeedMismatchHealthEntry 4}

 cienaCesChassisFanTraySpeedMismatchHealthMaxMeasurement                                OBJECT-TYPE
                SYNTAX          INTEGER
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        " Maximum measurement of the fan-tray mismatch speed."
                ::= { cienaCesChassisFanTraySpeedMismatchHealthEntry 5}

  cienaCesChassisFanTraySpeedMismatchHealthMaxThreshold                                 OBJECT-TYPE
                SYNTAX          INTEGER
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Maximum measurement threshold of the fan-tray mismatch speed."
                ::= { cienaCesChassisFanTraySpeedMismatchHealthEntry 6}

 --
 --FanSpeedMismatch
 --

 cienaCesChassisFanSpeedMismatchHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisFanSpeedMismatchHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of fan speed mismatch count."
        ::= { cienaCesChassisHealth 17 }

 cienaCesChassisFanSpeedMismatchHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisFanSpeedMismatchHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisFanSpeedMisMatchHealthTable."
        INDEX { cienaCesChassisFanSpeedMismatchHealthSubCategory, cienaCesChassisFanSpeedMismatchHealthOriginIndex}
        ::= { cienaCesChassisFanSpeedMismatchHealthTable 1 }

 CienaCesChassisFanSpeedMismatchHealthEntry ::= SEQUENCE {
        cienaCesChassisFanSpeedMismatchHealthSubCategory                        INTEGER,
        cienaCesChassisFanSpeedMismatchHealthOriginIndex                        Unsigned32,
        cienaCesChassisFanSpeedMismatchHealthState                              TceHealthStatus
 }

 cienaCesChassisFanSpeedMismatchHealthSubCategory               OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         status(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the fan speed mismatch monitored.
                        - 'none' is an enumeration  placeholder "
                ::= { cienaCesChassisFanSpeedMismatchHealthEntry 1 }

 cienaCesChassisFanSpeedMismatchHealthOriginIndex OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the fan being monitored."
                ::= { cienaCesChassisFanSpeedMismatchHealthEntry 2 }

 cienaCesChassisFanSpeedMismatchHealthState                             OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health Status of the fan being monitored."
                ::= { cienaCesChassisFanSpeedMismatchHealthEntry 3}


 cienaCesChassisFanTempHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisFanTempHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of fan temperature resources."
        ::= { cienaCesChassisHealth 18 }

 cienaCesChassisFanTempHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisFanTempHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisHealthFanTempTable."
        INDEX { cienaCesChassisFanTempHealthSubCategory, cienaCesChassisFanTempHealthOriginIndex }
        ::= { cienaCesChassisFanTempHealthTable 1 }

 CienaCesChassisFanTempHealthEntry ::= SEQUENCE {
        cienaCesChassisFanTempHealthSubCategory                         INTEGER,
        cienaCesChassisFanTempHealthOriginIndex                     Unsigned32,
        cienaCesChassisFanTempHealthState                               TceHealthStatus,
        cienaCesChassisFanTempHealthCurrMeasurement             Unsigned32,
        cienaCesChassisFanTempHealthMinMeasurement      Unsigned32,
        cienaCesChassisFanTempHealthMaxMeasurement      Unsigned32,
        cienaCesChassisFanTempHealthMinThreshold                Unsigned32,
        cienaCesChassisFanTempHealthMaxThreshold                Unsigned32
 }

 cienaCesChassisFanTempHealthSubCategory                OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         temperature(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the fan temperature being monitored.
                        - 'none' is an enumeration  placeholder"
                ::= { cienaCesChassisFanTempHealthEntry 1 }

 cienaCesChassisFanTempHealthOriginIndex                        OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the temperature sensor being monitored."
                ::= { cienaCesChassisFanTempHealthEntry 2}

 cienaCesChassisFanTempHealthState                              OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health state of the fan temperature sub-category for the temperature sensor being monitored."
                ::= { cienaCesChassisFanTempHealthEntry 3}

 cienaCesChassisFanTempHealthCurrMeasurement            OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current temperature of fan for the temperature sensor being monitored."
                ::= { cienaCesChassisFanTempHealthEntry 4 }

 cienaCesChassisFanTempHealthMinMeasurement     OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The minimum recorded temperature for the fan for the temperature sensor being monitored."
                ::= { cienaCesChassisFanTempHealthEntry 5 }

 cienaCesChassisFanTempHealthMaxMeasurement     OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The minimum recorded temperature for the fan for the temperature sensor being monitored."
                ::= { cienaCesChassisFanTempHealthEntry 6 }

 cienaCesChassisFanTempHealthMinThreshold       OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The minimum operational temperature for the fan temperature sensor."
                ::= { cienaCesChassisFanTempHealthEntry 7 }

 cienaCesChassisFanTempHealthMaxThreshold       OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum operational temperature for the fan temperature sensor."
                ::= { cienaCesChassisFanTempHealthEntry 8 }

  --
 -- Fan samples
 --
 cienaCesChassisFanSamplesHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisFanSamplesHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of fan samples."
        ::= { cienaCesChassisHealth 19 }

 cienaCesChassisFanSamplesHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisFanSamplesHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisFanSamplesHealthTable."
        INDEX { cienaCesChassisFanSamplesHealthSubCategory, cienaCesChassisFanSamplesHealthOriginIndex }
        ::= { cienaCesChassisFanSamplesHealthTable 1 }

 CienaCesChassisFanSamplesHealthEntry ::= SEQUENCE {
        cienaCesChassisFanSamplesHealthSubCategory                      INTEGER,
        cienaCesChassisFanSamplesHealthOriginIndex                  Unsigned32,
        cienaCesChassisFanSamplesHealthState                            TceHealthStatus,
        cienaCesChassisFanSamplesHealthCurrMeasurement          INTEGER,
        cienaCesChassisFanSamplesHealthMaxMeasurement           INTEGER
 }

 cienaCesChassisFanSamplesHealthSubCategory             OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         invalid(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the fan samples being monitored.
                        - 'none' is an enumeration  placeholder
                        - 'invalid' indicates the number of invalid sensor reads
                           if the 'cienaCesChassisFanSamplesHealthCurrMeasurement' associated with this instance
                           is a huge value ,it means something is broken."
                ::= { cienaCesChassisFanSamplesHealthEntry 1 }

 cienaCesChassisFanSamplesHealthOriginIndex                     OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisFanSamplesHealthEntry 2}

 cienaCesChassisFanSamplesHealthState                           OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "fan samples state of the slot."
                ::= { cienaCesChassisFanSamplesHealthEntry 3}

 cienaCesChassisFanSamplesHealthCurrMeasurement                                 OBJECT-TYPE
                SYNTAX          INTEGER
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current measurement of fan samples."
                ::= { cienaCesChassisFanSamplesHealthEntry 4}

 cienaCesChassisFanSamplesHealthMaxMeasurement                          OBJECT-TYPE
                SYNTAX          INTEGER
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Maximum measurement of fan samples."
                ::= { cienaCesChassisFanSamplesHealthEntry 5}


 cienaCesChassisFanRPMHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisFanRPMHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of fan RPM resources."
        ::= { cienaCesChassisHealth 20 }

 cienaCesChassisFanRPMHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisFanRPMHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisFanRPMHealthTable."
        INDEX { cienaCesChassisFanRPMHealthSubCategory, cienaCesChassisFanRPMHealthOriginIndex }
        ::= { cienaCesChassisFanRPMHealthTable 1 }

 CienaCesChassisFanRPMHealthEntry ::= SEQUENCE {
        cienaCesChassisFanRPMHealthSubCategory                  INTEGER,
        cienaCesChassisFanRPMHealthOriginIndex              Unsigned32,
        cienaCesChassisFanRPMHealthState                                TceHealthStatus,
        cienaCesChassisFanRPMHealthCurrMeasurement              Unsigned32,
        cienaCesChassisFanRPMHealthMinMeasurement       Unsigned32,
        cienaCesChassisFanRPMHealthMinThreshold                 Unsigned32
 }

 cienaCesChassisFanRPMHealthSubCategory                 OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         maxSpeed(1),
                                                         minSpeed(2)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the fan RPM item being monitored.
                        - 'none' is an enumeration  placeholder"
                ::= { cienaCesChassisFanRPMHealthEntry 1 }

 cienaCesChassisFanRPMHealthOriginIndex                         OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the fan being monitored."
                ::= { cienaCesChassisFanRPMHealthEntry 2}

 cienaCesChassisFanRPMHealthState                               OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health state of the fan speed being monitored."
                ::= { cienaCesChassisFanRPMHealthEntry 3}

 cienaCesChassisFanRPMHealthCurrMeasurement             OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "RPM"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current speed value of the fan being monitored."
                ::= { cienaCesChassisFanRPMHealthEntry 4 }

 cienaCesChassisFanRPMHealthMinMeasurement      OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "RPM"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "For rows with corresponding cienaCesChassisFanRPMHealthSubCategory
                         index of maxSpeed, this object represents the maximum recorded
                         speed of the fan indexed by cienaCesChassisFanRPMHealthOriginIndex.

                         For rows with corresponding cienaCesChassisFanRPMHealthSubCategory
                         index of minSpeed, this object represents the minimum recorded
                         speed of the fan indexed by cienaCesChassisFanRPMHealthOriginIndex."
                ::= { cienaCesChassisFanRPMHealthEntry 5 }

 cienaCesChassisFanRPMHealthMinThreshold        OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "RPM"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "For rows with corresponding cienaCesChassisFanRPMHealthSubCategory
                         index of maxSpeed, this object represents the maximum speed at
                         which the fan indexed by cienaCesChassisFanRPMHealthOriginIndex
                         can be operated while maintaining a normal health state.

                         For rows with corresponding cienaCesChassisFanRPMHealthSubCategory
                         index of minSpeed, this object represents the minimum speed at
                         which the fan indexed by cienaCesChassisFanRPMHealthOriginIndex
                         can be operated while maintaining a normal health state."
                ::= { cienaCesChassisFanRPMHealthEntry 6 }

 cienaCesChassisPowerHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisPowerHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of power resources."
        ::= { cienaCesChassisHealth 21 }

 cienaCesChassisPowerHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisPowerHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisPowerHealthTable."
        INDEX {  cienaCesChassisPowerHealthSubCategory, cienaCesChassisPowerHealthOriginIndex }
        ::= { cienaCesChassisPowerHealthTable 1 }

 CienaCesChassisPowerHealthEntry ::= SEQUENCE {
        cienaCesChassisPowerHealthSubCategory                   INTEGER,
        cienaCesChassisPowerHealthOriginIndex               Unsigned32,
        cienaCesChassisPowerHealthState                                 TceHealthStatus
 }

 cienaCesChassisPowerHealthSubCategory          OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         status(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the power supply item being monitored.
                        - 'none' is an enumeration  placeholder"
                ::= { cienaCesChassisPowerHealthEntry 1 }

 cienaCesChassisPowerHealthOriginIndex                  OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the power supply being monitored."
                ::= { cienaCesChassisPowerHealthEntry 2}

 cienaCesChassisPowerHealthState                                OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health state of the power supply being monitored."
                ::= { cienaCesChassisPowerHealthEntry 3}

--
-- feed power
--

 cienaCesChassisFeedPowerHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisFeedPowerHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of power resources."
        ::= { cienaCesChassisHealth 22 }

 cienaCesChassisFeedPowerHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisFeedPowerHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisPowerHealthTable."
        INDEX {  cienaCesChassisFeedPowerHealthSubCategory, cienaCesChassisFeedPowerHealthOriginIndex }
        ::= { cienaCesChassisFeedPowerHealthTable 1 }

 CienaCesChassisFeedPowerHealthEntry ::= SEQUENCE {
        cienaCesChassisFeedPowerHealthSubCategory                       INTEGER,
        cienaCesChassisFeedPowerHealthOriginIndex                   Unsigned32,
        cienaCesChassisFeedPowerHealthState                             TceHealthStatus
 }

 cienaCesChassisFeedPowerHealthSubCategory              OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         status(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the power supply item being monitored.
                        - 'none' is an enumeration  placeholder"
                ::= { cienaCesChassisFeedPowerHealthEntry 1 }

 cienaCesChassisFeedPowerHealthOriginIndex                      OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the power supply being monitored."
                ::= { cienaCesChassisFeedPowerHealthEntry 2}

 cienaCesChassisFeedPowerHealthState                            OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health state of the power supply being monitored."
                ::= { cienaCesChassisFeedPowerHealthEntry 3}


 cienaCesChassisResourceHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisResourceHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of dataplane resources."
        ::= { cienaCesChassisHealth 23 }

 cienaCesChassisResourceHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisResourceHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisResourceHealthTable."
        INDEX {  cienaCesChassisResourceHealthSubCategory }
        ::= { cienaCesChassisResourceHealthTable 1 }

 CienaCesChassisResourceHealthEntry ::= SEQUENCE {
        cienaCesChassisResourceHealthSubCategory                        INTEGER,
        cienaCesChassisResourceHealthState                              TceHealthStatus,
        cienaCesChassisResourceHealthCurrMeasurement            Unsigned32,
        cienaCesChassisResourceHealthMaxMeasurement     Unsigned32,
        cienaCesChassisResourceHealthMaxThreshold               Unsigned32
 }

 cienaCesChassisResourceHealthSubCategory               OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         aggTable(1),
                                                         meterProfileAttachmentTable(2),
                                                         meterProfileTable(3),
                                                         pbtDecapTable(4),
                                                         pbtEncapTable(5),
                                                         pbtServiceTable(6),
                                                         pbtTransitTable(7),
                                                         pbtTunnelGroupTable(8),
                                                         pbtLocalBridgeTable(9),
                                                         pbtRemoteBridgeTable(10),
                                                         perfMonBins(11),
                                                         perfMonInstances(12),
                                                         portStateGrpTable(13),
                                                         qosFlowTable(14),
                                                         subportTable(15),
                                                         vssTable(16),
                                                         trafficClassTermTable(17),
                                                         floodContainerTable(18),
                                                         floodContainerAttachments(19),
                                                         logicalInterfaces(20),
                                                         sharedRateProfiles(21),
                                                         sharedRateAttachments(22),
                                                         sharedRateTCEs(23),
                                                         sharedRateAclTCEs(24),
                                                         shapingProfiles(25),
                                                         shapingProfileAttachments(26),
                                                         cpuSubInterfaces(27),
                                                         accessFlows(28),
                                                         vIs(29),
                                                         mcastForwardingEnhancedVswitches(30),
                                                         statsAttachments(31),
                                                         cfmServices(32),
                                                         cfmMip(33),
                                                         cfmMep(34),
                                                         cfmRMep(35),
                                                         cfmLmm(36),
                                                         cfmDmm(37),
                                                         mplsTunnelPath(38),
                                                         mplsTunnelPathHops(39),
                                                         mplsTunnelGroups(40),
                                                         mplsEncapTunnels(41),
                                                         mplsNextHopEntries(42),
                                                         mplsDecapTunnels(43),
                                                         mplsVitualSwitchs(44),
                                                         mplsVCs(45),
                                                         mplsInterfaces(46),
                                                         vplsPeMeshGroups(47),
                                                         resolvedCosProfileTable(48),
                                                         resolvedCosMapTable(49),
                                                         frameCosMapTable(50),
                                                         congestionAvoidanceProfileTable(51),
                                                         rCosQueueMapTable(52),
                                                         queueGroupProfileTable(53),
                                                         queueGroupInstanceTable(54),
                                                         schedulerProfileTable(55),
                                                         schedulerInstanceTable(56),
                                                         smacId(57),
                                                         multicastGroup(58),
                                                         pltfmVfd(59),
                                                         pltfmMcGroupMap(60),
                                                         pltfmVInterface(61),
                                                         pltfmVpldIndex(62),
                                                         pltfmReservedMac(63),
                                                                 pltfmRateProfile(64),
                                                         pltfmTokenBucket(65),
                                                         pltfmResolvedCosMap(66),
                                                         pltfmFrameCopsMap(67),
                                                         pltfmTunnelGroup(68),
                                                         pltfmEgressTunnel(69),
                                                         pltfmVirtualTcam(70),
                                                         pltfmAclTcam(71),
                                                                 pltfmMstpInstance(72),
                                                         pltfmFlushGroupInstance(73),
                                                         pltfmVOQ(74),
                                                         pltfmCLScheduler(75),
                                                         pltfmFQScheduler(76),
                                                         pltfmSchedulerFlow(77),
                                                         pltfmDestFap(78),
                                                         pltfmMplsEncapIndex(79),
                                                         pltfmMplsVpldIndex(80),
                                                         pltfmEgressArpIndex(81),
                                                         pltfmEgressShapingCir(82),
                                                         mplsTransitTunnels(83),
                                                         pltfmLocalDestIndex(84),
                                                         pltfmBscp(85),
                                                         pltfmHighRateTokenBucket(86),
                                                         pltfmLowRateTokenBucket(87),
                                                         pltfmParentMeter(88),
                                                         pltfmChildMeter(89),
                                                         pltfmL2UserTypes(90),
                                                         pltfmLocalBridgeMacs(91),
                                                         pltfmPpRif(92),
                                                         pltfmLmPowerBudget(93),
                                                         pltfmPpIngressL2Xform(94),
                                                         pltfmPpEgressL2xform(95),
                                                         pltfmPpInternalTcam(96),
                                                         pltfmPpFECPointer(97),
                                                         ethLpTable(98),
                                                         pltfmPpFECPointerVRing(99),
                                                         l2CftProfile(100),
                                                         pfgProfile(101),
                                                         pltfmNpMaintPoint(102),
                                                         pltfmNpMaintPointSession(103),
                                                         pltfmNpFastTimer300Hz(104),
                                                         pltfmNpFastTimer10msec(105),
                                                         pltfmNpFastTimer100msec(106),
                                                         pltfmNpFastTimer1sec(107),
                                                         pltfmNpSlowTimer(108),
                                                         pltfmNpWatchdogTimer(109),
                                                         pltfmNpProtectionGroup(110),
                                                         benchmarkReflectorProfile(111),
                                                         aisSession(112)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the dataplane resource being monitored.
                        - 'none' is an enumeration  placeholder"
                ::= { cienaCesChassisResourceHealthEntry 1 }

 cienaCesChassisResourceHealthState                             OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health state of the dataplane resource being monitored."
                ::= { cienaCesChassisResourceHealthEntry 2}

 cienaCesChassisResourceHealthCurrMeasurement           OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current value of the dataplane resource being monitored."
                ::= { cienaCesChassisResourceHealthEntry 3 }

 cienaCesChassisResourceHealthMaxMeasurement    OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum recorded value of the dataplane resource being monitored."
                ::= { cienaCesChassisResourceHealthEntry 4 }

 cienaCesChassisResourceHealthMaxThreshold      OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum possible value for the dataplane resource being monitored."
                ::= { cienaCesChassisResourceHealthEntry 5 }


 cienaCesChassisMemoryHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisMemoryHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of memory resources."
        ::= { cienaCesChassisHealth 24 }

 cienaCesChassisMemoryHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisMemoryHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisMemoryHealthTable."
        INDEX {  cienaCesChassisMemoryHealthSubCategory, cienaCesChassisMemoryHealthOriginIndex }
        ::= { cienaCesChassisMemoryHealthTable 1 }

 CienaCesChassisMemoryHealthEntry ::= SEQUENCE {
        cienaCesChassisMemoryHealthSubCategory          INTEGER,
        cienaCesChassisMemoryHealthOriginIndex          Unsigned32,
        cienaCesChassisMemoryHealthState                TceHealthStatus,
        cienaCesChassisMemoryHealthCurrMeasurement      Unsigned32,
        cienaCesChassisMemoryHealthMaxMeasurement       Unsigned32,
        cienaCesChassisMemoryHealthMaxThreshold         Unsigned32
 }

 cienaCesChassisMemoryHealthSubCategory                 OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         globalHeap(1),
                                                         heap1(2),
                                                         heap2(3),
                                                         pool1(4),
                                                         pool2(5),
                                                         heap(6)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the memory being monitored.
                        - 'none' is an enumeration  placeholder"
                ::= { cienaCesChassisMemoryHealthEntry 1 }

 cienaCesChassisMemoryHealthOriginIndex                         OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisMemoryHealthEntry 2}

 cienaCesChassisMemoryHealthState                               OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health state of the memory sub-category being monitored."
                ::= { cienaCesChassisMemoryHealthEntry 3}

 cienaCesChassisMemoryHealthCurrMeasurement             OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "KB"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current value of the memory sub-category being monitored."
                ::= { cienaCesChassisMemoryHealthEntry 4 }

 cienaCesChassisMemoryHealthMaxMeasurement      OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "KB"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum recorded value of the memory sub-category being monitored."
                ::= { cienaCesChassisMemoryHealthEntry 5 }

 cienaCesChassisMemoryHealthMaxThreshold        OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "KB"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum possible value of the memory sub-category being monitored."
                ::= { cienaCesChassisMemoryHealthEntry 6 }


 cienaCesChassisMACHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisMACHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of mac-table resources."
        ::= { cienaCesChassisHealth 25 }

 cienaCesChassisMACHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisMACHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisMACHealthTable."
        INDEX { cienaCesChassisMACHealthSubCategory, cienaCesChassisMACHealthOriginIndex }
        ::= { cienaCesChassisMACHealthTable 1 }

 CienaCesChassisMACHealthEntry ::= SEQUENCE {
        cienaCesChassisMACHealthSubCategory                     INTEGER,
        cienaCesChassisMACHealthOriginIndex                 Unsigned32,
        cienaCesChassisMACHealthState                           TceHealthStatus,
        cienaCesChassisMACHealthCurrMeasurement                 Unsigned32,
        cienaCesChassisMACHealthMaxMeasurement  Unsigned32,
        cienaCesChassisMACHealthMaxThreshold            Unsigned32
 }

 cienaCesChassisMACHealthSubCategory            OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         mac-Table(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the MAC table being monitored.
                        - 'none' is an enumeration  placeholder"
                ::= { cienaCesChassisMACHealthEntry 1 }

 cienaCesChassisMACHealthOriginIndex                    OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisMACHealthEntry 2}

 cienaCesChassisMACHealthState                          OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health state of the system MAC table for the slot being monitored."
                ::= { cienaCesChassisMACHealthEntry 3}

 cienaCesChassisMACHealthCurrMeasurement                OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current number of entries in the MAC table for the slot being monitored."
                ::= { cienaCesChassisMACHealthEntry 4 }

 cienaCesChassisMACHealthMaxMeasurement         OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum recorded number of entries in the MAC table for the slot being monitored."
                ::= { cienaCesChassisMACHealthEntry 5 }

 cienaCesChassisMACHealthMaxThreshold   OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum possible number of entries in the MAC table for the slot being monitored."
                ::= { cienaCesChassisMACHealthEntry 6 }


 cienaCesChassisI2CHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisI2CHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of I2C based resources."
        ::= { cienaCesChassisHealth 26 }

 cienaCesChassisI2CHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisI2CHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisI2CHealthTable."
        INDEX {  cienaCesChassisI2CHealthSubCategory, cienaCesChassisI2CHealthOriginIndex }
        ::= { cienaCesChassisI2CHealthTable 1 }

 CienaCesChassisI2CHealthEntry ::= SEQUENCE {
        cienaCesChassisI2CHealthSubCategory                     INTEGER,
        cienaCesChassisI2CHealthOriginIndex                 Unsigned32,
        cienaCesChassisI2CHealthState                           TceHealthStatus
 }

 cienaCesChassisI2CHealthSubCategory            OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                     pS1(1),
                                                         pS2(2),
                                                         alarmCard(3),
                                                         fanTray(4),
                                                         iOM3(5),
                                                         iOM4(6),
                                                         iOM5(7),
                                                         iOM6(8),
                                                         iOM7(9),
                                                         pduA1(10),
                                                         pduB1(11),
                                                         pduA2(12),
                                                         pduB2(13),
                                                         pduA3(14),
                                                         pduB3(15),
                                                         pduA4(16),
                                                         pduB4(17),
                                                         cfu1(18),
                                                         cfu2(19),
                                                         cfu3(20),
                                                         cfu4(21),
                                                         pslm1(22),
                                                         pslm2(23),
                                                         pslm3(24),
                                                         pslm4(25),
                                                         pslm5(26),
                                                         pslm6(27),
                                                         pslm7(28),
                                                         pslm8(29),
                                                         pslm9(30),
                                                         pslm10(31),
                                                         pslm11(32),
                                                         pslm12(33),
                                                         pslm13(34),
                                                         pslm14(35),
                                                         pslm15(36),
                                                         pslm16(37),
                                                         pslm17(38),
                                                         pslm18(39),
                                                         pslm19(40),
                                                         pslm20(41),
                                                         sm1(42),
                                                         sm2(43),
                                                         sm3(44),
                                                         sm4(45),
                                                         sm5(46),
                                                         iom(47)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the I2C driver being monitored.
                        - 'none' is an enumeration  placeholder"
                ::= { cienaCesChassisI2CHealthEntry 1 }

 cienaCesChassisI2CHealthOriginIndex                    OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisI2CHealthEntry 3}

 cienaCesChassisI2CHealthState                          OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health state of the I2C driver for the slot being monitored."
                ::= { cienaCesChassisI2CHealthEntry 4}


 cienaCesChassisFlashDriverHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisFlashDriverHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of flash driver resources."
        ::= { cienaCesChassisHealth 27 }

 cienaCesChassisFlashDriverHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisFlashDriverHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisFlashDriverHealthTable."
        INDEX { cienaCesChassisFlashDriverHealthSubCategory, cienaCesChassisFlashDriverHealthOriginIndex }
        ::= { cienaCesChassisFlashDriverHealthTable 1 }

 CienaCesChassisFlashDriverHealthEntry ::= SEQUENCE {
        cienaCesChassisFlashDriverHealthSubCategory                     INTEGER,
        cienaCesChassisFlashDriverHealthOriginIndex                 Unsigned32,
        cienaCesChassisFlashDriverHealthState                           TceHealthStatus,
        cienaCesChassisFlashDriverHealthCurrMeasurement                 Unsigned32,
        cienaCesChassisFlashDriverHealthMaxMeasurement  Unsigned32
 }

 cienaCesChassisFlashDriverHealthSubCategory            OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         writeErasePart1(1),
                                                         writeErasePart2(2)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the health-item being monitored.
                        - 'none' is an enumeration  placeholder"
                ::= { cienaCesChassisFlashDriverHealthEntry 1 }

 cienaCesChassisFlashDriverHealthOriginIndex                    OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisFlashDriverHealthEntry 2}

 cienaCesChassisFlashDriverHealthState                          OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health state of the flash driver sub-category for the slot being monitored."
                ::= { cienaCesChassisFlashDriverHealthEntry 3}

 cienaCesChassisFlashDriverHealthCurrMeasurement                OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "ms"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current value of the flash driver sub-category for the slot being monitored."
                ::= { cienaCesChassisFlashDriverHealthEntry 4 }

 cienaCesChassisFlashDriverHealthMaxMeasurement         OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "ms"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum value of the flash driver sub-category for the slot being monitored."
                ::= { cienaCesChassisFlashDriverHealthEntry 5 }


 cienaCesChassisXcvrHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisXcvrHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of transciever resources."
        ::= { cienaCesChassisHealth 28 }

 cienaCesChassisXcvrHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisXcvrHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisXcvrHealthTable."
        INDEX { cienaCesChassisXcvrHealthSubCategory, cienaCesChassisXcvrHealthOriginIndex }
        ::= { cienaCesChassisXcvrHealthTable 1 }

 CienaCesChassisXcvrHealthEntry ::= SEQUENCE {
        cienaCesChassisXcvrHealthSubCategory                    INTEGER,
        cienaCesChassisXcvrHealthOriginIndex                Unsigned32,
        cienaCesChassisXcvrHealthState                          TceHealthStatus,
        cienaCesChassisXcvrHealthCurrMeasurement                Unsigned32,
        cienaCesChassisXcvrHealthMinMeasurement         Unsigned32,
        cienaCesChassisXcvrHealthMaxMeasurement         Unsigned32,
        cienaCesChassisXcvrHealthMinThreshold           Unsigned32,
        cienaCesChassisXcvrHealthMaxThreshold           Unsigned32,
        cienaCesChassisXcvrHealthUnit                           INTEGER
 }

 --has multiple units
 cienaCesChassisXcvrHealthSubCategory           OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         temp(1),
                                                         rxPower(2),
                                                         txPower(3),
                                                         bias(4),
                                                         vcc(5),
                                                         rxPowerLane1(6),
                                                         rxPowerLane2(7),
                                                         rxPowerLane3(8),
                                                         rxPowerLane4(9),
                                                         rxPowerLane5(10),
                                                         rxPowerLane6(11),
                                                         rxPowerLane7(12),
                                                         rxPowerLane8(13),
                                                         rxPowerLane9(14),
                                                         rxPowerLane10(15),
                                                         rxPowerLane11(16),
                                                         rxPowerLane12(17),
                                                         rxPowerLane13(18),
                                                         rxPowerLane14(19),
                                                         rxPowerLane15(20),
                                                         rxPowerLane16(21),
                                                         txPowerLane1(22),
                                                         txPowerLane2(23),
                                                         txPowerLane3(24),
                                                         txPowerLane4(25),
                                                         txPowerLane5(26),
                                                         txPowerLane6(27),
                                                         txPowerLane7(28),
                                                         txPowerLane8(29),
                                                         txPowerLane9(30),
                                                         txPowerLane10(31),
                                                         txPowerLane11(32),
                                                         txPowerLane12(33),
                                                         txPowerLane13(34),
                                                         txPowerLane14(35),
                                                         txPowerLane15(36),
                                                         txPowerLane16(37)
                                                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the transceiver being monitored.
                        - 'none' is an enumeration  placeholder"
                ::= { cienaCesChassisXcvrHealthEntry 1 }

 cienaCesChassisXcvrHealthOriginIndex                   OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Pgid of the transceiver being monitored."
                ::= { cienaCesChassisXcvrHealthEntry 2}

 cienaCesChassisXcvrHealthState                                 OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health state of the transceiver sub-category for the pgid being monitored."
                ::= { cienaCesChassisXcvrHealthEntry 3}

 cienaCesChassisXcvrHealthCurrMeasurement               OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current value of the transceiver sub-category for the pgid being monitored."
                ::= { cienaCesChassisXcvrHealthEntry 4 }

 cienaCesChassisXcvrHealthMinMeasurement        OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The minimum recorded value of the transceiver sub-category for the pgid being monitored."
                ::= { cienaCesChassisXcvrHealthEntry 5 }

 cienaCesChassisXcvrHealthMaxMeasurement        OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum recorded value of the transceiver sub-category for the pgid being monitored."
                ::= { cienaCesChassisXcvrHealthEntry 6 }

 cienaCesChassisXcvrHealthMinThreshold          OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The minimum operational value for the transceiver sub-category for the pgid being monitored."
                ::= { cienaCesChassisXcvrHealthEntry 7 }

 cienaCesChassisXcvrHealthMaxThreshold          OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum operational value for the transceiver sub-category for the pgid being monitored."
                ::= { cienaCesChassisXcvrHealthEntry 8 }

 cienaCesChassisXcvrHealthUnit                                  OBJECT-TYPE
                SYNTAX          INTEGER {
                                                         deg-C(1),
                                                         milli-watts(2)
                }
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The unit applicable to the current, minimum, maximum, minimum threshold and
                        maximum threshold values of the transceiver sub-category being monitored."
                ::= { cienaCesChassisXcvrHealthEntry 9 }

 cienaCesChassisPortLinkHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisPortLinkHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of port link based resources."
        ::= { cienaCesChassisHealth 29 }

 cienaCesChassisPortLinkHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisPortLinkHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisPortLinkHealthTable."
        INDEX {  cienaCesChassisPortLinkHealthSubCategory, cienaCesChassisPortLinkHealthOriginIndex }
        ::= { cienaCesChassisPortLinkHealthTable 1 }

 CienaCesChassisPortLinkHealthEntry ::= SEQUENCE {
        cienaCesChassisPortLinkHealthSubCategory                        INTEGER,
        cienaCesChassisPortLinkHealthOriginIndex                    Unsigned32,
        cienaCesChassisPortLinkHealthState                              TceHealthStatus
 }

 cienaCesChassisPortLinkHealthSubCategory               OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         portLink-State(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the port link being monitored.
                        - 'none' is an enumeration  placeholder"
                ::= { cienaCesChassisPortLinkHealthEntry 1 }

 cienaCesChassisPortLinkHealthOriginIndex                       OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Pgid of the port being monitored."
                ::= { cienaCesChassisPortLinkHealthEntry 2}

 cienaCesChassisPortLinkHealthState                             OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health state of the port link for the port being monitored."
                ::= { cienaCesChassisPortLinkHealthEntry 3}

--
-- iom status
--

 cienaCesChassisIOMStatusHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisIOMStatusHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of IOM."
        ::= { cienaCesChassisHealth 30 }

 cienaCesChassisIOMStatusHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisIOMStatusHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisIOMStatusHealthTable."
        INDEX {  cienaCesChassisIOMStatusHealthSubCategory, cienaCesChassisIOMStatusHealthOriginIndex }
        ::= { cienaCesChassisIOMStatusHealthTable 1 }

 CienaCesChassisIOMStatusHealthEntry ::= SEQUENCE {
        cienaCesChassisIOMStatusHealthSubCategory                       INTEGER,
        cienaCesChassisIOMStatusHealthOriginIndex                   Unsigned32,
        cienaCesChassisIOMStatusHealthState                             TceHealthStatus
 }

 cienaCesChassisIOMStatusHealthSubCategory              OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         state(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the IOM being monitored.
                        - 'none' is an enumeration  placeholder"
                ::= { cienaCesChassisIOMStatusHealthEntry 1 }

 cienaCesChassisIOMStatusHealthOriginIndex                      OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "IOM slot being monitored."
                ::= { cienaCesChassisIOMStatusHealthEntry 2}

 cienaCesChassisIOMStatusHealthState                            OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health state of the IOM for the slot being monitored."
                ::= { cienaCesChassisIOMStatusHealthEntry 3}


--
-- linx stats
--

 cienaCesChassisLinxStatHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisLinxStatHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of linx stats."
        ::= { cienaCesChassisHealth 31 }

 cienaCesChassisLinxStatHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisLinxStatHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisLinxStatHealthTable."
        INDEX { cienaCesChassisLinxStatHealthSubCategory, cienaCesChassisLinxStatHealthOriginIndex }
        ::= { cienaCesChassisLinxStatHealthTable 1 }

 CienaCesChassisLinxStatHealthEntry ::= SEQUENCE {
        cienaCesChassisLinxStatHealthSubCategory                INTEGER,
        cienaCesChassisLinxStatHealthOriginIndex                Unsigned32,
        cienaCesChassisLinxStatHealthState                              TceHealthStatus,
        cienaCesChassisLinxStatHealthCurrMeasurement    Unsigned32,
        cienaCesChassisLinxStatHealthMaxMeasurement     Unsigned32,
        cienaCesChassisLinxStatHealthMaxThreshold               Unsigned32
 }

 cienaCesChassisLinxStatHealthSubCategory               OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         timers(1),
                                                         attachments(2)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the health-item being monitored.
                        - 'none' is an enumeration  placeholder"
                ::= { cienaCesChassisLinxStatHealthEntry 1 }

 cienaCesChassisLinxStatHealthOriginIndex                       OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisLinxStatHealthEntry 2}

 cienaCesChassisLinxStatHealthState                             OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "LinxStat sub-category utilization state of the slot."
                ::= { cienaCesChassisLinxStatHealthEntry 3}

 cienaCesChassisLinxStatHealthCurrMeasurement           OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current value of LinxStat sub-category used for the slot."
                ::= { cienaCesChassisLinxStatHealthEntry 4 }

 cienaCesChassisLinxStatHealthMaxMeasurement    OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum recorded value of LinxStat sub-category used by the slot."
                ::= { cienaCesChassisLinxStatHealthEntry 5 }

 cienaCesChassisLinxStatHealthMaxThreshold      OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum size of the LinxStat sub-category."
                ::= { cienaCesChassisLinxStatHealthEntry 6 }

 --
 -- SM fabric health
 --
  cienaCesChassisSMFabricHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisSMFabricHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of SM fabric resources."
        ::= { cienaCesChassisHealth 32 }

 cienaCesChassisSMFabricHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisSMFabricHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisSMFabricHealthTable."
        INDEX { cienaCesChassisSMFabricHealthSubCategory, cienaCesChassisSMFabricHealthOriginIndex }
        ::= { cienaCesChassisSMFabricHealthTable 1 }

 CienaCesChassisSMFabricHealthEntry ::= SEQUENCE {
        cienaCesChassisSMFabricHealthSubCategory                        INTEGER,
        cienaCesChassisSMFabricHealthOriginIndex                    Unsigned32,
        cienaCesChassisSMFabricHealthState                              TceHealthStatus
 }

 cienaCesChassisSMFabricHealthSubCategory               OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         status(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the SM fabric being monitored.
                        - 'none' is an enumeration  placeholder "
                ::= { cienaCesChassisSMFabricHealthEntry 1 }


 cienaCesChassisSMFabricHealthOriginIndex               OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisSMFabricHealthEntry 2}

 cienaCesChassisSMFabricHealthState                             OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health state of the SM fabric sub-category."
                ::= { cienaCesChassisSMFabricHealthEntry 3}

 --
 -- SPI health
 --
 cienaCesChassisSPIHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisSPIHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of SPI based resources."
        ::= { cienaCesChassisHealth 33 }

 cienaCesChassisSPIHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisSPIHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisSPIHealthTable."
        INDEX {  cienaCesChassisSPIHealthSubCategory, cienaCesChassisSPIHealthOriginIndex }
        ::= { cienaCesChassisSPIHealthTable 1 }

 CienaCesChassisSPIHealthEntry ::= SEQUENCE {
        cienaCesChassisSPIHealthSubCategory                     INTEGER,
        cienaCesChassisSPIHealthOriginIndex                 Unsigned32,
        cienaCesChassisSPIHealthState                           TceHealthStatus
 }

 cienaCesChassisSPIHealthSubCategory            OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         cfu1(1),
                                                         cfu2(2),
                                                         iom(3)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the SPI driver being monitored.
                        - 'none' is an enumeration  placeholder"
                ::= { cienaCesChassisSPIHealthEntry 1 }

 cienaCesChassisSPIHealthOriginIndex                    OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisSPIHealthEntry 3}

 cienaCesChassisSPIHealthState                          OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health state of the SPI driver for the slot being monitored."
                ::= { cienaCesChassisSPIHealthEntry 4}

 --
 -- UsbFlash health
 --
 cienaCesChassisUsbFlashHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisUsbFlashHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of Usb Flash based resources."
        ::= { cienaCesChassisHealth 34 }

 cienaCesChassisUsbFlashHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisUsbFlashHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisUsbFlashHealthTable."
        INDEX {  cienaCesChassisUsbFlashHealthSubCategory, cienaCesChassisUsbFlashHealthOriginIndex }
        ::= { cienaCesChassisUsbFlashHealthTable 1 }

 CienaCesChassisUsbFlashHealthEntry ::= SEQUENCE {
        cienaCesChassisUsbFlashHealthSubCategory                        INTEGER,
        cienaCesChassisUsbFlashHealthOriginIndex                    Unsigned32,
        cienaCesChassisUsbFlashHealthState                              TceHealthStatus
 }

 cienaCesChassisUsbFlashHealthSubCategory               OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         devicePresent(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the Usb Flash being monitored.
                        - 'none' is an enumeration  placeholder"
                ::= { cienaCesChassisUsbFlashHealthEntry 1 }

 cienaCesChassisUsbFlashHealthOriginIndex                       OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisUsbFlashHealthEntry 3}

 cienaCesChassisUsbFlashHealthState                             OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health state of the Usb Flash for the slot being monitored."
                ::= { cienaCesChassisUsbFlashHealthEntry 4}

 --
 -- IOM temp health
 --
  cienaCesChassisIomTempHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisIomTempHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of IOM temperature resources."
        ::= { cienaCesChassisHealth 35 }

 cienaCesChassisIomTempHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisIomTempHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisIomTempHealthTable."
        INDEX { cienaCesChassisIomTempHealthSubCategory, cienaCesChassisIomTempHealthOriginIndex }
        ::= { cienaCesChassisIomTempHealthTable 1 }

 CienaCesChassisIomTempHealthEntry ::= SEQUENCE {
        cienaCesChassisIomTempHealthSubCategory                         INTEGER,
        cienaCesChassisIomTempHealthOriginIndex                     Unsigned32,
        cienaCesChassisIomTempHealthState                               TceHealthStatus,
        cienaCesChassisIomTempHealthCurrMeasurement             Unsigned32,
        cienaCesChassisIomTempHealthMinMeasurement              Unsigned32,
        cienaCesChassisIomTempHealthMaxMeasurement              Unsigned32,
        cienaCesChassisIomTempHealthMinThreshold                Unsigned32,
                cienaCesChassisIomTempHealthMaxThreshold                Unsigned32
 }

 cienaCesChassisIomTempHealthSubCategory                OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         temperature(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the IOM temperature being monitored.
                        - 'none' is an enumeration  placeholder "
                ::= { cienaCesChassisIomTempHealthEntry 1 }


 cienaCesChassisIomTempHealthOriginIndex                OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisIomTempHealthEntry 2}

 cienaCesChassisIomTempHealthState                              OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Temperature state of the IOM temperature sub-category."
                ::= { cienaCesChassisIomTempHealthEntry 3}

 cienaCesChassisIomTempHealthCurrMeasurement            OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current temperature of the IOM temperature sub-category."
                ::= { cienaCesChassisIomTempHealthEntry 4 }

 cienaCesChassisIomTempHealthMinMeasurement     OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The minimum recorded temperature reached by the IOM temperature sub-category."
                ::= { cienaCesChassisIomTempHealthEntry 5 }

 cienaCesChassisIomTempHealthMaxMeasurement     OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum recorded temperature reached by the IOM temperature sub-category."
                ::= { cienaCesChassisIomTempHealthEntry 6 }


 cienaCesChassisIomTempHealthMinThreshold       OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The minimum operational temperature of the IOM temperature sub-category."
                ::= { cienaCesChassisIomTempHealthEntry 7 }

 cienaCesChassisIomTempHealthMaxThreshold                       OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum operational temperature of the IOM temperature sub-category."
                ::= { cienaCesChassisIomTempHealthEntry 8 }

 --
 -- Power Parameters health
 --
  cienaCesChassisPowerParamsHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisPowerParamsHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of Power Supply parameters."
        ::= { cienaCesChassisHealth 36 }

 cienaCesChassisPowerParamsHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisPowerParamsHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisPowerParamsHealthTable."
        INDEX { cienaCesChassisPowerParamsHealthSubCategory, cienaCesChassisPowerParamsHealthOriginIndex }
        ::= { cienaCesChassisPowerParamsHealthTable 1 }

 CienaCesChassisPowerParamsHealthEntry ::= SEQUENCE {
        cienaCesChassisPowerParamsHealthSubCategory             INTEGER,
        cienaCesChassisPowerParamsHealthOriginIndex             Unsigned32,
        cienaCesChassisPowerParamsHealthState                   TceHealthStatus
 }

 cienaCesChassisPowerParamsHealthSubCategory            OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         loadFuse(1),
                                                         internalFuse(2),
                                                         voltageRegulator(3),
                                                         temperature(4),
                                                         acInput(5),
                                                         overloadProtection(6),
                                                         fan(7)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the Power Params being monitored.
                        - 'none' is an enumeration  placeholder "
                ::= { cienaCesChassisPowerParamsHealthEntry 1 }


 cienaCesChassisPowerParamsHealthOriginIndex            OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisPowerParamsHealthEntry 2}

 cienaCesChassisPowerParamsHealthState                          OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current status of the Power Parameters  sub-category."
                ::= { cienaCesChassisPowerParamsHealthEntry 3}

 --
 -- Power Output Voltage health
 --
  cienaCesChassisPowerOutputVoltageHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisPowerOutputVoltageHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of Power Supply output voltage."
        ::= { cienaCesChassisHealth 37 }

 cienaCesChassisPowerOutputVoltageHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisPowerOutputVoltageHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisPowerOutputVoltageHealthTable."
        INDEX { cienaCesChassisPowerOutputVoltageHealthSubCategory, cienaCesChassisPowerOutputVoltageHealthOriginIndex }
        ::= { cienaCesChassisPowerOutputVoltageHealthTable 1 }

 CienaCesChassisPowerOutputVoltageHealthEntry ::= SEQUENCE {
        cienaCesChassisPowerOutputVoltageHealthSubCategory              INTEGER,
        cienaCesChassisPowerOutputVoltageHealthOriginIndex              Unsigned32,
        cienaCesChassisPowerOutputVoltageHealthState                    TceHealthStatus,
        cienaCesChassisPowerOutputVoltageHealthCurrMeasurement                  Integer32,
        cienaCesChassisPowerOutputVoltageHealthMinMeasurement           Integer32,
        cienaCesChassisPowerOutputVoltageHealthMaxMeasurement           Integer32,
        cienaCesChassisPowerOutputVoltageHealthMinThreshold             Integer32,
        cienaCesChassisPowerOutputVoltageHealthMaxThreshold             Integer32
 }

 cienaCesChassisPowerOutputVoltageHealthSubCategory             OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         voltage(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the Power Supply output voltage being monitored.
                        - 'none' is an enumeration  placeholder "
                ::= { cienaCesChassisPowerOutputVoltageHealthEntry 1 }


 cienaCesChassisPowerOutputVoltageHealthOriginIndex             OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisPowerOutputVoltageHealthEntry 2}

 cienaCesChassisPowerOutputVoltageHealthState                           OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Voltage state of the Output Voltage sub-category."
                ::= { cienaCesChassisPowerOutputVoltageHealthEntry 3}

 cienaCesChassisPowerOutputVoltageHealthCurrMeasurement                 OBJECT-TYPE
                SYNTAX          Integer32
                UNITS           "mV"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current voltage of the Output VOltage sub-category."
                ::= { cienaCesChassisPowerOutputVoltageHealthEntry 4 }

 cienaCesChassisPowerOutputVoltageHealthMinMeasurement  OBJECT-TYPE
                SYNTAX          Integer32
                UNITS           "mV"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The minimum recorded voltage reached by the Power Supply output voltage sub-category."
                ::= { cienaCesChassisPowerOutputVoltageHealthEntry 5 }

 cienaCesChassisPowerOutputVoltageHealthMaxMeasurement  OBJECT-TYPE
                SYNTAX          Integer32
                UNITS           "mV"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum recorded voltage reached by the Power Supply output voltage sub-category."
                ::= { cienaCesChassisPowerOutputVoltageHealthEntry 6 }


 cienaCesChassisPowerOutputVoltageHealthMinThreshold    OBJECT-TYPE
                SYNTAX          Integer32
                UNITS           "mV"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The minimum operational voltage of the Power Supply output voltage sub-category."
                ::= { cienaCesChassisPowerOutputVoltageHealthEntry 7 }

 cienaCesChassisPowerOutputVoltageHealthMaxThreshold                    OBJECT-TYPE
                SYNTAX          Integer32
                UNITS           "mV"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum operational voltage of the Power Supply output voltage sub-category."
                ::= { cienaCesChassisPowerOutputVoltageHealthEntry 8 }

--
-- Modem Temp Health
--
 cienaCesChassisModemTempHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisModemTempHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of CSLM modem temperatures."
        ::= { cienaCesChassisHealth 38 }

 cienaCesChassisModemTempHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisModemTempHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisModemTempHealthTable."
        INDEX { cienaCesChassisModemTempHealthSubCategory, cienaCesChassisModemTempHealthOriginIndex }
        ::= { cienaCesChassisModemTempHealthTable 1 }

 CienaCesChassisModemTempHealthEntry ::= SEQUENCE {
        cienaCesChassisModemTempHealthSubCategory        INTEGER,
        cienaCesChassisModemTempHealthOriginIndex        Unsigned32,
        cienaCesChassisModemTempHealthState              TceHealthStatus,
        cienaCesChassisModemTempHealthCurrMeasurement    Unsigned32,
        cienaCesChassisModemTempHealthMinMeasurement     Unsigned32,
        cienaCesChassisModemTempHealthMaxMeasurement     Unsigned32,
        cienaCesChassisModemTempHealthMinThreshold       Unsigned32,
        cienaCesChassisModemTempHealthMaxThreshold       Unsigned32
 }

 cienaCesChassisModemTempHealthSubCategory              OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         sensor1(1),
                                                         sensor2(2),
                                                         sensor3(3),
                                                         sensor4(4),
                                                         sensor5(5)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the CSLM modem temp being monitored.
                        - 'none' is an enumeration  placeholder "
                ::= { cienaCesChassisModemTempHealthEntry 1 }

 cienaCesChassisModemTempHealthOriginIndex                      OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisModemTempHealthEntry 2}

 cienaCesChassisModemTempHealthState                            OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Temperature state of the CSLM modem temp sub-category being monitored."
                ::= { cienaCesChassisModemTempHealthEntry 3}

 cienaCesChassisModemTempHealthCurrMeasurement          OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current temperature of the modem temperature sub-category."
                ::= { cienaCesChassisModemTempHealthEntry 4 }

 cienaCesChassisModemTempHealthMinMeasurement   OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The minimum recorded temperature reached by the modem temperature sub-category."
                ::= { cienaCesChassisModemTempHealthEntry 5 }

 cienaCesChassisModemTempHealthMaxMeasurement   OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum recorded temperature reached by the modem temperature sub-category."
                ::= { cienaCesChassisModemTempHealthEntry 6 }


 cienaCesChassisModemTempHealthMinThreshold     OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The minimum operational temperature of the modem temperature sub-category."
                ::= { cienaCesChassisModemTempHealthEntry 7 }

 cienaCesChassisModemTempHealthMaxThreshold                     OBJECT-TYPE
                SYNTAX          Unsigned32
                UNITS           "deg C"
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum operational temperature of the modem temperature sub-category."
                ::= { cienaCesChassisModemTempHealthEntry 8 }

--
-- Modem Watermark Health
--
 cienaCesChassisModemWatermarkHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisModemWatermarkHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of CSLM watermarks."
        ::= { cienaCesChassisHealth 39 }

 cienaCesChassisModemWatermarkHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisModemWatermarkHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisModemWatermarkHealthTable."
        INDEX { cienaCesChassisModemWatermarkHealthSubCategory, cienaCesChassisModemWatermarkHealthOriginIndex }
        ::= { cienaCesChassisModemWatermarkHealthTable 1 }

 CienaCesChassisModemWatermarkHealthEntry ::= SEQUENCE {
        cienaCesChassisModemWatermarkHealthSubCategory                  INTEGER,
        cienaCesChassisModemWatermarkHealthOriginIndex             Unsigned32,
        cienaCesChassisModemWatermarkHealthState                                TceHealthStatus
 }

 cienaCesChassisModemWatermarkHealthSubCategory                 OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         watermark(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the CSLM modem watermark being monitored.
                        - 'none' is an enumeration  placeholder "
                ::= { cienaCesChassisModemWatermarkHealthEntry 1 }

 cienaCesChassisModemWatermarkHealthOriginIndex                         OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisModemWatermarkHealthEntry 2}

 cienaCesChassisModemWatermarkHealthState                               OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Modem watermark state of the slot."
                ::= { cienaCesChassisModemWatermarkHealthEntry 3}


--
-- File Descriptor Health
--
 cienaCesChassisFileDescriptorHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisFileDescriptorHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of file descriptor resources."
        ::= { cienaCesChassisHealth 40 }

 cienaCesChassisFileDescriptorHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisFileDescriptorHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisFileDescriptorHealthTable."
        INDEX {  cienaCesChassisFileDescriptorHealthSubCategory, cienaCesChassisFileDescriptorHealthOriginIndex }
        ::= { cienaCesChassisFileDescriptorHealthTable 1 }

 CienaCesChassisFileDescriptorHealthEntry ::= SEQUENCE {
        cienaCesChassisFileDescriptorHealthSubCategory          INTEGER,
        cienaCesChassisFileDescriptorHealthOriginIndex          Unsigned32,
        cienaCesChassisFileDescriptorHealthState                TceHealthStatus,
        cienaCesChassisFileDescriptorHealthCurrMeasurement      Unsigned32,
        cienaCesChassisFileDescriptorHealthMaxMeasurement       Unsigned32,
        cienaCesChassisFileDescriptorHealthMaxThreshold         Unsigned32
 }

 cienaCesChassisFileDescriptorHealthSubCategory                 OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         saos(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the file descriptors being monitored.
                        - 'none' is an enumeration  placeholder"
                ::= { cienaCesChassisFileDescriptorHealthEntry 1 }

 cienaCesChassisFileDescriptorHealthOriginIndex                         OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisFileDescriptorHealthEntry 2}

 cienaCesChassisFileDescriptorHealthState                               OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health state of the file descriptor sub-category being monitored."
                ::= { cienaCesChassisFileDescriptorHealthEntry 3}

 cienaCesChassisFileDescriptorHealthCurrMeasurement             OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current value of the file descriptor sub-category being monitored."
                ::= { cienaCesChassisFileDescriptorHealthEntry 4 }

 cienaCesChassisFileDescriptorHealthMaxMeasurement      OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum recorded value of the file descriptor sub-category being monitored."
                ::= { cienaCesChassisFileDescriptorHealthEntry 5 }

 cienaCesChassisFileDescriptorHealthMaxThreshold        OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum possible value of the file descriptor sub-category being monitored."
                ::= { cienaCesChassisFileDescriptorHealthEntry 6 }


--
-- Process Health
--
cienaCesChassisProcessHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisProcessHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of process resources."
        ::= { cienaCesChassisHealth 41 }

 cienaCesChassisProcessHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisProcessHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisProcessHealthTable."
        INDEX {  cienaCesChassisProcessHealthSubCategory, cienaCesChassisProcessHealthOriginIndex }
        ::= { cienaCesChassisProcessHealthTable 1 }

 CienaCesChassisProcessHealthEntry ::= SEQUENCE {
        cienaCesChassisProcessHealthSubCategory          INTEGER,
        cienaCesChassisProcessHealthOriginIndex          Unsigned32,
        cienaCesChassisProcessHealthState                TceHealthStatus,
        cienaCesChassisProcessHealthCurrMeasurement      Unsigned32,
        cienaCesChassisProcessHealthMaxMeasurement       Unsigned32,
        cienaCesChassisProcessHealthMaxThreshold         Unsigned32
 }

 cienaCesChassisProcessHealthSubCategory                 OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         linux(1)
                  }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the process being monitored.
                        - 'none' is an enumeration  placeholder"
                ::= { cienaCesChassisProcessHealthEntry 1 }

 cienaCesChassisProcessHealthOriginIndex                         OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisProcessHealthEntry 2}

 cienaCesChassisProcessHealthState                               OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health state of the process sub-category being monitored."
                ::= { cienaCesChassisProcessHealthEntry 3}

 cienaCesChassisProcessHealthCurrMeasurement             OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current value of the process sub-category being monitored."
                ::= { cienaCesChassisProcessHealthEntry 4 }

 cienaCesChassisProcessHealthMaxMeasurement      OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum recorded value of the process sub-category being monitored."
                ::= { cienaCesChassisProcessHealthEntry 5 }

 cienaCesChassisProcessHealthMaxThreshold        OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum possible value of the process sub-category being monitored."
                ::= { cienaCesChassisProcessHealthEntry 6 }


--
-- Thread Health
--
 cienaCesChassisThreadHealthTable OBJECT-TYPE
        SYNTAX          SEQUENCE OF CienaCesChassisThreadHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "Table listing the health information of thread resources."
        ::= { cienaCesChassisHealth 42 }

 cienaCesChassisThreadHealthEntry OBJECT-TYPE
        SYNTAX          CienaCesChassisThreadHealthEntry
        MAX-ACCESS      not-accessible
        STATUS          current
        DESCRIPTION     "An entry in the cienaCesChassisThreadHealthTable."
        INDEX {  cienaCesChassisThreadHealthSubCategory, cienaCesChassisThreadHealthOriginIndex }
        ::= { cienaCesChassisThreadHealthTable 1 }

 CienaCesChassisThreadHealthEntry ::= SEQUENCE {
        cienaCesChassisThreadHealthSubCategory          INTEGER,
        cienaCesChassisThreadHealthOriginIndex          Unsigned32,
        cienaCesChassisThreadHealthState                TceHealthStatus,
        cienaCesChassisThreadHealthCurrMeasurement      Unsigned32,
        cienaCesChassisThreadHealthMaxMeasurement       Unsigned32,
        cienaCesChassisThreadHealthMaxThreshold         Unsigned32
 }

 cienaCesChassisThreadHealthSubCategory                 OBJECT-TYPE
                SYNTAX          INTEGER         {
                                                         none(0),
                                                         saos(1)
                }
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "The sub-category of the thread being monitored.
                        - 'none' is an enumeration  placeholder"
                ::= { cienaCesChassisThreadHealthEntry 1 }

 cienaCesChassisThreadHealthOriginIndex                         OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      not-accessible
                STATUS          current
                DESCRIPTION
                        "Index of the slot being monitored."
                ::= { cienaCesChassisThreadHealthEntry 2}

 cienaCesChassisThreadHealthState                               OBJECT-TYPE
                SYNTAX          TceHealthStatus
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Health state of the thread sub-category being monitored."
                ::= { cienaCesChassisThreadHealthEntry 3}

 cienaCesChassisThreadHealthCurrMeasurement             OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "Current value of the thread sub-category being monitored."
                ::= { cienaCesChassisThreadHealthEntry 4 }

 cienaCesChassisThreadHealthMaxMeasurement      OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum recorded value of the thread sub-category being monitored."
                ::= { cienaCesChassisThreadHealthEntry 5 }

 cienaCesChassisThreadHealthMaxThreshold        OBJECT-TYPE
                SYNTAX          Unsigned32
                MAX-ACCESS      read-only
                STATUS          current
                DESCRIPTION
                        "The maximum possible value of the thread sub-category being monitored."
                ::= { cienaCesChassisThreadHealthEntry 6 }


 --
 -- Platform Description
 --
 cienaCesChassisPlatformType  OBJECT-TYPE
     SYNTAX        OCTET STRING (SIZE(1..4))
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the chassis platform type."
     ::= { cienaCesChassisPlatform 1 }

 cienaCesChassisPlatformName  OBJECT-TYPE
     SYNTAX        DisplayString
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the chassis platform name."
     ::= { cienaCesChassisPlatform 2 }

 cienaCesChassisPlatformDesc  OBJECT-TYPE
     SYNTAX        DisplayString
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the chassis description."
     ::= { cienaCesChassisPlatform 3 }

 cienaCesChassisNumSlots  OBJECT-TYPE
     SYNTAX        Integer32
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the maximum number of slots on the chassis."
     ::= { cienaCesChassisPlatform 4 }

  cienaCesChassisPrimaryCtrlSlot  OBJECT-TYPE
     SYNTAX        Integer32
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the slot number of primary control card."
     ::= { cienaCesChassisPlatform 5 }

  cienaCesChassisSecondaryCtrlSlot  OBJECT-TYPE
     SYNTAX        Integer32
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the slot number of secondary control card."
     ::= { cienaCesChassisPlatform 6 }

  cienaCesChassisNumFanTrays  OBJECT-TYPE
     SYNTAX        Integer32
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the number of fan trays in the chassis."
     ::= { cienaCesChassisPlatform 7 }

  cienaCesChassisNumFansPerTray  OBJECT-TYPE
     SYNTAX        Integer32
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the number of fans per tray."
     ::= { cienaCesChassisPlatform 8 }

  cienaCesChassisDcPower  OBJECT-TYPE
     SYNTAX        INTEGER {
                                yes(1),
                                no(2)
                                }
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates if DC power is supported for this chassis or not."
     ::= { cienaCesChassisPlatform 9 }

 cienaCesChassisRedunPower  OBJECT-TYPE
     SYNTAX        INTEGER {
                                yes(1),
                                no(2)
                                }
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates if redundant power is supported for this chassis or not."
     ::= { cienaCesChassisPlatform 10 }

 cienaCesChassisPhysicalPortsMax  OBJECT-TYPE
     SYNTAX        Integer32
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the maximum number of ports supported for this chassis."
     ::= { cienaCesChassisPlatform 11 }

 cienaCesChassisAggPortsMax  OBJECT-TYPE
     SYNTAX        Integer32
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the maximum aggregated ports supported on this chassis."
     ::= { cienaCesChassisPlatform 12 }

  cienaCesChassisVirtualSwitchMax  OBJECT-TYPE
     SYNTAX           Integer32
     MAX-ACCESS       read-only
     STATUS           current
     DESCRIPTION
             "This object indicates the maximum number of virtual switches supported on the chassis."
     ::= { cienaCesChassisPlatform 13 }

  cienaCesChassisVirtualInterfaceMax  OBJECT-TYPE
     SYNTAX           Integer32
     MAX-ACCESS       read-only
     STATUS           current
     DESCRIPTION
             "This object indicates the maximum number of virtual interfaces supported on the chassis."
     ::= { cienaCesChassisPlatform 14 }

  cienaCesChassisMulticastGrpsMax  OBJECT-TYPE
     SYNTAX           Integer32
     MAX-ACCESS       read-only
     STATUS           current
     DESCRIPTION
             "This object indicates the maximum number of multicast groups supported on the chassis."
     ::= { cienaCesChassisPlatform 15 }

  cienaCesChassisRstpDomainsMax  OBJECT-TYPE
     SYNTAX           Integer32
     MAX-ACCESS       read-only
     STATUS           current
     DESCRIPTION
             "This object indicates the maximum number of RSTP domains supported on the chassis."
     ::= { cienaCesChassisPlatform 16 }

  cienaCesChassisVirtualInterfacePerVsMax  OBJECT-TYPE
     SYNTAX           Integer32
     MAX-ACCESS       read-only
     STATUS           current
     DESCRIPTION
             "This object indicates the maximum number of virtual interfaces per VS supported on the chassis."
         ::= { cienaCesChassisPlatform 17 }

  cienaCesChassisReplicPerPortPerVsMax OBJECT-TYPE
         SYNTAX           Integer32
     MAX-ACCESS       read-only
     STATUS           current
     DESCRIPTION
             "This object indicates the maximum number of Virtual interfaces
              per logical port per Vswitch supported on the chassis."
         ::= { cienaCesChassisPlatform 18 }

  cienaCesChassisReplicMCPortPerVsMax OBJECT-TYPE
         SYNTAX           Integer32
     MAX-ACCESS       read-only
     STATUS           current
     DESCRIPTION
             "This object indicates the maximum number of Mcast Virtual Interfaces Per
              logical port per Vswitch supported on the chassis."
         ::= { cienaCesChassisPlatform 19 }

        cienaCesChassisSubPortsMax OBJECT-TYPE
         SYNTAX           Integer32
     MAX-ACCESS       read-only
     STATUS           current
     DESCRIPTION
             "This object indicates the maximum number of Sub Ports supported on the chassis."
         ::= { cienaCesChassisPlatform 20 }

  cienaCesChassisQosFlowsMax OBJECT-TYPE
         SYNTAX           Integer32
     MAX-ACCESS       read-only
     STATUS           current
     DESCRIPTION
             "This object indicates the maximum number of QoS Flows supported on the chassis."
         ::= { cienaCesChassisPlatform 21 }

  cienaCesChassisAccessFlowsMax OBJECT-TYPE
         SYNTAX           Integer32
     MAX-ACCESS       read-only
     STATUS           current
     DESCRIPTION
             "This object indicates the maximum number of Access Flows supported on the chassis."
         ::= { cienaCesChassisPlatform 22 }

  cienaCesChassisCPUSubIntfcsMax OBJECT-TYPE
          SYNTAX           Integer32
      MAX-ACCESS       read-only
      STATUS           current
      DESCRIPTION
             "This object indicates the maximum number of CPU sub interfaces supported on the chassis."
          ::= { cienaCesChassisPlatform 23 }

  cienaCesChassisPBTTunnelGroupsMax OBJECT-TYPE
           SYNTAX           Integer32
       MAX-ACCESS       read-only
       STATUS           current
       DESCRIPTION
             "This object indicates the maximum number of PBT Tunnel groups supported on the chassis."
           ::= { cienaCesChassisPlatform 24 }

  cienaCesChassisPBTEncapTunnelsMax OBJECT-TYPE
          SYNTAX           Integer32
      MAX-ACCESS       read-only
      STATUS           current
      DESCRIPTION
             "This object indicates the maximum number of PBT Encap Tunnels supported on the chassis."
           ::= { cienaCesChassisPlatform 25 }

  cienaCesChassisPBTDecapTunnelsMax OBJECT-TYPE
      SYNTAX           Integer32
      MAX-ACCESS       read-only
      STATUS           current
      DESCRIPTION
             "This object indicates the maximum number of PBT Decap Tunnels supported on the chassis."
          ::= { cienaCesChassisPlatform 26 }

  cienaCesChassisPBTServiceIntfcsMax OBJECT-TYPE
      SYNTAX           Integer32
      MAX-ACCESS       read-only
      STATUS           current
      DESCRIPTION
             "This object indicates the maximum number of PBT Service Interfaces supported on the chassis."
            ::= { cienaCesChassisPlatform 27 }

  cienaCesChassisPBTTransitIntfcsMax OBJECT-TYPE
      SYNTAX           Integer32
      MAX-ACCESS       read-only
      STATUS           current
      DESCRIPTION
             "This object indicates the maximum number of PBT Transit Interfaces supported on the chassis."
            ::= { cienaCesChassisPlatform 28 }

  cienaCesChassisMeterProfilesMax OBJECT-TYPE
          SYNTAX           Integer32
      MAX-ACCESS       read-only
      STATUS           current
      DESCRIPTION
             "This object indicates the maximum number of Meter Profiles supported on the chassis."
           ::= { cienaCesChassisPlatform 29 }

  cienaCesChassisFloodContainersMax OBJECT-TYPE
          SYNTAX           Integer32
      MAX-ACCESS       read-only
      STATUS           current
      DESCRIPTION
             "This object indicates the maximum number of Flood Containers supported on the chassis."
           ::= { cienaCesChassisPlatform 30 }

  cienaCesChassisRCOSProfilesMax OBJECT-TYPE
          SYNTAX           Integer32
      MAX-ACCESS       read-only
      STATUS           current
      DESCRIPTION
            "This object indicates the maximum number of ResolvedCos Profiles supported on the chassis."
           ::= { cienaCesChassisPlatform 31 }

  cienaCesChassisRCOSMappingsMax OBJECT-TYPE
          SYNTAX           Integer32
      MAX-ACCESS       read-only
      STATUS           current
      DESCRIPTION
             "This object indicates the maximum number of ResolvedCos Mappings supported on the chassis."
           ::= { cienaCesChassisPlatform 32 }

  cienaCesChassisFCOSMappingsMax OBJECT-TYPE
      SYNTAX           Integer32
      MAX-ACCESS       read-only
      STATUS           current
      DESCRIPTION
             "This object indicates the maximum number of FrameCos Mappings supported on the chassis."
            ::= { cienaCesChassisPlatform 33 }

  cienaCesChassisShapingProfilesMax OBJECT-TYPE
          SYNTAX           Integer32
      MAX-ACCESS       read-only
      STATUS           current
      DESCRIPTION
             "This object indicates the maximum number of Shaping Profiles supported on the chassis."
            ::= { cienaCesChassisPlatform 34 }

  cienaCesChassisMPLSTunnelGroupsMax OBJECT-TYPE
          SYNTAX           Integer32
      MAX-ACCESS       read-only
      STATUS           current
      DESCRIPTION
             "This object indicates the maximum number of MPLS tunnel groups supported on the chassis."
            ::= { cienaCesChassisPlatform 35 }

  cienaCesChassisMPLSTunnelsPerGroupMax OBJECT-TYPE
          SYNTAX           Integer32
      MAX-ACCESS       read-only
      STATUS           current
      DESCRIPTION
             "This object indicates the maximum number of MPLS tunnels per MPLS tunnel group
              supported on the chassis."
            ::= { cienaCesChassisPlatform 36 }

  cienaCesChassisMPLSEncapTunnelsMax OBJECT-TYPE
          SYNTAX           Integer32
      MAX-ACCESS       read-only
      STATUS           current
      DESCRIPTION
             "This object indicates the maximum number of MPLS encap tunnels supported on the chassis."
            ::= { cienaCesChassisPlatform 37 }

  cienaCesChassisMPLSDecapTunnelsMax OBJECT-TYPE
          SYNTAX           Integer32
      MAX-ACCESS       read-only
      STATUS           current
      DESCRIPTION
             "This object indicates the maximum number of MPLS decap tunnels supported on the chassis."
            ::= { cienaCesChassisPlatform 38 }

  cienaCesChassisMPLSVCMax OBJECT-TYPE
          SYNTAX           Integer32
      MAX-ACCESS       read-only
      STATUS           current
      DESCRIPTION
             "This object indicates the maximum number of MPLS virtual-circuits supported on the chassis."
            ::= { cienaCesChassisPlatform 39 }

  cienaCesChassisMPLSInterfacesMax OBJECT-TYPE
          SYNTAX           Integer32
      MAX-ACCESS       read-only
      STATUS           current
      DESCRIPTION
             "This object indicates the maximum number of MPLS interfaces supported on the chassis."
            ::= { cienaCesChassisPlatform 40 }

  cienaCesChassisMPLSTransitTunnelsMax OBJECT-TYPE
          SYNTAX           Integer32
      MAX-ACCESS       read-only
      STATUS           current
      DESCRIPTION
             "This object indicates the maximum number of MPLS transit tunnels supported on the chassis."
            ::= { cienaCesChassisPlatform 41 }

  cienaCesChassisRedundancyGroupsMax  OBJECT-TYPE
     SYNTAX        Unsigned32 
     MAX-ACCESS    read-only 
     STATUS        current
     DESCRIPTION
             "The maximum number of redundancy groups supported on this machine."
     ::= { cienaCesChassisPlatform 42 }

  cienaCesChassisLinksPerRedundancyGroupMax  OBJECT-TYPE
     SYNTAX        Unsigned32 
     MAX-ACCESS    read-only 
     STATUS        current
     DESCRIPTION
             "The maximum number of links per redundancy group supported on this machine."
     ::= { cienaCesChassisPlatform 43 }

 --
 -- ID Prom Contents
 --

 cienaCesChassisIDPEthBaseMac  OBJECT-TYPE
     SYNTAX             MacAddress
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
             "This object indicates the Ethernet Base MAC address stored in the chassis IDP."
     ::= { cienaCesChassisIDP 1 }

 cienaCesChassisIDPEthBaseMacRange  OBJECT-TYPE
     SYNTAX             Integer32
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
             "This object indicates the Ethernet MAC address range stored in the chassis IDP."
     ::= { cienaCesChassisIDP 2 }

 cienaCesChassisIDPModuleSerialNumber  OBJECT-TYPE
     SYNTAX        DisplayString
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the Module Serial Number stored in the chassis IDP."
     ::= { cienaCesChassisIDP 3 }

 cienaCesChassisIDPModelPartNumber  OBJECT-TYPE
     SYNTAX        DisplayString
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the Model Part Number stored in the chassis IDP."
     ::= { cienaCesChassisIDP 4 }

 cienaCesChassisIDPModelRevision  OBJECT-TYPE
     SYNTAX        DisplayString
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the Module Revision Number stored in the chassis IDP."
     ::= { cienaCesChassisIDP 5 }

 cienaCesChassisIDPProductID  OBJECT-TYPE
     SYNTAX        DisplayString
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the Product ID stored in the chassis IDP."
     ::= { cienaCesChassisIDP 6 }

 cienaCesChassisIDPMfgDate  OBJECT-TYPE
     SYNTAX        DisplayString
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the Manufactured Date stored in the chassis IDP."
     ::= { cienaCesChassisIDP 7 }

 cienaCesChassisIDPCleiCode  OBJECT-TYPE
     SYNTAX        DisplayString
     MAX-ACCESS    read-only
     STATUS        current
     DESCRIPTION
             "This object indicates the CLEI Code stored in the chassis IDP."
     ::= { cienaCesChassisIDP 8 }

 cienaCesChassisIDPBarcode  OBJECT-TYPE
     SYNTAX             DisplayString
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
             "This object indicates the Barcode stored in the chassis IDP."
     ::= { cienaCesChassisIDP 9 }

 cienaCesChassisIDPSWCompat  OBJECT-TYPE
     SYNTAX             Integer32
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
        "This object indicates the Software Compatibility Number stored in the chassis IDP."
     ::= { cienaCesChassisIDP 10 }

 cienaCesChassisIDPFTC  OBJECT-TYPE
     SYNTAX             Integer32
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
        "This object indicates the Functional Test Count stored in the chassis IDP."
     ::= { cienaCesChassisIDP 11 }


 --
 -- IOM
 --

 cienaCesChassisIOMState  OBJECT-TYPE
     SYNTAX             INTEGER {
                           enabled(1),
                           faulted(2),
                           uninstalled(3)
                        }
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
        "Current state of the IOM."
     ::= { cienaCesChassisIOM 1 }

 cienaCesChassisIOMBuzzerEnable  OBJECT-TYPE
     SYNTAX             TruthValue
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
        "This object represents whether the on-board buzzer will alert to fan tray removal.
         When true, the on-board buzzer will alert while the fan tray is removed.
         When false, the on-board buzzer will not alert to fan tray removal."
     ::= { cienaCesChassisIOM 2 }

 cienaCesChassisIOMBuzzerState  OBJECT-TYPE
     SYNTAX       INTEGER {
                     on(1),
                     off(2),
                     notSupported(3)
                  }
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
        "This object represents the current state of the on-board buzzer.
         When on, the on-board buzzer is presently alerting.
         When off, the on-board buzzer is presently silent.
         Value of notSupported is returned when the IOM is not presently installed."
     ::= { cienaCesChassisIOM 3 }

 cienaCesChassisIOMAlarmOutputTable  OBJECT-TYPE
     SYNTAX             SEQUENCE OF CienaCesChassisIOMAlarmOutputEntry
     MAX-ACCESS         not-accessible
     STATUS             current
     DESCRIPTION
        "A list of the alarm output signals and their state, as supported by the IOM."
     ::= { cienaCesChassisIOM 4 }

 cienaCesChassisIOMAlarmOutputEntry  OBJECT-TYPE
     SYNTAX             CienaCesChassisIOMAlarmOutputEntry
     MAX-ACCESS         not-accessible
     STATUS             current
     DESCRIPTION
        "An entry in the IOM alarm output table."
     INDEX  { cienaCesChassisIOMAlarmOutputIndex }
     ::= { cienaCesChassisIOMAlarmOutputTable 1 }

 CienaCesChassisIOMAlarmOutputEntry ::=  SEQUENCE  {
     cienaCesChassisIOMAlarmOutputIndex                      Integer32,
     cienaCesChassisIOMAlarmOutputDescription                DisplayString,
     cienaCesChassisIOMAlarmOutputState                      INTEGER
 }

 cienaCesChassisIOMAlarmOutputIndex  OBJECT-TYPE
     SYNTAX             Integer32
     MAX-ACCESS         not-accessible
     STATUS             current
     DESCRIPTION
        "IOM alarm output index, representing a distinct alarm output signal on the IOM."
     ::= { cienaCesChassisIOMAlarmOutputEntry 1 }

 cienaCesChassisIOMAlarmOutputDescription  OBJECT-TYPE
     SYNTAX             DisplayString
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
         "Description of the IOM alarm output signal."
     ::= { cienaCesChassisIOMAlarmOutputEntry 2 }

 cienaCesChassisIOMAlarmOutputState  OBJECT-TYPE
     SYNTAX       INTEGER {
                     on(1),
                     off(2),
                     notSupported(3)
                  }
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
         "Current state of the IOM alarm output signal.
          When on, the system presently is asserting the IOM alarm output signal.
          When off, the system is not presently asserting the IOM alarm output signal.
          Value of notSupported is returned when the IOM is not presently installed."
     ::= { cienaCesChassisIOMAlarmOutputEntry 3 }

 cienaCesChassisIOMAlarmInputTable  OBJECT-TYPE
     SYNTAX             SEQUENCE OF CienaCesChassisIOMAlarmInputEntry
     MAX-ACCESS         not-accessible
     STATUS             current
     DESCRIPTION
        "A list of the alarm input signals and their state, as supported by the IOM."
     ::= { cienaCesChassisIOM 5 }

  cienaCesChassisIOMName  OBJECT-TYPE
     SYNTAX             DisplayString
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
        "This object indicates the IOM name."
     ::= { cienaCesChassisIOM 6 }

  cienaCesChassisIOMChassisIndx  OBJECT-TYPE
     SYNTAX             Unsigned32 (1..1)
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
        "This object represents the chassis index."
     ::= { cienaCesChassisIOM 7 }

  cienaCesChassisIOMShelfIndx  OBJECT-TYPE
     SYNTAX             Unsigned32 (0..992)
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
        "This object represents the chassis shelf index."
     ::= { cienaCesChassisIOM 8 }

  cienaCesChassisIOMSlotIndx  OBJECT-TYPE
     SYNTAX             Unsigned32 (1..38)
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
        "This object represents the IOM slot index."
     ::= { cienaCesChassisIOM 9 }

  cienaCesChassisIOMSerialNumber OBJECT-TYPE
     SYNTAX             DisplayString
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
        "This object represent the IOM serial number."
     ::= { cienaCesChassisIOM 10 }

 cienaCesChassisIOMAlarmInputEntry  OBJECT-TYPE
     SYNTAX             CienaCesChassisIOMAlarmInputEntry
     MAX-ACCESS         not-accessible
     STATUS             current
     DESCRIPTION
        "An entry in the IOM alarm input table."
     INDEX  { cienaCesChassisIOMAlarmInputIndex }
     ::= { cienaCesChassisIOMAlarmInputTable 1 }

 CienaCesChassisIOMAlarmInputEntry ::=  SEQUENCE  {
     cienaCesChassisIOMAlarmInputIndex                      Integer32,
     cienaCesChassisIOMAlarmInputDescription                DisplayString,
     cienaCesChassisIOMAlarmInputState                      INTEGER
 }

 cienaCesChassisIOMAlarmInputIndex  OBJECT-TYPE
     SYNTAX             Integer32
     MAX-ACCESS         not-accessible
     STATUS             current
     DESCRIPTION
        "IOM alarm input index, representing a distinct alarm input signal on the IOM."
     ::= { cienaCesChassisIOMAlarmInputEntry 1 }

 cienaCesChassisIOMAlarmInputDescription  OBJECT-TYPE
     SYNTAX             DisplayString
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
         "Description of the IOM alarm input signal."
     ::= { cienaCesChassisIOMAlarmInputEntry 2 }

 cienaCesChassisIOMAlarmInputState  OBJECT-TYPE
     SYNTAX       INTEGER {
                     on(1),
                     off(2),
                     notSupported(3)
                  }
     MAX-ACCESS         read-only
     STATUS             current
     DESCRIPTION
         "Current input state of the IOM alarm input signal.
          When on, the IOM alarm input signal is presently being asserted.
          When off, the IOM alarm input signal is not presently being asserted.
          Value of notSupported is returned when the IOM is not presently installed."
     ::= { cienaCesChassisIOMAlarmInputEntry 3 }


 --
 -- Notification
 --

 cienaCesChassisPowerSupplyFaultedNotification NOTIFICATION-TYPE
        OBJECTS         {
                                 cienaGlobalSeverity,
                         cienaCesChassisMacAddress,
                cienaCesChassisPowerSupplyNotifIndx,
                cienaCesChassisPowerSupplyState,
                cienaCesChassisPowerSupplyType,
                cienaCesChassisPowerSupplySlotName,
                cienaCesChassisPowerSupplyChassisIndx,
                cienaCesChassisPowerSupplyShelfIndx,
                cienaCesChassisPowerSupplySlotIndx
                }
        STATUS  current
        DESCRIPTION
                "A cienaCesChassisPowerSupplyFaultedNotification is sent if a
                power supply state changes to one of these:
                                - faulted (2)
                                - offline (3)
                                - unequipped(4)
                To enable the device to send this trap:
                        -       cienaCesChassisAllPowerSupplyTrapState needs to be set to enabled
                cienaCesChassisAllPowerSupplyTrapState is set to enabled by default."
        ::= { cienaCesChassisMIBNotifications 1 }

 cienaCesChassisPowerSupplyOnlineNotification NOTIFICATION-TYPE
        OBJECTS         {
                                 cienaGlobalSeverity,
                 cienaCesChassisMacAddress,
                                cienaCesChassisPowerSupplyNotifIndx,
                cienaCesChassisPowerSupplyState,
                cienaCesChassisPowerSupplyType,
                cienaCesChassisPowerSupplySlotName,
                cienaCesChassisPowerSupplyChassisIndx,
                cienaCesChassisPowerSupplyShelfIndx,
                cienaCesChassisPowerSupplySlotIndx
                }
        STATUS  current
        DESCRIPTION
                "A cienaCesChassisPowerSupplyOnlineNotification is sent if a
                power supply state changes to online(1).
                To enable the device to send this trap:
                        -       cienaCesChassisAllPowerSupplyTrapState needs to be set to enabled
                cienaCesChassisAllPowerSupplyTrapState is set to enabled by default."
        ::= { cienaCesChassisMIBNotifications 2 }

 cienaCesChassisFanHiTempNotification NOTIFICATION-TYPE
        OBJECTS         {
                                 cienaGlobalSeverity,
                         cienaCesChassisMacAddress,
                cienaCesChassisFanTempTrayNotifIndx,
                cienaCesChassisFanTempNotifId,
                cienaCesChassisFanTemp,
                cienaCesChassisFanTempHiThreshold,
                cienaCesChassisFanTempName,
                cienaCesChassisFanTempChassisIndx,
                cienaCesChassisFanTempShelfIndx
                }
        STATUS  current
        DESCRIPTION
                "A cienaCesChassisFanTempNotification is sent if the cienaCesChassisFanTemp is changed to high.
                To enable the device to send this trap:
                        -       cienaCesChassisAllFanTempTrapState needs to be set to enabled
                The above object is set to enabled by default."
        ::= { cienaCesChassisMIBNotifications 3 }

 cienaCesChassisFanNormalTempNotification NOTIFICATION-TYPE
        OBJECTS         {
                                 cienaGlobalSeverity,
                         cienaCesChassisMacAddress,
                cienaCesChassisFanTempTrayNotifIndx,
                cienaCesChassisFanTempNotifId,
                cienaCesChassisFanTemp,
                cienaCesChassisFanTempLoThreshold,
                cienaCesChassisFanTempHiThreshold,
                cienaCesChassisFanTempName,
                cienaCesChassisFanTempChassisIndx,
                cienaCesChassisFanTempShelfIndx
                        }
        STATUS  current
        DESCRIPTION
                "A cienaCesChassisFanTempNotification is sent if the cienaCesChassisFanTemp is changed to normal.
                To enable the device to send this trap:
                        -       cienaCesChassisAllFanTempTrapState needs to be set to enabled
                The above object is set to enabled by default."
        ::= { cienaCesChassisMIBNotifications 4 }

 cienaCesChassisFanLoTempNotification NOTIFICATION-TYPE
        OBJECTS         {
                                 cienaGlobalSeverity,
                         cienaCesChassisMacAddress,
                cienaCesChassisFanTempTrayNotifIndx,
                cienaCesChassisFanTempNotifId,
                cienaCesChassisFanTemp,
                cienaCesChassisFanTempLoThreshold,
                cienaCesChassisFanTempName,
                cienaCesChassisFanTempChassisIndx,
                cienaCesChassisFanTempShelfIndx
                }
        STATUS  current
        DESCRIPTION
                "A cienaCesChassisFanTempNotification is sent if the cienaCesChassisFanTemp is changed to low.
                To enable the device to send this trap:
                        -       cienaCesChassisAllFanTempTrapState needs to be set to enabled
                The above object is set to enabled by default."
        ::= { cienaCesChassisMIBNotifications 5 }

 cienaCesChassisFanSpeedMinThresholdNotification NOTIFICATION-TYPE
        OBJECTS         {
                                 cienaGlobalSeverity,
                         cienaCesChassisMacAddress,
                cienaCesChassisFanTrayNotifIndex,
                cienaCesChassisFanNotifIndex,
                cienaCesChassisFanAvgSpeed,
                cienaCesChassisFanName,
                cienaCesChassisFanChassisIndx,
                cienaCesChassisFanShelfIndx
		        }
        STATUS  current
        DESCRIPTION
                "A cienaCesChassisFanSpeedMinThresholdNotification is sent if the fan speed drops to
                minimum threshold.
                To enable the device to send this trap:
                        -       cienaCesChassisAllFanTrapState needs to be set to enabled
                The above object is set to enabled by default."
        ::= { cienaCesChassisMIBNotifications 7 }

 cienaCesChassisFanSpeedNormalRangeNotification NOTIFICATION-TYPE
        OBJECTS         {
                                 cienaGlobalSeverity,
                         cienaCesChassisMacAddress,
                cienaCesChassisFanTrayNotifIndex,
                cienaCesChassisFanNotifIndex,
                cienaCesChassisFanAvgSpeed,
                cienaCesChassisFanName,
                cienaCesChassisFanChassisIndx,
                cienaCesChassisFanShelfIndx
                        }
        STATUS  current
        DESCRIPTION
                "A cienaCesChassisFanSpeedNormalRangeNotification is sent if the fan speed returns to normal value.
                To enable the device to send this trap:
                        -       cienaCesChassisAllFanTrapState needs to be set to enabled
                The above object is set to enabled by default."
        ::= { cienaCesChassisMIBNotifications 8 }

 cienaCesChassisFanTrayRemoveNotification NOTIFICATION-TYPE
        OBJECTS         {
                 cienaGlobalSeverity,
                 cienaCesChassisMacAddress,
                        cienaCesChassisFanTrayNotifIndex,
                        cienaCesChassisFanTrayType,
                        cienaCesChassisFanTrayName,
                        cienaCesChassisFanTrayChassisIndx,
                        cienaCesChassisFanTrayShelfIndx,
                        cienaCesChassisFanTraySlotIndx,
                        cienaCesChassisFanTraySerialNumber
                 }
        STATUS  current
        DESCRIPTION
                "This notification is sent whenever a fan tray is removed from the chassis.
                To enable the device to send this trap:
                        -       cienaCesChassisAllFanTrayTrapState needs to be set to enabled
                The above object is set to enabled by default."
        ::= { cienaCesChassisMIBNotifications 9 }

 cienaCesChassisFanTrayInsertNotification NOTIFICATION-TYPE
        OBJECTS         {
                        cienaGlobalSeverity,
                        cienaCesChassisMacAddress,
                        cienaCesChassisFanTrayNotifIndex,
                        cienaCesChassisFanTrayType,
                        cienaCesChassisFanTrayName,
                        cienaCesChassisFanTrayChassisIndx,
                        cienaCesChassisFanTrayShelfIndx,
                        cienaCesChassisFanTraySlotIndx,
                        cienaCesChassisFanTraySerialNumber
                        }
        STATUS  current
        DESCRIPTION
                "This notification is sent whenever a fan tray is inserted in the chassis.
                To enable the device to send this trap:
                        -       cienaCesChassisAllFanTrayTrapState needs to be set to enabled
                The above object is set to enabled by default."
        ::= { cienaCesChassisMIBNotifications 10 }

 cienaCesChassisFanTrayStatusFaultedNotification NOTIFICATION-TYPE
        OBJECTS         {
                 cienaGlobalSeverity,
                 cienaCesChassisMacAddress,
                        cienaCesChassisFanTrayNotifIndex,
                        cienaCesChassisFanTrayStatus,
                        cienaCesChassisFanTrayName,
                        cienaCesChassisFanTrayChassisIndx,
                        cienaCesChassisFanTrayShelfIndx,
                        cienaCesChassisFanTraySlotIndx,
                        cienaCesChassisFanTraySerialNumber
                 }
        STATUS  current
        DESCRIPTION
                "This notification is sent whenever the fan tray status changes to one of the following:
                        -            pending(2),
                        rpm-warning(3),
                        uninstalled(4),
                        unknown(99)
                To enable the device to send this trap:
                        -       cienaCesChassisAllFanTrayTrapState needs to be set to enabled
                The above object is set to enabled by default."
        ::= { cienaCesChassisMIBNotifications 11 }

 cienaCesChassisFanTrayStatusOkNotification NOTIFICATION-TYPE
        OBJECTS         {
                 cienaGlobalSeverity,
                 cienaCesChassisMacAddress,
                        cienaCesChassisFanTrayNotifIndex,
                        cienaCesChassisFanTrayStatus,
                        cienaCesChassisFanTrayName,
                        cienaCesChassisFanTrayChassisIndx,
                        cienaCesChassisFanTrayShelfIndx,
                        cienaCesChassisFanTraySlotIndx,
                        cienaCesChassisFanTraySerialNumber
                 }
        STATUS  current
        DESCRIPTION
                "This notification is sent whenever the fan tray status changes to ok(1).
                To enable the device to send this trap:
                        -       cienaCesChassisAllFanTrayTrapState needs to be set to enabled
                The above object is set to enabled by default."
        ::= { cienaCesChassisMIBNotifications 12 }


 cienaCesChassisHealthStatusUnknownNotification NOTIFICATION-TYPE
        OBJECTS         {
                 cienaGlobalSeverity,
                 cienaCesChassisMacAddress,
                        cienaCesChassisHealthCategory,
              cienaCesChassisHealthSubCategory,
              cienaCesChassisHealthStatus,
              cienaCesChassisHealthStatusLast
            }
        STATUS  current
        DESCRIPTION
               "This notification is sent whenever the health manager status changes to unknown for the specified cienaCesChassisHealthCategory and
                cienaCesChassisHealthSubCategory. The cienaCesChassisHealthStatus indicates a warning status while the cienaCesChassisHealthStatusLast
                indicates the previous health status. The health status can be one of unknown, normal, warning, degraded or faulted.
                An unknown status indicates that the system has not yet been able to determine the initial state.  This can be due to either
                the system still not completed booting or a component has just been installed and has not had sufficient time to initialize
                and provide its state to the health monitoring system.
                To enable the device to send this trap:
                        -       cienaCesChassisHealthTrapState needs to be set to enabled
                The above object is set to enabled by default."
        ::= { cienaCesChassisMIBNotifications 13 }

 cienaCesChassisHealthStatusWarningNotification NOTIFICATION-TYPE
        OBJECTS         {
                 cienaGlobalSeverity,
                 cienaCesChassisMacAddress,
                        cienaCesChassisHealthCategory,
              cienaCesChassisHealthSubCategory,
              cienaCesChassisHealthStatus,
              cienaCesChassisHealthStatusLast
            }
        STATUS  current
        DESCRIPTION
                "This notification is sent whenever the health manager status changes to warning for the specified cienaCesChassisHealthCategory and
                cienaCesChassisHealthSubCategory. The cienaCesChassisHealthStatus indicates a warning status while the cienaCesChassisHealthStatusLast
                indicates the previous health status. The health status can be one of unknown, normal, warning, degraded or faulted. A warning status
                indicates that the monitored item is experiencing some errors or is deviated from specified ranges, but is still functional.
                To enable the device to send this trap:
                        -       cienaCesChassisHealthTrapState needs to be set to enabled
                The above object is set to enabled by default."
        ::= { cienaCesChassisMIBNotifications 14 }

 cienaCesChassisHealthStatusFaultedNotification NOTIFICATION-TYPE
        OBJECTS         {
                 cienaGlobalSeverity,
                 cienaCesChassisMacAddress,
                        cienaCesChassisHealthCategory,
              cienaCesChassisHealthSubCategory,
              cienaCesChassisHealthStatus,
              cienaCesChassisHealthStatusLast
            }
        STATUS  current
        DESCRIPTION
                "This notification is sent whenever the health manager status changes to faulted for the specified cienaCesChassisHealthCategory and
                cienaCesChassisHealthSubCategory. The cienaCesChassisHealthStatus indicates a faulted status while the cienaCesChassisHealthStatusLast
                indicates the previous health status. The health status can be one of unknown, normal, warning, degraded or faulted.  A faulted status
                indicates that the monitored item has failed or is so far out of range that the item should be faulted.
                To enable the device to send this trap:
                        -       cienaCesChassisHealthTrapState needs to be set to enabled
                The above object is set to enabled by default."
        ::= { cienaCesChassisMIBNotifications 15 }

 cienaCesChassisHealthStatusDegradedNotification NOTIFICATION-TYPE
        OBJECTS         {
                 cienaGlobalSeverity,
                 cienaCesChassisMacAddress,
                        cienaCesChassisHealthCategory,
              cienaCesChassisHealthSubCategory,
              cienaCesChassisHealthStatus,
              cienaCesChassisHealthStatusLast
            }
        STATUS  current
        DESCRIPTION
                "This notification is sent whenever the health manager status changes to degraded for the specified cienaCesChassisHealthCategory and
                cienaCesChassisHealthSubCategory. The cienaCesChassisHealthStatus indicates a degraded status while the cienaCesChassisHealthStatusLast
                indicates the previous health status.  The health status can be one of unknown, normal, warning, degraded or faulted.  A degraded status
                typically indicates that the monitored item is experiencing major problems or is well out of range.  Performance or system reliability is
                being affected.
                To enable the device to send this trap:
                        -       cienaCesChassisHealthTrapState needs to be set to enabled
                The above object is set to enabled by default."
        ::= { cienaCesChassisMIBNotifications 16 }

 cienaCesChassisHealthStatusGoodNotification NOTIFICATION-TYPE
        OBJECTS         {
                 cienaGlobalSeverity,
                 cienaCesChassisMacAddress,
                        cienaCesChassisHealthCategory,
              cienaCesChassisHealthSubCategory,
              cienaCesChassisHealthStatus,
              cienaCesChassisHealthStatusLast
            }
        STATUS  current
        DESCRIPTION
                "This notification is sent whenever the health manager status changes to normal for the specified cienaCesChassisHealthCategory and
                cienaCesChassisHealthSubCategory. The cienaCesChassisHealthStatus indicates a normal status while the cienaCesChassisHealthStatusLast
                indicates the previous health status. The health status can be one of unknown, normal, warning, degraded or faulted.  A normal status
                indicates that the monitored item is working correctly or within tolerance.
                To enable the device to send this trap:
                        -       cienaCesChassisHealthTrapState needs to be set to enabled
                The above object is set to enabled by default."
        ::= { cienaCesChassisMIBNotifications 17 }

  cienaCesChassisRebootNotification NOTIFICATION-TYPE
        OBJECTS         {
                         cienaGlobalSeverity,
                         cienaCesChassisMacAddress,
                                cienaCesChassisRebootReasonErrorType
            }
        STATUS  current
        DESCRIPTION
                "A cienaCesChassisRebootNotification is sent when the device is rebooted.  The cienaCesChassisRebootNotification has no guarantee to be sent upon a reboot and will likely misbehave if informs are used.  If sent, the cienaCesChassisRebootReasonErrorType is set to indicate the reboot reason. "
        ::= { cienaCesChassisMIBNotifications 18 }

 cienaCesChassisIOMStateChangeNotification  NOTIFICATION-TYPE
      OBJECTS  {
                        cienaGlobalSeverity,
                        cienaCesChassisIOMState,
                        cienaCesChassisIOMName,
                        cienaCesChassisIOMChassisIndx,
                        cienaCesChassisIOMShelfIndx,
                        cienaCesChassisIOMSlotIndx,
                        cienaCesChassisIOMSerialNumber
               }
      STATUS            current
      DESCRIPTION
          "This notification is generated when the value of cienaCesChassisIOMState changes."
      ::= { cienaCesChassisMIBNotifications 19 }

 cienaCesChassisIOMBuzzerEnableChangeNotification  NOTIFICATION-TYPE
      OBJECTS  {
                        cienaGlobalSeverity,
                        cienaCesChassisIOMBuzzerEnable,
                        cienaCesChassisIOMName,
                        cienaCesChassisIOMChassisIndx,
                        cienaCesChassisIOMShelfIndx,
                        cienaCesChassisIOMSlotIndx,
                        cienaCesChassisIOMSerialNumber
               }
      STATUS            current
      DESCRIPTION
          "This notification is generated when the value of cienaCesChassisIOMBuzzerEnable changes."
      ::= { cienaCesChassisMIBNotifications 20 }

 cienaCesChassisIOMBuzzerStateChangeNotification  NOTIFICATION-TYPE
      OBJECTS  {
                        cienaGlobalSeverity,
                        cienaCesChassisIOMBuzzerState,
                        cienaCesChassisIOMName,
                        cienaCesChassisIOMChassisIndx,
                        cienaCesChassisIOMShelfIndx,
                        cienaCesChassisIOMSlotIndx,
                        cienaCesChassisIOMSerialNumber
               }
      STATUS            current
      DESCRIPTION
          "This notification is generated when the value of cienaCesChassisIOMBuzzerState changes."
      ::= { cienaCesChassisMIBNotifications 21 }

 cienaCesChassisIOMAlarmOutputStateChangeNotification  NOTIFICATION-TYPE
      OBJECTS  {
                        cienaGlobalSeverity,
                        cienaCesChassisIOMAlarmOutputDescription,
                        cienaCesChassisIOMAlarmOutputState,
                        cienaCesChassisIOMName,
                        cienaCesChassisIOMChassisIndx,
                        cienaCesChassisIOMShelfIndx,
                        cienaCesChassisIOMSlotIndx,
                        cienaCesChassisIOMSerialNumber
               }
      STATUS            current
      DESCRIPTION
          "This notification is generated when the value of cienaCesChassisIOMAlarmOutputState changes."
      ::= { cienaCesChassisMIBNotifications 22 }

 cienaCesChassisIOMAlarmInputStateChangeNotification  NOTIFICATION-TYPE
      OBJECTS  {
                        cienaGlobalSeverity,
                        cienaCesChassisIOMAlarmInputDescription,
                        cienaCesChassisIOMAlarmInputState,
                        cienaCesChassisIOMName,
                        cienaCesChassisIOMChassisIndx,
                        cienaCesChassisIOMShelfIndx,
                        cienaCesChassisIOMSlotIndx,
                        cienaCesChassisIOMSerialNumber
               }
      STATUS            current
      DESCRIPTION
          "This notification is generated when the value of cienaCesChassisIOMAlarmInputState changes."
      ::= { cienaCesChassisMIBNotifications 23 }

 cienaCesChassisUsbFlashEnabledNotification  NOTIFICATION-TYPE
      OBJECTS  {
                        cienaGlobalSeverity
               }
      STATUS            current
      DESCRIPTION
          "This notification is generated when CTX USB hubs are enabled via user config."
      ::= { cienaCesChassisMIBNotifications 24 }

 cienaCesChassisUsbFlashDisabledNotification  NOTIFICATION-TYPE
      OBJECTS  {
                        cienaGlobalSeverity
               }
      STATUS            current
      DESCRIPTION
          "This notification is generated when CTX USB hubs are disabled via user config."
      ::= { cienaCesChassisMIBNotifications 25 }

 cienaCesChassisAirFilterServiceNotification  NOTIFICATION-TYPE
      OBJECTS  {
                        cienaGlobalSeverity,
                        cienaCesChassisMacAddress
               }
      STATUS            current
      DESCRIPTION
          "This notification is generated when the air filter service interval is reached."
      ::= { cienaCesChassisMIBNotifications 26 }

 cienaCesChassisAlarmCutoffNotification NOTIFICATION-TYPE
      OBJECTS {
                        cienaGlobalSeverity,
                        cienaCesChassisAlarmCutoffOrigin
              }
      STATUS            current
      DESCRIPTION
          "A cienaCesChassisAlarmCutoffNotification is sent if:
             1) The alarm cutoff button on either CM is pushed.
             2) The CLI alarm cutoff command is issued.
             3) The SNMP alarm cutoff command is issued.
           To enable the device to send this trap:
             cienaCesChassisAlarmCutoffTrapState needs to be set to enabled."
      ::= { cienaCesChassisMIBNotifications 27 }

 cienaCesChassisDyingGaspNotification NOTIFICATION-TYPE
   OBJECTS {
            cienaCesChassisDeviceId,
            cienaCesChassisHardwareVersion,
            cienaCesChassisSerialNumber,
            cienaCesChassisMacAddress,
            cienaCesChassisMfgDate,
            cienaCesChassisParamVersion,
            cienaGlobalSeverity
            }
   STATUS current
   DESCRIPTION
            "This notification is sent when the system losses power"
   ::= { cienaCesChassisMIBNotifications 28 }
  
  --
  -- Groups
  --
  chassisGlobalGroup    OBJECT-GROUP
       OBJECTS { cienaCesChassisMacAddress, cienaCesChassisDeviceId,
                                 cienaCesChassisPartNumber, cienaCesChassisSerialNumber, cienaCesChassisMfgDate,
                                 cienaCesChassisParamVersion, cienaCesChassisSystemDateAndTime,
                                 cienaCesChassisSystemTimeOffset, cienaCesChassisSystemUTCDateAndTime }
       STATUS  current
       DESCRIPTION
               "A collection of objects providing information
                about chassis global attributes."
       ::= { cienaCesChassisMIBGroups 1 }

  chassisPlatformGroup    OBJECT-GROUP
       OBJECTS { cienaCesChassisPlatformType, cienaCesChassisPlatformName, cienaCesChassisPlatformDesc,
                                 cienaCesChassisNumSlots, cienaCesChassisPrimaryCtrlSlot, cienaCesChassisSecondaryCtrlSlot,
                                 cienaCesChassisNumFanTrays, cienaCesChassisNumFansPerTray, cienaCesChassisDcPower,
                                 cienaCesChassisRedunPower, cienaCesChassisPhysicalPortsMax, cienaCesChassisAggPortsMax,
                                 cienaCesChassisVirtualSwitchMax ,  cienaCesChassisVirtualInterfaceMax,
                                 cienaCesChassisMulticastGrpsMax,
                                 cienaCesChassisRstpDomainsMax, cienaCesChassisVirtualInterfacePerVsMax }
       STATUS  current
       DESCRIPTION
               "A collection of objects providing information
                about chassis platform attributes."
       ::= { cienaCesChassisMIBGroups 2 }

  chassisPowerSupplyGroup    OBJECT-GROUP
       OBJECTS { cienaCesChassisPowerSupplyState, cienaCesChassisPowerSupplyType,
                                 cienaCesChassisPowerSupplyManufacturer, cienaCesChassisPowerSupplySerialNumber,
                                 cienaCesChassisPowerSupplyPartNum }
       STATUS  current
       DESCRIPTION
               "A collection of objects providing information
                about chassis power supply attributes."
       ::= { cienaCesChassisMIBGroups 3 }

  chassisFanGroup    OBJECT-GROUP
       OBJECTS { cienaCesChassisFanStatus,
                                 cienaCesChassisFanAvgSpeed, cienaCesChassisFanCurrentSpeed,
                                 cienaCesChassisFanMinSpeed }
       STATUS  current
       DESCRIPTION
               "A collection of objects providing information
                about chassis fans."
       ::= { cienaCesChassisMIBGroups 4 }

  chassisFanTrayGroup    OBJECT-GROUP
       OBJECTS { cienaCesChassisFanTrayStatus, cienaCesChassisFanTrayType,
                 cienaCesChassisFanTrayMode, cienaCesChassisFanTrayNumFans
                 }
       STATUS  current
       DESCRIPTION
               "A collection of objects providing information
                about chassis fans tray."
       ::= { cienaCesChassisMIBGroups 5 }

  chassisFanTempGroup    OBJECT-GROUP
       OBJECTS { cienaCesChassisFanTempDesc, cienaCesChassisFanTemp, cienaCesChassisFanTempHigh,
                                 cienaCesChassisFanTempLow, cienaCesChassisFanTempLoThreshold, cienaCesChassisFanTempHiThreshold }
       STATUS  current
       DESCRIPTION
               "A collection of objects providing information
                about chassis fan temperature."
       ::= { cienaCesChassisMIBGroups 6 }

  chassisNotifGroup    NOTIFICATION-GROUP
       NOTIFICATIONS { cienaCesChassisFanHiTempNotification, cienaCesChassisFanLoTempNotification,
                                           cienaCesChassisFanNormalTempNotification, cienaCesChassisFanSpeedMinThresholdNotification,
                                           cienaCesChassisFanSpeedNormalRangeNotification, cienaCesChassisFanTrayInsertNotification,
                                           cienaCesChassisFanTrayRemoveNotification, cienaCesChassisAlarmCutoffNotification}
       STATUS  current
       DESCRIPTION
               "A collection of objects providing information
               about chassis notifications."
       ::= { cienaCesChassisMIBGroups 7 }

  chassisHealthGroup    OBJECT-GROUP
       OBJECTS { cienaCesChassisHealthCategory, cienaCesChassisHealthSubCategory,
                 cienaCesChassisHealthStatus,  cienaCesChassisHealthStatusLast}
       STATUS  current
       DESCRIPTION
               "A collection of objects providing information
                about chassis health attributes."
       ::= { cienaCesChassisMIBGroups 8 }

 chassisIomStateGroup  OBJECT-GROUP
       OBJECTS {
                  cienaCesChassisIOMState,
                  cienaCesChassisIOMBuzzerEnable,
                  cienaCesChassisIOMBuzzerState,
                  cienaCesChassisIOMAlarmOutputState,
                  cienaCesChassisIOMAlarmInputState
               }
       STATUS  current
       DESCRIPTION
            " A collection of objects providing information about the IOM."
       ::= { cienaCesChassisMIBGroups 9 }

 chassisIomNotifGroup  NOTIFICATION-GROUP
       NOTIFICATIONS {
                  cienaCesChassisIOMStateChangeNotification,
                  cienaCesChassisIOMBuzzerEnableChangeNotification,
                  cienaCesChassisIOMBuzzerStateChangeNotification,
                  cienaCesChassisIOMAlarmOutputStateChangeNotification,
                  cienaCesChassisIOMAlarmInputStateChangeNotification
               }
       STATUS  current
       DESCRIPTION
            " A collection of notifications that provide information about changes related to the IOM."
       ::= { cienaCesChassisMIBGroups 10 }

 END
