-- ZyXEL Communications Corporation
-- Private Enterprise MIB definition     
  
-- This file describes the ZyXEL Communications Corporation Enterprise MIB.
-- It contains ZyXEL products OIDs, and common managed objects.


ZYXEL-GS2200-24-MIB DEFINITIONS ::= BEGIN

    IMPORTS
	enterprises		FROM RFC1155-SMI                                
	
	OBJECT-TYPE
		FROM SNMPv2-SMI			-- RFC2578	
	RowStatus, DateAndTime, TruthValue, StorageType, MacAddress
		FROM SNMPv2-TC			-- RFC2579
	DisplayString
	  	FROM RFC1213-MIB
	EnabledStatus
		FROM  P-BRIDGE-MIB
	PortList
		FROM  Q-BRIDGE-MIB
	dot1dBasePort
        FROM BRIDGE-MIB
	InterfaceIndexOrZero			-- RFC2863
		FROM IF-MIB
	SnmpAdminString
		FROM SNMP-FRAMEWORK-MIB		-- RFC2571
	InetAddressType, InetAddress
		FROM INET-ADDRESS-MIB		-- RFC2851
	OperationResponseStatus
		FROM DISMAN-PING-<PERSON><PERSON>
	dot1agCfmMdIndex 
		FROM IEEE8021-CFM-<PERSON>B
	dot1agCfmMaIndex
		FROM IEEE8021-CFM-MIB	
	dot1agCfmMepIdentifier
		FROM IEEE8021-CFM-MIB			
    IpAddress
        FROM SNMPv2-SMI
   ifIndex
     FROM IF-MIB
     -- from [RFC2863]
        
    NOTIFICATION-TYPE
        FROM SNMPv2-SMI
    sysObjectID 
        FROM RFC1213-MIB
    Counter, TimeTicks
        FROM RFC1155-SMI
    BridgeId, Timeout
        FROM BRIDGE-MIB;

    zyxel			OBJECT IDENTIFIER ::= { enterprises 890 }	
	products		OBJECT IDENTIFIER ::= { zyxel 1 }
	accessSwitch  	OBJECT IDENTIFIER ::= { products 5 }	
	esSeries		OBJECT IDENTIFIER ::= { accessSwitch 8 }
	gs2200-24		OBJECT IDENTIFIER ::= { esSeries 55 }
	
	
	sysInfo     			OBJECT IDENTIFIER ::= { gs2200-24 1 }
	rateLimitSetup 			OBJECT IDENTIFIER ::= { gs2200-24 2 }
	brLimitSetup 			OBJECT IDENTIFIER ::= { gs2200-24 3 }
	portSecuritySetup 		OBJECT IDENTIFIER ::= { gs2200-24 4 }
	vlanTrunkSetup 			OBJECT IDENTIFIER ::= { gs2200-24 5 }
	dot1xSetup 				OBJECT IDENTIFIER ::= { gs2200-24 8 }
	hwMonitorInfo 			OBJECT IDENTIFIER ::= { gs2200-24 9 }
	snmpSetup 				OBJECT IDENTIFIER ::= { gs2200-24 10 }
	dateTimeSetup     		OBJECT IDENTIFIER ::= { gs2200-24 11 }
	sysMgmt     			OBJECT IDENTIFIER ::= { gs2200-24 12 }
	layer2Setup     		OBJECT IDENTIFIER ::= { gs2200-24 13 }
	ipSetup     			OBJECT IDENTIFIER ::= { gs2200-24 14 }
	filterSetup     		OBJECT IDENTIFIER ::= { gs2200-24 15 }
	mirrorSetup     		OBJECT IDENTIFIER ::= { gs2200-24 16 }
	aggrSetup     			OBJECT IDENTIFIER ::= { gs2200-24 17 }
	accessCtlSetup     		OBJECT IDENTIFIER ::= { gs2200-24 18 }
	queuingMethodSetup     	OBJECT IDENTIFIER ::= { gs2200-24 19 }
	dhcpSetup     			OBJECT IDENTIFIER ::= { gs2200-24 20 }
	staticRouteSetup     	OBJECT IDENTIFIER ::= { gs2200-24 21 }
	arpInfo     			OBJECT IDENTIFIER ::= { gs2200-24 22 }
	portOpModeSetup     	OBJECT IDENTIFIER ::= { gs2200-24 24 }
	portBasedVlanSetup     	OBJECT IDENTIFIER ::= { gs2200-24 25 }
	
	
	faultMIB MODULE-IDENTITY
   		LAST-UPDATED "200411031200Z" -- YYYY/MM/DD, HH/MM
   		ORGANIZATION "ZyXEL"
    	CONTACT-INFO ""
    	DESCRIPTION
            "Fault event table definitions"
   		::= { gs2200-24 26 }


	faultTrapsMIB MODULE-IDENTITY
    	LAST-UPDATED "200411011200Z" -- YYYY/MM/DD, HH/MM
    	ORGANIZATION "ZyXEL"
    	CONTACT-INFO ""
    	DESCRIPTION
            "Fault event trap definitions"
    	::= { gs2200-24 27 } 
	
	
	multicastPortSetup     			OBJECT IDENTIFIER ::= { gs2200-24 28 }
	multicastStatus    				OBJECT IDENTIFIER ::= { gs2200-24 29 }
	igmpFilteringProfileSetup     	OBJECT IDENTIFIER ::= { gs2200-24 30 }
	mvrSetup     					OBJECT IDENTIFIER ::= { gs2200-24 31 }
	clusterSetup     				OBJECT IDENTIFIER ::= { gs2200-24 32 }
	sysLogSetup     				OBJECT IDENTIFIER ::= { gs2200-24 33 }
	diffservSetup     				OBJECT IDENTIFIER ::= { gs2200-24 34 }
	protoBasedVlanSetup     		OBJECT IDENTIFIER ::= { gs2200-24 35 }
	mrstp     						OBJECT IDENTIFIER ::= { gs2200-24 36 }
	classifierSetup     			OBJECT IDENTIFIER ::= { gs2200-24 37 }
	policySetup     				OBJECT IDENTIFIER ::= { gs2200-24 38 }
	
	
    dhcpSnp     					OBJECT IDENTIFIER ::= { gs2200-24 100 }
	ipsg 							OBJECT IDENTIFIER ::= { gs2200-24 101 }
	arpInspect     					OBJECT IDENTIFIER ::= { gs2200-24 102 }
	loopGuardSetup     				OBJECT IDENTIFIER ::= { gs2200-24 104 }
    subnetBasedVlanSetup 			OBJECT IDENTIFIER ::= { gs2200-24 105 }
	mstp            				OBJECT IDENTIFIER ::= { gs2200-24 107 }    
	radiusServerSetup     			OBJECT IDENTIFIER ::= { gs2200-24 108 }
	tacacsServerSetup     			OBJECT IDENTIFIER ::= { gs2200-24 109 }
	aaaSetup     					OBJECT IDENTIFIER ::= { gs2200-24 110 }
	portIsolationSetup 				OBJECT IDENTIFIER ::= { gs2200-24 112 }
	l2ptSetup      					OBJECT IDENTIFIER ::= { gs2200-24 115 }
	transceiverInfo 				OBJECT IDENTIFIER ::= { gs2200-24 117 }
	dot3OamSetup     				OBJECT IDENTIFIER ::= { gs2200-24 118 }
	dot1agCfmSetup					OBJECT IDENTIFIER ::= { gs2200-24 119 }
	sysMemoryPool     				OBJECT IDENTIFIER ::= { gs2200-24 124 }
	pppoe							OBJECT IDENTIFIER ::= { gs2200-24 125 }
	arpSetup 						OBJECT IDENTIFIER ::= { gs2200-24 126 }
	errdisable     					OBJECT IDENTIFIER ::= { gs2200-24 130 }         
	cpuProtectionSetup     			OBJECT IDENTIFIER ::= { gs2200-24 131 }    				
		
--  1. sysInfo

        sysSwPlatformMajorVers OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      "SW platform major version, e.g. 3."
        ::= { sysInfo 1 }
        
        sysSwPlatformMinorVers OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      "SW platform minor version, e.g. 50."
        ::= { sysInfo 2 }
        
        sysSwModelString OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      "Model letters, e.g. TJ"
        ::= { sysInfo 3 }
 
        sysSwVersionControlNbr OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      "Version control number, e.g. 0."
        ::= { sysInfo 4 }
       
        sysSwDay OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      "SW compilation day, e.g. 19."
        ::= { sysInfo 5 }

        sysSwMonth OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      "SW compilation month, e.g. 8."
        ::= { sysInfo 6 }

        sysSwYear OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      "SW compilation year, e.g. 2008."
        ::= { sysInfo 7 }

        sysHwMajorVers OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      "HW major version number, e.g. 1."
        ::= { sysInfo 8 }

        sysHwMinorVers OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      "HW minor version number, e.g. 0."
        ::= { sysInfo 9 }

        sysSerialNumber OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      "Serial number"
        ::= { sysInfo 10 }

--  2.rateLimitSetup
	
        rateLimitState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
        	"Ingress/egress rate limiting enabled/disabled for the switch."
        ::= { rateLimitSetup 1 }

-- rateLimitPortTable

        rateLimitPortTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF RateLimitPortEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { rateLimitSetup 2 }
        
		rateLimitPortEntry OBJECT-TYPE
        SYNTAX	RateLimitPortEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in rateLimitPortTable."
        INDEX          	{ dot1dBasePort }
        ::= { rateLimitPortTable 1 }

        RateLimitPortEntry ::=
           SEQUENCE {		
			rateLimitPortIngRate	INTEGER,      			
			rateLimitPortEgrRate	INTEGER,
			rateLimitPortIngState	EnabledStatus,
			rateLimitPortEgrState	EnabledStatus
           }
        
        rateLimitPortIngRate OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Ingress peak rate in Kbit/s.  The range of FE port is between 1 and 100,000. For GE port, the range is between 1 and 1000,000."
        ::= { rateLimitPortEntry 2 }

        rateLimitPortEgrRate OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Egress peak rate in Kbit/s.  The range of FE port is between 1 and 100,000. For GE port, the range is between 1 and 1000,000."
        ::= { rateLimitPortEntry 3 }              
        
        rateLimitPortIngState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Ingress peak rate limiting enabled/disabled on the port."
        ::= { rateLimitPortEntry 5 }                                              
        
        rateLimitPortEgrState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Egress peak rate limiting enabled/disabled on the port."
        ::= { rateLimitPortEntry 6 }        
    
                
--  3. brLimitSetup
	
        brLimitState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Broadcast/multicast/DLF rate limiting enabled/disabled for the switch."
        ::= { brLimitSetup 1 }

-- brLimitPortTable       
        brLimitPortTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF BrLimitPortEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { brLimitSetup 2 }
        
		brLimitPortEntry OBJECT-TYPE
        SYNTAX	BrLimitPortEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in brLimitPortTable."
        INDEX          	{ dot1dBasePort }
        ::= { brLimitPortTable 1 }

        BrLimitPortEntry ::=
           SEQUENCE {
        	brLimitPortBrState	EnabledStatus,
			brLimitPortBrRate	INTEGER,
			brLimitPortMcState	EnabledStatus,
			brLimitPortMcRate	INTEGER,
			brLimitPortDlfState	EnabledStatus,
			brLimitPortDlfRate	INTEGER
           }

        brLimitPortBrState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Broadcast rate limiting enabled/disabled on the port."
        ::= { brLimitPortEntry 1 }
        
        brLimitPortBrRate OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Allowed broadcast rate in pkts/s. For FE port, 
                      	the maximum value is 148800. For GE port, the maximum value is 262143."
        ::= { brLimitPortEntry 2 }
        
        brLimitPortMcState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Multicast rate limiting enabled/disabled on the port."
        ::= { brLimitPortEntry 3 }

        brLimitPortMcRate OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"AAllowed mullticast rate in pkts/s. For FE port, 
                      	the maximum value is 148800. For GE port, the maximum value is 262143."
        ::= { brLimitPortEntry 4 }

        brLimitPortDlfState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Destination lookup failure frames rate limiting enabled/disabled on the port."
        ::= { brLimitPortEntry 5 }

        brLimitPortDlfRate OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Allowed destination lookup failure frames rate in pkts/s. 
                      	For FE port, the maximum value is 148800. For GE port, the maximum value is 262143."
        ::= { brLimitPortEntry 6 }
       
        
--  4. portSecuritySetup
	
		portSecurityState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Port Security enabled/disabled on the switch."
        ::= { portSecuritySetup 1 }
        
        portSecurityPortTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF PortSecurityPortEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { portSecuritySetup 2 }
        
		portSecurityPortEntry OBJECT-TYPE
        SYNTAX	PortSecurityPortEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in portSecurityPortTable."
        INDEX          	{ dot1dBasePort }
        ::= { portSecurityPortTable 1 }

        PortSecurityPortEntry ::=
           SEQUENCE {
        	portSecurityPortState		EnabledStatus,
			portSecurityPortLearnState	EnabledStatus,
			portSecurityPortCount		INTEGER
           }

        portSecurityPortState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Port Security enabled/disabled on the port. 
                      	Active(1) means this port only accept frames from static MAC addresses that are configured for the port, 
                      	and dynamic MAC address frames up to the number specified by portSecurityPortCount object."
        ::= { portSecurityPortEntry 1 }
        
        portSecurityPortLearnState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"MAC address learning  enabled/disable on the port."
        ::= { portSecurityPortEntry 2 }
        
        portSecurityPortCount OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Number of (dynamic) MAC addresses that may be learned on the port."
        ::= { portSecurityPortEntry 3 }

		portSecurityMacFreeze OBJECT-TYPE
        SYNTAX  PortList
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"This entry is used to execute MacFreeze action to port.
                      	It's meaningless while reading this entry."
        ::= { portSecuritySetup 3 }


--  5. vlanTrunkPortTable
	
        vlanTrunkPortTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF VlanTrunkPortEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { vlanTrunkSetup 1 }
        
		vlanTrunkPortEntry OBJECT-TYPE
        SYNTAX	VlanTrunkPortEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in vlanTrunkPortTable."
        INDEX          	{ dot1dBasePort }
        ::= { vlanTrunkPortTable 1 }

        VlanTrunkPortEntry ::=
           SEQUENCE {
        	vlanTrunkPortState		EnabledStatus
           }

        vlanTrunkPortState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"VlanTrunking enabled/disabled on the port. 
                      	Active(1) to allow frames belonging to unknown 
                      	VLAN groups to pass through the switch."
        ::= { vlanTrunkPortEntry 1 }
   
   
--  6. ctlProtTransSetup
    
    
--  7. vlanStackSetup
          

-- ------------------------------------------------------------------
--
--  dot1x Setup
--
--  for AAA Service
--  1. rename radius8021xSetup as dot1xSetup
--  2. remove radiusLoginPrecedence (AAA Service: authentication login)
--  3. remove radiusAnd8021xServer (AAA Service: radius server setup)
--  4. DO NOT change oid for backward compatible
--
-- ------------------------------------------------------------------
--  8. dot1xSetup
	                             
--  portAuthState                               
        portAuthState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"802.1x port authentication enabled/disabled for the switch."
        ::= { dot1xSetup 1 }                           
                                
--  portAuthTable

        portAuthTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF PortAuthEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { dot1xSetup 2 }
        
		portAuthEntry OBJECT-TYPE
        SYNTAX	PortAuthEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in portAuthTable."
        INDEX          	{ dot1dBasePort }
        ::= { portAuthTable 1 }

        PortAuthEntry ::=
           SEQUENCE {
        	portAuthEntryState	EnabledStatus,
        	portReAuthEntryState	EnabledStatus,
        	portReAuthEntryTimer	INTEGER,
        	portAuthQuietPeriod	INTEGER ,
        	portAuthTxPeriod	INTEGER ,
        	portAuthSupplicantTimeout	INTEGER ,
        	portAuthMaxRequest	INTEGER ,
        	portAuthGuestVlanState	EnabledStatus,
        	portAuthGuestVlan	INTEGER ,
        	portAuthGuestVlanHostMode	INTEGER ,
        	portAuthGuestVlanHostModeMultiSecureNumber	INTEGER 
           }

        portAuthEntryState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"802.1x port authentication enabled or disabled on the port."
        ::= { portAuthEntry 1 }

        portReAuthEntryState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"802.1x port re-authentication enabled or disabled on the port."
        ::= { portAuthEntry 2 }

        portReAuthEntryTimer OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Re-authentication timer in seconds."
        ::= { portAuthEntry 3 }

		portAuthQuietPeriod OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Quiet period in seconds."
        ::= { portAuthEntry 4 }
        
        portAuthTxPeriod OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Tx period in seconds."
        ::= { portAuthEntry 5 }   
        
        portAuthSupplicantTimeout OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Supplicant Timeout in seconds."
        ::= { portAuthEntry 6 } 
        
        portAuthMaxRequest OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Max request."
        ::= { portAuthEntry 7 }

        portAuthGuestVlanState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Guest Vlan State."
        ::= { portAuthEntry 8 }
                                 
        portAuthGuestVlan OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Guest Vlan ID."
        ::= { portAuthEntry 9 }

        portAuthGuestVlanHostMode OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Guest Vlan Host Mode."
        ::= { portAuthEntry 10 }   
        
        portAuthGuestVlanHostModeMultiSecureNumber OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Guest Vlan Host Mode Multi-Secure number."
        ::= { portAuthEntry 11 }
        
--  9. hwMonitorInfo   

-- 		tempTable

        tempTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF TempEntry
        ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
                 	""
        ::= { hwMonitorInfo 2 }
        
		tempEntry OBJECT-TYPE
        SYNTAX	TempEntry
        ACCESS	not-accessible
        STATUS	current
        DESCRIPTION    	"An entry in tempTable."
        INDEX          	{ tempIndex }
        ::= { tempTable 1 }

        TempEntry ::=
           SEQUENCE {
        	tempIndex	INTEGER,
        	tempCurValue	INTEGER,
        	tempMaxValue	INTEGER,
        	tempMinValue	INTEGER,
        	tempHighThresh	INTEGER,
        	tempDescr	DisplayString
           }

        tempIndex OBJECT-TYPE
        SYNTAX  INTEGER {
        	mac (1),
        	cpu (2),
        	phy (3)
        }
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"Index of temperature unit. 1:MAC, 2:CPU, 3:PHY"
        ::= { tempEntry 1 }

        tempCurValue OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"The current temperature measured at this sensor."
        ::= { tempEntry 2 }

        tempMaxValue OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"The maximum temperature measured at this sensor."
        ::= { tempEntry 3 }
        
        tempMinValue OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"The minimum temperature measured at this sensor."
        ::= { tempEntry 4 }

        tempHighThresh OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"The upper temperature limit at this sensor."
        ::= { tempEntry 5 }

        tempDescr OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"'Normal' indicates temperatures below the threshold and 'Error' for those above."
        ::= { tempEntry 6 }

-- 		voltageTable

        voltageTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF VoltageEntry
        ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
                 	""
        ::= { hwMonitorInfo 3 }
        
		voltageEntry OBJECT-TYPE
        SYNTAX	VoltageEntry
        ACCESS	not-accessible
        STATUS	current
        DESCRIPTION    	"An entry in voltageTable."
        INDEX          	{ voltageIndex }
        ::= { voltageTable 1 }

        VoltageEntry ::=
           SEQUENCE {
        	voltageIndex		INTEGER,
        	voltageCurValue		INTEGER,
        	voltageMaxValue		INTEGER,
        	voltageMinValue		INTEGER,
        	voltageNominalValue	INTEGER,
        	voltageLowThresh	INTEGER,
        	voltageDescr		DisplayString
           }

        voltageIndex OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"Index of voltage."
        ::= { voltageEntry 1 }

        voltageCurValue OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"The current voltage reading."
        ::= { voltageEntry 2 }

        voltageMaxValue OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"The maximum voltage measured at this point."
        ::= { voltageEntry 3 }
        
        voltageMinValue OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"The minimum voltage measured at this point."
        ::= { voltageEntry 4 }

        voltageNominalValue OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"The normal voltage at wchich the switch work."
        ::= { voltageEntry 5 }

        voltageLowThresh OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"The minimum voltage at which the switch should work."
        ::= { voltageEntry 6 }

        voltageDescr OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"'Normal' indicates that the voltage is within an acceptable operating range 
                      	at this point; otherwise 'Error' is displayed."
        ::= { voltageEntry 7 }       
         
--  10. snmpSetup
	
        snmpGetCommunity OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { snmpSetup 1 }

        snmpSetCommunity OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { snmpSetup 2 }

        snmpTrapCommunity OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { snmpSetup 3 }

--  snmpTrapDestTable
        snmpTrapDestTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF SnmpTrapDestEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { snmpSetup 4 }
        
		snmpTrapDestEntry OBJECT-TYPE
        SYNTAX	SnmpTrapDestEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in snmpTrapDestTable."
        INDEX          	{ snmpTrapDestIP }
        ::= { snmpTrapDestTable 1 }

        SnmpTrapDestEntry ::=
           SEQUENCE {
        	snmpTrapDestIP			IpAddress,
        	snmpTrapDestRowStatus	RowStatus,
        	snmpTrapDestPort		INTEGER,
        	snmpTrapVersion			INTEGER,
        	snmpTrapUserName		DisplayString  
           }
           
        snmpTrapDestIP OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"IP address of trap destination."
        ::= { snmpTrapDestEntry 1 }
        
        snmpTrapDestRowStatus OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-create
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { snmpTrapDestEntry 2 }

        snmpTrapDestPort OBJECT-TYPE
        SYNTAX INTEGER
        MAX-ACCESS read-write
        STATUS  mandatory
        DESCRIPTION
                      	"The UDP port of the trap destination."
        ::= { snmpTrapDestEntry 3 }
        
        snmpTrapVersion OBJECT-TYPE
        SYNTAX  INTEGER {
			v1(0),
			v2c(1),
			v3(2)
		}
        MAX-ACCESS read-write
        STATUS  mandatory
        DESCRIPTION
                      	"The SNMP protocol version to send traps."
        ::= { snmpTrapDestEntry 4 }

        snmpTrapUserName OBJECT-TYPE
        SYNTAX DisplayString
        MAX-ACCESS read-write
        STATUS  mandatory
        DESCRIPTION
                      	"The user name for sending SNMPv3 traps."
        ::= { snmpTrapDestEntry 5 }   

--snmpVersion
        snmpVersion OBJECT-TYPE
		SYNTAX 	INTEGER {
			v2c(0),
			v3(1),
			v3v2c(2)
		}
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
		"The SNMP version to be used. v3v2c means that the manager 
		can get/set by SNMPv3 and can get by SNMPv2c."
        ::= { snmpSetup 5 }

-- snmpUserTable
	
		snmpUserTable OBJECT-TYPE
    	SYNTAX SEQUENCE OF SnmpUserEntry
    	MAX-ACCESS not-accessible
    	STATUS current
    	DESCRIPTION
                "A table that contains SNMPv3 user information."
    	::= { snmpSetup 6 }

    	snmpUserEntry OBJECT-TYPE
        SYNTAX SnmpUserEntry
        MAX-ACCESS not-accessible
        STATUS current
        DESCRIPTION
        	"An entry of snmpUserTable."
        INDEX { snmpUserName }
        ::= { snmpUserTable 1 }

   		SnmpUserEntry ::=
       	SEQUENCE {
           	 snmpUserName			DisplayString,
             snmpUserSecurityLevel	INTEGER,
             snmpUserAuthProtocol	INTEGER,
             snmpUserPrivProtocol	INTEGER,
			 snmpUserGroup	        DisplayString
--         	 snmpUserAuthPassword	DisplayString,            
--	     	 snmpUserPrivPassword	DisplayString,
--			 snmpUserRowStatus	    RowStatus			 
    	   	} 

		snmpUserName OBJECT-TYPE
		SYNTAX DisplayString
		MAX-ACCESS read-only
		STATUS current
		DESCRIPTION
			"The user name."
		::= { snmpUserEntry 1 }

		snmpUserSecurityLevel OBJECT-TYPE
		SYNTAX  INTEGER {
			noAuthNoPriv(0),
			authNoPriv(1),
			authPriv(2)
		}
		MAX-ACCESS read-write
		STATUS current
		DESCRIPTION
			"The level of security at which SNMP messages can be sent or 
			with which operations are being processed."
		::= { snmpUserEntry 2 }	

		snmpUserAuthProtocol OBJECT-TYPE
		SYNTAX  INTEGER {
			md5(0),
			sha(1)
		}
		MAX-ACCESS read-write
		STATUS current
		DESCRIPTION
			"The type of authentication protocol to be used."
		::= { snmpUserEntry 3 }

		snmpUserPrivProtocol OBJECT-TYPE
		SYNTAX 	INTEGER {
			des(0),
			aes(1)
		}
		MAX-ACCESS read-write
		STATUS current
		DESCRIPTION
			"The type of privacy protocol to be used."
		::= { snmpUserEntry 4 }
		
		snmpUserGroup OBJECT-TYPE
		SYNTAX DisplayString
		MAX-ACCESS read-only
		STATUS current
		DESCRIPTION
			"The group name which this user belongs to."
		::= { snmpUserEntry 5 }

--		snmpUserAuthPassword OBJECT-TYPE
--		SYNTAX DisplayString
--		MAX-ACCESS read-write
--		STATUS current
--		DESCRIPTION
--			"The authentication password."
--		::= { snmpUserEntry 6 }
		                                 
--		snmpUserPrivPassword OBJECT-TYPE
--		SYNTAX DisplayString
--		MAX-ACCESS read-write
--		STATUS current
--		DESCRIPTION
--			"The privacy password."
--		::= { snmpUserEntry 7 } 
				
--        snmpUserRowStatus OBJECT-TYPE
--        SYNTAX  RowStatus
--        ACCESS  read-create
--        STATUS  mandatory
--        DESCRIPTION
--                      	""
--        ::= { snmpUserEntry 8 }		


--  snmpTrapGroupTable
        snmpTrapGroupTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF SnmpTrapGroupEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { snmpSetup 7 }
        
		snmpTrapGroupEntry OBJECT-TYPE
        SYNTAX	SnmpTrapGroupEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in snmpTrapGroupTable."
        INDEX          	{ snmpTrapDestIP }
        ::= { snmpTrapGroupTable 1 }

        SnmpTrapGroupEntry ::=
           SEQUENCE {
        	snmpTrapSystemGroup	 	BITS,
        	snmpTrapInterfaceGroup	BITS,
        	snmpTrapAAAGroup  		BITS,
        	snmpTrapIPGroup			BITS,
        	snmpTrapSwitchGroup	 	BITS
           }

        snmpTrapSystemGroup OBJECT-TYPE
        SYNTAX	BITS {
			coldStart(0),
			warmStart(1),
			temperature(3),
			voltage(4),			
			reset(5),
			timeSync(6),
			intrusionlock(7),
			loopGuard(13),
			errdisable(14)
			}
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { snmpTrapGroupEntry 1 }
        
        snmpTrapInterfaceGroup OBJECT-TYPE
        SYNTAX	BITS {
			linkup(0),
			linkdown(1),
			lldp(3),
			transceiver-ddm(4)
		}
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { snmpTrapGroupEntry 2 }
        
        snmpTrapAAAGroup OBJECT-TYPE
        SYNTAX	BITS {
			authentication(0),
			accounting(1)
		}
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { snmpTrapGroupEntry 3 }        
        
        snmpTrapIPGroup OBJECT-TYPE
        SYNTAX	BITS {
			ping(0),
			traceroute(1)
		}
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { snmpTrapGroupEntry 4 }                
        
        snmpTrapSwitchGroup OBJECT-TYPE
       	SYNTAX	BITS {
			stp(0),
			mactable(1),
			rmon(2),
			cfm(3)
		}
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { snmpTrapGroupEntry 5 }                
          
          
--  11. dateTimeSetup
	
        dateTimeServerType OBJECT-TYPE
        SYNTAX  INTEGER {
        	none(1),
			daytime(2),
			time(3),
			ntp(4)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "The time service protocol."
        ::= { dateTimeSetup 1 }
        
        dateTimeServerIP OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "IP address of time server."
        ::= { dateTimeSetup 2 }

        dateTimeZone OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "The time difference between UTC. Ex: +01"
        ::= { dateTimeSetup 3 }

        dateTimeNewDateYear OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "The new date in year."
        ::= { dateTimeSetup 4 }

        dateTimeNewDateMonth OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "The new date in month."
        ::= { dateTimeSetup 5 }

        dateTimeNewDateDay OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "The new date in day."
        ::= { dateTimeSetup 6 }
        
        dateTimeNewTimeHour OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "The new time in hour."
        ::= { dateTimeSetup 7 }

        dateTimeNewTimeMinute OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "The new time in minute."
        ::= { dateTimeSetup 8 }

        dateTimeNewTimeSecond OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "The new time in second."
        ::= { dateTimeSetup 9 }
        
--  dateTimeDaylightSavingTimeSetup OBJECT-TYPE
	dateTimeDaylightSavingTimeSetup     	OBJECT IDENTIFIER ::= { dateTimeSetup 10 }
	
        daylightSavingTimeState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "Daylight saving time service enabled/disabled for the switch."
        ::= { dateTimeDaylightSavingTimeSetup 1 }

        daylightSavingTimeStartDateWeek OBJECT-TYPE
        SYNTAX  INTEGER {
         		first(1),
         		second(2),
         		third(3),
         		fourth(4),
         		last(5)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "Daylight saving time service start week."
        ::= { dateTimeDaylightSavingTimeSetup 2 }

        daylightSavingTimeStartDateDay OBJECT-TYPE
        SYNTAX  INTEGER {
         		sunday(0),
         		monday(1),
         		tuesday(2),
         		wednesday(3),
         		thursday(4),
         		friday(5),
         		saturday(6)         		
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "Daylight saving time service start day."
        ::= { dateTimeDaylightSavingTimeSetup 3 }

        daylightSavingTimeStartDateMonth OBJECT-TYPE
        SYNTAX  INTEGER {
         		january(1),
         		february(2),
         		march(3),
         		april(4),
         		may(5),
         		june(6),
         		july(7),
         		august(8),
         		september(9),
         		october(10),
         		november(11),
         		december(12)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "Daylight saving time service start month."
        ::= { dateTimeDaylightSavingTimeSetup 4 }

        daylightSavingTimeStartDateHour OBJECT-TYPE
        SYNTAX  INTEGER 
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "Daylight saving time service start time."
        ::= { dateTimeDaylightSavingTimeSetup 5 }

        daylightSavingTimeEndDateWeek OBJECT-TYPE
        SYNTAX  INTEGER {
         		first(1),
         		second(2),
         		third(3),
         		fourth(4),
         		last(5)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "Daylight saving time service end week."
        ::= { dateTimeDaylightSavingTimeSetup 6 }

        daylightSavingTimeEndDateDay OBJECT-TYPE
        SYNTAX  INTEGER {
         		sunday(0),
         		monday(1),
         		tuesday(2),
         		wednesday(3),
         		thursday(4),
         		friday(5),
         		saturday(6)         		
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "Daylight saving time service end day."
        ::= { dateTimeDaylightSavingTimeSetup 7 }

        daylightSavingTimeEndDateMonth OBJECT-TYPE
        SYNTAX  INTEGER {
         		january(1),
         		february(2),
         		march(3),
         		april(4),
         		may(5),
         		june(6),
         		july(7),
         		august(8),
         		september(9),
         		october(10),
         		november(11),
         		december(12)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "Daylight saving time service end month."
        ::= { dateTimeDaylightSavingTimeSetup 8 }

        daylightSavingTimeEndDateHour OBJECT-TYPE
        SYNTAX  INTEGER 
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "Daylight saving time service end time."
        ::= { dateTimeDaylightSavingTimeSetup 9 }
        
        
--  12. sysMgmt
	
        sysMgmtConfigSave OBJECT-TYPE
        SYNTAX  INTEGER {
         		config_1(1),
         		config_2(2)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "If setting value is given, the variable write index will be set and running-config will be written to the assigned configuration file. 
                      If not, running-config will be written to the booting one.
					  Note: This action can access all configuration includes user account and AA configuration."
        ::= { sysMgmt 1 }
        
        sysMgmtBootupConfig OBJECT-TYPE
        SYNTAX  INTEGER {
         		config_1(1),
         		config_2(2)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "The setting value (read index) will be written into non-volatile memory. 
                      While rebooting, the variable write index is equal to read index initially. 
                      You can change the value of write index by CLI / MIB."
        ::= { sysMgmt 2 }
        
        sysMgmtReboot OBJECT-TYPE
        SYNTAX  INTEGER {
        		nothing(0),
        		reboot(1)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "Reboot switch from SNMP. 1:Reboot, 0:Nothing"
        ::= { sysMgmt 3 }

		sysMgmtDefaultConfig OBJECT-TYPE
        SYNTAX  INTEGER {
        		nothing(0),
        		reset_to_default(1)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "Erase running config and reset to default.
					   Note: This action can access all configuration includes user account and AA configuration."
        ::= { sysMgmt 4 }
        
		sysMgmtLastActionStatus OBJECT-TYPE
        SYNTAX  INTEGER {
        		none(0),
        		success(1),
        		fail(2)
        }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      "Display status of last mgmt action."
        ::= { sysMgmt 5 }        

		sysMgmtSystemStatus OBJECT-TYPE
		SYNTAX	BITS {
			sysAlarmDetected(0),
			sysTemperatureError(1),
			sysFanRPMError(2),
			sysVoltageRangeError(3)
		}
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
			"This variable indicates the status of the system.
 			The sysMgmtAlarmStatus is a bit map represented
 			a sum, therefore, it can represent multiple defects
 			simultaneously. The sysNoDefect should be set if and only if
 			no other flag is set.
 	
	 		The various bit positions are:
				0   sysAlarmDetected
				1   sysTemperatureError
				2   sysFanRPMError
				3   sysVoltageRangeError"
        ::= { sysMgmt 6 }        
        
		sysMgmtCPUUsage OBJECT-TYPE
		SYNTAX  INTEGER
		ACCESS  read-only
		STATUS  mandatory
		DESCRIPTION
			"Show device CPU load in %, it's the snapshot of CPU load when 
			getting the values."
		::= { sysMgmt 7 }
		
		sysMgmtBootupImage OBJECT-TYPE
        SYNTAX  INTEGER {
         		image_1(1),
         		image_2(2)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "The setting value (read index) will be written into non-volatile memory. 
                      When rebooting, the selected image will be used. 
                      You can change the value of write index by CLI / MIB."
        ::= { sysMgmt 8 }  
        
		sysMgmtCounterReset OBJECT-TYPE
        SYNTAX  INTEGER {
         		enable(1),
         		disable(2)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "Reset all port counters."
        ::= { sysMgmt 9 }  	

-- Tftp service                                                      
        sysMgmtTftpServiceSetup OBJECT IDENTIFIER ::= { sysMgmt 10 }                      

        sysMgmtTftpServerIp OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	" IP address of TFTP server"
        ::= { sysMgmtTftpServiceSetup 1 }
        
        sysMgmtTftpRemoteFileName OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"The file name that you want to backup to or restore from TFTP server"
        ::= { sysMgmtTftpServiceSetup 2 }     
        
        sysMgmtTftpConfigIndex OBJECT-TYPE
        SYNTAX  INTEGER {
        	    config_1(1),
				config_2(2)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Index of system configuration which you want to restore."
        ::= { sysMgmtTftpServiceSetup 3 }               
                               
        sysMgmtTftpAction OBJECT-TYPE
        SYNTAX  INTEGER {         
        		none(0),
         		backup_config(1),
				restore_config(2),
				merge_config(3)
        }
        ACCESS  read-write
        STATUS  mandatory        
		DESCRIPTION
                      	"Action of tftp service. 1: Backup running-config to tftp server, 2: Restore config from tftp server, 3: Merge config from tftp server.
						 Note: This action can access all configuration includes user account and AA configuration."
        ::= { sysMgmtTftpServiceSetup 4 }        	
        
		sysMgmtTftpActionStatus OBJECT-TYPE
        SYNTAX  INTEGER {
        		none(0),
        		success(1),
        		fail(2),
        		under-action(3)
        }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      "Display status of mgmt action."
        ::= { sysMgmtTftpServiceSetup 5 }    

		sysMgmtTftpActionPrivilege13 OBJECT-TYPE
        SYNTAX  INTEGER {         
        		none(0),
         		backup_config(1),
				restore_config(2),
				merge_config(3)
        }
        ACCESS  read-write
        STATUS  mandatory        
		DESCRIPTION
                      	" Action of tftp service. 1: Backup running-config to tftp server, 2: Restore config from tftp server, 3: Merge config from tftp server. 
						  Note: This action can access configuration with privilege 13."
        ::= { sysMgmtTftpServiceSetup 113 }
        
-- sysMgmt action with Privilege      
        sysMgmtConfigSavePrivilege13 OBJECT-TYPE
        SYNTAX  INTEGER {
         		config_1(1),
         		config_2(2)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "If setting value is given, the variable write index will be set and running-config will be written to the assigned configuration file. 
                      If not, running-config will be written to the booting one.
                      Note: This action can access configuration with privilege 13."
        ::= { sysMgmt 113 }

		sysMgmtDefaultConfigPrivilege13 OBJECT-TYPE
        SYNTAX  INTEGER {
        		nothing(0),
        		reset_to_default(1)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "Erase running config and reset to default. 
					  Note: This action can access configuration with privilege 13."
        ::= { sysMgmt 213 }  		
        
--  13. layer2Setup
	
        vlanTypeSetup OBJECT-TYPE
        SYNTAX  INTEGER {
         		dot1Q(1),
         		port_based(2)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { layer2Setup 1 }
        
        igmpSnoopingStateSetup OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { layer2Setup 2 }
               
        tagVlanPortIsolationState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "This setting will also show the result in the portIsolationState"
        ::= { layer2Setup 3 }

        stpState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { layer2Setup 4 }
               
        tagVlanIngressCheckState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { layer2Setup 5 }
               
        igmpFilteringStateSetup OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { layer2Setup 6 }
        
        unknownMulticastFrameForwarding OBJECT-TYPE
        SYNTAX  INTEGER {
         	flooding(1),
         	drop(2)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { layer2Setup 7 }
 
        multicastGrpHostTimeout OBJECT-TYPE
        SYNTAX  INTEGER 
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "Specify host timeout for all multicast groups when the specific port is in auto mode."
        ::= { layer2Setup 8 }
        
        reservedMulticastFrameForwarding OBJECT-TYPE
        SYNTAX  INTEGER {
         	flooding(1),
         	drop(2)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { layer2Setup 9 }  

        igmpsnp8021pPriority OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "Set the 802.1p priority of control messages for igmp-snooping(0~8, 8-No Change)"
        ::= { layer2Setup 10 }  
                
		igmpsnpVlanMode OBJECT-TYPE
        SYNTAX  INTEGER {
         	auto(1),
         	fixed(2)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { layer2Setup 11 }                
        
        stpMode OBJECT-TYPE
        SYNTAX  INTEGER {
         	rstp(1),
         	mrstp(2),
         	mstp (3)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { layer2Setup 12 }  
				

--  igmpsnpVlanTable
        igmpsnpVlanTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF IgmpsnpVlanEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { layer2Setup 13 }    

		igmpsnpVlanEntry OBJECT-TYPE
        SYNTAX	IgmpsnpVlanEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in IgmpsnpVlanTable."
        INDEX          	{ igmpsnpVid }
        ::= { igmpsnpVlanTable 1 }

        IgmpsnpVlanEntry ::=
           SEQUENCE {
			igmpsnpVid				INTEGER,
        	igmpsnpVlanName			DisplayString,        	
        	igmpsnpVlanRowStatus	RowStatus
           }    
   
        igmpsnpVid OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpsnpVlanEntry 1 }        


    	igmpsnpVlanName OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpsnpVlanEntry 2 }
        
        
        igmpsnpVlanRowStatus OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-create
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= {  igmpsnpVlanEntry 3 }

		igmpsnpQuerierMode OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { layer2Setup 14 }

        ethernetCfmStateSetup OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { layer2Setup 15 }
               
		lldpStateSetup OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { layer2Setup 16 }
		
		igmpSnpReportProxySetup OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      "IGMP Snooping report-proxy mode"
        ::= { layer2Setup 18 } 
             
--  14. ipSetup
	
        dnsIpAddress OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { ipSetup 1 }	
	
		inbandIpSetup     	OBJECT IDENTIFIER ::= { ipSetup 3 }

        inbandIpType OBJECT-TYPE
        SYNTAX  INTEGER {
        	dhcp_client(0),
        	static_ip(1)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { inbandIpSetup 1 }
        
        inbandVid OBJECT-TYPE
        SYNTAX  INTEGER 
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { inbandIpSetup 2 }	
	
        inbandStaticIp OBJECT-TYPE
        SYNTAX  IpAddress 
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { inbandIpSetup 3 }	
	
        inbandStaticSubnetMask OBJECT-TYPE
        SYNTAX  IpAddress 
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { inbandIpSetup 4 }	

        inbandStaticGateway OBJECT-TYPE
        SYNTAX  IpAddress 
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { inbandIpSetup 5 }	

        maxNumOfInbandIp OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { ipSetup 4 }

--  inbandIpTable
        inbandIpTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF InbandIpEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { ipSetup 5 }
        
		inbandIpEntry OBJECT-TYPE
        SYNTAX	InbandIpEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in inbandIpTable."
        INDEX          	{ inbandEntryIp, inbandEntryVid }
        ::= { inbandIpTable 1 }

        InbandIpEntry ::=
		SEQUENCE {
        	inbandEntryIp			IpAddress,
        	inbandEntrySubnetMask	IpAddress,
        	inbandEntryGateway		IpAddress,
        	inbandEntryVid			INTEGER,
        	inbandEntryManageable	EnabledStatus,
        	inbandEntryRowStatus	RowStatus
        }

        inbandEntryIp OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { inbandIpEntry 1 }

        inbandEntrySubnetMask OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { inbandIpEntry 2 }

        inbandEntryGateway OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { inbandIpEntry 3 }

        inbandEntryVid OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { inbandIpEntry 4 }

        inbandEntryManageable OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { inbandIpEntry 5 }

        inbandEntryRowStatus OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-create
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { inbandIpEntry 6 }
      
        
--  15. filterSetup
	
--  filterTable
       	filterTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF FilterEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { filterSetup 1 }
        
		filterEntry OBJECT-TYPE
        SYNTAX	FilterEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in filterTable."
        INDEX          	{ filterMacAddr, filterVid }
        ::= { filterTable 1 }

        FilterEntry ::=
           SEQUENCE {
         	filterName			DisplayString,
        	filterActionState	INTEGER,
        	filterMacAddr		MacAddress,
        	filterVid			INTEGER,
        	filterRowStatus		RowStatus
           }
       
        filterName OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { filterEntry 1 }

        filterActionState OBJECT-TYPE
        SYNTAX  INTEGER {
		 discard_source(1),
		 discard_destination(2),
	 	 both(3)
		}
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { filterEntry 2 }

        filterMacAddr OBJECT-TYPE
        SYNTAX  MacAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { filterEntry 3 }

   		filterVid OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { filterEntry 4 }

   		filterRowStatus OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-create
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { filterEntry 5 }

--  16. mirrorSetup        
	
--  mirrorState                               
        mirrorState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { mirrorSetup 1 }
                                
        mirrorMonitorPort OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { mirrorSetup 2 }
                                
-- mirrorTable

        mirrorTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF MirrorEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { mirrorSetup 3 }
        
		mirrorEntry OBJECT-TYPE
        SYNTAX	MirrorEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in mirrorTable."
        INDEX          	{ dot1dBasePort }
        ::= { mirrorTable 1 }

        MirrorEntry ::=
           SEQUENCE {
        	mirrorMirroredState	EnabledStatus,
        	mirrorDirection		INTEGER
           }

        mirrorMirroredState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { mirrorEntry 1 }

        mirrorDirection OBJECT-TYPE
        SYNTAX  INTEGER {
        	ingress(0),
        	egress(1),
        	both(2)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { mirrorEntry 2 }


--  17. aggrSetup
	                           
        aggrState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { aggrSetup 1 }
                                
        aggrSystemPriority OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { aggrSetup 2 }
                             
-- aggrGroupTable

        aggrGroupTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF AggrGroupEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { aggrSetup 3 }
        
		aggrGroupEntry OBJECT-TYPE
        SYNTAX	AggrGroupEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in aggrGroupTable."
        INDEX          	{ aggrGroupIndex }
        ::= { aggrGroupTable 1 }

        AggrGroupEntry ::=
           SEQUENCE {
        	aggrGroupIndex			INTEGER,
        	aggrGroupState			EnabledStatus,
        	aggrGroupDynamicState	EnabledStatus,
        	aggrGroupCriteria    	INTEGER
           }

        aggrGroupIndex OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { aggrGroupEntry 1 }

        aggrGroupState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { aggrGroupEntry 2 }

        aggrGroupDynamicState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { aggrGroupEntry 3 }
        
        aggrGroupCriteria OBJECT-TYPE
        SYNTAX  INTEGER  {
        src-mac (1),
        dst-mac (2),
        src-dst-mac (3),
        src-ip  (4),
        dst-ip  (5),
        src-dst-ip (6)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                  	""
        ::= { aggrGroupEntry 4 }

-- aggrPortTable

        aggrPortTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF AggrPortEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { aggrSetup 4 }
        
		aggrPortEntry OBJECT-TYPE
        SYNTAX	AggrPortEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in aggrPortTable."
        INDEX          	{ dot1dBasePort }
        ::= { aggrPortTable 1 }

        AggrPortEntry ::=
           SEQUENCE {
        	aggrPortGroup				INTEGER,
        	aggrPortDynamicStateTimeout	INTEGER
           }

        aggrPortGroup OBJECT-TYPE
        SYNTAX  INTEGER {
        	none(0),
        	t1(1),
        	t2(2),
        	t3(3),
        	t4(4),
        	t5(5),
        	t6(6),
        	t7(7),
        	t8(8)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { aggrPortEntry 1 }

        aggrPortDynamicStateTimeout OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { aggrPortEntry 2 }
         
         
--  18. accessCtlSetup
	                                                         
--  accessCtlTable

        accessCtlTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF AccessCtlEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { accessCtlSetup 1 }
        
		accessCtlEntry OBJECT-TYPE
        SYNTAX	AccessCtlEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in accessCtlTable."
        INDEX          	{ accessCtlService }
        ::= { accessCtlTable 1 }

        AccessCtlEntry ::=
           SEQUENCE {
        	accessCtlService		INTEGER,
        	accessCtlEnable			EnabledStatus,
        	accessCtlServicePort	INTEGER,
        	accessCtlTimeout		INTEGER
           }

        accessCtlService OBJECT-TYPE
        SYNTAX  INTEGER {
        	telnet(1),
        	ssh(2),
        	ftp(3),
        	http(4),
        	https(5),
        	icmp(6),
        	snmp(7)
        }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { accessCtlEntry 1 }

        accessCtlEnable OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { accessCtlEntry 2 }

        accessCtlServicePort OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { accessCtlEntry 3 }

        accessCtlTimeout OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { accessCtlEntry 4 }

--  securedClientTable
        securedClientTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF SecuredClientEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { accessCtlSetup 2 }
        
		securedClientEntry OBJECT-TYPE
        SYNTAX	SecuredClientEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in securedClientTable."
        INDEX          	{ securedClientIndex }
        ::= { securedClientTable 1 }

        SecuredClientEntry ::=
           SEQUENCE {
           	securedClientIndex		INTEGER,
           	securedClientEnable		EnabledStatus,
        	securedClientStartIp	IpAddress,
        	securedClientEndIp		IpAddress,
        	securedClientService	BITS
           }

        securedClientIndex OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { securedClientEntry 1 }

        securedClientEnable OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { securedClientEntry 2 }

        securedClientStartIp OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { securedClientEntry 3 }

        securedClientEndIp OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { securedClientEntry 4 }

        securedClientService OBJECT-TYPE
        SYNTAX	BITS {
			telnet(0),
			ftp(1),
			http(2),
			icmp(3),
			snmp(4),
			ssh(5),
			https(6)
		}
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { securedClientEntry 5 }
           
           
--  19. queuingMethodSetup
		
--  portQueuingMethodTable
        portQueuingMethodTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF PortQueuingMethodEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { queuingMethodSetup 1 }
        
		portQueuingMethodEntry OBJECT-TYPE
        SYNTAX	PortQueuingMethodEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in portQueuingMethodTable."
        INDEX          	{ dot1dBasePort, portQueuingMethodQueue }
        ::= {portQueuingMethodTable 1}

        PortQueuingMethodEntry ::=
           SEQUENCE {
        	portQueuingMethodQueue	INTEGER,
        	portQueuingMethodWeight	INTEGER,
        	portQueuingMethodMode	INTEGER
           }

        portQueuingMethodQueue OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"0...7"
        ::= { portQueuingMethodEntry 1 }

        portQueuingMethodWeight OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	"0...15"
        ::= { portQueuingMethodEntry 2 }
 
        portQueuingMethodMode OBJECT-TYPE
        SYNTAX  INTEGER {
        	strictly_priority(0),
        	weighted_fair_scheduling(1),
        	weighted_round_robin(2)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { portQueuingMethodEntry 3 }

        portQueuingMethodHybridSpqTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF	PortQueuingMethodHybridSpqEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { queuingMethodSetup 2 }
        
		portQueuingMethodHybridSpqEntry OBJECT-TYPE
        SYNTAX	PortQueuingMethodHybridSpqEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in portQueuingMethodTable."
        INDEX          	{ dot1dBasePort }
        ::= { portQueuingMethodHybridSpqTable 1 }

        PortQueuingMethodHybridSpqEntry ::=
           SEQUENCE {
        		portQueuingMethodHybridSpq		INTEGER
           }    
                  
        portQueuingMethodHybridSpq	OBJECT-TYPE
        SYNTAX  INTEGER {
        	none(0),
        	q0(1),
        	q1(2),
        	q2(3),
        	q3(4),
        	q4(5),
        	q5(6),
        	q6(7),
        	q7(8)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { portQueuingMethodHybridSpqEntry 1 }
        
        
--  20. dhcpSetup
	
        globalDhcpRelay OBJECT IDENTIFIER ::= { dhcpSetup 1 }
        
        globalDhcpRelayEnable OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { globalDhcpRelay 1 }

        globalDhcpRelayOption82Enable OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { globalDhcpRelay 2 }

        globalDhcpRelayInfoEnable OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { globalDhcpRelay 3 }

        globalDhcpRelayInfoData OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { globalDhcpRelay 4 }

--  globalDhcpRelayRemoteServer

        maxNumberOfGlobalDhcpRelayRemoteServer OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { globalDhcpRelay 5 }

-- globalDhcpRelayRemoteServerTable
        globalDhcpRelayRemoteServerTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF GlobalDhcpRelayRemoteServerEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { globalDhcpRelay 6 }
        
		globalDhcpRelayRemoteServerEntry OBJECT-TYPE
        SYNTAX	GlobalDhcpRelayRemoteServerEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in globalDhcpRelayRemoteServerTable."
        INDEX          	{ globalDhcpRelayRemoteServerIp }
        ::= { globalDhcpRelayRemoteServerTable 1 }

        GlobalDhcpRelayRemoteServerEntry ::=
           SEQUENCE {
        	globalDhcpRelayRemoteServerIp			IpAddress,
        	globalDhcpRelayRemoteServerRowStatus	RowStatus
           }

        globalDhcpRelayRemoteServerIp OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { globalDhcpRelayRemoteServerEntry 1 }

        globalDhcpRelayRemoteServerRowStatus OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-create
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { globalDhcpRelayRemoteServerEntry 2 }
        
--
	dhcpRelay OBJECT IDENTIFIER ::= { dhcpSetup 2 }

   		dhcpRelayInfoData OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { dhcpRelay 1 }

		maxNumberOfDhcpRelay OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"The maximum number of DHCP relay entries that can be created. 
                      	A value of 0 for this object implies that there exists settings for 
                      	global DHCP relay."
        ::= { dhcpRelay 2 }
        
   		maxNumberOfDhcpRelayRemoteServer OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { dhcpRelay 3 }
 
 -- dhcpRelayTable   
   
     -- dhcpRelayRemoteServerTable
        dhcpRelayRemoteServerTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF DhcpRelayRemoteServerEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { dhcpRelay 4 }
        
		dhcpRelayRemoteServerEntry OBJECT-TYPE
        SYNTAX	DhcpRelayRemoteServerEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in dhcpRelayRemoteServerTable."
        INDEX          	{  dhcpRelayVid, dhcpRelayRemoteServerIp }
        ::= { dhcpRelayRemoteServerTable 1 }

        DhcpRelayRemoteServerEntry ::=
           SEQUENCE {  
            dhcpRelayVid		          	INTEGER,
           	dhcpRelayRemoteServerIp			IpAddress,
        	dhcpRelayRemoteServerRowStatus	RowStatus
           }     
           
        dhcpRelayVid OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { dhcpRelayRemoteServerEntry 1 }   
        
        dhcpRelayRemoteServerIp OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { dhcpRelayRemoteServerEntry 2 }

        dhcpRelayRemoteServerRowStatus OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-create
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { dhcpRelayRemoteServerEntry 3 }  
   
        
        dhcpRelayTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF DhcpRelayEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { dhcpRelay 5 }  	


		dhcpRelayEntry OBJECT-TYPE
        SYNTAX	DhcpRelayEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in dhcpRelayTable."
        INDEX          	{ dhcpRelayVid }
        ::= { dhcpRelayTable 1 }

        DhcpRelayEntry ::=
           SEQUENCE {
        	dhcpRelayOption82Enable   EnabledStatus,
        	dhcpRelayInfoEnable       EnabledStatus
           }

        dhcpRelayOption82Enable OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { dhcpRelayEntry 1 }

        dhcpRelayInfoEnable OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { dhcpRelayEntry 2 }        
        
      
      
--  21. staticRouteSetup
	
        maxNumberOfStaticRoutes OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { staticRouteSetup 1 }

-- staticRouteTable
        staticRouteTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF StaticRouteEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { staticRouteSetup 2 }
        
		staticRouteEntry OBJECT-TYPE
        SYNTAX	StaticRouteEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in staticRouteTable."
        INDEX          	{ staticRouteIp, staticRouteMask }
        ::= { staticRouteTable 1 }

        StaticRouteEntry ::=
           SEQUENCE {
           	staticRouteName			DisplayString,
        	staticRouteIp			IpAddress,
        	staticRouteMask			IpAddress,
        	staticRouteGateway		IpAddress,
        	staticRouteMetric		INTEGER,
        	staticRouteRowStatus	RowStatus
           }

        staticRouteName OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { staticRouteEntry 1 }

        staticRouteIp OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { staticRouteEntry 2 }

        staticRouteMask OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { staticRouteEntry 3 }
        
        staticRouteGateway OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { staticRouteEntry 4 }
        
        staticRouteMetric OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { staticRouteEntry 5 }

        staticRouteRowStatus OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-create
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { staticRouteEntry 6 }
           
           
--  22. arpInfo
	
--  arpTable
      	arpTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF ArpEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { arpInfo 1 }
        
		arpEntry OBJECT-TYPE
        SYNTAX	ArpEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in arpTable."
        INDEX          	{ arpIpAddr, arpMacVid }
        ::= { arpTable 1 }

        ArpEntry ::=
           SEQUENCE {
           	arpIndex	INTEGER,
        	arpIpAddr	IpAddress,
        	arpMacAddr	MacAddress,
        	arpMacVid	INTEGER,
        	arpType		INTEGER
           }

        arpIndex OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpEntry 1 }
        
        arpIpAddr OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpEntry 2 }

        arpMacAddr OBJECT-TYPE
        SYNTAX  MacAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpEntry 3 }

        arpMacVid OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpEntry 4 }

        arpType OBJECT-TYPE
        SYNTAX  INTEGER {
        	static(1),
        	dynamic(2)
        }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"1-static, 2-dynamic"
        ::= { arpEntry 5 }
     
  
-- =============================================== 23. PLT ??? ==========================================   
     
   
--  24. portOpModeSetup
	                                                         
--  portOpModePortTable 
        portOpModePortTable  OBJECT-TYPE
        SYNTAX	SEQUENCE OF PortOpModePortEntry 
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { portOpModeSetup 1 }
        
		portOpModePortEntry  OBJECT-TYPE
        SYNTAX	PortOpModePortEntry 
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in portOpModePortTable."
        INDEX          	{ dot1dBasePort }
        ::= { portOpModePortTable 1 }

        PortOpModePortEntry ::=
           SEQUENCE {
           	portOpModePortSpeedDuplex	INTEGER,
         	portOpModePortFlowCntl		INTEGER,
        	portOpModePortName			DisplayString,
        	portOpModePortModuleType	INTEGER,
        	portOpModePortLinkUpType	INTEGER,
        	portOpModePortIntrusionLock	EnabledStatus,
        	portOpModePortLBTestStatus	INTEGER,
	       	portOpModePortCounterReset	INTEGER
           }

        portOpModePortSpeedDuplex OBJECT-TYPE
        SYNTAX  INTEGER {
        	auto(0),
        	speed_10_half(1),
        	speed_10_full(2),
        	speed_100_half(3),
        	speed_100_full(4),
        	speed_1000_full(5)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { portOpModePortEntry 1 }
        
        portOpModePortFlowCntl OBJECT-TYPE
        SYNTAX  INTEGER {
        	off(0),
        	on(1)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { portOpModePortEntry 2 }

        portOpModePortName  OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { portOpModePortEntry 3 }
        
        portOpModePortModuleType  OBJECT-TYPE
        SYNTAX  INTEGER {
        	fast_ethernet_10_100(0),
        	gigabit_ethernet_100_1000(1)
        }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { portOpModePortEntry 4 }

        portOpModePortLinkUpType  OBJECT-TYPE
        SYNTAX  INTEGER {
        	down(0),
        	copper(1),
        	fiber(2)
        }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { portOpModePortEntry 5 }

		portOpModePortIntrusionLock OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { portOpModePortEntry 6 }

   		portOpModePortLBTestStatus OBJECT-TYPE
        SYNTAX  INTEGER {
        	none(0),
        	under_testing(1),
        	success(2),
        	fail(3)
        }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
               "This entry display latest loopback test status of port while performing loopback test."
        ::= { portOpModePortEntry 7 }
        
		portOpModePortCounterReset OBJECT-TYPE
        SYNTAX  INTEGER {
        	enable(1),
        	disable(2)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
               "This entry resets port counter."
        ::= { portOpModePortEntry 8 }      
                       
                       
-- 25.portBasedVlanSetup
	                                                        
-- portBasedVlanPortListTable 

        portBasedVlanPortListTable  OBJECT-TYPE
        SYNTAX	SEQUENCE OF PortBasedVlanPortListEntry 
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { portBasedVlanSetup 1 }
        
		portBasedVlanPortListEntry  OBJECT-TYPE
        SYNTAX	PortBasedVlanPortListEntry 
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in portBasedVlanPortListTable."
        INDEX          	{ dot1dBasePort }
        ::= { portBasedVlanPortListTable 1 }

        PortBasedVlanPortListEntry ::=
           SEQUENCE {
        	portBasedVlanPortListMembers	OCTET STRING
           }

        portBasedVlanPortListMembers OBJECT-TYPE
        SYNTAX  PortList
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { portBasedVlanPortListEntry 1 }  
        
        
--26. fault MIB    


-- **************************************************************************
-- Local Textual Conventions
-- **************************************************************************

		UtcTimeStamp ::= TEXTUAL-CONVENTION
    	STATUS current
    	DESCRIPTION
        		"Universal Time Coordinated as a 32-bit value that designates
            	the number of seconds since Jan 1, 1970 12:00AM."
    	SYNTAX  Unsigned32

		EventIdNumber ::= TEXTUAL-CONVENTION
   		STATUS current
   		DESCRIPTION
          		"This textual convention describes the index that uniquely 
           		identifies a fault event type in the entire system. Every fault 
           		event type, e.g. link down, has a unique EventIdNumber."
   		SYNTAX  Integer32

		EventSeverity ::= TEXTUAL-CONVENTION
   		STATUS current
   		DESCRIPTION
          		"This textual convention describes the severity of a fault event.
          		 The decreasing order of severity is shown in the textual 
           		convention."
   		SYNTAX  INTEGER{
             critical (1),
             major (2),
             minor (3),
             informational (4)
        }

		EventServiceAffective ::= TEXTUAL-CONVENTION
   		STATUS current
   		DESCRIPTION
        		"This textual convention indicates whether an event is immediately 
        		service affecting or not."
   		SYNTAX  INTEGER{
   	    	noServiceAffected (1),
	    	serviceAffected (2)
        }
          
		InstanceType ::= TEXTUAL-CONVENTION
    	STATUS current
    	DESCRIPTION
          	"This textual convention describes the type of an instanceId
            associated with each event and by that means specifies how 
            the instanceId variable should be intepreted.
            
            Various instanceId types are specified below to enable fault 
            monitoring for different kind of devices from fixed 
            configuration pizza boxes to multi chassis nodes. All 
            instanceId types may not need to be used in every device 
            type. 
            
            Note also that instanceId semantics are element type dependent
            (e.g. different kind of interface naming conventions may be used)
            and thus instanceId usage may vary from element to element.    
            
    =========================================================================
    Type            Description                                 Example form 
                                                                of InstanceId
    =========================================================================
    unknown    (1)  unknown type                               - Irrelevant-
    -------------------------------------------------------------------------
    node       (2)  Associated with events originating from          1
                    the node. Used for general events that     (Node number)
                    can not be associated with any specific 
                    block. InstanceId value 1 is used for 
                    single node equipment.                                 
    -------------------------------------------------------------------------
    shelf      (3)  Associated with events originating from          1
                    the shelf. In the case of fixed       	(shelf number)
                    configuration devices this type is used 
                    for events that are associated with the
                    physical enclosure, e.g. faults related 
                    to fan etc. InstanceId value 1 is used 
                    for single self equipment.
    -------------------------------------------------------------------------  
    line       (4)  Associated with events originating from
                    physical interfaces or associated 
                    components such as line cards. 
                    InstanceId usage examples for faults 
                    originating from: 
                    - Physical port: Simply port number, e.g. .......1 
    -------------------------------------------------------------------------
    switch     (5)  Associated with events originating from          1
                    from a switch chip or a switch card.      (switch number) 
                    For single switch equipment InstanceId 
                    value 1 is used, for multi swich nodes 
                    InstanceId semantics if for further 
                    study. 
    -------------------------------------------------------------------------
    lsp        (6)  Associated with events originating from          1
                    a particular lsp.                           (lsp index)
                    NOTE: In this case the InstanceName 
                    contains the lsp name and InstanceId 
                    contains lsp index.
    -------------------------------------------------------------------------
    l2Interface(7)  Associated with events originating from        - TBD - 
                    a particular layer 2 interface. Used for
                    layer 2 related events such as L2 control 
                    protocol faults. InstanceId semantics is 
                    for further study. 
    -------------------------------------------------------------------------
    l3Interface(8)  Associated with events originating from        - TBD - 
                    a particular layer 3 interface. Used for
                    layer 3 related events such as L3 control 
                    protocol faults. InstanceId semantics is 
                    for further study.
    -------------------------------------------------------------------------
    rowIndex  (9)   Associated with events reporting about a 
                    'logical' or conceptual table that consists 
                    of rows.  The Instance Id is the index/key
                    for a row in the table.  The format of the 
                    Instance Id will simply be a series of decimal 
                    numbers seperated by a '.':   
    ========================================================================="
    
    	SYNTAX  INTEGER {
              unknown (1),
              node (2),
              shelf (3),
              line (4),
              switch (5),
              lsp (6),
              l2Interface(7),
              l3Interface(8),
              rowIndex(9)
        }


-- *****************************************************************************
-- Top level structure of this MIB.
-- *****************************************************************************

		eventObjects        OBJECT IDENTIFIER ::= { faultMIB  1 }

-- *****************************************************************************
-- Event Table
-- 
-- Event table reports all currently active fault events. Only normal type of 
-- faults (i.e. faults that are not automatically and immediately cleared) are 
-- present in the event table. Delta faults (i.e. faults that are automatically 
-- cleared) are reported only by using trap messages. 
-- *****************************************************************************

		eventTable OBJECT-TYPE
   		SYNTAX      SEQUENCE OF EventEntry
    	MAX-ACCESS  not-accessible
    	STATUS      current
    	DESCRIPTION
            "A list of currently active fault events. All faults 
             of normal type regardless of their severity level 
             are recorded in the event table. When a normal 
             type fault is cleared it is deleted from the event 
             table."
    	::= { eventObjects 1 }

		eventEntry OBJECT-TYPE
    	SYNTAX      EventEntry
    	MAX-ACCESS  not-accessible
    	STATUS      current
    	DESCRIPTION
            "An entry containing information about an
             event in the event table."
    	INDEX   { eventSeqNum }
    	::= { eventTable 1 }

		EventEntry ::=
    	SEQUENCE {
      		eventSeqNum           Integer32,
      		eventEventId          EventIdNumber,
      		eventName             DisplayString,
      		eventInstanceType     InstanceType,
      		eventInstanceId       DisplayString, 
      		eventInstanceName     DisplayString, 
      		eventSeverity         EventSeverity,
      		eventSetTime          UtcTimeStamp, 
      		eventDescription      DisplayString,
      		eventServAffective    EventServiceAffective, 
      		eventInstanceIdNumber Integer32 
   		}

		eventSeqNum OBJECT-TYPE
   		SYNTAX      Integer32
    	MAX-ACCESS  read-only
    	STATUS      current
    	DESCRIPTION
            "This variable represents the sequence number of an event. 
             Sequence number is incremented monotonically starting 
             from 0 until it reaches its maximum and wraps around back 
             to 0.
             
             Sequence number is incremented when 
             - the state of a normal type fault is set on (the same sequence 
             number is present in the events table as well as in the trap 
             that is sent to notify about the fault on event)
             - delta event occurs (sequence number present in trap message)
             - the state of a normal type fault is set off (sequence number 
             present in trap that is sent to notify for clearing)."
    	::= { eventEntry 1 }

		eventEventId OBJECT-TYPE
    	SYNTAX      EventIdNumber
    	MAX-ACCESS  read-only
    	STATUS      current
    	DESCRIPTION
            "This variable represents the event ID which uniquely 
             identifies the event in the entire system."
   		::= { eventEntry 2 }

		eventName OBJECT-TYPE
    	SYNTAX      DisplayString (SIZE (0..40))
    	MAX-ACCESS  read-only
    	STATUS      current
    	DESCRIPTION
            "This variable represents the name of the event, for 
             example 'Ethernet Link Down'"
    	::= { eventEntry 3 }

		eventInstanceType OBJECT-TYPE
    	SYNTAX      InstanceType 
   		MAX-ACCESS  read-only
    	STATUS      current
    	DESCRIPTION
            "This variable represents the type of InstanceId of a 
             particular event in the event table. In brief 
             the instanceType refers to the type of sub-component
             generating this event in the system, for example 
             switch (5). For more details see the textual 
             conventions section.

             AFFECTS:   eventInstanceId, 
                        eventInstanceName"
    	::= { eventEntry 4 }

		eventInstanceId OBJECT-TYPE
    	SYNTAX  DisplayString
    	MAX-ACCESS  read-only
    	STATUS      current
   		DESCRIPTION
            "This variable represents the InstanceId of a particular 
             event in the event current table. In brief the instanceId 
             refers to the sub-component generating this event in the 
             system, for example '1' for port 1. For more details see 
             the textual conventions section.

             DEPENDS ON:  eventInstanceType"
    	::= { eventEntry 5 }

		eventInstanceName OBJECT-TYPE
    	SYNTAX      DisplayString
    	MAX-ACCESS  read-only
    	STATUS      current
    	DESCRIPTION
            "This variable is mainly used to store additional information
             about the sub-component that is generating an event. For 
             example this field may specify what cooling fan is faulty.

             DEPENDS ON:  eventInstanceType"
    	::= { eventEntry 6 }

		eventSeverity OBJECT-TYPE
    	SYNTAX      EventSeverity
    	MAX-ACCESS  read-only
    	STATUS      current
    	DESCRIPTION
            "This variable dictates the urgency of action when a event
             occurs. There are four severity levels - Critical, Major, 
             Minor, and Informational.  Critical events are those, which 
             require immediate operator intervention to prevent/reduce 
             system down time. Major events require quick attention and 
             Minor events possibly require some attention. Informational 
             events indicate the occurrence of events that may need to be 
             investigated."
    	::= { eventEntry 7 }

		eventSetTime OBJECT-TYPE
    	SYNTAX      UtcTimeStamp
    	MAX-ACCESS  read-only
    	STATUS      current
    	DESCRIPTION
            "This table contains only normal events and this variable 
             represents the time when the event become active, i.e. the 
             number of seconds since Jan 1, 1970 12:00AM."
    	::= { eventEntry 8 }


		eventDescription OBJECT-TYPE
    	SYNTAX      DisplayString (SIZE (0..255))
    	MAX-ACCESS  read-only
    	STATUS      current
    	DESCRIPTION
            "This variable contains a description of the event and reasons 
             behind the event. This is a free format alpha-numeric string 
             that is set by the entity generating this event. This variable 
             may be empty if there is no usefull information to report. 
             The maximum length of this string is 255 characters."
    	::= { eventEntry 9 }
    
		eventServAffective OBJECT-TYPE
    	SYNTAX      EventServiceAffective
    	MAX-ACCESS  read-only
    	STATUS      current
    	DESCRIPTION
            "This variable indicates whether the event is service affective or not" 
    	::= { eventEntry 10 }        

		eventInstanceIdNumber OBJECT-TYPE
   		SYNTAX  Integer32
    	MAX-ACCESS  read-only
    	STATUS      current
    	DESCRIPTION
            "This variable represents the InstanceId of a particular 
             event in the event current table. In brief the instanceId 
             refers to the sub-component generating this event in the 
             system, for example '1' for port 1. For more details see 
             the textual conventions section.

             DEPENDS ON:  eventInstanceType"
    	::= { eventEntry 11 }


-- fault Trap MIB     
                                                                                         
		trapInfoObjects             OBJECT IDENTIFIER ::= { faultTrapsMIB 1}
		trapNotifications           OBJECT IDENTIFIER ::= { faultTrapsMIB 2}


-- **************************************************************************
-- Local Textual Conventions
-- **************************************************************************

		EventPersistence ::= TEXTUAL-CONVENTION
   		STATUS current
   		DESCRIPTION
          "This textual convention indicates whether the event is delta 
           (automatically and immediately cleared) or normal (not 
           automatically cleared)."
   		SYNTAX  INTEGER{
   	     	normal (1),
	     	delta (2)
        }

-- **************************************************************************
-- Trap information object definitions 
-- **************************************************************************

		trapRefSeqNum OBJECT-TYPE
   		SYNTAX      Integer32
    	MAX-ACCESS  read-only
    	STATUS      current
    	DESCRIPTION
            "Indicates the former sequence number of a cleared event 
             in the event table. Not intended to read but only used in 
             trap notifications."
    	::= { trapInfoObjects 1 }
    
		trapPersistence OBJECT-TYPE
    	SYNTAX      EventPersistence
    	MAX-ACCESS  read-only
    	STATUS      current
    	DESCRIPTION
            "Indicates whether the event is delta (automatically and 
             immediately cleared) or normal (not automatically cleared). 
             Not intended to read but only used in trap notifications."
    	::= { trapInfoObjects 2 }

		trapSenderNodeId OBJECT-TYPE
    	SYNTAX      Integer32
    	MAX-ACCESS  read-only
    	STATUS      current
    	DESCRIPTION
            "Represents the node ID of the sending network element. If not 
             supported should be set to 0. Not intended to read but only 
             used in trap notifications."
    	::= { trapInfoObjects 3 }     
    
		trapSenderStatus OBJECT-TYPE
    	SYNTAX      Integer32
    	MAX-ACCESS  read-only
    	STATUS      current
   		DESCRIPTION
            "Represents the trap status. 0 means cleared, 1 means happened."
  		::= { trapInfoObjects 4 }
    
-- ***************************************************************************
-- Trap definitions 
-- ***************************************************************************

		eventOnTrap NOTIFICATION-TYPE
        OBJECTS {
                  eventSeqNum,
                  eventEventId,
                  eventName,
                  eventSetTime,
                  eventSeverity,
                  eventInstanceType,
                  eventInstanceId,
                  eventInstanceName,
                  eventServAffective,
                  eventDescription, 
                  eventInstanceIdNumber,
                  trapPersistence,
                  trapSenderNodeId, 
                  trapSenderStatus,                   
                  sysObjectID}
        STATUS current
        DESCRIPTION
           "This trap is used to inform network management system that a delta 
           fault event (events that are automatically cleared) has occured 
           or a normal fault event (not automatically cleared) state has 
           been set on.  
          
           Objects are used as follows:
           - eventSeqNum is the sequence number of the event. For normal 
           type of events must equal to the sequence number of the event 
           in the events table. 
           - eventEventId specifies what fault event has occured.
           - eventName specifies the name of the fault event.
           - eventSetTime indicates when fault event has occured 
           (delta events) or when fault has been set on (normal events).
           - eventSeverity reports the severity level of the event.
           - eventInstanceType indicates what kind of object is faulty.
           - eventInstanceId specifies what instance is faulty.
           - eventInstanceName may contain textual description for 
           the faulty object.
           - eventServAffective specifies whether the event is 
           immediately service affcetive. 
           - eventDescription reports possible additional information about the event. 
           - trapPersistence tells whether this event is a delta or normal event. 
           - trapSenderNodeId specifies the node ID of the sending network element if 
           configuring it is supported for the network element, otherwise 0. 
           - trapSenderStatus specifes the trap status. 
           - sysObjectID specifies what kind of equipment reports the fault event.
           
           For more information see the eventTable specification"
        ::= { trapNotifications 1 }
         
		eventClearedTrap NOTIFICATION-TYPE
        OBJECTS {
                  eventSeqNum,
                  eventEventId,
                  eventSetTime,          
                  eventInstanceType,
                  eventInstanceId, 
                  eventInstanceIdNumber,
                  trapRefSeqNum,
                  trapSenderNodeId,
                  trapSenderStatus,
                  sysObjectID}
        STATUS current
        DESCRIPTION
           "This trap is used to inform network management system that a normal 
           type fault event has been cleared (state set off). 
           
           Objects are used as follows:
           - eventSeqNum is the sequence number of the this clearing event. Note that 
           the sequence number of the cleared event is reported in the trapRefSeqNum 
           object. 
           - eventEventId specifies what event has been cleared.
           - eventSetTime indicates when fault event has been cleared.
           - eventInstanceType indicates what kind of object has been 
           faulty.
           - eventInstanceId specifies what instance has been faulty.
           - trapRefSeqNum specifies the sequence number of the cleared event (i.e. 
           the sequence number was assigned for the event in the events table).  
           - trapSenderNodeId specifies the node ID of the sending network element if 
           configuring it is supported for the network element, otherwise 0. 
           - sysObjectID specifies what kind of equipment reports the clearing event.
           
           For more information see the eventTable specification"
        ::= { trapNotifications 2 }
              
              
--  28.multicastPortSetup
	
        multicastPortTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF MulticastPortEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { multicastPortSetup 1 }
        
		multicastPortEntry OBJECT-TYPE
        SYNTAX	MulticastPortEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in multicastPortTable."
        INDEX          	{ dot1dBasePort }
        ::= { multicastPortTable 1 }

        MulticastPortEntry ::=
		SEQUENCE {
         	multicastPortMaxGroupLimited		EnabledStatus,
        	multicastPortMaxOfGroup				INTEGER,
        	multicastPortIgmpFilteringProfile	DisplayString,
        	multicastPortQuerierMode   			INTEGER,
        	multicastPortThrottlingAction  		INTEGER,
        	multicastPortLeaveMode   			INTEGER,
        	multicastPortLeaveTimeout  			INTEGER,
        	multicastPortFastLeaveTimeout  		INTEGER
        }

        multicastPortMaxGroupLimited OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { multicastPortEntry 2 }

        multicastPortMaxOfGroup OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"0..255"
        ::= { multicastPortEntry 3 }
        
        multicastPortIgmpFilteringProfile OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { multicastPortEntry 4 }

        multicastPortQuerierMode OBJECT-TYPE
        SYNTAX  INTEGER {
           	auto(1),
        	fixed(2),
        	edge(3)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { multicastPortEntry 5 }
        
        multicastPortThrottlingAction OBJECT-TYPE
        SYNTAX  INTEGER {
         deny(1),
         replace(2)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                       "Specify throttling action for each port"
        ::= { multicastPortEntry 6 } 
        
        multicastPortLeaveMode OBJECT-TYPE
        SYNTAX  INTEGER {
           	normal(0),
        	immediate(1),
        	fast(2)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { multicastPortEntry 7 }
        
        multicastPortLeaveTimeout  OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"200..6348800"
        ::= { multicastPortEntry 8 }
        
        multicastPortFastLeaveTimeout  OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"200..6348800"
        ::= { multicastPortEntry 9 }
                

--  29. multicastStatus
	
        multicastStatusTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF MulticastStatusEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { multicastStatus 1 }    
        
		multicastStatusEntry OBJECT-TYPE
        SYNTAX	MulticastStatusEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in multicastStatusTable."
        INDEX          	{ multicastStatusVlanID, multicastStatusPort, multicastStatusGroup }
        ::= { multicastStatusTable 1 }

        MulticastStatusEntry ::=
		SEQUENCE {
        	multicastStatusIndex    INTEGER,
        	multicastStatusVlanID	INTEGER,
        	multicastStatusPort		INTEGER,
        	multicastStatusGroup	IpAddress
         }

        multicastStatusIndex OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { multicastStatusEntry 1 }

        multicastStatusVlanID OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { multicastStatusEntry 2 }

        multicastStatusPort OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { multicastStatusEntry 3 }

        multicastStatusGroup OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { multicastStatusEntry 4 }     

-- igmpSnpCountStatus
		
       igmpSnpCountTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF IgmpSnpCountEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	"A count table of igmp query/report/leave message."
        ::= { multicastStatus 2 }    
                        
	igmpSnpCountEntry OBJECT-TYPE
        SYNTAX	IgmpSnpCountEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in igmpSnpCountTable."
        INDEX          	{ igmpSnpCountIndex }
        ::= { igmpSnpCountTable 1 }

        IgmpSnpCountEntry ::=
		SEQUENCE {
        	igmpSnpCountIndex    		INTEGER,
        	igmpSnpV2CountQueryRx		INTEGER,
        	igmpSnpV2CountReportRx		INTEGER,
        	igmpSnpV2CountLeaveRx		INTEGER,        	
        	igmpSnpV2CountQueryRxDrop   INTEGER,
        	igmpSnpV2CountReportRxDrop  INTEGER,
        	igmpSnpV2CountLeaveRxDrop   INTEGER,
        	igmpSnpV2CountQueryTx   	INTEGER,
        	igmpSnpV2CountReportTx   	INTEGER,
        	igmpSnpV2CountLeaveTx   	INTEGER,
        	igmpSnpV3CountQueryRx		INTEGER,
        	igmpSnpV3CountReportRx		INTEGER,
        	igmpSnpV3CountQueryRxDrop	INTEGER,
        	igmpSnpV3CountReportRxDrop	INTEGER,
        	igmpSnpV3CountQueryTx		INTEGER,
        	igmpSnpV3CountReportTx		INTEGER
        }
       
        igmpSnpCountIndex OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Index of IgmpCountEntry. 0 means total count in whole system"
        ::= { igmpSnpCountEntry 1 }    
        
   --igmpSnpV2CountSystem
        igmpSnpV2CountQueryRx OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpSnpCountEntry 2 }

        igmpSnpV2CountReportRx OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpSnpCountEntry 3 }

        igmpSnpV2CountLeaveRx OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpSnpCountEntry 4 }
        
        igmpSnpV2CountQueryRxDrop OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpSnpCountEntry 5 }
        
        igmpSnpV2CountReportRxDrop OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpSnpCountEntry 6 }                              
        
        igmpSnpV2CountLeaveRxDrop OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpSnpCountEntry 7 }

        igmpSnpV2CountQueryTx OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpSnpCountEntry 8 }
        
        igmpSnpV2CountReportTx OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpSnpCountEntry 9 }

        igmpSnpV2CountLeaveTx OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpSnpCountEntry 10 }
  -- igmpSnpV3CountSystem    
  igmpSnpV3CountQueryRx OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpSnpCountEntry 11 }

        igmpSnpV3CountReportRx OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpSnpCountEntry 12 }

            
        igmpSnpV3CountQueryRxDrop OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpSnpCountEntry 13 }
        
        igmpSnpV3CountReportRxDrop OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpSnpCountEntry 14 }                              
        
    
        igmpSnpV3CountQueryTx OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpSnpCountEntry 15 }
        
        igmpSnpV3CountReportTx OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpSnpCountEntry 16 } 
 
 -- multicastVlanStatusTable

    multicastVlanStatusTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF MulticastVlanStatusEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { multicastStatus 3 }
               
		multicastVlanStatusEntry OBJECT-TYPE
        SYNTAX	MulticastVlanStatusEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in multicastVlanStatusTable."
        INDEX          	{ multicastVlanStatusVlanID }
        ::= { multicastVlanStatusTable 1 }

        MulticastVlanStatusEntry ::=
		SEQUENCE {
        	multicastVlanStatusVlanID   INTEGER,
        	multicastVlanStatusType		INTEGER,
        	multicastVlanQueryPort		PortList   	
        }

        multicastVlanStatusVlanID OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { multicastVlanStatusEntry 1 }

        multicastVlanStatusType OBJECT-TYPE
        SYNTAX  INTEGER {
   		dynamic(1),
 		mvr (2),
		static(3)
        }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { multicastVlanStatusEntry 2 }
	
        multicastVlanQueryPort OBJECT-TYPE
        SYNTAX  PortList
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { multicastVlanStatusEntry 3 }  
    
  --  igmpSnpCountVlanStatus
	    igmpSnpCountVlanTable OBJECT-TYPE
 	    SYNTAX SEQUENCE OF IgmpSnpCountVlanEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION	""
	    ::= { multicastStatus 4}  
	   
	    igmpSnpCountVlanEntry OBJECT-TYPE
        SYNTAX	IgmpSnpCountVlanEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in igmpGroupVlanStatus."
        INDEX          	{ igmpSnpCountVlanIndex }
        ::= { igmpSnpCountVlanTable 1 }

        IgmpSnpCountVlanEntry ::=
		SEQUENCE {
        	igmpSnpCountVlanIndex    		INTEGER,
        	igmpSnpV2CountVlanQueryRx		INTEGER,
        	igmpSnpV2CountVlanReportRx 		INTEGER,
        	igmpSnpV2CountVlanLeaveRx 		INTEGER,
        	igmpSnpV2CountVlanQueryRxDrop 	INTEGER,
        	igmpSnpV2CountVlanReportRxDrop 	INTEGER,
        	igmpSnpV2CountVlanLeaveRxDrop 	INTEGER,
        	igmpSnpV2CountVlanQueryTx 		INTEGER,
        	igmpSnpV2CountVlanReportTx 		INTEGER,
        	igmpSnpV2CountVlanLeaveTx 		INTEGER,
        	igmpSnpV3CountVlanQueryRx		INTEGER,
        	igmpSnpV3CountVlanReportRx 		INTEGER,
        	igmpSnpV3CountVlanQueryRxDrop 	INTEGER,
        	igmpSnpV3CountVlanReportRxDrop 	INTEGER,
        	igmpSnpV3CountVlanQueryTx 		INTEGER,
        	igmpSnpV3CountVlanReportTx 		INTEGER          	        	
        }
   
	    igmpSnpCountVlanIndex OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "Input vlan"
	    ::={ igmpSnpCountVlanEntry 1}
	   
	    igmpSnpV2CountVlanQueryRx OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Query Rx counters for vlan"
	    ::={ igmpSnpCountVlanEntry 2}
	   
	    igmpSnpV2CountVlanReportRx OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Report Rx counters for vlan"
	    ::={ igmpSnpCountVlanEntry 3} 
	   
	    igmpSnpV2CountVlanLeaveRx OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Leave Rx counters for vlan"
	    ::={ igmpSnpCountVlanEntry 4}
	   
	    igmpSnpV2CountVlanQueryRxDrop OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Query Rx Error counters for vlan"
	    ::={ igmpSnpCountVlanEntry 5}
	   
	    igmpSnpV2CountVlanReportRxDrop OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Report Rx Error counters for vlan"
	    ::={ igmpSnpCountVlanEntry 6}
	   
	    igmpSnpV2CountVlanLeaveRxDrop OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Leave Rx Error counters for vlan"
	    ::={ igmpSnpCountVlanEntry 7}
	    
	    igmpSnpV2CountVlanQueryTx OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Query Tx counters for vlan"
	    ::={ igmpSnpCountVlanEntry 8}
	   
	    igmpSnpV2CountVlanReportTx OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Report Tx counters for vlan"
	    ::={ igmpSnpCountVlanEntry 9}
	   
	    igmpSnpV2CountVlanLeaveTx OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Leave Tx counters for vlan"
	    ::={ igmpSnpCountVlanEntry 10}
	  
	--igmpSnpV3CountVlan   
	    igmpSnpV3CountVlanQueryRx OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Query Rx counters for vlan"
	    ::={ igmpSnpCountVlanEntry 11}
	   
	    igmpSnpV3CountVlanReportRx OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Report Rx counters for vlan"
	    ::={ igmpSnpCountVlanEntry 12} 
	   
	    igmpSnpV3CountVlanQueryRxDrop OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Query Rx Error counters for vlan"
	    ::={ igmpSnpCountVlanEntry 13}
	   
	    igmpSnpV3CountVlanReportRxDrop OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Report Rx Error counters for vlan"
	    ::={ igmpSnpCountVlanEntry 14}
	   
	    igmpSnpV3CountVlanQueryTx OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Query Tx counters for vlan"
	    ::={ igmpSnpCountVlanEntry 15}
	   
	    igmpSnpV3CountVlanReportTx OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Report Tx counters for vlan"
	    ::={ igmpSnpCountVlanEntry 16}   
	   	   
 --igmpSnpCountPortStatus
 	    igmpSnpCountPortTable OBJECT-TYPE
 	    SYNTAX SEQUENCE OF IgmpSnpCountPortEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION	""
	    ::= { multicastStatus 5}    
	   
	    igmpSnpCountPortEntry OBJECT-TYPE
        SYNTAX	IgmpSnpCountPortEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in igmpSnpCountPortStatus."
        INDEX          	{ dot1dBasePort }
        ::= { igmpSnpCountPortTable 1 }

        IgmpSnpCountPortEntry ::=
		SEQUENCE {
               igmpSnpV2CountPortQueryRx		INTEGER,
               igmpSnpV2CountPortReportRx 		INTEGER,
               igmpSnpV2CountPortLeaveRx 		INTEGER,
               igmpSnpV2CountPortReportRxDrop 	INTEGER,
               igmpSnpV2CountPortLeaveRxDrop 	INTEGER,
               igmpSnpV2CountPortReportTx 		INTEGER,
               igmpSnpV2CountPortLeaveTx 		INTEGER,
               igmpSnpV3CountPortQueryRx 		INTEGER,
               igmpSnpV3CountPortReportRx 		INTEGER,
               igmpSnpV3CountPortReportRxDrop 	INTEGER,
			   igmpSnpV3CountPortReportTx 		INTEGER
        }
	   
	    igmpSnpV2CountPortQueryRx  OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Query Rx counters for port"
	    ::={ igmpSnpCountPortEntry 1} 
	   
	    igmpSnpV2CountPortReportRx  OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Report Rx counters for port"
	    ::={ igmpSnpCountPortEntry 2}
	   
	    igmpSnpV2CountPortLeaveRx  OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Leave Rx counters for port"
	    ::={ igmpSnpCountPortEntry 3}
	   
	    igmpSnpV2CountPortReportRxDrop  OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Report Rx Error counters for port"
	    ::={ igmpSnpCountPortEntry 4}
	   
	    igmpSnpV2CountPortLeaveRxDrop  OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Leave Rx Error counters for port"
	    ::={ igmpSnpCountPortEntry 5}
	   
	    igmpSnpV2CountPortReportTx  OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Report Tx counters for port"
	    ::={ igmpSnpCountPortEntry 6}
	   
	    igmpSnpV2CountPortLeaveTx  OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Leave Tx counters for port"
	    ::={ igmpSnpCountPortEntry 7}
	   
        igmpSnpV3CountPortQueryRx  OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Query Rx counters for port"
	    ::={ igmpSnpCountPortEntry 8} 
	   
	    igmpSnpV3CountPortReportRx  OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Report Rx counters for port"
	    ::={ igmpSnpCountPortEntry 9}
	    	   
	    igmpSnpV3CountPortReportRxDrop  OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Report Rx Error counters for port"
	    ::={ igmpSnpCountPortEntry 10}
	   
	    igmpSnpV3CountPortReportTx  OBJECT-TYPE
	    SYNTAX	INTEGER
	    ACCESS 	read-only
	    STATUS	mandatory
	    DESCRIPTION    "show igmpsnp Report Tx counters for port"
	    ::={ igmpSnpCountPortEntry 11} 
   
--igmpSnpGroupCountStatus                                
 		igmpSnpGroupCountStatus OBJECT IDENTIFIER
        ::= { multicastStatus 6 }

        igmpSnpGroupCountNum OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION	"Show total IGMP snooping group number"
        ::= { igmpSnpGroupCountStatus 1 }

-- igmpGroupCountVlanTable
	    igmpSnpGroupCountVlanTable OBJECT-TYPE 
	    SYNTAX SEQUENCE OF IgmpSnpGroupCountVlanEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION	""
	    ::= { igmpSnpGroupCountStatus 2} 
	    
	    igmpSnpGroupCountVlanEntry OBJECT-TYPE
        SYNTAX	IgmpSnpGroupCountVlanEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in igmpSnpGroupVlanStatus."
        INDEX          	{ igmpSnpGroupCountVlanIndex }
        ::= { igmpSnpGroupCountVlanTable 1 }

        IgmpSnpGroupCountVlanEntry ::=
		SEQUENCE {
        	igmpSnpGroupCountVlanIndex  INTEGER,
        	igmpSnpGroupCountVlanNum	INTEGER        	
         }
  
	    igmpSnpGroupCountVlanIndex OBJECT-TYPE
	    SYNTAX INTEGER
	    ACCESS read-only
	    STATUS mandatory
	    DESCRIPTION "Show IGMP snooping group index for vlan" 
	    ::= {  igmpSnpGroupCountVlanEntry 1}
	    
	    igmpSnpGroupCountVlanNum OBJECT-TYPE
	    SYNTAX INTEGER
	    ACCESS read-only
	    STATUS mandatory
	    DESCRIPTION "Show IGMP snooping group number for vlan" 
	    ::={  igmpSnpGroupCountVlanEntry 2} 
	    
-- 	 igmpGroupCountPortTable
	    igmpSnpGroupCountPortTable OBJECT-TYPE
	    SYNTAX SEQUENCE OF IgmpSnpGroupCountPortEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION	""
	    ::= {igmpSnpGroupCountStatus 3} 
	    
	    igmpSnpGroupCountPortEntry OBJECT-TYPE
        SYNTAX	IgmpSnpGroupCountPortEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in igmpGroupCountPortStatus."
        INDEX          	{ dot1dBasePort }
        ::= { igmpSnpGroupCountPortTable 1 }

        IgmpSnpGroupCountPortEntry ::=
		SEQUENCE {
               	igmpSnpGroupCountPortNum	INTEGER
        }
	    
	    igmpSnpGroupCountPortNum OBJECT-TYPE
	    SYNTAX INTEGER
	    ACCESS read-only
	    STATUS mandatory
	    DESCRIPTION "Show IGMP snooping group number for port" 
	    ::={  igmpSnpGroupCountPortEntry 1}

	
--  30. igmpFilteringProfileSetup
	
		igmpFilteringMaxNumberOfProfile OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpFilteringProfileSetup 1 }
	
        igmpFilteringProfileTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF IgmpFilteringProfileEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { igmpFilteringProfileSetup 2 }
        
		igmpFilteringProfileEntry OBJECT-TYPE
        SYNTAX	IgmpFilteringProfileEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in igmpFilteringProfileTable."
        INDEX          	{ igmpFilteringProfileName, igmpFilteringProfileStartAddress, igmpFilteringProfileEndAddress }
        ::= { igmpFilteringProfileTable 1 }

        IgmpFilteringProfileEntry ::=
		SEQUENCE {
        	igmpFilteringProfileName			DisplayString,
        	igmpFilteringProfileStartAddress	IpAddress,
        	igmpFilteringProfileEndAddress		IpAddress,
        	igmpFilteringProfileRowStatus		RowStatus
        }

        igmpFilteringProfileName OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpFilteringProfileEntry 1 }

        igmpFilteringProfileStartAddress OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpFilteringProfileEntry 2 }

        igmpFilteringProfileEndAddress OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpFilteringProfileEntry 3 }
        
        igmpFilteringProfileRowStatus OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-create
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { igmpFilteringProfileEntry 4 }
    
    
--  31. MVRSetup
	
		maxNumberOfMVR OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { mvrSetup 1 }
	
        mvrTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF MvrEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { mvrSetup 2 }
        
		mvrEntry OBJECT-TYPE
        SYNTAX	MvrEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in mvrTable."
        INDEX          	{ mvrVlanID }
        ::= { mvrTable 1 }

        MvrEntry ::=
		SEQUENCE {
        	mvrVlanID			INTEGER,
        	mvrName				DisplayString,
        	mvrMode				INTEGER,
        	mvrRowStatus		RowStatus,
        	mvr8021pPriority	INTEGER
        }

        mvrVlanID OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"1..4094"
        ::= { mvrEntry 1 }

        mvrName OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { mvrEntry 2 }

        mvrMode OBJECT-TYPE
        SYNTAX  INTEGER {
        	dynamic(0),
        	compatible(1)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { mvrEntry 3 }
        
        mvrRowStatus OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-create
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { mvrEntry 4 }

        mvr8021pPriority OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Set the 802.1p priority of control messages within MVR (0~7)"
        ::= { mvrEntry 5 }

        mvrPortTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF MvrPortEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { mvrSetup 3 }
        
		mvrPortEntry OBJECT-TYPE
        SYNTAX	MvrPortEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in mvrPortTable."
        INDEX          	{ mvrVlanID, dot1dBasePort }
        ::= { mvrPortTable 1 }

        MvrPortEntry ::=
		SEQUENCE {
        	mvrPortRole		INTEGER,
        	mvrPortTagging	EnabledStatus
        }

        mvrPortRole OBJECT-TYPE
        SYNTAX  INTEGER {
        	none(1),
        	source_port(2),
        	receiver_port(3)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { mvrPortEntry 1 }

        mvrPortTagging OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { mvrPortEntry 2 }

		maxNumberOfMvrGroup OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { mvrSetup 4 }
	
        mvrGroupTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF MvrGroupEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { mvrSetup 5 }
        
		mvrGroupEntry OBJECT-TYPE
        SYNTAX	MvrGroupEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in mvrGroupTable."
        INDEX          	{ mvrVlanID, mvrGroupName }
        ::= { mvrGroupTable 1 }

        MvrGroupEntry ::=
		SEQUENCE {
        	mvrGroupName			DisplayString,
        	mvrGroupStartAddress	IpAddress,
        	mvrGroupEndAddress		IpAddress,
        	mvrGroupRowStatus		RowStatus
        }

        mvrGroupName OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { mvrGroupEntry 1 }

        mvrGroupStartAddress OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { mvrGroupEntry 2 }

        mvrGroupEndAddress OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { mvrGroupEntry 3 }
        
        mvrGroupRowStatus OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-create
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { mvrGroupEntry 4 }
     
     
-- 32. clusterSetup
	
-- clusterManager
		clusterManager     	OBJECT IDENTIFIER ::= { clusterSetup 1 }

        clusterMaxNumOfManager OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { clusterManager 1 }
        
-- clusterManagerTable
        clusterManagerTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF ClusterManagerEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { clusterManager 2 }
        
		clusterManagerEntry OBJECT-TYPE
        SYNTAX	ClusterManagerEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in clusterManagerTable."
        INDEX          	{ clusterManagerVid }
        ::= { clusterManagerTable 1 }

        ClusterManagerEntry ::=
           SEQUENCE {
        	clusterManagerVid	INTEGER,
        	clusterManagerName	DisplayString,
        	clusterManagerRowStatus	RowStatus
           }

        clusterManagerVid OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { clusterManagerEntry 1 }

        clusterManagerName OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { clusterManagerEntry 2 }

        clusterManagerRowStatus OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-create
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { clusterManagerEntry 3 }

-- clusterMembers
		clusterMembers     	OBJECT IDENTIFIER ::= { clusterSetup 2 }

        clusterMaxNumOfMember OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { clusterMembers 1 }
        
-- clusterMemberTable
        clusterMemberTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF ClusterMemberEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { clusterMembers 2 }
        
		clusterMemberEntry OBJECT-TYPE
        SYNTAX	ClusterMemberEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in clusterMemberTable."
        INDEX          	{ clusterMemberMac }
        ::= { clusterMemberTable 1 }

        ClusterMemberEntry ::=
           SEQUENCE {
        	clusterMemberMac		MacAddress,
        	clusterMemberName		DisplayString,
        	clusterMemberModel		DisplayString,
        	clusterMemberPassword	DisplayString,
        	clusterMemberRowStatus	RowStatus
           }

        clusterMemberMac OBJECT-TYPE
        SYNTAX  MacAddress
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { clusterMemberEntry 1 }

        clusterMemberName OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { clusterMemberEntry 2 }

        clusterMemberModel OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { clusterMemberEntry 3 }

        clusterMemberPassword OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { clusterMemberEntry 4 }
        
        clusterMemberRowStatus OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-create
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { clusterMemberEntry 5 }
        
-- clusterCandidates
		clusterCandidates     	OBJECT IDENTIFIER ::= { clusterSetup 3 }
        
-- clusterCandidateTable
        clusterCandidateTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF ClusterCandidateEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { clusterCandidates 1 }
        
		clusterCandidateEntry OBJECT-TYPE
        SYNTAX	ClusterCandidateEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in clusterCandidateTable."
        INDEX          	{ clusterCandidateMac }
        ::= { clusterCandidateTable 1 }

        ClusterCandidateEntry ::=
           SEQUENCE {
        	clusterCandidateMac		MacAddress,
        	clusterCandidateName	DisplayString,
        	clusterCandidateModel	DisplayString
           }

        clusterCandidateMac OBJECT-TYPE
        SYNTAX  MacAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { clusterCandidateEntry 1 }

        clusterCandidateName OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { clusterCandidateEntry 2 }

        clusterCandidateModel OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { clusterCandidateEntry 3 }

-- clusterStatus
		clusterStatus     	OBJECT IDENTIFIER ::= { clusterSetup 4 }

        clusterStatusRole OBJECT-TYPE
        SYNTAX  INTEGER {
        	none(0),
        	manager(1),
        	member(2)
        }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { clusterStatus 1 }

        clusterStatusManager OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { clusterStatus 2 }

        clsuterStatusNumOfMember OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { clusterStatus 3 }
        
-- clusterStatusMemberTable
        clusterStatusMemberTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF ClusterStatusMemberEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { clusterStatus 4 }
        
		clusterStatusMemberEntry OBJECT-TYPE
        SYNTAX	ClusterStatusMemberEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in clusterStatusMemberTable."
        INDEX          	{ clusterStatusMemberMac }
        ::= { clusterStatusMemberTable 1 }

        ClusterStatusMemberEntry ::=
           SEQUENCE {
        	clusterStatusMemberMac		MacAddress,
        	clusterStatusMemberName		DisplayString,
        	clusterStatusMemberModel	DisplayString,
        	clusterStatusMemberStatus	INTEGER
           }

        clusterStatusMemberMac OBJECT-TYPE
        SYNTAX  MacAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { clusterStatusMemberEntry 1 }

        clusterStatusMemberName OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { clusterStatusMemberEntry 2 }

        clusterStatusMemberModel OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { clusterStatusMemberEntry 3 }

        clusterStatusMemberStatus OBJECT-TYPE
        SYNTAX  INTEGER {
        	error(0),
        	online(1),
        	offline(2)
        }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { clusterStatusMemberEntry 4 }    
                                             
--  33. sysLogSetup
	
		sysLogState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"sysLog enabled/disabled for the switch."
        ::= { sysLogSetup 1 }
            	
--  sysLogTypeTable
       	sysLogTypeTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF SysLogTypeEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { sysLogSetup 2 }
        
		sysLogTypeEntry OBJECT-TYPE
        SYNTAX	SysLogTypeEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in sysLogTypeTable."
        INDEX          	{ sysLogTypeIndex }
        ::= { sysLogTypeTable 1 }

        SysLogTypeEntry ::=
           SEQUENCE {
         	sysLogTypeIndex		INTEGER,
        	sysLogTypeName		DisplayString,
        	sysLogTypeState		EnabledStatus,
        	sysLogTypeFacility	INTEGER
           }
       
        sysLogTypeIndex OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { sysLogTypeEntry 1 }

        sysLogTypeName OBJECT-TYPE
        SYNTAX  DisplayString 
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { sysLogTypeEntry 2 }

        sysLogTypeState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { sysLogTypeEntry 3 }

   		sysLogTypeFacility OBJECT-TYPE
        SYNTAX  INTEGER {
        	local_user0(0),
        	local_user1(1),
        	local_user2(2),
        	local_user3(3),
        	local_user4(4),
        	local_user5(5),
        	local_user6(6),
        	local_user7(7)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { sysLogTypeEntry 4 }

--  sysLogServerTable
       	sysLogServerTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF SysLogServerEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { sysLogSetup 3 }
        
		sysLogServerEntry OBJECT-TYPE
        SYNTAX	SysLogServerEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in sysLogServerTable."
        INDEX          	{ sysLogServerAddress }
        ::= { sysLogServerTable 1 }

        SysLogServerEntry ::=
           SEQUENCE {
         	sysLogServerAddress		IpAddress,
        	sysLogServerLogLevel	INTEGER,
        	sysLogServerRowStatus	RowStatus
           }
       
        sysLogServerAddress OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { sysLogServerEntry 1 }

        sysLogServerLogLevel OBJECT-TYPE
        SYNTAX  INTEGER {
        	level0(0),
        	level0-1(1),
        	level0-2(2),
        	level0-3(3),
        	level0-4(4),
        	level0-5(5),
        	level0-6(6),
        	level0-7(7)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { sysLogServerEntry 2 }

        sysLogServerRowStatus OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-create
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { sysLogServerEntry 3 }
                                             
                                             
--  34. diffservSetup
	
        diffservState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { diffservSetup 1 }
        
-- diffservMapTable
        diffservMapTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF DiffservMapEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { diffservSetup 2 }
        
		diffservMapEntry OBJECT-TYPE
        SYNTAX	DiffservMapEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in diffservMapTable."
        INDEX          	{ diffservMapDscp }
        ::= { diffservMapTable 1 }

        DiffservMapEntry ::=
           SEQUENCE {
        	diffservMapDscp		INTEGER,
        	diffservMapPriority	INTEGER
           }

        diffservMapDscp OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"0-63"
        ::= { diffservMapEntry 1 }

        diffservMapPriority OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"0-7"
        ::= { diffservMapEntry 2 }
                                             

--  35. protocol-based VLAN
	
	    protoBasedVlanTable  OBJECT-TYPE
	    SYNTAX	SEQUENCE OF ProtoBasedVlanEntry 
	    ACCESS  not-accessible
	    STATUS  mandatory
	    DESCRIPTION
	             	""
	    ::= { protoBasedVlanSetup 1 }	

		protoBasedVlanEntry  OBJECT-TYPE
        SYNTAX	ProtoBasedVlanEntry 
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in protoBasedVlanTable."
        INDEX          	{ protoBasedVlanPort, protoBasedVlanPacketType, protoBasedVlanEtherType }
        ::= { protoBasedVlanTable 1 }

        ProtoBasedVlanEntry ::=
           SEQUENCE {
            protoBasedVlanPort			INTEGER,
        	protoBasedVlanPacketType	INTEGER,
        	protoBasedVlanEtherType		INTEGER,
        	protoBasedVlanName			DisplayString,
        	protoBasedVlanVid			INTEGER,
        	protoBasedVlanPriority		INTEGER,
        	protoBasedVlanState			RowStatus
           }

        protoBasedVlanPort OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { protoBasedVlanEntry 1 }

        protoBasedVlanPacketType OBJECT-TYPE
        SYNTAX  INTEGER {
        	etherII(1),
        	snap(2),
        	llc(3)
        }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { protoBasedVlanEntry 2 }

        protoBasedVlanEtherType OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { protoBasedVlanEntry 3 }

        protoBasedVlanName OBJECT-TYPE
        SYNTAX  DisplayString (SIZE (0..32))
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { protoBasedVlanEntry 4 }

        protoBasedVlanVid OBJECT-TYPE
        SYNTAX  INTEGER (1..4094)
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { protoBasedVlanEntry 5 }

        protoBasedVlanPriority OBJECT-TYPE
        SYNTAX  INTEGER (0..7)
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { protoBasedVlanEntry 6 }

        protoBasedVlanState OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-create
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { protoBasedVlanEntry 7 }     
        
--  36. MRSTP
	
    	mrstpSetup  OBJECT IDENTIFIER ::= { mrstp 1 }
	                 
		mrstpBridgeTable  OBJECT-TYPE
		SYNTAX	SEQUENCE OF MrstpBridgeEntry 
		ACCESS  not-accessible
		STATUS  mandatory
		DESCRIPTION
		    	""
	    ::= { mrstpSetup 1 }	

		mrstpBridgeEntry  OBJECT-TYPE
        SYNTAX	MrstpBridgeEntry 
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in mrstpBridgeTable."
        INDEX          	{ mrstpBridgeIndex }
        ::= { mrstpBridgeTable 1 }

        MrstpBridgeEntry ::=
           SEQUENCE {
           	mrstpBridgeIndex				INTEGER,
           	mrstpState						INTEGER,  
 			mrstpProtocolSpecification		INTEGER,      	
           	mrstpPriority					INTEGER,
        	mrstpTimeSinceTopologyChange	TimeTicks,
        	mrstpTopChanges					Counter,
        	mrstpDesignatedRoot				BridgeId,
        	mrstpRootCost					INTEGER,
        	mrstpRootPort					INTEGER,
        	mrstpMaxAge						Timeout,
        	mrstpHelloTime					Timeout,
        	mrstpHoldTime					INTEGER,
        	mrstpForwardDelay				Timeout,
        	mrstpBridgeMaxAge				Timeout,
        	mrstpBridgeHelloTime			Timeout,
        	mrstpBridgeForwardDelay			Timeout
           }

          -- the dot1dStp group

          -- Implementation of the dot1dStp group is optional.  It is
          -- implemented by those bridges that support the Spanning Tree
          -- Protocol.
          
          mrstpBridgeIndex OBJECT-TYPE
              SYNTAX  INTEGER
              ACCESS  read-only
              STATUS  mandatory
              DESCRIPTION
                      "The tree index of the MRSTP."
              ::= { mrstpBridgeEntry 1 }
			
		  mrstpState OBJECT-TYPE
        	SYNTAX  EnabledStatus
        	ACCESS  read-write
        	STATUS  mandatory
        	DESCRIPTION
                      "Enabled/disabled on the mrstp bridge."
        	::= { mrstpBridgeEntry 2 }
        
          mrstpProtocolSpecification OBJECT-TYPE
              SYNTAX  INTEGER {
                          unknown(1),
                          decLb100(2),
                          ieee8021d(3)
                      }
              ACCESS  read-only
              STATUS  mandatory
              DESCRIPTION
                      "An indication of what version of the Spanning
                      Tree Protocol is being run.  The value
                      'decLb100(2)' indicates the DEC LANbridge 100
                      Spanning Tree protocol.  IEEE 802.1d
                      implementations will return 'ieee8021d(3)'.  If
                      future versions of the IEEE Spanning Tree Protocol
                      are released that are incompatible with the
                      current version a new value will be defined."
              ::= { mrstpBridgeEntry 3 }
        
          mrstpPriority OBJECT-TYPE
              SYNTAX  INTEGER (0..65535)
              ACCESS  read-write
              STATUS  mandatory
              DESCRIPTION
                      "The value of the write-able portion of the Bridge
                      ID, i.e., the first two octets of the (8 octet
                      long) Bridge ID.  The other (last) 6 octets of the
                      Bridge ID are given by the value of
                      dot1dBaseBridgeAddress."
              REFERENCE
                      "IEEE 802.1D-1990: Section *******"
              ::= { mrstpBridgeEntry 4 }

          mrstpTimeSinceTopologyChange OBJECT-TYPE
              SYNTAX  TimeTicks
              ACCESS  read-only
              STATUS  mandatory
              DESCRIPTION
                      "The time (in hundredths of a second) since the
                      last time a topology change was detected by the
                      bridge entity."
              REFERENCE
                      "IEEE 802.1D-1990: Section *******.3"
              ::= { mrstpBridgeEntry 5 }

          mrstpTopChanges OBJECT-TYPE
              SYNTAX  Counter
              ACCESS  read-only
              STATUS  mandatory
              DESCRIPTION
                      "The total number of topology changes detected by
                      this bridge since the management entity was last
                      reset or initialized."
              REFERENCE
                      "IEEE 802.1D-1990: Section *******.3"
              ::= { mrstpBridgeEntry 6 }

          mrstpDesignatedRoot OBJECT-TYPE
              SYNTAX  BridgeId
              ACCESS  read-only
              STATUS  mandatory
              DESCRIPTION
                      "The bridge identifier of the root of the spanning
                      tree as determined by the Spanning Tree Protocol
                      as executed by this node.  This value is used as
                      the Root Identifier parameter in all Configuration
                      Bridge PDUs originated by this node."
              REFERENCE
                      "IEEE 802.1D-1990: Section *******"
              ::= { mrstpBridgeEntry 7 }

          mrstpRootCost OBJECT-TYPE
              SYNTAX  INTEGER
              ACCESS  read-only
              STATUS  mandatory
              DESCRIPTION
                      "The cost of the path to the root as seen from
                      this bridge."
              REFERENCE
                      "IEEE 802.1D-1990: Section *******"
              ::= { mrstpBridgeEntry 8 }

          mrstpRootPort OBJECT-TYPE
              SYNTAX  INTEGER
              ACCESS  read-only
              STATUS  mandatory
              DESCRIPTION
                      "The port number of the port which offers the
                      lowest cost path from this bridge to the root
                      bridge."
              REFERENCE
                      "IEEE 802.1D-1990: Section *******"
              ::= { mrstpBridgeEntry 9 }

          mrstpMaxAge OBJECT-TYPE
              SYNTAX  Timeout
              ACCESS  read-only
              STATUS  mandatory
              DESCRIPTION
                      "The maximum age of Spanning Tree Protocol
                      information learned from the network on any port
                      before it is discarded, in units of hundredths of
                      a second.  This is the actual value that this
                      bridge is currently using."
              REFERENCE
                      "IEEE 802.1D-1990: Section *******"
              ::= { mrstpBridgeEntry 10 }

          mrstpHelloTime OBJECT-TYPE
              SYNTAX  Timeout
              ACCESS  read-only
              STATUS  mandatory
              DESCRIPTION
                      "The amount of time between the transmission of
                      Configuration bridge PDUs by this node on any port
                      when it is the root of the spanning tree or trying
                      to become so, in units of hundredths of a second.
                      This is the actual value that this bridge is
                      currently using."
              REFERENCE
                      "IEEE 802.1D-1990: Section *******"
              ::= { mrstpBridgeEntry 11 }

          mrstpHoldTime OBJECT-TYPE
              SYNTAX  INTEGER
              ACCESS  read-only
              STATUS  mandatory
              DESCRIPTION
                      "This time value determines the interval length
                      during which no more than two Configuration bridge
                      PDUs shall be transmitted by this node, in units
                      of hundredths of a second."
              REFERENCE
                      "IEEE 802.1D-1990: Section *******4"
              ::= { mrstpBridgeEntry 12 }

          mrstpForwardDelay OBJECT-TYPE
              SYNTAX  Timeout
              ACCESS  read-only
              STATUS  mandatory
              DESCRIPTION
                      "This time value, measured in units of hundredths
                      of a second, controls how fast a port changes its
                      spanning state when moving towards the Forwarding
                      state.  The value determines how long the port
                      stays in each of the Listening and Learning
                      states, which precede the Forwarding state.  This
                      value is also used, when a topology change has
                      been detected and is underway, to age all dynamic
                      entries in the Forwarding Database.  [Note that
                      this value is the one that this bridge is
                      currently using, in contrast to
                      mrstpBridgeForwardDelay which is the value that
                      this bridge and all others would start using
                      if/when this bridge were to become the root.]"
              REFERENCE
                      "IEEE 802.1D-1990: Section *******"
              ::= { mrstpBridgeEntry 13 }

          mrstpBridgeMaxAge OBJECT-TYPE
              SYNTAX  Timeout (600..4000)
              ACCESS  read-write
              STATUS  mandatory
              DESCRIPTION
                      "The value that all bridges use for MaxAge when
                      this bridge is acting as the root.  Note that
                      802.1D-1990 specifies that the range for this
                      parameter is related to the value of
                      mrstpBridgeHelloTime. The granularity of this
                      timer is specified by 802.1D-1990 to be 1 second.
                      An agent may return a badValue error if a set is
                      attempted to a value which is not a whole number
                      of seconds."
              REFERENCE
                      "IEEE 802.1D-1990: Section *******"
              ::= { mrstpBridgeEntry 14 }

          mrstpBridgeHelloTime OBJECT-TYPE
              SYNTAX  Timeout (100..1000)
              ACCESS  read-write
              STATUS  mandatory
              DESCRIPTION
                      "The value that all bridges use for HelloTime when
                      this bridge is acting as the root.  The
                      granularity of this timer is specified by 802.1D-
                      1990 to be 1 second.  An agent may return a
                      badValue error if a set is attempted to a value
                      which is not a whole number of seconds."
              REFERENCE
                      "IEEE 802.1D-1990: Section *******"
              ::= { mrstpBridgeEntry 15 }

          mrstpBridgeForwardDelay OBJECT-TYPE
              SYNTAX  Timeout (400..3000)
              ACCESS  read-write
              STATUS  mandatory
              DESCRIPTION
                      "The value that all bridges use for ForwardDelay
                      when this bridge is acting as the root.  Note that
                      802.1D-1990 specifies that the range for this
                      parameter is related to the value of
                      mrstpBridgeMaxAge.  The granularity of this
                      timer is specified by 802.1D-1990 to be 1 second.
                      An agent may return a badValue error if a set is
                      attempted to a value which is not a whole number
                      of seconds."
              REFERENCE
                      "IEEE 802.1D-1990: Section ********"
              ::= { mrstpBridgeEntry 16 }

          -- The Spanning Tree Port Table

          mrstpPortTable OBJECT-TYPE
              SYNTAX  SEQUENCE OF MrstpPortEntry
              ACCESS  not-accessible
              STATUS  mandatory
              DESCRIPTION
                      "A table that contains port-specific information
                      for the Spanning Tree Protocol."
              ::= { mrstpSetup 2 }

          mrstpPortEntry OBJECT-TYPE
              SYNTAX  MrstpPortEntry
              ACCESS  not-accessible
              STATUS  mandatory
              DESCRIPTION
                      "A list of information maintained by every port
                      about the Spanning Tree Protocol state for that
                      port."
              INDEX   { mrstpPort }
              ::= { mrstpPortTable 1 }

          MrstpPortEntry ::=
              SEQUENCE {
                  mrstpPort						INTEGER,
                  mrstpPortPriority				INTEGER,
                  mrstpPortState				INTEGER,
                  mrstpPortEnable				INTEGER,
                  mrstpPortPathCost				INTEGER,
                  mrstpPortDesignatedRoot		BridgeId,
                  mrstpPortDesignatedCost		INTEGER,
                  mrstpPortDesignatedBridge		BridgeId,
                  mrstpPortDesignatedPort		OCTET STRING,
                  mrstpPortForwardTransitions	Counter,
                  mrstpPortOnBridgeIndex		INTEGER,
				  mrstpPortAdminEdgePort		INTEGER,
                  mrstpPortOperEdgePort			INTEGER				  

              }

          mrstpPort OBJECT-TYPE
              SYNTAX  INTEGER (1..65535)
              ACCESS  read-only
              STATUS  mandatory
              DESCRIPTION
                      "The port number of the port for which this entry
                      contains Spanning Tree Protocol management
                      information."
              REFERENCE
                      "IEEE 802.1D-1990: Section *******.2"
              ::= { mrstpPortEntry 1 }

          mrstpPortPriority OBJECT-TYPE
              SYNTAX  INTEGER (0..255)
              ACCESS  read-write
              STATUS  mandatory
              DESCRIPTION
                      "The value of the priority field which is
                      contained in the first (in network byte order)
                      octet of the (2 octet long) Port ID.  The other
                      octet of the Port ID is given by the value of
                      mrstpPort."
              REFERENCE
                      "IEEE 802.1D-1990: Section *******"
              ::= { mrstpPortEntry 2 }

          mrstpPortState OBJECT-TYPE
              SYNTAX  INTEGER {
                          disabled(1),
                          blocking(2),
                          listening(3),
                          learning(4),
                          forwarding(5),
                          broken(6)
                      }
              ACCESS  read-only
              STATUS  mandatory
              DESCRIPTION
                      "The port's current state as defined by
                      application of the Spanning Tree Protocol.  This
                      state controls what action a port takes on
                      reception of a frame.  If the bridge has detected
                      a port that is malfunctioning it will place that
                      port into the broken(6) state.  For ports which
                      are disabled (see mrstpPortEnable), this object
                      will have a value of disabled(1)."
              REFERENCE
                      "IEEE 802.1D-1990: Section *******"
              ::= { mrstpPortEntry 3 }

          mrstpPortEnable OBJECT-TYPE
              SYNTAX  INTEGER {
                          enabled(1),
                          disabled(2)
                      }
              ACCESS  read-write
              STATUS  mandatory
              DESCRIPTION
                      "The enabled/disabled status of the port."
              REFERENCE
                      "IEEE 802.1D-1990: Section *******"
              ::= { mrstpPortEntry 4 }

          mrstpPortPathCost OBJECT-TYPE
              SYNTAX  INTEGER (1..65535)
              ACCESS  read-write
              STATUS  mandatory
              DESCRIPTION
                      "The contribution of this port to the path cost of
                      paths towards the spanning tree root which include
                      this port.  802.1D-1990 recommends that the
                      default value of this parameter be in inverse
                      proportion to the speed of the attached LAN."
              REFERENCE
                      "IEEE 802.1D-1990: Section *******"
              ::= { mrstpPortEntry 5 }

          mrstpPortDesignatedRoot OBJECT-TYPE
              SYNTAX  BridgeId
              ACCESS  read-only
              STATUS  mandatory
              DESCRIPTION
                      "The unique Bridge Identifier of the Bridge
                      recorded as the Root in the Configuration BPDUs
                      transmitted by the Designated Bridge for the
                      segment to which the port is attached."
              REFERENCE
                      "IEEE 802.1D-1990: Section *******"
              ::= { mrstpPortEntry 6 }

          mrstpPortDesignatedCost OBJECT-TYPE
              SYNTAX  INTEGER
              ACCESS  read-only
              STATUS  mandatory
              DESCRIPTION
                      "The path cost of the Designated Port of the
                      segment connected to this port.  This value is
                      compared to the Root Path Cost field in received
                      bridge PDUs."
              REFERENCE
                      "IEEE 802.1D-1990: Section *******"
              ::= { mrstpPortEntry 7 }

          mrstpPortDesignatedBridge OBJECT-TYPE
              SYNTAX  BridgeId
              ACCESS  read-only
              STATUS  mandatory
              DESCRIPTION
                      "The Bridge Identifier of the bridge which this
                      port considers to be the Designated Bridge for
                      this port's segment."
              REFERENCE
                      "IEEE 802.1D-1990: Section *******"
              ::= { mrstpPortEntry 8 }

          mrstpPortDesignatedPort OBJECT-TYPE
              SYNTAX  OCTET STRING (SIZE (2))
              ACCESS  read-only
              STATUS  mandatory
              DESCRIPTION
                      "The Port Identifier of the port on the Designated
                      Bridge for this port's segment."
              REFERENCE
                      "IEEE 802.1D-1990: Section *******"
              ::= { mrstpPortEntry 9 }

          mrstpPortForwardTransitions OBJECT-TYPE
              SYNTAX  Counter
              ACCESS  read-only
              STATUS  mandatory
              DESCRIPTION
                      "The number of times this port has transitioned
                      from the Learning state to the Forwarding state."
              ::= { mrstpPortEntry 10 }

          mrstpPortOnBridgeIndex OBJECT-TYPE
              SYNTAX  INTEGER
              ACCESS  read-write
              STATUS  mandatory
              DESCRIPTION
                      "Indetify the bridge index that this port joined to in MRSTP."
              ::= { mrstpPortEntry 11 }

		  mrstpPortAdminEdgePort OBJECT-TYPE
              SYNTAX  INTEGER {
                          true(1),
                          false(2)
                      }
              ACCESS  read-write
              STATUS  mandatory
              DESCRIPTION
                      "	The administrative value of the Edge Port parameter.  A
         				value of true(1) indicates that this port should be
         				assumed as an edge-port, and a value of false(2) indicates
         				that this port should be assumed as a non-edge-port. "
              REFERENCE
                      "IEEE 802.1t clause 14.8.2, 18.3.3"
              ::= { mrstpPortEntry 12 }
                                                                   
          mrstpPortOperEdgePort OBJECT-TYPE
              SYNTAX  INTEGER {
                          true(1),
                          false(2)
                      }
              ACCESS  read-only
              STATUS  mandatory
              DESCRIPTION
  					  "	The operational value of the Edge Port parameter.  The
			        	object is initialized to the value of the corresponding
				        instance of dot1dStpPortAdminEdgePort.  When the
				        corresponding instance of dot1dStpPortAdminEdgePort is
				        set, this object will be changed as well.  This object
				        will also be changed to false on reception of a BPDU."
              REFERENCE
                      "IEEE 802.1t clause 14.8.2, 18.3.4"
              ::= { mrstpPortEntry 13 }
              
              
--  MRSTP Trap
	mrstpNotifications     	OBJECT IDENTIFIER ::= { mrstp 2 }              
	
	  	newRoot NOTIFICATION-TYPE
      	OBJECTS {
        	mrstpBridgeIndex
      	}
      	STATUS  current
      	DESCRIPTION
          ""
      	::= { mrstpNotifications 1 }
                                             
		topologyChange NOTIFICATION-TYPE
      	OBJECTS {
        	mrstpBridgeIndex
      	}
      	STATUS  current
      	DESCRIPTION
          ""
      	::= { mrstpNotifications 2 }                                                              
                
--  classifierSetup
	
        classifierRuleTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF ClassifierRuleEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { classifierSetup 1 }

		classifierRuleEntry OBJECT-TYPE
        SYNTAX	ClassifierRuleEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in classifierRuleTable."
        INDEX          	{classifierName }
        ::= { classifierRuleTable 1 }

      	ClassifierRuleEntry ::=
		SEQUENCE {
        	classifierName			DisplayString,      
        	classifierEnable		INTEGER, 
        	classifierEtherType 	INTEGER,
        	classifierSrcMAC 		MacAddress,
        	classifierIncomingPort  INTEGER,
        	classifierDstMAC 		MacAddress,
        	classifierIpProtocol 	INTEGER,
        	classifierEstablishOnly INTEGER,
        	classifierSrcIp	    	IpAddress,
        	classifierSrcIpMask		INTEGER,
        	classifierSrcSocket 	INTEGER,
        	classifierDstIp			IpAddress,  
        	classifierDstIpMask		INTEGER,  
        	classifierDstSocket 	INTEGER,
        	classifierRowstatus 	RowStatus        	
           }                                             
                        
        classifierName OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Name of classifier rule."
        ::= { classifierRuleEntry 1 }                        
                        
        classifierEnable OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
        	"Classifier rule enabled/disabled."
        ::= { classifierRuleEntry 2 }
        
   		classifierEtherType OBJECT-TYPE
        SYNTAX  INTEGER 
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Ether type for classifier rule, in decimal expression. Value 65535 means any"
        ::= { classifierRuleEntry 6 }
         
        classifierSrcMAC OBJECT-TYPE
        SYNTAX  MacAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Source mac address for classifier rule. 00:00:00:00:00:00 means any"
        ::= { classifierRuleEntry 7 }
     	
        classifierIncomingPort OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Incoming Port for classifier rule. Value 65536 means any"                      	
        ::= { classifierRuleEntry 8 }

        classifierDstMAC OBJECT-TYPE
        SYNTAX  MacAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Destination mac address for classifier rule. 00:00:00:00:00:00 means any"
        ::= { classifierRuleEntry 9 }      
       
        classifierIpProtocol OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"IP Protocol for classifier rule. Value 255 means any"                      	
        ::= { classifierRuleEntry 11 }       
           
        classifierEstablishOnly OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"IP Protocol for EstablishOnly in classifier rule"                      	
        ::= { classifierRuleEntry 12 }  
           
    	classifierSrcIp OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Source IP address for classifier rule. 0.0.0.0 means any"
        ::= { classifierRuleEntry 13 }
        
        classifierSrcIpMask OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Source IP Mask for classifier rule."
        ::= { classifierRuleEntry 14 }
 
        classifierSrcSocket OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Source Socket Number for classifier rule. Value 0 means any"                      	
        ::= { classifierRuleEntry 15 }
        
        classifierDstIp OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Destination IP address for classifier rule. 0.0.0.0 means any"
        ::= { classifierRuleEntry 16 }
        
        classifierDstIpMask OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Destination IP Mask for classifier rule"
        ::= { classifierRuleEntry 17 }
 
        classifierDstSocket OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Destination Socket Number for classifier rule. Value 0 means any"                      	
        ::= { classifierRuleEntry 18 }
        
        classifierRowstatus OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Row Status for classifier rule"
        ::= { classifierRuleEntry 19 } 
      

--  policySetup
	
        policyTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF PolicyEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { policySetup 1 }  
        
 		policyEntry OBJECT-TYPE
        SYNTAX	PolicyEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in policyTable."
        INDEX          	{policyName }
        ::= { policyTable 1 }
       
      	PolicyEntry ::=
		SEQUENCE {
        	policyName				 DisplayString,      
        	policyEnable			 INTEGER,  
        	policyClassifier  		 DisplayString, 
        	policyEgressPort 		 INTEGER,
        	policyVlanId 			 INTEGER,
        	policy8021pPriority 	 INTEGER,
        	policyBandwidth     	 INTEGER,
        	policyForwardingAction   INTEGER,
        	policyPriorityAction     INTEGER,
        	policyOutgoingAction	 BITS,
        	policyRateLimitEnable    INTEGER,            
            policyRowstatus 		 RowStatus            
        }  
 
        policyName OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Name of policy rule."
        ::= { policyEntry 1 }   
         
        policyEnable OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
        	"Policy rule enabled/disabled."
        ::= { policyEntry 2 }  
           
        policyClassifier OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Classifier of policy rule."
        ::= { policyEntry 3 }      

        policyVlanId OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Vlan id for policy rule"
        ::= { policyEntry 4 }
                                                   
        policyEgressPort OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Egress Port for policy rule"
        ::= { policyEntry 5 }  
                
        policy8021pPriority OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"802.1p priority for policy rule, 0~7."
        ::= { policyEntry 7 }    
                       
        policyBandwidth OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Bandwidth for policy rule"
        ::= { policyEntry 10 }  
        
        policyForwardingAction OBJECT-TYPE
        SYNTAX  INTEGER
        {   
        	no-change(1),
        	discard-the-packet(2),        	
        }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Forwarding Action for policy rule"
        ::= { policyEntry 12 } 
   
        policyPriorityAction OBJECT-TYPE
        SYNTAX  INTEGER
        {   
        	no-change(1),
        	set-the-packets-802_1-priority(2),
        }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Priority Action for policy rule"
        ::= { policyEntry 13}    
        
        policyOutgoingAction OBJECT-TYPE
		SYNTAX	BITS {			
			send-the-packet-to-the-egress-port(1),			
			set-the-packets-VLAN-ID(3) 
		}
		ACCESS  read-create
		STATUS      current
		DESCRIPTION
	        "Outgoing Action for policy rule. Started bit is from MSB bit7 (bit7~bit0). "
		::= { policyEntry 15 }

        policyRateLimitEnable OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
        	"Metering for Policy rule enabled/disabled."
        ::= { policyEntry 16 }  
      
 
        policyRowstatus OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"Row Status for policy rule"
        ::= { policyEntry 18 }   
                                                   
---------------------------------------------------
--
--  100. dhcp snooping
--
---------------------------------------------------

--
--  dhcp snooping vlan table
--
	
        dhcpSnpVlanTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF DhcpSnpVlanEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { dhcpSnp 1 }

		dhcpSnpVlanEntry OBJECT-TYPE
        SYNTAX	DhcpSnpVlanEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	""
        INDEX          	{ dhcpSnpVlanEntryVid }
        ::= { dhcpSnpVlanTable 1 }

        DhcpSnpVlanEntry ::=
		SEQUENCE {
        	dhcpSnpVlanEntryVid				INTEGER,
        	dhcpSnpVlanEntryEnable			EnabledStatus,
        	dhcpSnpVlanEntryOption82Enable	EnabledStatus,
        	dhcpSnpVlanEntryInfo			EnabledStatus
        }

        dhcpSnpVlanEntryVid OBJECT-TYPE
        SYNTAX  INTEGER (1..4094)
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { dhcpSnpVlanEntry 1 }

        dhcpSnpVlanEntryEnable OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { dhcpSnpVlanEntry 2 }
        
        dhcpSnpVlanEntryOption82Enable OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { dhcpSnpVlanEntry 3 }
        
        dhcpSnpVlanEntryInfo OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { dhcpSnpVlanEntry 4 }

--
--  dhcp snooping interface table
--

        dhcpSnpPortTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF DhcpSnpPortEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { dhcpSnp 2 }

		dhcpSnpPortEntry OBJECT-TYPE
        SYNTAX	DhcpSnpPortEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	""
        INDEX          	{ dhcpSnpPortEntryPort }
        ::= { dhcpSnpPortTable 1 }

        DhcpSnpPortEntry ::=
		SEQUENCE {
        	dhcpSnpPortEntryPort		INTEGER,
        	dhcpSnpPortEntryTrust		EnabledStatus,
        	dhcpSnpPortEntryRate		INTEGER
        }

        dhcpSnpPortEntryPort OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { dhcpSnpPortEntry 1 }

        dhcpSnpPortEntryTrust OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { dhcpSnpPortEntry 2 }

        dhcpSnpPortEntryRate OBJECT-TYPE
        SYNTAX  INTEGER (0..2048)
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"0 means unlimited"
        ::= { dhcpSnpPortEntry 3 }
--
--  dhcp snooping binding table
--

        dhcpSnpBindTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF DhcpSnpBindEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { dhcpSnp 3 }

		dhcpSnpBindEntry OBJECT-TYPE
        SYNTAX	DhcpSnpBindEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	""
        INDEX          	{ dhcpSnpBindEntryMac, dhcpSnpBindEntryVid }
        ::= { dhcpSnpBindTable 1 }

        DhcpSnpBindEntry ::=
		SEQUENCE {
        	dhcpSnpBindEntryMac		MacAddress,
        	dhcpSnpBindEntryVid		INTEGER,
        	dhcpSnpBindEntryIP		IpAddress,
        	dhcpSnpBindEntryLease	INTEGER,
        	dhcpSnpBindEntryType	INTEGER,
        	dhcpSnpBindEntryPort	INTEGER
        }

        dhcpSnpBindEntryMac OBJECT-TYPE
        SYNTAX  MacAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { dhcpSnpBindEntry 1 }

        dhcpSnpBindEntryVid OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { dhcpSnpBindEntry 2 }
        
        dhcpSnpBindEntryIP OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { dhcpSnpBindEntry 3 }

        dhcpSnpBindEntryLease OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { dhcpSnpBindEntry 4 }

        dhcpSnpBindEntryType OBJECT-TYPE
        SYNTAX  INTEGER { dynamic(2) }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { dhcpSnpBindEntry 5 }

        dhcpSnpBindEntryPort OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { dhcpSnpBindEntry 6 }

--
--  dhcp snooping
--

	dhcpSnpEnable OBJECT-TYPE
	SYNTAX  EnabledStatus
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnp 4 }                                      

--
--  dhcp snooping database
--

	
	dhcpSnpDb     	OBJECT IDENTIFIER ::= { dhcpSnp 5 }
	
	dhcpSnpDbAbort OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnpDb 1 }                                      	

	dhcpSnpDbWriteDelay OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnpDb 2 }                                      
	
	dhcpSnpDbUrl OBJECT-TYPE
	SYNTAX  DisplayString (SIZE (0..255))
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnpDb 3 }

	dhcpSnpDbUrlRenew OBJECT-TYPE
	SYNTAX  DisplayString (SIZE (0..255))
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnpDb 4 }

	dhcpSnpDbStat     	OBJECT IDENTIFIER ::= { dhcpSnpDb 5 }

	dhcpSnpDbStatClear OBJECT-TYPE
	SYNTAX  EnabledStatus
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnpDbStat 1 }  
		
	dhcpSnpDbStatAgentRunning OBJECT-TYPE
	SYNTAX  INTEGER { none(0), read(1), write(2) }
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnpDbStat 2 }                                      	

	dhcpSnpDbStatDelayExpiry OBJECT-TYPE
	SYNTAX  INTEGER 
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnpDbStat 3 }	

	dhcpSnpDbStatAbortExpiry OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnpDbStat 4 }

	dhcpSnpDbStatLastSuccTime OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnpDbStat 5 }
	
	dhcpSnpDbStatLastFailTime OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnpDbStat 6 }
	
	dhcpSnpDbStatLastFailReason OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnpDbStat 7 }

	dhcpSnpDbStatTotalAttempt OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnpDbStat 8 }
	
	dhcpSnpDbStatStartupFail OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnpDbStat 9 }

	dhcpSnpDbStatSuccTrans OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnpDbStat 10 }
	
	dhcpSnpDbStatFailTrans OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnpDbStat 11 }

	dhcpSnpDbStatSuccRead OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnpDbStat 12 }

	dhcpSnpDbStatFailRead OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnpDbStat 13 }

	dhcpSnpDbStatSuccWrite OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnpDbStat 14 }

	dhcpSnpDbStatFailWrite OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnpDbStat 15 }

	dhcpSnpDbStatFirstSuccAccess OBJECT-TYPE
	SYNTAX  INTEGER { none(0), read(1), write(2) }
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              ""
	::= { dhcpSnpDbStat 16 }

	dhcpSnpDbStatLastIgnoreBindCol OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              "Last ignored: binding collision"
	::= { dhcpSnpDbStat 17 }

	dhcpSnpDbStatLastIgnoreExpireLease OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              "Last ignored: expired leases"
	::= { dhcpSnpDbStat 18 }

	dhcpSnpDbStatLastIgnoreInvalidIntf OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              "Last ignored: invalid interface"
	::= { dhcpSnpDbStat 19 }

	dhcpSnpDbStatLastIgnoreUnsuppVlan OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              "Last ignored: unsupported vlans"
	::= { dhcpSnpDbStat 20 }

	dhcpSnpDbStatLastIgnoreParse OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              "Last ignored: parsing error"
	::= { dhcpSnpDbStat 21 }

	dhcpSnpDbStatTotalIgnoreBindCol OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              "Total ignored: binding collision"
	::= { dhcpSnpDbStat 22 }

	dhcpSnpDbStatTotalIgnoreExpireLease OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              "Total ignored: expired leases"
	::= { dhcpSnpDbStat 23 }

	dhcpSnpDbStatTotalIgnoreInvalidIntf OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              "Total ignored: invalid interface"
	::= { dhcpSnpDbStat 24 }

	dhcpSnpDbStatTotalIgnoreUnsuppVlan OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              "Total ignored: unsupported vlans"
	::= { dhcpSnpDbStat 25 }

	dhcpSnpDbStatTotalIgnoreParse OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              "Total ignored: parsing error"
	::= { dhcpSnpDbStat 26 }


	dhcpSnpDbStatFirstSuccessAccess OBJECT-TYPE
	SYNTAX INTEGER{
        		none(0),
        		read(1),
        		write(2)
        }
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
	              "First Success Access"
	::= { dhcpSnpDbStat 27 }

--
--  dhcp snooping dhcp vlan
--

	dhcpSnpDhcpVlan     	OBJECT IDENTIFIER ::= { dhcpSnp 6 }

	dhcpSnpDhcpVlanVid OBJECT-TYPE
	SYNTAX  INTEGER (0..4094)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
	              "0: disable DHCP VLAN."
	::= { dhcpSnpDhcpVlan 1 }                                      



-- --------------------------------------------------------------
--
--  101. ip source guard
--
-- --------------------------------------------------------------

	    ipsgTable  OBJECT-TYPE
	    SYNTAX	SEQUENCE OF IpsgEntry 
	    ACCESS  not-accessible
	    STATUS  mandatory
	    DESCRIPTION
	             	""
	    ::= { ipsg 1 }	

		ipsgEntry  OBJECT-TYPE
        SYNTAX	IpsgEntry 
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	""
        INDEX          	{ ipsgEntryMac, ipsgEntryVid }
        ::= { ipsgTable 1 }

        IpsgEntry ::=
           SEQUENCE {
            ipsgEntryMac		MacAddress,
        	ipsgEntryVid		INTEGER,
        	ipsgEntryIp			IpAddress,
        	ipsgEntryLease		INTEGER,
        	ipsgEntryType		INTEGER,
        	ipsgEntryPort		INTEGER,
        	ipsgEntryState		RowStatus
           }

        ipsgEntryMac OBJECT-TYPE
        SYNTAX  MacAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { ipsgEntry 1 }

        ipsgEntryVid OBJECT-TYPE
        SYNTAX  INTEGER (1.. 4094)
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { ipsgEntry 2 }

        ipsgEntryIp OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { ipsgEntry 3 }

        ipsgEntryLease OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"second"
        ::= { ipsgEntry 4 }

        ipsgEntryType OBJECT-TYPE
        SYNTAX  INTEGER { static(1), dhcp(2) }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { ipsgEntry 5 }

        ipsgEntryPort OBJECT-TYPE
        SYNTAX  INTEGER 
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"0 means any port"
        ::= { ipsgEntry 6 }

        ipsgEntryState OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-create
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { ipsgEntry 7 } 
--
--  ARP Freeze
--
	ipsgArpFreeze     	OBJECT IDENTIFIER ::= { ipsg 2 }
	
	ipsgArpFreezeAll OBJECT-TYPE
	SYNTAX  INTEGER (1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
        "Add all learned ARP table entries to static binding table.
	It's meaningless while reading this entry."
	::= { ipsgArpFreeze 1 }                                      	

	ipsgArpFreezeByPort OBJECT-TYPE
	SYNTAX  PortList
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
	"Add learned ARP table entries to static binding table by ports.
	It's meaningless while reading this entry."
	::= { ipsgArpFreeze 2 }                                      
		
	ipsgArpFreezeByVlan OBJECT-TYPE
	SYNTAX  INTEGER (1..4094)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
	"Add learned ARP table entries to static binding table by VLAN.
	It's meaningless while reading this entry."
	::= { ipsgArpFreeze 3 }
		
-- --------------------------------------------------------------
--
--  102. arpInspect
--
-- --------------------------------------------------------------

		arpInspectSetup  OBJECT IDENTIFIER ::= { arpInspect 1 }

        arpInspectState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { arpInspectSetup 1 }

        arpInspectFilterAgingTime OBJECT-TYPE
        SYNTAX  INTEGER (0..2147483647)
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { arpInspectSetup 2 }

		arpInspectLog  OBJECT IDENTIFIER ::= { arpInspectSetup 3 }
	
        arpInspectLogEntries OBJECT-TYPE
        SYNTAX  INTEGER	(0..1024)
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { arpInspectLog 1 }

        arpInspectLogRate OBJECT-TYPE
        SYNTAX  INTEGER (0..1024)
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { arpInspectLog 2 }
        
        arpInspectLogInterval OBJECT-TYPE
        SYNTAX  INTEGER (0..2147483647)
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { arpInspectLog 3 }                	

	--arpInspectVlanTable
        arpInspectVlanTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF ArpInspectVlanEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { arpInspectSetup 4 }
        
		arpInspectVlanEntry OBJECT-TYPE
        SYNTAX	ArpInspectVlanEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	""
        INDEX          	{ arpInspectVlanVid }
        ::= { arpInspectVlanTable 1 }

        ArpInspectVlanEntry ::=
           SEQUENCE {
        	arpInspectVlanVid	INTEGER,
        	arpInspectVlanLog	INTEGER,
			arpInspectVlanStatus	INTEGER       	
           }

        arpInspectVlanVid OBJECT-TYPE
        SYNTAX  INTEGER (1..4094)
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectVlanEntry 1 }

        arpInspectVlanLog OBJECT-TYPE
        SYNTAX  INTEGER {
        		all (1),
        		none (2),
        		permit (3),
        		deny (4)
        }
        		
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectVlanEntry 2 }

        arpInspectVlanStatus OBJECT-TYPE
        SYNTAX  INTEGER {
        		enabled(1),
        		disabled(2)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectVlanEntry 3 }

	--arpInspectPortTable
        arpInspectPortTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF ArpInspectPortEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { arpInspectSetup 5 }
        
		arpInspectPortEntry OBJECT-TYPE
        SYNTAX	ArpInspectPortEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	""
        INDEX          	{ arpInspectPortIndex }
        ::= { arpInspectPortTable 1 }

        ArpInspectPortEntry ::=
           SEQUENCE {
        	arpInspectPortIndex	INTEGER,
        	arpInspectPortTrust	INTEGER,
			arpInspectPortRate	INTEGER,       	
			arpInspectPortInterval	INTEGER
           }

        arpInspectPortIndex OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectPortEntry 1 }

        arpInspectPortTrust OBJECT-TYPE
        SYNTAX  INTEGER {
        		trusted(1),
        		untrusted(2)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectPortEntry 2 }

        arpInspectPortRate OBJECT-TYPE
        SYNTAX  INTEGER	(0..2048)
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectPortEntry 3 }

        arpInspectPortInterval OBJECT-TYPE
        SYNTAX  INTEGER	(1..15)
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectPortEntry 4 }

		arpInspectStatus  OBJECT IDENTIFIER ::= { arpInspect 2 }

        arpInspectFilterClear OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { arpInspectStatus 1 }

        arpInspectLogClear OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { arpInspectStatus 2 }

	--arpInspectFilterTable
        arpInspectFilterTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF ArpInspectFilterEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { arpInspectStatus 3 }
        
		arpInspectFilterEntry OBJECT-TYPE
        SYNTAX	ArpInspectFilterEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	""
        INDEX          	{ arpInspectFilterMac, arpInspectFilterVid }
        ::= { arpInspectFilterTable 1 }

        ArpInspectFilterEntry ::=
           SEQUENCE {
        	arpInspectFilterMac	MacAddress,
        	arpInspectFilterVid	INTEGER,
			arpInspectFilterPort	INTEGER,       	
			arpInspectFilterExpiry	INTEGER,
			arpInspectFilterReason	INTEGER,
			arpInspectFilterRowStatus RowStatus
           }

        arpInspectFilterMac OBJECT-TYPE
        SYNTAX  MacAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectFilterEntry 1 }

        arpInspectFilterVid OBJECT-TYPE
        SYNTAX  INTEGER (1..4094)
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectFilterEntry 2 }

        arpInspectFilterPort OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectFilterEntry 3 }

        arpInspectFilterExpiry OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectFilterEntry 4 }	

        arpInspectFilterReason OBJECT-TYPE
        SYNTAX  INTEGER {
        		macVid(1),
        		port(2),
        		ip(3)
        }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectFilterEntry 5 }		
        
       arpInspectFilterRowStatus OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-create
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectFilterEntry 6 }	

	--arpInspectLogTable
        arpInspectLogTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF ArpInspectLogEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { arpInspectStatus 4 }
        
		arpInspectLogEntry OBJECT-TYPE
        SYNTAX	ArpInspectLogEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	""
        INDEX          	{ arpInspectLogMac, arpInspectLogVid, arpInspectLogPort, arpInspectLogIp, arpInspectLogReason }
        ::= { arpInspectLogTable 1 }

        ArpInspectLogEntry ::=
           SEQUENCE {
        	arpInspectLogMac	MacAddress,
        	arpInspectLogVid	INTEGER,
			arpInspectLogPort	INTEGER,       	
			arpInspectLogIp	IpAddress,
			arpInspectLogNumPkt	INTEGER,
			arpInspectLogReason	INTEGER,
			arpInspectLogTime	DateAndTime
           }

        arpInspectLogMac OBJECT-TYPE
        SYNTAX  MacAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectLogEntry 1 }

        arpInspectLogVid OBJECT-TYPE
        SYNTAX  INTEGER (1..4094)
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectLogEntry 2 }

        arpInspectLogPort OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectLogEntry 3 }

        arpInspectLogIp OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectLogEntry 4 }	

        arpInspectLogNumPkt OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectLogEntry 5 }		

        arpInspectLogReason OBJECT-TYPE
        SYNTAX  INTEGER {
        		deny (1),
        		denyStatic (2),
        		denyDHCP (3),
        		permitStatic (4),
        		permitDHCP (5)
        }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectLogEntry 6 }		
        
        arpInspectLogTime OBJECT-TYPE
        SYNTAX  DateAndTime
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectLogEntry 7 }		
        
	--arpInspectStatisticsTable
        arpInspectStatisticsTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF ArpInspectStatisticsEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { arpInspectStatus 5 }
        
		arpInspectStatisticsEntry OBJECT-TYPE
        SYNTAX	ArpInspectStatisticsEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	""
        INDEX          	{ arpInspectStatisticsVid }
        ::= { arpInspectStatisticsTable 1 }

        ArpInspectStatisticsEntry ::=
           SEQUENCE {
        	arpInspectStatisticsVid	INTEGER,
			arpInspectStatisticsReceived	Counter,       	
			arpInspectStatisticsRequest	Counter,
			arpInspectStatisticsReply	Counter,
			arpInspectStatisticsForward	Counter,
			arpInspectStatisticsDrop	Counter,
			arpInspectStatisticsClear	EnabledStatus
           }

        arpInspectStatisticsVid OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectStatisticsEntry 1 }

        arpInspectStatisticsReceived OBJECT-TYPE
        SYNTAX  Counter
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectStatisticsEntry 2 }

        arpInspectStatisticsRequest OBJECT-TYPE
        SYNTAX  Counter
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectStatisticsEntry 3 }

        arpInspectStatisticsReply OBJECT-TYPE
        SYNTAX  Counter
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectStatisticsEntry 4 }	

        arpInspectStatisticsForward OBJECT-TYPE
        SYNTAX  Counter
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectStatisticsEntry 5 }		

        arpInspectStatisticsDrop OBJECT-TYPE
        SYNTAX  Counter
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectStatisticsEntry 6 }		

        arpInspectStatisticsClear OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { arpInspectStatisticsEntry 7 } 
  
        
--  104. loopGuardSetup
	                           
        loopGuardState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { loopGuardSetup 1 }

                                               
-- loopGuardPortTable

        loopGuardPortTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF LoopGuardPortEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { loopGuardSetup 2 }
        
		loopGuardPortEntry OBJECT-TYPE
        SYNTAX	LoopGuardPortEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in loopGuardPortTable."
        INDEX          	{ dot1dBasePort }
        ::= { loopGuardPortTable 1 }

        LoopGuardPortEntry ::=
           SEQUENCE {
        	loopGuardPortState	EnabledStatus
           }

        loopGuardPortState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { loopGuardPortEntry 1 }


--  105. subnetBasedVlanSetup
	
        subnetBasedVlanState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
        	"subnet-based vlan feature enabled/disabled for the switch."
        ::= { subnetBasedVlanSetup 1 }

        dhcpVlanOverrideState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
        	"dhcp vlan override enabled/disabled when subnet-based vlan is enabled."
        ::= { subnetBasedVlanSetup 2 }
             
             
	    subnetBasedVlanTable  OBJECT-TYPE
	    SYNTAX	SEQUENCE OF SubnetBasedVlanEntry 
	    ACCESS  not-accessible
	    STATUS  mandatory
	    DESCRIPTION
	             	""
	    ::= { subnetBasedVlanSetup 3 }	

		subnetBasedVlanEntry  OBJECT-TYPE
        SYNTAX	SubnetBasedVlanEntry 
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in subnetBasedVlanTable."
        INDEX          	{subnetBasedVlanSrcIp , subnetBasedVlanSrcMaskBit}
        ::= { subnetBasedVlanTable 1 }

        SubnetBasedVlanEntry ::=
           SEQUENCE {
            subnetBasedVlanSrcIp			IpAddress,
            subnetBasedVlanSrcMaskBit		INTEGER,
            subnetBasedVlanName				DisplayString,     
            subnetBasedVlanVid				INTEGER,
            subnetBasedVlanPriority			INTEGER,          
            subnetBasedVlanEntryState		RowStatus            
           }

        subnetBasedVlanSrcIp OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                      	"source ip for subnet-based vlan entry"
        ::= { subnetBasedVlanEntry 1 }


        subnetBasedVlanSrcMaskBit OBJECT-TYPE
        SYNTAX  INTEGER (1..32)
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                      	"source ip mask-bits for subnet-based vlan entry"
        ::= { subnetBasedVlanEntry 2 }
             
        subnetBasedVlanName OBJECT-TYPE
        SYNTAX  DisplayString (SIZE (0..32))
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"name for subnet-based vlan entry"
        ::= { subnetBasedVlanEntry 3 }
                                                   
        subnetBasedVlanVid OBJECT-TYPE
        SYNTAX  INTEGER (1..4094)
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"vid for subnet-based vlan entry"
        ::= { subnetBasedVlanEntry 4 }


        subnetBasedVlanPriority OBJECT-TYPE
        SYNTAX  INTEGER (0..7)
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"priority for subnet-based vlan entry"
        ::= { subnetBasedVlanEntry 5 }

        subnetBasedVlanEntryState OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-create
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { subnetBasedVlanEntry 6 } 


--  107. MSTP  
--****************************************************************
-- TEXTUAL-CONVENTIONs
--****************************************************************
		MstiOrCistInstanceIndex ::= TEXTUAL-CONVENTION
      		STATUS       current
    		DESCRIPTION
	            "This textual convention is an extension of the
	            MstiInstanceIndex convention.  This extension permits the
	            additional value of zero, which means Common and Internal
	            Spanning Tree (CIST)."
            SYNTAX      Integer32 (0..16) 
            
-- mstpGen group reflects configurations/statuses
-- the Bridge as a unit  
	    mstpGen			OBJECT IDENTIFIER ::= { mstp 1}  
	    
        mstpGenState		OBJECT-TYPE
        SYNTAX    EnabledStatus
        ACCESS    read-write
        STATUS    mandatory
        DESCRIPTION
        "Enabled/disabled on the mrstp bridge."
        ::= { mstpGen 1 }
        
        mstpGenCfgIdName    OBJECT-TYPE
        SYNTAX    DisplayString
        ACCESS    read-write
        STATUS    mandatory 
        DESCRIPTION 
        "The configuration name that identifies the MST
         region and is used as one of the inputs in the 
         computation of the MST Configuration Identifier." 
        REFERENCE
            "*********.2.b)"
        ::= { mstpGen 2 }
        
        mstpGenCfgIdRevLevel OBJECT-TYPE
        SYNTAX    Integer32
        ACCESS    read-write
        STATUS    mandatory 
        DESCRIPTION 
        "This object identifies the MST revision that
         identifies the MST region and is used as one
         of the inputs in the computation of the MST 
         configuration Identifier."
        REFERENCE
            "*********.2.c)"
        ::= { mstpGen 3 }
	    
	    
	   mstpGenCfgIdCfgDigest  OBJECT-TYPE
            SYNTAX    OCTET STRING(SIZE(16))
            ACCESS    read-only
            STATUS    mandatory
            DESCRIPTION
              "Configuration Digest."
            REFERENCE
               "*********.3.a.4"
            ::= { mstpGen 4 }
	    
	    mstpGenHelloTime  OBJECT-TYPE
	        SYNTAX    Timeout (1..10)
            ACCESS    read-write
            STATUS    mandatory 
            DESCRIPTION
            ""                  
            ::= { mstpGen 5 }
       
        mstpGenMaxAge    OBJECT-TYPE
            SYNTAX  Timeout (6..40)
            ACCESS  read-write
            STATUS  mandatory 
            DESCRIPTION
            "" 
            ::= { mstpGen 6 }
         
        mstpGenForwardDelay   OBJECT-TYPE 
            SYNTAX  Timeout (4..30)
            ACCESS  read-write
            STATUS  mandatory
            DESCRIPTION
            ""
            ::= {mstpGen 7}
        
        mstpGenMaxHops  OBJECT-TYPE
            SYNTAX      Integer32 (1..255)
            MAX-ACCESS  read-write
            STATUS      mandatory
            DESCRIPTION
               "13.22.f)"
            ::= { mstpGen 8 }
	    
		mstpGenCistRootPathCost  OBJECT-TYPE
            SYNTAX      Integer32
            MAX-ACCESS  read-only
            STATUS      mandatory
            DESCRIPTION
	    "."
            ::= { mstpGen 9 }
	    
		mstpGenCistRootBrid  OBJECT-TYPE
            SYNTAX      OCTET STRING(SIZE(8))
            MAX-ACCESS  read-only
            STATUS      mandatory
            DESCRIPTION
            "."
            ::= { mstpGen 10 }
-- MSTP MAP TABLE
        mstMapTable        OBJECT-TYPE
            SYNTAX      SEQUENCE OF MstMapEntry
            ACCESS  not-accessible
            STATUS      mandatory
            DESCRIPTION
               "This table contains one entry for each instance of MSTP."
            ::= { mstp 20 }	
         
        mstMapEntry        OBJECT-TYPE
            SYNTAX      MstMapEntry
            ACCESS  not-accessible
            STATUS      mandatory
            DESCRIPTION
               "A conceptual row containing the status of the MSTP instance."
            INDEX  { mstMapIndex }
            ::= { mstMapTable 1 }
        
        MstMapEntry ::= SEQUENCE {
                   mstMapIndex             MstiOrCistInstanceIndex,
                   mstMapVlans1k           OCTET STRING,
                   mstMapVlans2k           OCTET STRING,
                   mstMapVlans3k           OCTET STRING,
                   mstMapVlans4k           OCTET STRING,
                   mstMapRowStatus         RowStatus
        }
        
        mstMapIndex    OBJECT-TYPE
            SYNTAX      MstiOrCistInstanceIndex
            MAX-ACCESS  not-accessible
            STATUS      mandatory
            DESCRIPTION
            "Uniquely identifies an instance. The entry of this table with index 0
            presents always, represents CIST. When SET operation "
            ::= { mstMapEntry 1 }  
        
        mstMapVlans1k      OBJECT-TYPE
            SYNTAX      OCTET STRING (SIZE (0..128))
            ACCESS  read-write
            STATUS      mandatory
            DESCRIPTION
           "A string of octets containing one bit per VLAN. The
            first octet corresponds to VLANs with VlanIndex values
            1 through 8; the second octet to VLANs 9 through
            16 etc.  The most significant bit of each octet
            corresponds to the lowest VlanIndex value in that octet.

            For each VLAN that is mapped to this MSTP instance,
            the bit corresponding to that VLAN is set to '1'.
            Empty (zero) most significant octes are not mandatory."
            ::= { mstMapEntry 2 } 
        
        mstMapVlans2k      OBJECT-TYPE
            SYNTAX  OCTET STRING (SIZE (0..128))
            ACCESS  read-write
            STATUS  mandatory
            DESCRIPTION
            "A string of octets containing one bit per VLAN for
             VLANS with VlanIndex values 1024 through 2047. The
             first octet corresponds to VLANs with VlanIndex values
             1024 through 1031; the second octet to VLANs 1032
             through 1039 etc.  The most significant bit of each
             octet corresponds to the lowest VlanIndex value in that
             octet.
             
             For each VLAN that is mapped to this MSTP instance,
             the bit corresponding to that VLAN is set to '1'.

             Empty (zero) most significant octes are not mandatory."
             ::= { mstMapEntry 3 }
        
        mstMapVlans3k      OBJECT-TYPE
            SYNTAX      OCTET STRING (SIZE (0..128))
            ACCESS  read-write
            STATUS      current
            DESCRIPTION
            "A string of octets containing one bit per VLAN for
             VLANS with VlanIndex values 2048 through 3071. The
             first octet corresponds to VLANs with VlanIndex values
             of 2048 through 2055; the second octet to VLANs 2056
             through 2063 etc.  The most significant bit of each
             octet corresponds to the lowest VlanIndex value in that
             octet.

             For each VLAN that is mapped to this MSTP instance,
             the bit corresponding to that VLAN is set to '1'.

             Empty (zero) most significant octes are not mandatory."
             ::= { mstMapEntry 4 }
        
        mstMapVlans4k      OBJECT-TYPE
            SYNTAX      OCTET STRING (SIZE (0..128))
            MAX-ACCESS  read-write
            STATUS      current
            DESCRIPTION
            "A string of octets containing one bit per VLAN for
             VLANS with VlanIndex values 3072 through 4095. The
             first octet corresponds to VLANs with VlanIndex values
             3072 through 3079; the second octet to VLANs 3080
             through 3087 etc.  The most significant bit of each
             octet corresponds to the lowest VlanIndex value in that
             octet.
        
             For each VLAN that is mapped to this MSTP instance,
             the bit corresponding to that VLAN is set to '1'.
           
             Empty (zero) most significant octes are not mandatory."
             ::= { mstMapEntry 5 }   
        
        mstMapRowStatus    OBJECT-TYPE
            SYNTAX  RowStatus
            ACCESS  read-create
            STATUS  mandatory
            DESCRIPTION 
            ""
        ::= {  mstMapEntry 6 }
    	
-- MSTP Vlan Table, map vlan to msti
        mstVlanTable       OBJECT-TYPE
            SYNTAX      SEQUENCE OF MstVlanEntry
            ACCESS  not-accessible
            STATUS      mandatory
            DESCRIPTION
            "This table contains one entry for each VlanId."
            ::= { mstp 30 } 
        
        mstVlanEntry  OBJECT-TYPE
            SYNTAX      MstVlanEntry
            ACCESS      not-accessible
            STATUS      mandatory
            DESCRIPTION
                "Information regarding the instance to which each Vlan is mapped."
            INDEX  { mstVlanIndex }
            ::= { mstVlanTable 1 }
           
        MstVlanEntry ::= SEQUENCE {
              mstVlanIndex         INTEGER,
              mstVlanMstIndex      MstiOrCistInstanceIndex
        }
        
        
        mstVlanIndex     OBJECT-TYPE
            SYNTAX        INTEGER(1..4094)
            ACCESS        not-accessible
            STATUS        mandatory
            DESCRIPTION
                "The VlanId for which this entry contains the instance mapped."
            ::= { mstVlanEntry 1 } 
         
        mstVlanMstIndex    OBJECT-TYPE
             SYNTAX      MstiOrCistInstanceIndex
             ACCESS  read-only
             STATUS      mandatory
             DESCRIPTION
                 "An integer with values ranging from 0 to 64 that identify a 
                      the CIST/MSTI instance to which this VLAN is mapped" 
             ::= { mstVlanEntry 2 }   
-- MSTP Port Table, information for all instance of a port
        mstpPortTable      OBJECT-TYPE
            SYNTAX      SEQUENCE OF MstpPortEntry
            ACCESS  not-accessible
            STATUS      mandatory
            DESCRIPTION
                "A table that contains generic information about
                every port that is associated with this bridge."
            ::= { mstp 40 }
        
        mstpPortEntry      OBJECT-TYPE
            SYNTAX  MstpPortEntry
            ACCESS  not-accessible
            STATUS  mandatory
            DESCRIPTION
            "A list of information for each port of the
            bridge."
            INDEX  { mstpPortIndex }
            ::= { mstpPortTable 1 }
        
        MstpPortEntry ::= SEQUENCE {
             mstpPortIndex                    INTEGER,
             mstpPortOperEdgePort             TruthValue,
             mstpPortOperPointToPointMAC      TruthValue,
			 mstpPortAdminEdgePort            TruthValue
        }
        
        mstpPortIndex	OBJECT-TYPE
            SYNTAX    INTEGER (1..65535)
            ACCESS  not-accessible
            STATUS      mandatory
            DESCRIPTION
            "A unique value, greater than zero, for each Port.
            The value for each interface sub-layer
            must remain constant at least from one re-initialization
            of the entity's network management system to the next re-
            initialization."
            ::= { mstpPortEntry 1 }
        
        mstpPortOperEdgePort    OBJECT-TYPE
            SYNTAX    TruthValue
            ACCESS    read-only
            STATUS    mandatory 
            DESCRIPTION
            ""
            REFERENCE   ""
            ::= { mstpPortEntry 2 }
        
        mstpPortOperPointToPointMAC    OBJECT-TYPE
            SYNTAX    TruthValue
            ACCESS    read-only
            STATUS    mandatory
            DESCRIPTION
            ""
            REFERENCE    ""
            ::= {mstpPortEntry 3} 

		mstpPortAdminEdgePort      OBJECT-TYPE
            SYNTAX  INTEGER {
					true(1),
					false(2)
				}
            ACCESS      read-write
            STATUS      mandatory
            DESCRIPTION
            "The administrative value of the Edge Port parameter.  A
			value of true(1) indicates that this port should be
			assumed as an edge-port, and a value of false(2) indicates
			that this port should be assumed as a non-edge-port."
            ::= { mstpPortEntry 4 }  
			
		
-- MSTP Xst Table, Cist/Mst status/setting
        mstpXstTable  OBJECT-TYPE
              SYNTAX      SEQUENCE OF MstpXstEntry
              ACCESS  not-accessible
              STATUS      mandatory
              DESCRIPTION
                "."
              ::= { mstp 50 }

        mstpXstEntry OBJECT-TYPE
              SYNTAX      MstpXstEntry
              ACCESS  not-accessible
              STATUS      mandatory
              DESCRIPTION
                "."
              INDEX { mstpXstId }
              ::= { mstpXstTable 1 }  
        
       MstpXstEntry ::= SEQUENCE {
	           mstpXstId                      MstiOrCistInstanceIndex,
	           mstpXstBridgePriority          Integer32,
	           mstpXstBridgeId                BridgeId,
	           mstpXstInternalRootCost        Integer32,
	           mstpXstRootPort                INTEGER,
	           mstpXstTimeSinceTopologyChange TimeTicks,
	           mstpXstTopologyChangesCount    Counter32
       }
       
       mstpXstId   OBJECT-TYPE
           SYNTAX      MstiOrCistInstanceIndex
           ACCESS      read-only
           STATUS      mandatory
           DESCRIPTION
             "0 means CIST."
           ::= { mstpXstEntry 1 }
       
       mstpXstBridgePriority   OBJECT-TYPE
           SYNTAX      Integer32 (0..61440)
           ACCESS  read-write
           STATUS      mandatory
           DESCRIPTION
              "Bridge priority, in steps of 4096."
           DEFVAL       { 32768 }
           ::= { mstpXstEntry 2 } 
       
       mstpXstBridgeId   OBJECT-TYPE
           SYNTAX      BridgeId
           ACCESS      read-only
           STATUS      mandatory
           DESCRIPTION
             "."
           ::= { mstpXstEntry 3 }   
        
        
       mstpXstInternalRootCost     OBJECT-TYPE
           SYNTAX      Integer32
           ACCESS      read-only
           STATUS      mandatory
           DESCRIPTION
             "."
           ::= { mstpXstEntry 4 }

       mstpXstRootPort      OBJECT-TYPE
           SYNTAX      INTEGER 
           ACCESS      read-only
           STATUS      mandatory
           DESCRIPTION
           "."
           ::= { mstpXstEntry 5 }
       
       mstpXstTimeSinceTopologyChange  OBJECT-TYPE
           SYNTAX      TimeTicks
           ACCESS      read-only
           STATUS      mandatory
           DESCRIPTION
             "."
           ::= { mstpXstEntry 6 }

       mstpXstTopologyChangesCount     OBJECT-TYPE
           SYNTAX      Counter32
           ACCESS      read-only
           STATUS      mandatory
           DESCRIPTION
             "."
           ::= { mstpXstEntry 7 }  
-- MSTP Xst Port Table, Cist/Mst Port status/setting
       mstpXstPortTable    OBJECT-TYPE
           SYNTAX  SEQUENCE OF MstpXstPortEntry
           ACCESS  not-accessible
           STATUS  mandatory
           DESCRIPTION
             "."
           ::= { mstp 60 }
       
       mstpXstPortEntry    OBJECT-TYPE
           SYNTAX  MstpXstPortEntry
           ACCESS  not-accessible
           STATUS  mandatory
           DESCRIPTION
             "."
           REFERENCE
             "."
           INDEX  { mstpXstPortXstId, mstpXstPortIndex }
           ::= { mstpXstPortTable 1 }
       
        
        MstpXstPortEntry ::=
        SEQUENCE {
         	mstpXstPortXstId                 MstiOrCistInstanceIndex,
          	mstpXstPortIndex                 INTEGER,
          	mstpXstPortEnable                EnabledStatus, 
          	mstpXstPortPriority              Integer32,
          	mstpXstPortPathCost              INTEGER,   
          	mstpXstPortState				 INTEGER,
          	mstpXstPortDesignatedRoot        BridgeId,
          	mstpXstPortDesignatedCost        Integer32,
          	mstpXstPortDesignatedBridge      BridgeId,
          	mstpXstPortDesignatedPort        INTEGER
        }
        
        mstpXstPortXstId     OBJECT-TYPE
            SYNTAX      MstiOrCistInstanceIndex
            ACCESS      not-accessible
            STATUS      mandatory
            DESCRIPTION
              "0 means CIST."
            ::= { mstpXstPortEntry 1 }

        mstpXstPortIndex     OBJECT-TYPE
            SYNTAX      INTEGER(1..65535)
            ACCESS      read-only
            STATUS      mandatory
            DESCRIPTION
              "The value of mstpPortIndex of the Port
              in mstpPortTable."
            ::= { mstpXstPortEntry 2 }
        
        
        mstpXstPortEnable     OBJECT-TYPE
            SYNTAX      EnabledStatus
            ACCESS      read-write
            STATUS      mandatory
            DESCRIPTION
              "."
            ::= { mstpXstPortEntry 3 }
         
         mstpXstPortPriority              OBJECT-TYPE 
             SYNTAX      Integer32 (0..255)
             ACCESS      read-write
             STATUS      mandatory
             DESCRIPTION
               "Port priority, in steps of 16."
             DEFVAL       { 128 }
             ::= { mstpXstPortEntry 4 }
        
         mstpXstPortPathCost              OBJECT-TYPE 
             SYNTAX      INTEGER (1..65535)
             ACCESS      read-write
             STATUS      mandatory
             DESCRIPTION
               "."
             ::= { mstpXstPortEntry 5 }

         mstpXstPortState                 OBJECT-TYPE 
             SYNTAX      INTEGER {
               disabled(0),
               discarding(1),
               learning(2),
               forwarding(3),
               unknown(4)
             }
             ACCESS      read-only
             STATUS      mandatory
             DESCRIPTION
               "."
             ::= { mstpXstPortEntry 6 }
         
         mstpXstPortDesignatedRoot        OBJECT-TYPE 
              SYNTAX      BridgeId
              ACCESS      read-only
              STATUS      mandatory
              DESCRIPTION
              "."
              ::= { mstpXstPortEntry 7 }
         
         mstpXstPortDesignatedCost        OBJECT-TYPE
             SYNTAX      Integer32
             ACCESS      read-only
             STATUS      mandatory
             DESCRIPTION
             "."
             ::= { mstpXstPortEntry 8 }
         
         mstpXstPortDesignatedBridge      OBJECT-TYPE
             SYNTAX      BridgeId
             ACCESS      read-only
             STATUS      mandatory
             DESCRIPTION
             "."
             ::= { mstpXstPortEntry 9 }
        
         mstpXstPortDesignatedPort      OBJECT-TYPE
             SYNTAX      INTEGER
             ACCESS      read-only
             STATUS      mandatory
             DESCRIPTION
             "."
             ::= { mstpXstPortEntry 10 }  
--MSTP Traps 
 	     mstpNotifications     	OBJECT IDENTIFIER ::= { mstp 70 }              
	
	     newRoot NOTIFICATION-TYPE
         OBJECTS {
                mstpXstId
         }
         STATUS  current
         DESCRIPTION
          ""
         ::= { mstpNotifications 1 }
      
         topologyChange NOTIFICATION-TYPE
         OBJECTS {
                mstpXstId
         }
         STATUS  current
         DESCRIPTION
          ""
         ::= { mstpNotifications 2 }
       
--  108. radiusServerSetup
	
        radiusAuthServerSetup OBJECT IDENTIFIER ::= { radiusServerSetup 1 }
        
        radiusAuthServerMode OBJECT-TYPE
        SYNTAX  INTEGER{
        		index-priority(1),
				round-robin(2),
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { radiusAuthServerSetup 1 }

        radiusAuthServerTimeout OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { radiusAuthServerSetup 2 }

-- radiusAuthServerTable
        radiusAuthServerTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF RadiusAuthServerEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { radiusAuthServerSetup 3 }
        
		radiusAuthServerEntry OBJECT-TYPE
        SYNTAX	RadiusAuthServerEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in radiusAuthServerTable."
        INDEX          	{ radiusAuthServerIndex }
        ::= { radiusAuthServerTable 1 }

        RadiusAuthServerEntry ::=
           SEQUENCE {
        	radiusAuthServerIndex			INTEGER,
        	radiusAuthServerIpAddr			IpAddress,
        	radiusAuthServerUdpPort			INTEGER,
        	radiusAuthServerSharedSecret	DisplayString
           }

        radiusAuthServerIndex OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { radiusAuthServerEntry 1 }

        radiusAuthServerIpAddr OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { radiusAuthServerEntry 2 }

        radiusAuthServerUdpPort OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { radiusAuthServerEntry 3 }

        radiusAuthServerSharedSecret OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { radiusAuthServerEntry 4 }

        radiusAcctServerSetup OBJECT IDENTIFIER ::= { radiusServerSetup 2 }

        radiusAcctServerTimeout OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { radiusAcctServerSetup 1 }

-- radiusAcctServerTable
        radiusAcctServerTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF RadiusAcctServerEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { radiusAcctServerSetup 2 }
        
		radiusAcctServerEntry OBJECT-TYPE
        SYNTAX	RadiusAcctServerEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in radiusAcctServerTable."
        INDEX          	{ radiusAcctServerIndex }
        ::= { radiusAcctServerTable 1 }

        RadiusAcctServerEntry ::=
           SEQUENCE {
        	radiusAcctServerIndex			INTEGER,
        	radiusAcctServerIpAddr			IpAddress,
        	radiusAcctServerUdpPort			INTEGER,
        	radiusAcctServerSharedSecret	DisplayString
           }

        radiusAcctServerIndex OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { radiusAcctServerEntry 1 }

        radiusAcctServerIpAddr OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { radiusAcctServerEntry 2 }

        radiusAcctServerUdpPort OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { radiusAcctServerEntry 3 }

        radiusAcctServerSharedSecret OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { radiusAcctServerEntry 4 }
        
--  109. tacacsServerSetup
	
        tacacsAuthServerSetup OBJECT IDENTIFIER ::= { tacacsServerSetup 1 }
        
        tacacsAuthServerMode OBJECT-TYPE
        SYNTAX  INTEGER{
        		index-priority(1),
				round-robin(2)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { tacacsAuthServerSetup 1 }

        tacacsAuthServerTimeout OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { tacacsAuthServerSetup 2 }

-- tacacsAuthServerTable
        tacacsAuthServerTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF TacacsAuthServerEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { tacacsAuthServerSetup 3 }
        
		tacacsAuthServerEntry OBJECT-TYPE
        SYNTAX	TacacsAuthServerEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in tacacsAuthServerTable."
        INDEX          	{ tacacsAuthServerIndex }
        ::= { tacacsAuthServerTable 1 }

        TacacsAuthServerEntry ::=
           SEQUENCE {
        	tacacsAuthServerIndex			INTEGER,
        	tacacsAuthServerIpAddr			IpAddress,
        	tacacsAuthServerTcpPort			INTEGER,
        	tacacsAuthServerSharedSecret	DisplayString
           }

        tacacsAuthServerIndex OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { tacacsAuthServerEntry 1 }

        tacacsAuthServerIpAddr OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { tacacsAuthServerEntry 2 }

        tacacsAuthServerTcpPort OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { tacacsAuthServerEntry 3 }

        tacacsAuthServerSharedSecret OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { tacacsAuthServerEntry 4 }

        tacacsAcctServerSetup OBJECT IDENTIFIER ::= { tacacsServerSetup 2 }

        tacacsAcctServerTimeout OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { tacacsAcctServerSetup 1 }

-- tacacsAcctServerTable
        tacacsAcctServerTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF TacacsAcctServerEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { tacacsAcctServerSetup 2 }
        
		tacacsAcctServerEntry OBJECT-TYPE
        SYNTAX	TacacsAcctServerEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in tacacsAcctServerTable."
        INDEX          	{ tacacsAcctServerIndex }
        ::= { tacacsAcctServerTable 1 }

        TacacsAcctServerEntry ::=
           SEQUENCE {
        	tacacsAcctServerIndex		INTEGER,
        	tacacsAcctServerIpAddr		IpAddress,
        	tacacsAcctServerTcpPort		INTEGER,
        	tacacsAcctServerSharedSecret	DisplayString
           }

        tacacsAcctServerIndex OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { tacacsAcctServerEntry 1 }

        tacacsAcctServerIpAddr OBJECT-TYPE
        SYNTAX  IpAddress
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { tacacsAcctServerEntry 2 }

        tacacsAcctServerTcpPort OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { tacacsAcctServerEntry 3 }

        tacacsAcctServerSharedSecret OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { tacacsAcctServerEntry 4 }

--  110. aaaSetup
	
        authenticationSetup OBJECT IDENTIFIER ::= { aaaSetup 1 }

-- authenticationTypeTable
        authenticationTypeTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF AuthenticationTypeEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { authenticationSetup 1 }
        
		authenticationTypeEntry OBJECT-TYPE
        SYNTAX	AuthenticationTypeEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in authenticationTypeTable."
        INDEX          	{ authenticationTypeName }
        ::= { authenticationTypeTable 1 }

        AuthenticationTypeEntry ::=
           SEQUENCE {
        	authenticationTypeName	DisplayString,
        	authenticationTypeMethodList	OCTET STRING
           }

        authenticationTypeName OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { authenticationTypeEntry 1 }

        authenticationTypeMethodList OBJECT-TYPE
        SYNTAX  OCTET STRING
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { authenticationTypeEntry 2 }

    	accountingSetup      	OBJECT IDENTIFIER ::= { aaaSetup 2 }      
		accountingUpdatePeriod OBJECT-TYPE
		SYNTAX  Integer32
		ACCESS  read-write
		STATUS  mandatory
		DESCRIPTION
                      	""
		::= { accountingSetup 1 }		
	
-- accountingTypeTable
        accountingTypeTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF AccountingTypeEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { accountingSetup 2 }
        
		accountingTypeEntry OBJECT-TYPE
        SYNTAX	AccountingTypeEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in accountingTypeTable."
        INDEX          	{ accountingTypeName }
        ::= { accountingTypeTable 1 }

        AccountingTypeEntry ::=
           SEQUENCE {
         	accountingTypeName		DisplayString,
         	accountingTypeActive		EnabledStatus,	         	
         	accountingTypeBroadcast		EnabledStatus,	         	
         	accountingTypeMode		INTEGER,
         	accountingTypeMethod		INTEGER,
         	accountingTypePrivilege		INTEGER        	
           }

		accountingTypeName OBJECT-TYPE
		SYNTAX  DisplayString
		ACCESS  read-only
		STATUS  mandatory
		DESCRIPTION
                      	""
		::= { accountingTypeEntry 1 }

		accountingTypeActive OBJECT-TYPE
		SYNTAX  EnabledStatus
		ACCESS  read-write
		STATUS  mandatory
		DESCRIPTION
                      	""
		::= { accountingTypeEntry 2 }

		accountingTypeBroadcast OBJECT-TYPE
		SYNTAX  EnabledStatus
		ACCESS  read-write
		STATUS  mandatory
		DESCRIPTION
                      	""
		::= { accountingTypeEntry 3 }

		accountingTypeMode OBJECT-TYPE
		SYNTAX  INTEGER{
			not-available(255),
        	start-stop(1),
			stop-only(2)		
		}
		ACCESS  read-write
		STATUS  mandatory
		DESCRIPTION
                      	""
		::= { accountingTypeEntry 4 }

		accountingTypeMethod OBJECT-TYPE
		SYNTAX  INTEGER{		
        	radius(1),
			tacacs(2)		
		}
		ACCESS  read-write
		STATUS  mandatory
		DESCRIPTION
                      	""
		::= { accountingTypeEntry 5 }

		accountingTypePrivilege OBJECT-TYPE
		SYNTAX  INTEGER{	
			not-available(255),
			privilege-0(0),
			privilege-1(1),
			privilege-2(2),
			privilege-3(3),
			privilege-4(4),
			privilege-5(5),
			privilege-6(6),
			privilege-7(7),
			privilege-8(8),
			privilege-9(9),
			privilege-10(10),
			privilege-11(11),
			privilege-12(12),
			privilege-13(13),
			privilege-14(14)        		
		}
		ACCESS  read-write
		STATUS  mandatory
		DESCRIPTION
                      	""
		::= { accountingTypeEntry 6 }

		authorizationSetup      	OBJECT IDENTIFIER ::= { aaaSetup 3 }
	
-- authorizationTypeTable
		authorizationTypeTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF AuthorizationTypeEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { authorizationSetup 1 }
        
   		authorizationTypeEntry OBJECT-TYPE
        SYNTAX	AuthorizationTypeEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in authorizationTypeTable."
        INDEX          	{ authorizationTypeName }
        ::= { authorizationTypeTable 1 }

        AuthorizationTypeEntry ::=
           SEQUENCE {
         	authorizationTypeName		DisplayString,
         	authorizationTypeActive		EnabledStatus,	         	
         	authorizationTypeMethod		INTEGER
           }

		authorizationTypeName OBJECT-TYPE
		SYNTAX  DisplayString
		ACCESS  read-only
		STATUS  mandatory
		DESCRIPTION
                      	""
		::= { authorizationTypeEntry 1 }

		authorizationTypeActive OBJECT-TYPE
		SYNTAX  EnabledStatus
		ACCESS  read-write
		STATUS  mandatory
		DESCRIPTION
                      	""
		::= { authorizationTypeEntry 2 }

		authorizationTypeMethod OBJECT-TYPE
		SYNTAX  INTEGER{		
    	    radius(1),
			tacacs(2)		
		}
		ACCESS  read-write
		STATUS  mandatory
		DESCRIPTION
                      	""
		::= { authorizationTypeEntry 3 }
	
	
---------------------------------------------------
--
--  Port Isolation
--
---------------------------------------------------

--  112. portIsolationTable
	
        portIsolationTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF PortIsolationEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { portIsolationSetup 1 }
        
		portIsolationEntry OBJECT-TYPE
        SYNTAX	PortIsolationEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in portIsolationTable."
        INDEX          	{ dot1dBasePort }
        ::= { portIsolationTable 1 }

        PortIsolationEntry ::=
           SEQUENCE {
        	portIsolationState		EnabledStatus
           }

        portIsolationState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"Port isolation enabled/disabled on the port. 
                      	Enable(1) to be isolated and not exchange packets."
        ::= { portIsolationEntry 1 }


---------------------------------------------------
--
--  115. Layer 2 Protocol Tunneling
--
---------------------------------------------------
	
--  l2ptState                               
        l2ptState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                       "L2PT feature enabled/disabled for the switch"
        ::= { l2ptSetup 1 }
                                
        l2ptMacAddr OBJECT-TYPE
        SYNTAX  MacAddress
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                       "L2PT destination mac address"
        ::= { l2ptSetup 2 }
                                
-- l2ptTable
 
        l2ptTable OBJECT-TYPE
        SYNTAX SEQUENCE OF L2ptEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                  ""
        ::= { l2ptSetup 3 }
        
 		l2ptEntry OBJECT-TYPE
        SYNTAX L2ptEntry
        ACCESS not-accessible
        STATUS mandatory
        DESCRIPTION     "An entry in l2ptTable."
        INDEX           { dot1dBasePort }
        ::= { l2ptTable 1 }
 
        L2ptEntry ::=
           SEQUENCE {
         	l2ptProtocolGroup  BITS,
         	l2ptPointToPointProtocolGroup BITS,
         	l2ptMode   INTEGER
           }
 
     	l2ptProtocolGroup OBJECT-TYPE
        SYNTAX BITS {
		  cdp(0),
		  stp(1),
		  vtp(2)
		 }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                       "L2PT protocols enabled/disabled on the port"
        ::= { l2ptEntry 1 }
     	
    	l2ptPointToPointProtocolGroup OBJECT-TYPE
        SYNTAX BITS {
		  pagp(0),
		  lacp(1),
		  udld(2)
		 }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                       "L2PT point-to-point protocols enabled/disabled on the port"
        ::= { l2ptEntry 2 }
                
        l2ptMode OBJECT-TYPE
        SYNTAX  INTEGER {
         access(1),
         tunnel(2)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                       "L2PT port role on the port"
        ::= { l2ptEntry 3 }   
        
--  117. transceiverInfoTable
	
-- transceiverSerialInfoTable
	
        transceiverSerialInfoTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF TransceiverSerialInfoEntry
        ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
                 	""
        ::= { transceiverInfo 1 }
        
		transceiverSerialInfoEntry OBJECT-TYPE
        SYNTAX	TransceiverSerialInfoEntry
        ACCESS	not-accessible
        STATUS	current
        DESCRIPTION    	"An entry in transceiverSerialInfoTable."
        INDEX          	{ transceiverSerialInfoEntryPort }
        ::= { transceiverSerialInfoTable 1 }

        TransceiverSerialInfoEntry ::=
           SEQUENCE {
        	transceiverSerialInfoEntryPort			INTEGER,
        	transceiverSerialInfoEntryStatus		INTEGER,
        	transceiverSerialInfoEntryVendor		DisplayString,
        	transceiverSerialInfoEntryPartNo		DisplayString,
        	transceiverSerialInfoEntrySerialNo		DisplayString,
        	transceiverSerialInfoEntryRevision		DisplayString,
        	transceiverSerialInfoEntryDateCode		DisplayString,
        	transceiverSerialInfoEntryTransceiver	DisplayString
           }

        transceiverSerialInfoEntryPort OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"Index of transceiverSerialInfo. It is referred to dot1dBasePort"
        ::= { transceiverSerialInfoEntry 1 }
        
        transceiverSerialInfoEntryStatus OBJECT-TYPE
        SYNTAX  INTEGER {
        		ok_with_DDM(1),
        		ok_without_DDM(2),
        		nonoperational(3)
        }
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"Transceiver module status."
        ::= { transceiverSerialInfoEntry 2 }


        transceiverSerialInfoEntryVendor OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"Transceiver module vendor name."
        ::= { transceiverSerialInfoEntry 3 }

        transceiverSerialInfoEntryPartNo OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"Part number provided by transceiver module vendor."
        ::= { transceiverSerialInfoEntry 4 }
        
        transceiverSerialInfoEntrySerialNo OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"Serial number provided by transceiver module vendor."
        ::= { transceiverSerialInfoEntry 5 }

        transceiverSerialInfoEntryRevision OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"Revision level for part number provided by transceiver module vendor."
        ::= { transceiverSerialInfoEntry 6 }

        transceiverSerialInfoEntryDateCode OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"Transceiver module vendor's manufacturing date code"
        ::= { transceiverSerialInfoEntry 7 }
        
        transceiverSerialInfoEntryTransceiver OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"Transceiver module type names"
        ::= { transceiverSerialInfoEntry 8 }

-- transceiverDdmInfoTable
	
        transceiverDdmInfoTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF TransceiverDdmInfoEntry
        ACCESS  not-accessible
        STATUS  current
        DESCRIPTION
                 	""
        ::= { transceiverInfo 2 }
        
		transceiverDdmInfoEntry OBJECT-TYPE
        SYNTAX	TransceiverDdmInfoEntry
        ACCESS	not-accessible
        STATUS	current
        DESCRIPTION    	"An entry in transceiverDdmInfoTable."
        INDEX          	{ transceiverDdmInfoEntryPort, transceiverDdmInfoEntryType }
        ::= { transceiverDdmInfoTable 1 }

        TransceiverDdmInfoEntry ::=
           SEQUENCE {
        	transceiverDdmInfoEntryPort			INTEGER,
        	transceiverDdmInfoEntryType			INTEGER,
        	transceiverDdmInfoEntryAlarmMax		INTEGER,
        	transceiverDdmInfoEntryAlarmMin		INTEGER,
        	transceiverDdmInfoEntryWarnMax		INTEGER,
        	transceiverDdmInfoEntryWarnMin		INTEGER,
        	transceiverDdmInfoEntryCurrent		INTEGER,
        	transceiverDdmInfoEntryDescription	DisplayString
           }

        transceiverDdmInfoEntryPort OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"Index of transceiverDdmInfo. This will be referred to dot1dBasePort"
        ::= { transceiverDdmInfoEntry 1 }
        
        transceiverDdmInfoEntryType OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"Transceiver module status."
        ::= { transceiverDdmInfoEntry 2 }


        transceiverDdmInfoEntryAlarmMax OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"Transceiver module vendor name."
        ::= { transceiverDdmInfoEntry 3 }

        transceiverDdmInfoEntryAlarmMin OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"Part number provided by transceiver module vendor."
        ::= { transceiverDdmInfoEntry 4 }
        
        transceiverDdmInfoEntryWarnMax OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"Serial number provided by transceiver module vendor."
        ::= { transceiverDdmInfoEntry 5 }

        transceiverDdmInfoEntryWarnMin OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"Revision level for part number provided by transceiver module vendor."
        ::= { transceiverDdmInfoEntry 6 }

        transceiverDdmInfoEntryCurrent OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"Transceiver module vendor's manufacturing date code"
        ::= { transceiverDdmInfoEntry 7 }
        
        transceiverDdmInfoEntryDescription OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-only
        STATUS  current
        DESCRIPTION
                      	"Transceiver module type names"
        ::= { transceiverDdmInfoEntry 8 }    


--  118. dot3OamSetup
	                            
        dot3OamState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { dot3OamSetup 1 }


--  119. dot1agCfmMib 
	
		dot1agCfmMIBObjects         OBJECT IDENTIFIER ::= { dot1agCfmSetup 1 }
	
	-- ******************************************************************
	-- Groups in the CFM MIB Module
	-- ******************************************************************
		dot1agCfmMep             OBJECT IDENTIFIER ::= { dot1agCfmMIBObjects 7 }
	
	-- ******************************************************************
	-- The MEP Table
	-- ******************************************************************   	
	       	
		zyswdot1agCfmMepTable OBJECT-TYPE
	    SYNTAX      SEQUENCE OF Zyswdot1agCfmMepEntry
	    ACCESS  not-accessible
	    STATUS      mandatory
	    DESCRIPTION
	       ""
	    ::= { dot1agCfmMep 1 }        
	    
	   	zyswdot1agCfmMepEntry OBJECT-TYPE
	    SYNTAX      Zyswdot1agCfmMepEntry
	    ACCESS  not-accessible
	    STATUS      mandatory
	    DESCRIPTION
	       "The dot1agCfmMep table entry"
	    INDEX { dot1agCfmMdIndex,
	            dot1agCfmMaIndex,
	            dot1agCfmMepIdentifier
	          }
	    ::= { zyswdot1agCfmMepTable 1 }
    
		Zyswdot1agCfmMepEntry ::= SEQUENCE {
	      zyswdot1agCfmMepTransmitLbmDataTlvSize           Unsigned32	      
	    }
       	    
		zyswdot1agCfmMepTransmitLbmDataTlvSize OBJECT-TYPE
	    SYNTAX      Unsigned32 (0..1500)
	    ACCESS  	read-write
	    STATUS      mandatory
	    DESCRIPTION
	       "Size of data to be included in the LBM Data TLV"
	    REFERENCE
	       ""
	    ::= { zyswdot1agCfmMepEntry 1 }  

-- ******************************************************************
-- *
-- 124.memory usage MIB
-- *
-- ******************************************************************  

        sysMemoryPoolTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF SysMemoryPoolEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
        ""              
    	::= { sysMemoryPool 1 }
    	    
    	sysMemoryPoolEntry OBJECT-TYPE
        SYNTAX      SysMemoryPoolEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A system memory pool entry"
        INDEX {
            sysMemoryPoolId                 
        }
    	::= { sysMemoryPoolTable 1 }
    
    	SysMemoryPoolEntry ::=
        SEQUENCE {
            sysMemoryPoolId       Unsigned32, 
            sysMemoryPoolName     OCTET STRING,
            sysMemoryPoolTotal    Unsigned32, 
            sysMemoryPoolUsed     Unsigned32,
            sysMemoryPoolUtil     Unsigned32
        } 
    
    	sysMemoryPoolId OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
         "The memory pool id "
    	::= { sysMemoryPoolEntry 1 }
    
    	sysMemoryPoolName OBJECT-TYPE
        SYNTAX      OCTET STRING(SIZE (0..32))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
         "Name of the memory pool "
    	::= { sysMemoryPoolEntry 2 }
        
    	sysMemoryPoolTotal OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "Total size of memory pool in bytes "
    	::= { sysMemoryPoolEntry 3 }
    
   		sysMemoryPoolUsed OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "Used size of memory pool in bytes "
    	::= { sysMemoryPoolEntry 4 }
    
    	sysMemoryPoolUtil OBJECT-TYPE
        SYNTAX      Unsigned32 (0..100)
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "Utilization of memory pool in bytes "
    	::= { sysMemoryPoolEntry 5 }
		
-- ******************************************************************
-- *
-- 125. PPPoE IA
-- *
-- ******************************************************************  

	  	pppoeIaSetup	OBJECT IDENTIFIER ::= { pppoe 1 }
 
        pppoeIaState	OBJECT-TYPE
		SYNTAX	EnabledStatus
		ACCESS	read-write
		STATUS	mandatory
		DESCRIPTION
	    	          ""
		::= { pppoeIaSetup 1 }
        
        pppoeIaAccessNodeIdentifierString	OBJECT-TYPE
        SYNTAX	DisplayString
        ACCESS	read-write
        STATUS	mandatory
        DESCRIPTION
                      	""
        ::= { pppoeIaSetup 2 }
    
        pppoeIaFlexibleCircuitIDSyntaxActive OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { pppoeIaSetup 3 }

        pppoeIaFlexibleCircuitIDSyntaxIdentifierString OBJECT-TYPE
        SYNTAX	DisplayString
        ACCESS	read-write
        STATUS	mandatory
        DESCRIPTION
                       	""
        ::= { pppoeIaSetup 4 }    
                                     
        pppoeIaFlexibleCircuitIDSyntaxOption OBJECT-TYPE
        SYNTAX  INTEGER {
            sp(1),
        	sv(2),
        	pv(3),
        	spv(4)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { pppoeIaSetup 5 }

        pppoeIaFlexibleCircuitIDSyntaxDelimiter OBJECT-TYPE
        SYNTAX  INTEGER {
        	pound-sign(1),
        	dot(2),
        	comma(3),
        	semicolon(4),
        	slash(5),
        	space(6)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { pppoeIaSetup 6 }                    

--
--  	pppoe ia interface table
--

        pppoeIaPortTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF PppoeIaPortEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { pppoeIaSetup 7 }

		pppoeIaPortEntry OBJECT-TYPE
        SYNTAX	PppoeIaPortEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	""
        INDEX          	{ dot1dBasePort }
        ::= { pppoeIaPortTable 1 }

        PppoeIaPortEntry ::=
		SEQUENCE {
        	pppoeIaPortEntryPort				INTEGER,
        	pppoeIaPortEntryTrust				EnabledStatus, 
        	pppoeIaPortEntryCircuitIDString		DisplayString,
        	pppoeIaPortEntryRemoteIDString		DisplayString
        }

        pppoeIaPortEntryPort OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { pppoeIaPortEntry 1 }

        pppoeIaPortEntryTrust OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { pppoeIaPortEntry 2 }        
                
        pppoeIaPortEntryCircuitIDString OBJECT-TYPE
        SYNTAX	DisplayString
        ACCESS	read-write
        STATUS	mandatory
        DESCRIPTION
                       	""
        ::= { pppoeIaPortEntry 3 }

        pppoeIaPortEntryRemoteIDString OBJECT-TYPE
        SYNTAX	DisplayString
        ACCESS	read-write
        STATUS	mandatory
        DESCRIPTION
                       	""
        ::= { pppoeIaPortEntry 4 }

           
--
--  	pppoe ia vlan table
--
        pppoeIaVlanTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF PppoeIaVlanEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { pppoeIaSetup 8 }

		pppoeIaVlanEntry OBJECT-TYPE
        SYNTAX	PppoeIaVlanEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	""
        INDEX          	{ pppoeIaVlanEntryVid }
        ::= { pppoeIaVlanTable 1 }

        PppoeIaVlanEntry ::=
		SEQUENCE {
        	pppoeIaVlanEntryVid				INTEGER,
        	pppoeIaVlanEntryCircuitID		EnabledStatus,
        	pppoeIaVlanEntryRemoteID		EnabledStatus,
        	pppoeIaVlanEntryRowStatus		RowStatus
        }

        pppoeIaVlanEntryVid	OBJECT-TYPE
        SYNTAX  INTEGER (1..4094)
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { pppoeIaVlanEntry 1 }
        
        pppoeIaVlanEntryCircuitID	OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { pppoeIaVlanEntry 2 }
        
        pppoeIaVlanEntryRemoteID	OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { pppoeIaVlanEntry 3 }
        
        pppoeIaVlanEntryRowStatus	OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-create
        STATUS  mandatory
        DESCRIPTION 
						""
        ::= { pppoeIaVlanEntry 4 }

--
--  	pppoe ia port and vlan table
--
        pppoeIaPortVlanTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF PppoeIaPortVlanEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { pppoeIaSetup 9 }
        
		pppoeIaPortVlanEntry OBJECT-TYPE
        SYNTAX	PppoeIaPortVlanEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in pppoe ia port & vlan table"
        INDEX          	{ pppoeIaPortVlanEntryPort, pppoeIaPortVlanEntryVid }
        ::= { pppoeIaPortVlanTable 1 }

        PppoeIaPortVlanEntry ::=
           SEQUENCE {
        	pppoeIaPortVlanEntryPort				INTEGER,
        	pppoeIaPortVlanEntryVid					INTEGER,
        	pppoeIaPortVlanEntryCircuitIDString		DisplayString,   
        	pppoeIaPortVlanEntryRemoteIDString		DisplayString,
         	pppoeIaPortVlanEntryRowStatus			RowStatus
           }
        pppoeIaPortVlanEntryPort OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                     	"interface port id"
        ::= { pppoeIaPortVlanEntry 1 }

        pppoeIaPortVlanEntryVid OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                     	"vid"
        ::= { pppoeIaPortVlanEntry 2 }               
               
        pppoeIaPortVlanEntryCircuitIDString OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"The circuit-id string of pppoe ia port & vlan entry"
        ::= { pppoeIaPortVlanEntry 3 }
   
       pppoeIaPortVlanEntryRemoteIDString OBJECT-TYPE
        SYNTAX  DisplayString
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"The remote-id string of pppoe ia port & vlan entry"
        ::= { pppoeIaPortVlanEntry 4 }
   
        pppoeIaPortVlanEntryRowStatus OBJECT-TYPE
        SYNTAX  RowStatus
        ACCESS  read-create
        STATUS  mandatory
        DESCRIPTION
                     	""
        ::= { pppoeIaPortVlanEntry 5 }
		
		
-- ******************************************************************
-- *
-- 126.arpSetup
-- *
-- ******************************************************************  
		
        arpLearningPortTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF ArpLearningPortEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { arpSetup 1 }
        
		arpLearningPortEntry OBJECT-TYPE
        SYNTAX	ArpLearningPortEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in arpLearningPortTable.
        				 Modes: arp-reply(0), gratuitous-arp(1), arp-request(2)"
        INDEX          	{ dot1dBasePort }
        ::= { arpLearningPortTable 1 }

        ArpLearningPortEntry ::=
           SEQUENCE {
        	arpLearningPortMode		INTEGER
           }
                  
        arpLearningPortMode OBJECT-TYPE
        SYNTAX  INTEGER {
        		arp-reply(0),
        		gratuitous-arp(1),
        		arp-request(2)
       	}
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"ArpLearning Mode on the port."
        ::= { arpLearningPortEntry 1 }
		
	arpAgingTime OBJECT-TYPE
        SYNTAX	INTEGER
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                 	"Set or read ARP Aging Time value"
        ::= { arpSetup 2 }
		
                
-- ******************************************************************
-- *
-- 130. errdisable
-- *
-- ****************************************************************** 

	recovery             OBJECT IDENTIFIER ::= { errdisable 1 }

	errdisableRecoverySetup  OBJECT IDENTIFIER ::= { recovery 1 }

        errdisableRecoveryState OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      ""
        ::= { errdisableRecoverySetup 1 }

	--errdisableRecoveryReasonTable
	errdisableRecoveryReasonTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF ErrdisableRecoveryReasonEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { errdisableRecoverySetup 2 }
        
	errdisableRecoveryReasonEntry OBJECT-TYPE
        SYNTAX	ErrdisableRecoveryReasonEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	""
        INDEX          	{ errdisableRecoveryReason }
        ::= { errdisableRecoveryReasonTable 1 }

        ErrdisableRecoveryReasonEntry ::=
           SEQUENCE {
        	errdisableRecoveryReason	INTEGER,
        	errdisableRecoveryReasonActive	EnabledStatus,
			errdisableRecoveryReasonInterval	INTEGER       	
           }

        errdisableRecoveryReason OBJECT-TYPE
        SYNTAX  INTEGER {
        		loopguard(0), 
        		arp(1),
        		bpdu(2),
				igmp(3)
        }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { errdisableRecoveryReasonEntry 1 }

        errdisableRecoveryReasonActive OBJECT-TYPE
        SYNTAX  INTEGER {
        		enabled(1),
        		disabled(2),
        }
        		
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { errdisableRecoveryReasonEntry 2 }

        errdisableRecoveryReasonInterval OBJECT-TYPE
        SYNTAX  INTEGER (30..2592000)
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { errdisableRecoveryReasonEntry 3 }

	--errdisableRecoveryIfStatusTable
	errdisableRecoveryIfStatusTable OBJECT-TYPE
        SYNTAX	SEQUENCE OF ErrdisableRecoveryIfStatusEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { errdisableRecoverySetup 3 }
        
	errdisableRecoveryIfStatusEntry OBJECT-TYPE
        SYNTAX	ErrdisableRecoveryIfStatusEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	""
        INDEX          	{ errdisableRecoveryIfStatusReason,errdisableRecoveryIfStatusPort }
        ::= { errdisableRecoveryIfStatusTable 1 }

        ErrdisableRecoveryIfStatusEntry ::=
           SEQUENCE {
        	errdisableRecoveryIfStatusReason	INTEGER,
        	errdisableRecoveryIfStatusPort	    INTEGER,
			errdisableRecoveryIfStatusTimeToRecover	INTEGER       	
           }

        errdisableRecoveryIfStatusReason OBJECT-TYPE
        SYNTAX  INTEGER {
        	loopguard(0),    
        	arp(1),
        	bpdu(2),
			igmp(3)
        }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { errdisableRecoveryIfStatusEntry 1 }

        errdisableRecoveryIfStatusPort OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { errdisableRecoveryIfStatusEntry 2 }

        errdisableRecoveryIfStatusTimeToRecover OBJECT-TYPE
        SYNTAX  INTEGER (30..2592000)
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { errdisableRecoveryIfStatusEntry 3 }              

	detect             OBJECT IDENTIFIER ::= { errdisable 2 }       
	                                        

	    errdisableDetectReasonTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF  ErrdisableDetectReasonEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { detect 1 }  	 
        
       	errdisableDetectReasonEntry OBJECT-TYPE
        SYNTAX	ErrdisableDetectReasonEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in errdisableDetectReasonTable."
        INDEX          	{ errdisableDetectReason }
        ::= { errdisableDetectReasonTable 1 }          
        
        ErrdisableDetectReasonEntry ::=
	SEQUENCE {
        	errdisableDetectReason    INTEGER,
        	errdisableDetectReasonEnable	EnabledStatus,
        	errdisableDetectReasonMode	INTEGER
         }        
         

        errdisableDetectReason OBJECT-TYPE
        SYNTAX  INTEGER{
        	arp(1),
        	bpdu(2),
			igmp(3)
       	}
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { errdisableDetectReasonEntry 1 }
                                                    
                                                    

        errdisableDetectReasonEnable OBJECT-TYPE
        SYNTAX  EnabledStatus
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { errdisableDetectReasonEntry 2 }
                                                

        errdisableDetectReasonMode OBJECT-TYPE
        SYNTAX  INTEGER{
        	inactive-port(1),
        	inactive-reason(2),
			rate-limitation(3)
        }
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	""
        ::= { errdisableDetectReasonEntry 3 }
                                                                    
	errdisableTrapInfoObject  OBJECT IDENTIFIER ::= { errdisable 3 }    

    errdisableTrapPort OBJECT-TYPE
        SYNTAX  INTEGER
        ACCESS   read-only
        STATUS  mandatory
        DESCRIPTION
                      ""             
        ::= { errdisableTrapInfoObject 1 }     

    errdisableTrapReason OBJECT-TYPE
        SYNTAX  INTEGER
        {
        	loopguard(0),
			arp(1),
			bpdu(2),
			igmp(3)
        }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      ""             
        ::= { errdisableTrapInfoObject 2 }     
        

	errdisableTrapMode  OBJECT-TYPE
        SYNTAX  INTEGER
        {
        	inactive-port(0),
        	inactive-reason(1),
        	rate-limitation(2)
        }
        ACCESS   read-only
        STATUS  mandatory
        DESCRIPTION
                      ""             
        ::= { errdisableTrapInfoObject 3 }        
        

	errdisableTrapNotifications  OBJECT IDENTIFIER ::= { errdisable 4 }         
	   
	
	     errdisableDetectTrap NOTIFICATION-TYPE
         OBJECTS {
               errdisableTrapPort,
               errdisableTrapReason,
               errdisableTrapMode
         }
         STATUS  current
         DESCRIPTION
          ""
		   
		  ::= { errdisableTrapNotifications 1 }
      
         errdisableRecoveryTrap NOTIFICATION-TYPE
         OBJECTS {
               errdisableTrapPort,
               errdisableTrapReason,
               errdisableTrapMode
         }
         STATUS  current
         DESCRIPTION
          ""
		   ::= { errdisableTrapNotifications 2 }
	                                                                  

         errdisableDetectModeChangeTrap NOTIFICATION-TYPE
         OBJECTS {
               errdisableTrapPort,
               errdisableTrapReason,
               errdisableTrapMode
         }
         STATUS  current
         DESCRIPTION
          ""
		   ::= { errdisableTrapNotifications 3 }
	                                                                  


-- ******************************************************************
-- *
-- 131. CPU protection
-- *
-- ****************************************************************** 

     cpuProtectionTable	OBJECT-TYPE
        SYNTAX	SEQUENCE OF CPUProtectionEntry
        ACCESS  not-accessible
        STATUS  mandatory
        DESCRIPTION
                 	""
        ::= { cpuProtectionSetup 1 }   
        
		cpuProtectionEntry OBJECT-TYPE
        SYNTAX	CPUProtectionEntry
        ACCESS	not-accessible
        STATUS	mandatory
        DESCRIPTION    	"An entry in CPUProtectionTable."
        INDEX          	{ cpuProtectionPort, cpuProtectionReason}
        ::= { cpuProtectionTable 1 }

        CPUProtectionEntry ::=
	SEQUENCE {
        	cpuProtectionPort          INTEGER,
        	cpuProtectionReason    	INTEGER,
        	cpuProtectionRateLimitSet 	INTEGER
          }
                
        cpuProtectionPort OBJECT-TYPE
        SYNTAX  INTEGER           
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"The Protected Port"
        ::= { cpuProtectionEntry 1 }      
        
        
        cpuProtectionReason OBJECT-TYPE
        SYNTAX  INTEGER{
        arp(1),
        bpdu(2),
	    igmp(3)
        }
        ACCESS  read-only
        STATUS  mandatory
        DESCRIPTION
                      	"The Protected Reason"
        ::= { cpuProtectionEntry 2}    
        

        cpuProtectionRateLimitSet OBJECT-TYPE
        SYNTAX  INTEGER(0..256)
        ACCESS  read-write
        STATUS  mandatory
        DESCRIPTION
                      	"The rate limit of a reason on a port"
        ::= { cpuProtectionEntry 3} 
        
END
