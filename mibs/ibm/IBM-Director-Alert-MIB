IBM-Director-Alert-<PERSON>B DEFINITIONS ::= BEGIN

IMPORTS
   MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE,
   Integer32                                             FROM SNMPv2-SMI
   DisplayString, TruthValue                             FROM SNMPv2-TC
   MODULE-COMPLIANCE, OB<PERSON>ECT-G<PERSON><PERSON>, NOTIFICATION-GROUP   FROM SNMPv2-CONF
   SnmpAdminString                                       FROM SNMP-FRAMEWORK-MIB;

ibm OBJECT IDENTIFIER ::= { enterprises 2 }

ibmProd     OBJECT IDENTIFIER ::= { ibm 6 }

director    OBJECT IDENTIFIER ::= { ibmProd 159 }

description OBJECT IDENTIFIER ::= { director 202 }

details     OBJECT IDENTIFIER ::= { director 9696 }

directorTraps MODULE-IDENTITY
   LAST-UPDATED "200404210000Z"
   ORGANIZATION "IBM Corp."
   CONTACT-INFO
      "IBM Corporation"
   DESCRIPTION
      "This MIB is for use with the Event Action Plan Builder of
       IBM Director."
   REVISION     "200404210000Z"
   DESCRIPTION
      "This MIB was revised to include traps for more possible
      events generated by the IBM Director event subsystem."
   REVISION     "200307090000Z"
   DESCRIPTION
      "The initial version of this MIB."
   ::= { director 201 }

--
-- Generic trap for events
--
generalEvent NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory }
   STATUS  current
   DESCRIPTION
      "This is a IBM Director event converted into an SNMP trap.
       This trap is only used for events with no mapping."
::= { directorTraps 2 }

--
-- Topology traps
--

directorFamily OBJECT IDENTIFIER ::= { directorTraps 1 }

topology OBJECT IDENTIFIER ::= { directorFamily 1 }

online NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory }
   STATUS  current
   DESCRIPTION
      "The managed object's state has changed to online."
::= { topology 1 }

offline NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory }
   STATUS  current
   DESCRIPTION
      "The managed object's state has changed to offline."
::= { topology 2 }

create NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory }
   STATUS  current
   DESCRIPTION
      "The managed object has been created."
::= { topology 3 }

change NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory }
   STATUS  current
   DESCRIPTION
      "The managed object has changed."
::= { topology 4 }

destroy NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory }
   STATUS  current
   DESCRIPTION
      "The managed object has been destroyed."
::= { topology 5 }

--
-- Director Agent traps
--

directorAgent  OBJECT IDENTIFIER ::= { directorFamily 2 }

processMonitor NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             actualValue, duration, monitorResource, thresholdName, thresholdValue }
   STATUS  current
   DESCRIPTION
      "The process monitor threshold has generated
      an event."
::= { directorAgent 1 }

cpuMonitors  OBJECT IDENTIFIER ::= { directorAgent 2 }

cpuUtilization NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             actualValue, duration, monitorResource, thresholdName, thresholdValue }
   STATUS  current
   DESCRIPTION
      "The CPU utilization monitor threshold has generated
      an event."
::= { cpuMonitors 1 }

processCount NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             actualValue, duration, monitorResource, thresholdName, thresholdValue }
   STATUS  current
   DESCRIPTION
      "The process count monitor threshold has generated
      an event."
::= { cpuMonitors 2 }

diskMonitors  OBJECT IDENTIFIER ::= { directorAgent 3 }

driveSpaceUsed NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             actualValue, duration, monitorResource, thresholdName, thresholdValue }
   STATUS  current
   DESCRIPTION
      "The drive space used monitor threshold has generated
      an event."
::= { diskMonitors 1 }

driveSpaceUsedPercent NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             actualValue, duration, monitorResource, thresholdName, thresholdValue }
   STATUS  current
   DESCRIPTION
      "The percentage of drive space used monitor threshold has generated
      an event."
::= { diskMonitors 2 }

driveSpaceRemaining NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             actualValue, duration, monitorResource, thresholdName, thresholdValue }
   STATUS  current
   DESCRIPTION
      "The drive space remaining monitor threshold has generated
      an event."
::= { diskMonitors 3 }

driveWorkload NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             actualValue, duration, monitorResource, thresholdName, thresholdValue }
   STATUS  current
   DESCRIPTION
      "The drive workload monitor threshold has generated
      an event."
::= { diskMonitors 4 }

memoryMonitors  OBJECT IDENTIFIER ::= { directorAgent 4 }

lockedMemory NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             actualValue, duration, monitorResource, thresholdName, thresholdValue }
   STATUS  current
   DESCRIPTION
      "The locked memory monitor threshold has generated
      an event."
::= { memoryMonitors 1 }

memoryUsage NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             actualValue, duration, monitorResource, thresholdName, thresholdValue }
   STATUS  current
   DESCRIPTION
      "The memory usage monitor threshold has generated
      an event."
::= { memoryMonitors 2 }

nicMonitors  OBJECT IDENTIFIER ::= { directorAgent 5 }

-- Add nic monitors here

ntPerfMonitors  OBJECT IDENTIFIER ::= { directorAgent 6 }

totalPrivilegedTimePercent NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             actualValue, duration, monitorResource, thresholdName, thresholdValue }
   STATUS  current
   DESCRIPTION
      "The percentage of total privileged time monitor
      threshold has generated an event."
::= { ntPerfMonitors 1 }

fileReadOperationsPerSec NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             actualValue, duration, monitorResource, thresholdName, thresholdValue }
   STATUS  current
   DESCRIPTION
      "The files read per second monitor threshold has
      generated an event."
::= { ntPerfMonitors 2 }

-- thread NOTIFICATION-TYPE
--   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory }
--   STATUS  current
--   DESCRIPTION
--      ""
--::= { ntPerfMonitor 3 }

tcpipMonitors  OBJECT IDENTIFIER ::= { directorAgent 7 }

udpPacketsSentPerSec NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             actualValue, duration, monitorResource, thresholdName, thresholdValue }
   STATUS  current
   DESCRIPTION
      "A threshold event for the number of UDP packets
      sent per second."
::= { tcpipMonitors 1 }

udpPacketsReceivedPerSec NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             actualValue, duration, monitorResource, thresholdName, thresholdValue }
   STATUS  current
   DESCRIPTION
      "A threshold event for the number of UDP packets
      received per second."
::= { tcpipMonitors 2 }

ipPacketsSentPerSec NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             actualValue, duration, monitorResource, thresholdName, thresholdValue }
   STATUS  current
   DESCRIPTION
      "A threshold event for the number of IP packets
      sent per second."
::= { tcpipMonitors 3 }

ipPacketsReceivedPerSec NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             actualValue, duration, monitorResource, thresholdName, thresholdValue }
   STATUS  current
   DESCRIPTION
      "A threshold event for the number of IP packets
      received per second."
::= { tcpipMonitors 4 }

ipErrorPacketsReceivedPerSec NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             actualValue, duration, monitorResource, thresholdName, thresholdValue }
   STATUS  current
   DESCRIPTION
      "A threshold event for the number of IP packets
      received with errors per second."
::= { tcpipMonitors 5 }

tcpConnections NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             actualValue, duration, monitorResource, thresholdName, thresholdValue }
   STATUS  current
   DESCRIPTION
      "A threshold event for the number of TCP connections."
::= { tcpipMonitors 6 }

monitorEventDetails  OBJECT IDENTIFIER ::= { directorAgent 8 }

actualValue OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The actual value of the monitor."
   ::= { monitorEventDetails 1 }

duration OBJECT-TYPE
   SYNTAX      DisplayString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The duration of this monitor."
   ::= { monitorEventDetails 2 }

monitorResource OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The monitor resource."
   ::= { monitorEventDetails 3 }

thresholdName OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The name of the monitor threshold."
   ::= { monitorEventDetails 4 }

thresholdValue OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The value of the monitor threshold."
   ::= { monitorEventDetails 5 }


--
-- Test trap
--
test  OBJECT IDENTIFIER ::= { directorFamily 3 }

action NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory }
   STATUS  current
   DESCRIPTION
      "The test action event.  An internally generated event
      for the purpose of testing the action configuration."
::= { test 1 }

--
-- Console traps
--
console  OBJECT IDENTIFIER ::= { directorFamily 4 }

consoleEventDetails  OBJECT IDENTIFIER ::= { console 1 }

userID OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The console user ID."
   ::= { consoleEventDetails 1 }

address OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The console address."
   ::= { consoleEventDetails 2 }

userName OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The console username."
   ::= { consoleEventDetails 3 }

userDescription OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The console username's description."
   ::= { consoleEventDetails 4 }

userLocale OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The console user's locale."
   ::= { consoleEventDetails 5 }

logonFailure  OBJECT IDENTIFIER ::= { console 2 }

badPassword NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             userID, address }
   STATUS  current
   DESCRIPTION
      "When attempting to logon to the console,
      a bad password was used."
::= { logonFailure 1 }

badUserID NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             userID, address }
   STATUS  current
   DESCRIPTION
      "When attempting to logon to the console,
      a bad user ID was used."
::= { logonFailure 2 }

disabledUserID NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             userID, address }
   STATUS  current
   DESCRIPTION
      "When attempting to logon to the console,
      a disabled user ID was used."
::= { logonFailure 3 }

downlevelConsole NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             userID, address }
   STATUS  current
   DESCRIPTION
      "When attempting to logon to the console,
      a downlevel console was used."
::= { logonFailure 4 }

expiredPassword NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             userID, address }
   STATUS  current
   DESCRIPTION
      "When attempting to logon to the console,
      an expired password was used."
::= { logonFailure 5 }

tooManyActiveIDs NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             userID, address }
   STATUS  current
   DESCRIPTION
      "When attempting to logon to the console,
      too many IDs were active."
::= { logonFailure 6 }

tooManyActiveLogons NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             userID, address }
   STATUS  current
   DESCRIPTION
      "When attempting to logon to the console,
      too many logons were active."
::= { logonFailure 7 }

uplevelConsole NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             userID, address }
   STATUS  current
   DESCRIPTION
      "When attempting to logon to the console,
      an uplevel console was used."
::= { logonFailure 8 }

logoff NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             userID, address, userName, userDescription, userLocale }
   STATUS  current
   DESCRIPTION
      "A user has logged off the console."
::= { console 3 }

logon NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             userID, address, userName, userDescription, userLocale }
   STATUS  current
   DESCRIPTION
      "A user has logged on the console."
::= { console 4 }

--
-- CIM traps
--
cimFamily  OBJECT IDENTIFIER ::= { directorTraps 3 }

windowsNTEventLog  OBJECT IDENTIFIER ::= { cimFamily 1 }

windowsNTEventLogEventDetails  OBJECT IDENTIFIER ::= { windowsNTEventLog 1 }

category OBJECT-TYPE
   SYNTAX      Integer32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The Windows Event Log category."
   ::= { windowsNTEventLogEventDetails 1 }

categoryString OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The Windows Event Log category string."
   ::= { windowsNTEventLogEventDetails 2 }

computerName OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The computer name of the Windows Event Log event."
   ::= { windowsNTEventLogEventDetails 3 }

data OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The Windows Event Log data for the event."
   ::= { windowsNTEventLogEventDetails 4 }

eventLogCode OBJECT-TYPE
   SYNTAX      Integer32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The Windows Event Log numeric event ID."
   ::= { windowsNTEventLogEventDetails 5 }

eventLogIdentifier OBJECT-TYPE
   SYNTAX      DisplayString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The Windows Event Log numeric event identifier."
   ::= { windowsNTEventLogEventDetails 6 }

eventLogType OBJECT-TYPE
   SYNTAX      Integer32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The Windows Event Log numeric event type."
   ::= { windowsNTEventLogEventDetails 7 }

insertionStrings OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The Windows Event Log insertion strings."
   ::= { windowsNTEventLogEventDetails 8 }

logFile OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The Windows Event Log file name."
   ::= { windowsNTEventLogEventDetails 9 }

message OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The Windows Event Log message string."
   ::= { windowsNTEventLogEventDetails 10 }

recordNumber OBJECT-TYPE
   SYNTAX      DisplayString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The Windows Event Log record number."
   ::= { windowsNTEventLogEventDetails 11 }

sourceName OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The Windows Event Log source name."
   ::= { windowsNTEventLogEventDetails 12 }

type OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The Windows Event Log type."
   ::= { windowsNTEventLogEventDetails 13 }

user OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The Windows Event Log user."
   ::= { windowsNTEventLogEventDetails 14 }

applicationLog NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             category, categoryString, computerName, data, eventLogCode, eventLogIdentifier, eventLogType,
             insertionStrings, logFile, message, recordNumber, sourceName, type, user }
   STATUS  current
   DESCRIPTION
      "The Windows NT application event log has changed."
::= { windowsNTEventLog 2 }

securityLog NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             category, categoryString, computerName, data, eventLogCode, eventLogIdentifier, eventLogType,
             insertionStrings, logFile, message, recordNumber, sourceName, type, user }
   STATUS  current
   DESCRIPTION
      "The Windows NT application security log has changed."
::= { windowsNTEventLog 3 }

systemLog NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory }
   STATUS  current
   DESCRIPTION
      "The Windows NT application system log has changed."
::= { windowsNTEventLog 4 }

windowsNTService  OBJECT IDENTIFIER ::= { cimFamily 2 }

windowsNTServiceEventDetails  OBJECT IDENTIFIER ::= { windowsNTService 1 }

acceptPause OBJECT-TYPE
   SYNTAX      TruthValue
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Whether this service accepts the pause state change."
   ::= { windowsNTServiceEventDetails 1 }

acceptStop OBJECT-TYPE
   SYNTAX      TruthValue
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Whether this service accepts the stop state change."
   ::= { windowsNTServiceEventDetails 2 }

caption OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The caption string for this service."
   ::= { windowsNTServiceEventDetails 3 }

checkPoint OBJECT-TYPE
   SYNTAX      DisplayString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The service's check point."
   ::= { windowsNTServiceEventDetails 4 }

creationClassName OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The creation class name for the service."
   ::= { windowsNTServiceEventDetails 5 }

serviceDescription OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The description of the service."
   ::= { windowsNTServiceEventDetails 6 }

desktopInteract OBJECT-TYPE
   SYNTAX      TruthValue
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Whether the service interacts with the desktop."
   ::= { windowsNTServiceEventDetails 7 }

displayName OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The displayable name for the service."
   ::= { windowsNTServiceEventDetails 8 }

errorControl OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The error control value for the service."
   ::= { windowsNTServiceEventDetails 9 }

exitCode OBJECT-TYPE
   SYNTAX      DisplayString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The exit code of the process for the service."
   ::= { windowsNTServiceEventDetails 10 }

name OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The name of the service."
   ::= { windowsNTServiceEventDetails 11 }

pathName OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The path name for the service."
   ::= { windowsNTServiceEventDetails 12 }

processId OBJECT-TYPE
   SYNTAX      DisplayString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The process ID of the running service."
   ::= { windowsNTServiceEventDetails 13 }

serviceSpecificExitCode OBJECT-TYPE
   SYNTAX      DisplayString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The specific exit code for the service."
   ::= { windowsNTServiceEventDetails 14 }

serviceType OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The type of service."
   ::= { windowsNTServiceEventDetails 15 }

startMode OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The start mode for the service."
   ::= { windowsNTServiceEventDetails 16 }

startName OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The start name for the service."
   ::= { windowsNTServiceEventDetails 17 }

started OBJECT-TYPE
   SYNTAX      TruthValue
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Whether this service has started."
   ::= { windowsNTServiceEventDetails 18 }

serviceState OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The state of the service."
   ::= { windowsNTServiceEventDetails 19 }

status OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The status of the service."
   ::= { windowsNTServiceEventDetails 20 }

systemCreationClassName OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The system creation class name for the service."
   ::= { windowsNTServiceEventDetails 21 }

systemName OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The system name for the service."
   ::= { windowsNTServiceEventDetails 22 }

tagId OBJECT-TYPE
   SYNTAX      DisplayString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The tag ID of the service."
   ::= { windowsNTServiceEventDetails 23 }

waitHint OBJECT-TYPE
   SYNTAX      DisplayString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The value of the wait hint for the service."
   ::= { windowsNTServiceEventDetails 24 }

startedService NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             acceptPause, acceptStop, caption, checkPoint, creationClassName, serviceDescription,
             desktopInteract, displayName, errorControl, exitCode, name, pathName, processId,
             serviceSpecificExitCode, serviceType, startMode, startName, started, serviceState,
             status, systemCreationClassName, systemName, tagId, waitHint }
   STATUS  current
   DESCRIPTION
      "A Windows NT service has started."
::= { windowsNTService 2 }

stoppedService NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             acceptPause, acceptStop, caption, checkPoint, creationClassName, serviceDescription,
             desktopInteract, displayName, errorControl, exitCode, name, pathName, processId,
             serviceSpecificExitCode, serviceType, startMode, startName, started, serviceState,
             status, systemCreationClassName, systemName, tagId, waitHint }
   STATUS  current
   DESCRIPTION
      "A Windows NT service has stopped."
::= { windowsNTService 3 }

windowsRegistry  OBJECT IDENTIFIER ::= { cimFamily 3 }

windowsRegistryEventDetails  OBJECT IDENTIFIER ::= { windowsRegistry 1 }

hive OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The Windows Registry hive for this event."
   ::= { windowsRegistryEventDetails 1 }

rootPath OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The Windows Registry root path for this event."
   ::= { windowsRegistryEventDetails 2 }

securityDescriptor OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The Windows Registry security descriptor for this event."
   ::= { windowsRegistryEventDetails 3 }

timeCreated OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The time this item was created in the Windows Registry."
   ::= { windowsRegistryEventDetails 4 }

softwareTreeChanged NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             hive, rootPath, securityDescriptor, timeCreated }
   STATUS  current
   DESCRIPTION
      "The Windows software tree section of the registry
      has changed."
::= { windowsRegistry 2 }

systemTreeChanged NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             hive, rootPath, securityDescriptor, timeCreated }
   STATUS  current
   DESCRIPTION
      "The Windows system tree section of the registry
      has changed."
::= { windowsRegistry 3 }


--
-- MPA traps
--

mpaFamily  OBJECT IDENTIFIER ::= { directorTraps 4 }

mpaEventDetails  OBJECT IDENTIFIER ::= { mpaFamily 1 }

alertCode OBJECT-TYPE
   SYNTAX      Integer32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The alert code."
   ::= { mpaEventDetails 1 }

busId OBJECT-TYPE
   SYNTAX      Integer32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The bus ID."
   ::= { mpaEventDetails 2 }

componentId OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The component ID."
   ::= { mpaEventDetails 3 }

firmwareCode OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The firmware identification code."
   ::= { mpaEventDetails 4 }

ipAddress1 OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The first IP address."
   ::= { mpaEventDetails 5 }

ipAddress2 OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The second IP address."
   ::= { mpaEventDetails 6 }

issue OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The issue for the event."
   ::= { mpaEventDetails 7 }

powerDomain OBJECT-TYPE
   SYNTAX      Integer32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The domain of the power supply."
   ::= { mpaEventDetails 8 }

reason OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The reason for the event."
   ::= { mpaEventDetails 9 }

scsiId OBJECT-TYPE
   SYNTAX      Integer32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The ID of the SCSI device."
   ::= { mpaEventDetails 10 }

side OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The side in which the event occured."
   ::= { mpaEventDetails 11 }

newState OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The new state."
   ::= { mpaEventDetails 12 }

temperatureSensor OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The current temperature."
   ::= { mpaEventDetails 13 }

threshold OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The threshold for the event."
   ::= { mpaEventDetails 14 }

universalUniqueId OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The universal unique ID."
   ::= { mpaEventDetails 15 }

senderUniversalUniqueId OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The universal unique ID of the sender."
   ::= { mpaEventDetails 16 }

sourceUniversalUniqueId OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The universal unique ID of the source."
   ::= { mpaEventDetails 17 }

unitNumber OBJECT-TYPE
   SYNTAX      Integer32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The unit number."
   ::= { mpaEventDetails 18 }

voltageSensor OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The voltage sensor."
   ::= { mpaEventDetails 19 }

component  OBJECT IDENTIFIER ::= { mpaFamily 2 }

bladeServer  OBJECT IDENTIFIER ::= { component 1 }

coD  OBJECT IDENTIFIER ::= { bladeServer 1 }

codEnabled NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The CoD has been enabled."
::= { coD 1 }

bladeServerCommunication NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "Communication with a blade has been established."
::= { bladeServer 2 }

bladeServerInserted NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "A blade has been inserted."
::= { bladeServer 3 }

bladeServerRemoved NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "A blade has been removed."
::= { bladeServer 4 }

bus  OBJECT IDENTIFIER ::= { component 2 }

busCommunication NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             busId, firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "Communication with a bus has been established."
::= { bus 1 }

chassis  OBJECT IDENTIFIER ::= { component 3 }

chassisConfiguration NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             componentId, firmwareCode, issue, powerDomain, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "The blade chassis configuration has changed."
::= { chassis 1 }

chassisFailed NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, issue, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The blade chassis has failed."
::= { chassis 2 }

dasd  OBJECT IDENTIFIER ::= { component 4 }

dasdFailed NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, scsiId, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "a direct access storage device has failed."
::= { dasd 1 }

dasdInserted NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, scsiId, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "A direct access storage device has been inserted."
::= { dasd 2 }

dasdRemoved NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, scsiId, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "A direct access storage device has been removed."
::= { dasd 3 }

memory  OBJECT IDENTIFIER ::= { component 5 }

memoryFailed NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "Some memory has failed."
::= { memory 1 }

componentFan  OBJECT IDENTIFIER ::= { component 6 }

componentFanFailed NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "A fan has failed."
::= { componentFan 1 }

componentFanInserted NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "A fan has been inserted."
::= { componentFan 2 }

componentFanPfa NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "The fan component has generated a PFA."
::= { componentFan 3 }

componentFanRemoved NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "A fan has been removed."
::= { componentFan 4 }

hardwareInformation  OBJECT IDENTIFIER ::= { component 7 }

hardwareCrashDump  OBJECT IDENTIFIER ::= { hardwareInformation 1 }

hardwareCrashDumpAborted NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "A hardware crash dump was aborted."
::= { hardwareCrashDump 1 }

hardwareCrashDumpCompleted NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "A hardware crash dump has completed."
::= { hardwareCrashDump 2 }

hardwareCrashDumpInitiated NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "A hardware crash dump has been initiated."
::= { hardwareCrashDump 3 }

ioModule  OBJECT IDENTIFIER ::= { component 8 }

ioModuleConfiguration NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, ipAddress1, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "An I/O module's configuration has changed."
::= { ioModule 1 }

ioModuleFailed NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "An I/O module has failed."
::= { ioModule 2 }

ioModuleInserted NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "An I/O module has been inserted."
::= { ioModule 3 }

ioModulePost NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "An I/O module has past POST."
::= { ioModule 4 }

ioModulePower  OBJECT IDENTIFIER ::= { ioModule 5 }

ioModulePowerOn NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "An I/O module has been powered on."
::= { ioModulePower 1 }

ioModulePowerOff NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "An I/O module has been powered off."
::= { ioModulePower 2 }

ioModuleRedundancy NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "An I/O module is a redundancy."
::= { ioModule 6 }

ioModuleRemoved NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "An I/O module has been removed."
::= { ioModule 7 }

kvm  OBJECT IDENTIFIER ::= { component 9 }

kvmOwner NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The KVM owner has changed."
::= { kvm 1 }

osImage  OBJECT IDENTIFIER ::= { component 10 }

osImageCrashDump  OBJECT IDENTIFIER ::= { osImage 1 }

osImageCrashDumpAborted NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The operating system image crash dump was aborted."
::= { osImageCrashDump 1 }

osImageCrashDumpCompleted NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The operating system image crash dump has completed."
::= { osImageCrashDump 2 }

osImageCrashDumpInitiated NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The operating system image crash dump has been initiated."
::= { osImageCrashDump 3 }

componentPfa NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "A component has generated a PFA."
::= { component 11 }

powerSubsystem  OBJECT IDENTIFIER ::= { component 12 }

powerSubsystemLowFuel NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The power subsystem indicates low fuel."
::= { powerSubsystem 1 }

powerSubsystemOverCurrent NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The power subsystem indicates over current."
::= { powerSubsystem 2 }

powerSubsystemOverPower NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The power subsystem indicates over power."
::= { powerSubsystem 3 }

powerSubsystemRedundancy NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The power subsystem indicates a redundancy."
::= { powerSubsystem 4 }

powerSupply  OBJECT IDENTIFIER ::= { component 13 }

powerSupplyFailed NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, reason, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "A power supply has failed."
::= { powerSupply 1 }

powerSupplyInserted NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "A power supply has been inserted."
::= { powerSupply 2 }

powerSupplyRemoved NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "A power supply has been removed."
::= { powerSupply 3 }

server  OBJECT IDENTIFIER ::= { component 14 }

serverPower  OBJECT IDENTIFIER ::= { server 1 }

serverPowerOff NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The server has powered off."
::= { serverPower 1 }

serverPowerOn NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The server has powered on."
::= { serverPower 2 }

serverPowerState NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, newState, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The server's power state has changed."
::= { server 2 }

serviceProcessor  OBJECT IDENTIFIER ::= { component 15 }

serviceProcessorActive NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "A service processor is now active."
::= { serviceProcessor 1 }

serviceProcessorConfiguration NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "A service processor configuration has changed."
::= { serviceProcessor 2 }

serviceProcessorInserted NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "A service processor has been inserted."
::= { serviceProcessor 3 }

serviceProcessorLog NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "A service processor has generated a log."
::= { serviceProcessor 4 }

serviceProcessorNetworkStack NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, ipAddress1, ipAddress2, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "A service processor network stack has changed."
::= { serviceProcessor 5 }

serviceProcessorRedundancy NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "A service processor detected a redundancy."
::= { serviceProcessor 6 }

serviceProcessorRemoteLogin NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "A remove login was made into the service processor."
::= { serviceProcessor 7 }

serviceProcessorRemoved NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "A service processor has been removed."
::= { serviceProcessor 8 }

serviceProcessorRestart NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "A service processor has been restarted."
::= { serviceProcessor 9 }

serviceProcessorTest NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "A service processor test was issued."
::= { serviceProcessor 10 }

smpExpansionModule  OBJECT IDENTIFIER ::= { component 16 }

smpExpansionModuleDisabled NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "An SMP expansion module has been disabled."
::= { smpExpansionModule 1 }

usb  OBJECT IDENTIFIER ::= { component 17 }

usbInserted NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "A USB device has been inserted."
::= { usb 1 }

usbOwner NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "A USB device owner has changed."
::= { usb 2 }

usbRemoved NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "A USB device has been removed."
::= { usb 3 }

vrm  OBJECT IDENTIFIER ::= { component 18 }

vrmFailed NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber }
   STATUS  current
   DESCRIPTION
      "The VRM has failed."
::= { vrm 1 }

critical  OBJECT IDENTIFIER ::= { mpaFamily 3 }

hardDiskDrive NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A hard disk drive is in critical state."
::= { critical 1 }

multipleFanFailure NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             universalUniqueId }
   STATUS  current
   DESCRIPTION
      "Multiple fans have failed."
::= { critical 2 }

powerFailure  OBJECT IDENTIFIER ::= { critical 3 }

powerFailureEpow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             unitNumber, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "There was a critical power failure as a result of an EPOW."
::= { powerFailure 1 }

powerFailureFailed NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             unitNumber, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "There was a critical power failure as a result of something failing."
::= { powerFailure 2 }

powerFailureRemoved NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             unitNumber, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "There was a critical power failure as a result of something removed."
::= { powerFailure 3 }

criticalTamper NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             universalUniqueId }
   STATUS  current
   DESCRIPTION
      "Tampering with the system has caused a critical state."
::= { critical 4 }

criticalTemperature  OBJECT IDENTIFIER ::= { critical 5 }

criticalTemperatureAmbient NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "The ambient temperature is in a critical state."
::= { criticalTemperature 1 }

criticalTemperaturePci NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "The PCI temperature is in a critical state."
::= { criticalTemperature 2 }

criticalTemperaturePlanar NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "The planar temperature is in a critical state."
::= { criticalTemperature 3 }

criticalVoltage  OBJECT IDENTIFIER ::= { critical 6 }

criticalTwelveVolts  OBJECT IDENTIFIER ::= { criticalVoltage 1 }

criticalTwelveVoltsHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 12V high has been detected."
::= { criticalTwelveVolts 1 }

criticalTwelveVoltsLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 12V low has been detected."
::= { criticalTwelveVolts 2 }

criticalTwelveVoltsFaultA NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 12V fault A has been detected."
::= { criticalVoltage 2 }

criticalTwelveVoltsFaultB NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 12V fault B has been detected."
::= { criticalVoltage 3 }

criticalTwelveVoltsFaultC NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 12V fault C has been detected."
::= { criticalVoltage 4 }

criticalTwelveVoltsFaultD NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 12V fault D has been detected."
::= { criticalVoltage 5 }

criticalOneVolt  OBJECT IDENTIFIER ::= { criticalVoltage 6 }

criticalOneVoltHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 1V high has been detected."
::= { criticalOneVolt 1 }

criticalOneVoltLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 1V low has been detected."
::= { criticalOneVolt 2 }

criticalTwoVolts  OBJECT IDENTIFIER ::= { criticalVoltage 7 }

criticalTwoVoltsHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 2V high has been detected."
::= { criticalTwoVolts 1 }

criticalTwoVoltsLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 2V low has been detected."
::= { criticalTwoVolts 2 }

criticalThreeVolts  OBJECT IDENTIFIER ::= { criticalVoltage 8 }

criticalThreeVoltsHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 3V high has been detected."
::= { criticalThreeVolts 1 }

criticalThreeVoltsLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 3V low has been detected."
::= { criticalThreeVolts 2 }

criticalThreeVoltsPci  OBJECT IDENTIFIER ::= { criticalVoltage 9 }

criticalThreeVoltsPciHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 3V high has been detected for the PCI."
::= { criticalThreeVoltsPci 1 }

criticalThreeVoltsPciLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 3V low has been detected for the PCI."
::= { criticalThreeVoltsPci 2 }

criticalThreeVoltsStandby  OBJECT IDENTIFIER ::= { criticalVoltage 10 }

criticalThreeVoltsStandbyHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 3V standby high has been detected."
::= { criticalThreeVoltsStandby 1 }

criticalThreeVoltsStandbyLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 3V standby low has been detected."
::= { criticalThreeVoltsStandby 2 }

criticalFiveVolts  OBJECT IDENTIFIER ::= { criticalVoltage 11 }

criticalFiveVoltsHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 5V high has been detected."
::= { criticalFiveVolts 1 }

criticalFiveVoltsLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 5V low has been detected."
::= { criticalFiveVolts 2 }

criticalFiveVoltsFault  OBJECT IDENTIFIER ::= { criticalVoltage 12 }

criticalFiveVoltsFaultHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 5V fault high has been detected."
::= { criticalFiveVoltsFault 1 }

criticalFiveVoltsFaultLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 5V fault low has been detected."
::= { criticalFiveVoltsFault 2 }

criticalFiveVoltsPci  OBJECT IDENTIFIER ::= { criticalVoltage 13 }

criticalFiveVoltsPciHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 5V high has been detected for the PCI."
::= { criticalFiveVoltsPci 1 }

criticalFiveVoltsPciLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 5V low has been detected for the PCI."
::= { criticalFiveVoltsPci 2 }

criticalFiveVoltsStandby  OBJECT IDENTIFIER ::= { criticalVoltage 14 }

criticalFiveVoltsStandbyHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 5V standby high has been detected."
::= { criticalFiveVoltsStandby 1 }

criticalFiveVoltsStandbyLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of 5V standby low has been detected."
::= { criticalFiveVoltsStandby 2 }

criticalNTwelveVolts  OBJECT IDENTIFIER ::= { criticalVoltage 15 }

criticalNTwelveVoltsHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of negative 12V high has been detected."
::= { criticalNTwelveVolts 1 }

criticalNTwelveVoltsLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A critical state of negative 12V low has been detected."
::= { criticalNTwelveVolts 2 }

voltageRegulatorModuleFailure NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             universalUniqueId }
   STATUS  current
   DESCRIPTION
      "The voltage regulator module has failed."
::= { critical 7 }

deployment  OBJECT IDENTIFIER ::= { mpaFamily 4 }

deploymentBoot NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "Deployment has generated a boot event."
::= { deployment 1 }

deploymentLoader NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "Deployment has generated a loader event."
::= { deployment 2 }

deploymentOs NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "Deployment has generated an operating system event."
::= { deployment 3 }

deploymentPost NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "Deployment has generated a POST event."
::= { deployment 4 }

environmental  OBJECT IDENTIFIER ::= { mpaFamily 5 }

environmentalTemperature NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, side, sourceUniversalUniqueId, temperatureSensor,
             unitNumber }
   STATUS  current
   DESCRIPTION
      "The environmental temperature has changed."
::= { environmental 1 }

environmentalVoltage NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             componentId, firmwareCode, senderUniversalUniqueId, side, sourceUniversalUniqueId, threshold,
             voltageSensor }
   STATUS  current
   DESCRIPTION
      "The environmental voltage has changed."
::= { environmental 2 }

nonCritical  OBJECT IDENTIFIER ::= { mpaFamily 6 }

nonCriticalFan  OBJECT IDENTIFIER ::= { nonCritical 1 }

nonCriticalFanRemoved NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A fan has been removed."
::= { nonCriticalFan 1 }

redundantPower NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             universalUniqueId }
   STATUS  current
   DESCRIPTION
      "The system is now running on redundant power."
::= { nonCritical 2 }

singleFanFailure NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             unitNumber, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A single fan has failed."
::= { nonCritical 3 }

nonCriticalTemperature  OBJECT IDENTIFIER ::= { nonCritical 4 }

nonCriticalTemperatureAmbient NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "The ambient temperature is in a non critical state."
::= { nonCriticalTemperature 1 }

nonCriticalTemperaturePci NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "The PCI temperature is in a non critical state."
::= { nonCriticalTemperature 2 }

nonCriticalTemperaturePlanar NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "The planar temperature is in a non critical state."
::= { nonCriticalTemperature 3 }

nonCriticalVoltage  OBJECT IDENTIFIER ::= { nonCritical 5 }

nonCriticalTwelveVolts  OBJECT IDENTIFIER ::= { nonCriticalVoltage 1 }

nonCriticalTwelveVoltsHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A non-critical state of 12V high has been detected."
::= { nonCriticalTwelveVolts 1 }

nonCriticalTwelveVoltsLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A non-critical state of 12V low has been detected."
::= { nonCriticalTwelveVolts 2 }

nonCriticalOneVolt  OBJECT IDENTIFIER ::= { nonCriticalVoltage 2 }

nonCriticalOneVoltHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A non-critical state of 1V high has been detected."
::= { nonCriticalOneVolt 1 }

nonCriticalOneVoltLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A non-critical state of 1V low has been detected."
::= { nonCriticalOneVolt 2 }

nonCriticalTwoVolts  OBJECT IDENTIFIER ::= { nonCriticalVoltage 3 }

nonCriticalTwoVoltsHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A non-critical state of 2V high has been detected."
::= { nonCriticalTwoVolts 1 }

nonCriticalTwoVoltsLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A non-critical state of 2V low has been detected."
::= { nonCriticalTwoVolts 2 }

nonCriticalThreeVolts  OBJECT IDENTIFIER ::= { nonCriticalVoltage 4 }

nonCriticalThreeVoltsHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A non-critical state of 3V high has been detected."
::= { nonCriticalThreeVolts 1 }

nonCriticalThreeVoltsLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A non-critical state of 3V low has been detected."
::= { nonCriticalThreeVolts 2 }

nonCriticalThreeVoltsPci  OBJECT IDENTIFIER ::= { nonCriticalVoltage 5 }

nonCriticalThreeVoltsPciHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A non-critical state of 3V high has been detected for the PCI."
::= { nonCriticalThreeVoltsPci 1 }

nonCriticalThreeVoltsPciLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A non-critical state of 3V low has been detected for the PCI."
::= { nonCriticalThreeVoltsPci 2 }

nonCriticalThreeVoltsStandby  OBJECT IDENTIFIER ::= { nonCriticalVoltage 6 }

nonCriticalThreeVoltsStandbyHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A non-critical state of 3V standby high has been detected."
::= { nonCriticalThreeVoltsStandby 1 }

nonCriticalThreeVoltsStandbyLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A non-critical state of 3V standby low has been detected."
::= { nonCriticalThreeVoltsStandby 2 }

nonCriticalFiveVolts  OBJECT IDENTIFIER ::= { nonCriticalVoltage 7 }

nonCriticalFiveVoltsHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A non-critical state of 5V high has been detected."
::= { nonCriticalFiveVolts 1 }

nonCriticalFiveVoltsLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A non-critical state of 5V low has been detected."
::= { nonCriticalFiveVolts 2 }

nonCriticalFiveVoltsPci  OBJECT IDENTIFIER ::= { nonCriticalVoltage 8 }

nonCriticalFiveVoltsPciHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A non-critical state of 5V high has been detected for the PCI."
::= { nonCriticalFiveVoltsPci 1 }

nonCriticalFiveVoltsPciLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A non-critical state of 5V low has been detected for the PCI."
::= { nonCriticalFiveVoltsPci 2 }

nonCriticalFiveVoltsStandby  OBJECT IDENTIFIER ::= { nonCriticalVoltage 9 }

nonCriticalFiveVoltsStandbyHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A non-critical state of 5V standby high has been detected."
::= { nonCriticalFiveVoltsStandby 1 }

nonCriticalFiveVoltsStandbyLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A non-critical state of 5V standby low has been detected."
::= { nonCriticalFiveVoltsStandby 2 }

nonCriticalNTwelveVolts  OBJECT IDENTIFIER ::= { nonCriticalVoltage 10 }

nonCriticalNTwelveVoltsHigh NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A non-critical state of negative 12V high has been detected."
::= { nonCriticalNTwelveVolts 1 }

nonCriticalNTwelveVoltsLow NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             side, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "A non-critical state of negative 12V low has been detected."
::= { nonCriticalNTwelveVolts 2 }

platform  OBJECT IDENTIFIER ::= { mpaFamily 7 }

scalableNode  OBJECT IDENTIFIER ::= { platform 1 }

scalableNodeMode  OBJECT IDENTIFIER ::= { scalableNode 1 }

scalableNodeModeNullOrUnknown NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The scalable node is in a null or unknown mode."
::= { scalableNodeMode 1 }

scalableNodeModePrimary NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The scalable node is in primary mode."
::= { scalableNodeMode 2 }

scalableNodeModeSecondary NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The scalable node is in secondary mode."
::= { scalableNodeMode 3 }

scalableNodeModeStandalone NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The scalable node is now in standalone mode."
::= { scalableNodeMode 4 }

scalableNodeStandalone  OBJECT IDENTIFIER ::= { scalableNode 2 }

standaloneMode  OBJECT IDENTIFIER ::= { scalableNodeStandalone 1 }

primary NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The scalable node is now in standalone primary mode."
::= { standaloneMode 1 }

reset  OBJECT IDENTIFIER ::= { standaloneMode 2 }

resetPrimary NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The scalable node is now in standalone primary reset mode."
::= { reset 1 }

resetSecondary NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The scalable node is now in standalone primary secondary mode."
::= { reset 2 }

secondary NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The scalable node is now in standalone secondary mode."
::= { standaloneMode 3 }

scalablePartition  OBJECT IDENTIFIER ::= { platform 2 }

scalablePartitionAlert NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             alertCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The scalabel partition has generated an alert."
::= { scalablePartition 1 }

scalablePartitionState  OBJECT IDENTIFIER ::= { scalablePartition 2 }

scalablePartitionNullOrUnknown NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The scalable partition state is 'null or unknown'."
::= { scalablePartitionState 1 }

scalablePartitionPoweredOff NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The scalable partition state is 'powered off'."
::= { scalablePartitionState 2 }

scalablePartitionPoweringOn NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The scalable partition state is 'powering on'."
::= { scalablePartitionState 3 }

scalablePartitionResetting NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The scalable partition state is 'resetting'."
::= { scalablePartitionState 4 }

scalablePartitionShuttingDown NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The scalable partition state is 'shutting down'."
::= { scalablePartitionState 5 }

system  OBJECT IDENTIFIER ::= { mpaFamily 8 }

bootFailure NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             universalUniqueId }
   STATUS  current
   DESCRIPTION
      "The system indicated a boot failure."
::= { system 1 }

fuelGauge  OBJECT IDENTIFIER ::= { system 2 }

fuelGaugeLowFuel NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             universalUniqueId }
   STATUS  current
   DESCRIPTION
      "The fuel gauge indicates low fuel."
::= { fuelGauge 1 }

fuelGaugeNotRedundant NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             universalUniqueId }
   STATUS  current
   DESCRIPTION
      "The fuel gauge indicates not redundant."
::= { fuelGauge 2 }

fuelGaugeOverCurrent NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             universalUniqueId }
   STATUS  current
   DESCRIPTION
      "The fuel gauge indicates over current."
::= { fuelGauge 3 }

systemLoaderTimeout NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             universalUniqueId }
   STATUS  current
   DESCRIPTION
      "The system loader has timed out."
::= { system 3 }

systemOsTimeout NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             universalUniqueId }
   STATUS  current
   DESCRIPTION
      "The operating system has timed out."
::= { system 4 }

systemPfa  OBJECT IDENTIFIER ::= { system 5 }

fanSystemPfa NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             unitNumber, universalUniqueId }
   STATUS  current
   DESCRIPTION
      "The fan system has generated a PFA."
::= { systemPfa 1 }

systemPostTimeout NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             universalUniqueId }
   STATUS  current
   DESCRIPTION
      "The POST has timed out."
::= { system 6 }

systemPowerOff NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             universalUniqueId }
   STATUS  current
   DESCRIPTION
      "The sytsem has powered off."
::= { system 7 }

systemPowerOn NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             universalUniqueId }
   STATUS  current
   DESCRIPTION
      "The sytsem has powered on."
::= { system 8 }

systemRedundantPower NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             universalUniqueId }
   STATUS  current
   DESCRIPTION
      "The system is running on redundant power."
::= { system 9 }

systemTamper NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             universalUniqueId }
   STATUS  current
   DESCRIPTION
      "The system has been tampered with."
::= { system 10 }

unknown NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             firmwareCode, senderUniversalUniqueId, sourceUniversalUniqueId }
   STATUS  current
   DESCRIPTION
      "The system is in an unknown state."
::= { mpaFamily 9 }

--
-- PET/ASF traps
--

petFamily  OBJECT IDENTIFIER ::= { directorTraps 5 }

petFamilyEventDetails  OBJECT IDENTIFIER ::= { petFamily 1 }

allVariableBindings OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "All of the variable bindings for this PET trap."
   ::= { petFamilyEventDetails 1 }

entity OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The entity of this PET event."
   ::= { petFamilyEventDetails 2 }

entityInstance OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The entity instance of this PET event."
   ::= { petFamilyEventDetails 3 }

petEventData OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The event data of this PET event."
   ::= { petFamilyEventDetails 4 }

petEventSeverity OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The severity of this PET event."
   ::= { petFamilyEventDetails 5 }

petEventSourceType OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The source type of this PET event."
   ::= { petFamilyEventDetails 6 }

petEventType OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The type of the PET event."
   ::= { petFamilyEventDetails 7 }

guid OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The GUID for this PET event."
   ::= { petFamilyEventDetails 8 }

languageCode OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The language code for this PET event."
   ::= { petFamilyEventDetails 9 }

localTimeStamp OBJECT-TYPE
   SYNTAX      DisplayString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The local time stamp of this PET event."
   ::= { petFamilyEventDetails 10 }

manufacturerId OBJECT-TYPE
   SYNTAX      DisplayString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The manufacturer ID for the source of the PET event."
   ::= { petFamilyEventDetails 11 }

oemCustomField OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The value for the OEM customer field of this PET event."
   ::= { petFamilyEventDetails 12 }

offset OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The offset for this PET event."
   ::= { petFamilyEventDetails 13 }

sensorDevice OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The sensor device for this PET event."
   ::= { petFamilyEventDetails 14 }

sensorNumber OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The sensor number for this PET event."
   ::= { petFamilyEventDetails 15 }

sensorType OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The sensor type for this PET event."
   ::= { petFamilyEventDetails 16 }

sequenceId OBJECT-TYPE
   SYNTAX      Integer32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The sequence ID for this PET event."
   ::= { petFamilyEventDetails 17 }

systemId OBJECT-TYPE
   SYNTAX      Integer32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The system ID for this PET event."
   ::= { petFamilyEventDetails 18 }

trapSourceType OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The trap source type for this PET event."
   ::= { petFamilyEventDetails 19 }

utcOffset OBJECT-TYPE
   SYNTAX      Integer32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The UTC offset for this PET event."
   ::= { petFamilyEventDetails 20 }

petEnvironmental  OBJECT IDENTIFIER ::= { petFamily 2 }

petEnvironmentalSensor  OBJECT IDENTIFIER ::= { petEnvironmental 1 }

sensorCaseIntrusion NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             allVariableBindings, entity, entityInstance, petEventData, petEventSeverity, petEventSourceType,
             petEventType, guid, languageCode, localTimeStamp, manufacturerId, oemCustomField, offset,
             sensorDevice, sensorNumber, sensorType, sequenceId, systemId, trapSourceType, utcOffset }
   STATUS  current
   DESCRIPTION
      "A sensor has detected a case intrusion."
::= { petEnvironmentalSensor 1 }

sensorCurrent NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             allVariableBindings, entity, entityInstance, petEventData, petEventSeverity, petEventSourceType,
             petEventType, guid, languageCode, localTimeStamp, manufacturerId, oemCustomField, offset,
             sensorDevice, sensorNumber, sensorType, sequenceId, systemId, trapSourceType, utcOffset }
   STATUS  current
   DESCRIPTION
      "A sensor has detected a current change."
::= { petEnvironmentalSensor 2 }

sensorFan NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             allVariableBindings, entity, entityInstance, petEventData, petEventSeverity, petEventSourceType,
             petEventType, guid, languageCode, localTimeStamp, manufacturerId, oemCustomField, offset,
             sensorDevice, sensorNumber, sensorType, sequenceId, systemId, trapSourceType, utcOffset }
   STATUS  current
   DESCRIPTION
      "A sensor has detected a fan change."
::= { petEnvironmentalSensor 3 }

sensorPowerSupply NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             allVariableBindings, entity, entityInstance, petEventData, petEventSeverity, petEventSourceType,
             petEventType, guid, languageCode, localTimeStamp, manufacturerId, oemCustomField, offset,
             sensorDevice, sensorNumber, sensorType, sequenceId, systemId, trapSourceType, utcOffset }
   STATUS  current
   DESCRIPTION
      "A sensor has detected a power supply change."
::= { petEnvironmentalSensor 4 }

sensorTemperature NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             allVariableBindings, entity, entityInstance, petEventData, petEventSeverity, petEventSourceType,
             petEventType, guid, languageCode, localTimeStamp, manufacturerId, oemCustomField, offset,
             sensorDevice, sensorNumber, sensorType, sequenceId, systemId, trapSourceType, utcOffset }
   STATUS  current
   DESCRIPTION
      "A sensor has detected a temperature change."
::= { petEnvironmentalSensor 5 }

sensorVoltage NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             allVariableBindings, entity, entityInstance, petEventData, petEventSeverity, petEventSourceType,
             petEventType, guid, languageCode, localTimeStamp, manufacturerId, oemCustomField, offset,
             sensorDevice, sensorNumber, sensorType, sequenceId, systemId, trapSourceType, utcOffset }
   STATUS  current
   DESCRIPTION
      "A sensor has detected a voltage change."
::= { petEnvironmentalSensor 6 }

firmware  OBJECT IDENTIFIER ::= { petFamily 3 }

bios  OBJECT IDENTIFIER ::= { firmware 1 }

progress NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             allVariableBindings, entity, entityInstance, petEventData, petEventSeverity, petEventSourceType,
             petEventType, guid, languageCode, localTimeStamp, manufacturerId, oemCustomField, offset,
             sensorDevice, sensorNumber, sensorType, sequenceId, systemId, trapSourceType, utcOffset }
   STATUS  current
   DESCRIPTION
      "The progress of the BIOS has changed."
::= { bios 1 }

hardware  OBJECT IDENTIFIER ::= { petFamily 4 }

cableInterconnect NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             allVariableBindings, entity, entityInstance, petEventData, petEventSeverity, petEventSourceType,
             petEventType, guid, languageCode, localTimeStamp, manufacturerId, oemCustomField, offset,
             sensorDevice, sensorNumber, sensorType, sequenceId, systemId, trapSourceType, utcOffset }
   STATUS  current
   DESCRIPTION
      "The cable/interconnect hardware has generated an event."
::= { hardware 1 }

drivebay NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             allVariableBindings, entity, entityInstance, petEventData, petEventSeverity, petEventSourceType,
             petEventType, guid, languageCode, localTimeStamp, manufacturerId, oemCustomField, offset,
             sensorDevice, sensorNumber, sensorType, sequenceId, systemId, trapSourceType, utcOffset }
   STATUS  current
   DESCRIPTION
      "The monitor drivebay hardware has generated an event."
::= { hardware 2 }

moduleBoard NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             allVariableBindings, entity, entityInstance, petEventData, petEventSeverity, petEventSourceType,
             petEventType, guid, languageCode, localTimeStamp, manufacturerId, oemCustomField, offset,
             sensorDevice, sensorNumber, sensorType, sequenceId, systemId, trapSourceType, utcOffset }
   STATUS  current
   DESCRIPTION
      "The monitor module/board hardware has generated an event."
::= { hardware 3 }

monitorAsicIc NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             allVariableBindings, entity, entityInstance, petEventData, petEventSeverity, petEventSourceType,
             petEventType, guid, languageCode, localTimeStamp, manufacturerId, oemCustomField, offset,
             sensorDevice, sensorNumber, sensorType, sequenceId, systemId, trapSourceType, utcOffset }
   STATUS  current
   DESCRIPTION
      "The monitor ASIC/IC hardware has generated an event."
::= { hardware 4 }

network NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             allVariableBindings, entity, entityInstance, petEventData, petEventSeverity, petEventSourceType,
             petEventType, guid, languageCode, localTimeStamp, manufacturerId, oemCustomField, offset,
             sensorDevice, sensorNumber, sensorType, sequenceId, systemId, trapSourceType, utcOffset }
   STATUS  current
   DESCRIPTION
      "The network hardware has generated an event."
::= { hardware 5 }

watchdog1 NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             allVariableBindings, entity, entityInstance, petEventData, petEventSeverity, petEventSourceType,
             petEventType, guid, languageCode, localTimeStamp, manufacturerId, oemCustomField, offset,
             sensorDevice, sensorNumber, sensorType, sequenceId, systemId, trapSourceType, utcOffset }
   STATUS  current
   DESCRIPTION
      "The hardware has generated a watchdog 1 event."
::= { hardware 6 }

watchdog2 NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             allVariableBindings, entity, entityInstance, petEventData, petEventSeverity, petEventSourceType,
             petEventType, guid, languageCode, localTimeStamp, manufacturerId, oemCustomField, offset,
             sensorDevice, sensorNumber, sensorType, sequenceId, systemId, trapSourceType, utcOffset }
   STATUS  current
   DESCRIPTION
      "The hardware has generated a watchdog 2 event."
::= { hardware 7 }

petFamilySystem  OBJECT IDENTIFIER ::= { petFamily 5 }

petFamilySystemOs  OBJECT IDENTIFIER ::= { petFamilySystem 1 }

petFamilySystemOsBoot NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             allVariableBindings, entity, entityInstance, petEventData, petEventSeverity, petEventSourceType,
             petEventType, guid, languageCode, localTimeStamp, manufacturerId, oemCustomField, offset,
             sensorDevice, sensorNumber, sensorType, sequenceId, systemId, trapSourceType, utcOffset }
   STATUS  current
   DESCRIPTION
      "The operating system on the host has booted."
::= { petFamilySystemOs 1 }

petFamilySystemOsOperation  OBJECT IDENTIFIER ::= { petFamilySystemOs 2 }

heartbeat NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory,
             allVariableBindings, entity, entityInstance, petEventData, petEventSeverity, petEventSourceType,
             petEventType, guid, languageCode, localTimeStamp, manufacturerId, oemCustomField, offset,
             sensorDevice, sensorNumber, sensorType, sequenceId, systemId, trapSourceType, utcOffset }
   STATUS  current
   DESCRIPTION
      "The operating system has generated a heartbeat event."
::= { petFamilySystemOsOperation 1 }

--
-- Storage traps
--

storageFamily  OBJECT IDENTIFIER ::= { directorTraps 6 }

serveRaidController  OBJECT IDENTIFIER ::= { storageFamily 1 }

physicalDrive  OBJECT IDENTIFIER ::= { serveRaidController 1 }

vendorUnsupported NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory }
   STATUS  current
   DESCRIPTION
      "The vendor for this physical drive is unsupported."
::= { physicalDrive 1 }

state  OBJECT IDENTIFIER ::= { serveRaidController 2 }

notFound NOTIFICATION-TYPE
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText, trapCategory }
   STATUS  current
   DESCRIPTION
      "No controllers were found in this system."
::= { state 1 }

--
-- The trap description objects
--

trapType OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The type of the event."
   ::= { description 1 }

trapSeverity OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The severity of the event."
   ::= { description 2 }

trapSenderName OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The system name from which the event was sent."
   ::= { description 3 }

trapManagedObjectName OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The system name for which the event was generated."
   ::= { description 4 }

trapText OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Text associated with the event."
   ::= { description 5 }

trapCategory OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The category of the event."
   ::= { description 6 }

--
-- Trap detail types
--
-- NOTE:
--
--  When viewing a trap holding an event detail type, the number following these
--  OIDs refer to different values of this type.  They increment according to
--  each trap, ergo, you should never see a { char 2 } without a { char 1 }.
--

char OBJECT-TYPE
   SYNTAX      OCTET STRING
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Eight bit unsigned event detail."
   ::= { details 1 }

short OBJECT-TYPE
   SYNTAX      Integer32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Sixteen bit signed event detail."
   ::= { details 2 }

int OBJECT-TYPE
   SYNTAX      Integer32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Thirty-two bit signed event detail."
   ::= { details 3 }

long OBJECT-TYPE
   SYNTAX      DisplayString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Sixty-four bit signed event detail."
   ::= { details 4 }

boolean OBJECT-TYPE
   SYNTAX      TruthValue
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Thirty-two bit unsigned event detail."
   ::= { details 5 }

float OBJECT-TYPE
   SYNTAX      DisplayString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Thirty-two bit decimal pointed event detail."
   ::= { details 6 }

double OBJECT-TYPE
   SYNTAX      DisplayString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Sixty-four bit decimal pointed event detail."
   ::= { details 7 }

octet OBJECT-TYPE
   SYNTAX      DisplayString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "A string of bytes holding an event detail."
   ::= { details 8 }

string OBJECT-TYPE
   SYNTAX      SnmpAdminString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "A string of unicode chars (normal text) holding
       an event detail."
   ::= { details 9 }

dateTime OBJECT-TYPE
   SYNTAX      DisplayString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Date and time since 1/1970 as an event
       detail."
   ::= { details 10 }

uniChar OBJECT-TYPE
   SYNTAX      DisplayString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "A unicode char holding an event detail."
   ::= { details 11 }

byte OBJECT-TYPE
   SYNTAX      DisplayString
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "One byte holding an event detail."
   ::= { details 12 }

directorTrapsConformance OBJECT IDENTIFIER ::= { directorTraps 100 }

directorTrapsGroups      OBJECT IDENTIFIER ::= { directorTrapsConformance 1 }
directorTrapsCompliances OBJECT IDENTIFIER ::= { directorTrapsConformance 2 }

directorTrapsCompliance MODULE-COMPLIANCE
   STATUS  current
   DESCRIPTION
      "The compliance statement for this module."
   MODULE  -- this module
   MANDATORY-GROUPS { directorTrapsGroup, directorNotificationsGroup }
   ::= { directorTrapsCompliances 1 }

directorTrapsGroup OBJECT-GROUP
   OBJECTS { trapType, trapSeverity, trapSenderName, trapManagedObjectName, trapText,
             trapCategory, char, short, int, long, boolean, float, octet, double, string, dateTime,
             uniChar, byte, actualValue, duration, monitorResource, thresholdName, thresholdValue,
             userID, address, userName, userDescription, userLocale, category, categoryString,
             computerName, data, eventLogCode, eventLogIdentifier, eventLogType, insertionStrings,
             logFile, message, recordNumber, sourceName, type, user, acceptPause, acceptStop,
             caption, checkPoint, creationClassName, serviceDescription, desktopInteract,
             displayName, errorControl, exitCode, name, pathName, processId, serviceSpecificExitCode,
             serviceType, startMode, startName, started, serviceState, status,
             systemCreationClassName, systemName, tagId, waitHint, hive, rootPath, securityDescriptor,
             timeCreated, alertCode, busId, componentId, firmwareCode, ipAddress1, ipAddress2, issue,
             powerDomain, reason, scsiId, side, newState, temperatureSensor, threshold,
             universalUniqueId, senderUniversalUniqueId, sourceUniversalUniqueId, unitNumber,
             voltageSensor, allVariableBindings, entity, entityInstance, petEventData, petEventSeverity,
             petEventSourceType, petEventType, guid, languageCode, localTimeStamp, manufacturerId,
             oemCustomField, offset, sensorDevice, sensorNumber, sensorType, sequenceId, systemId,
             trapSourceType, utcOffset }
   STATUS  current
   DESCRIPTION
      "The objects group for objects defined in this module."
   ::= { directorTrapsGroups 1 }

directorNotificationsGroup NOTIFICATION-GROUP
   NOTIFICATIONS { generalEvent, online, offline, create, change, destroy, processMonitor,
                   cpuUtilization, processCount, driveSpaceUsed, driveSpaceUsedPercent,
                   driveSpaceRemaining, driveWorkload, lockedMemory, memoryUsage,
                   totalPrivilegedTimePercent, fileReadOperationsPerSec, udpPacketsSentPerSec,
                   udpPacketsReceivedPerSec, ipPacketsSentPerSec, ipPacketsReceivedPerSec,
                   ipErrorPacketsReceivedPerSec, tcpConnections, action, badPassword,
                   badUserID, disabledUserID, downlevelConsole, expiredPassword, tooManyActiveIDs,
                   tooManyActiveLogons, uplevelConsole, logoff, logon, applicationLog, securityLog,
                   systemLog, startedService, stoppedService, softwareTreeChanged, systemTreeChanged,
                   codEnabled, bladeServerCommunication, bladeServerInserted, bladeServerRemoved,
                   busCommunication, chassisConfiguration, chassisFailed, dasdFailed, dasdInserted,
                   dasdRemoved, memoryFailed, componentFanFailed, componentFanInserted, componentFanPfa,
                   componentFanRemoved, hardwareCrashDumpAborted, hardwareCrashDumpCompleted,
                   hardwareCrashDumpInitiated, ioModuleConfiguration, ioModuleFailed, ioModuleInserted,
                   ioModulePost, ioModulePowerOn, ioModulePowerOff, ioModuleRedundancy, ioModuleRemoved,
                   kvmOwner, osImageCrashDumpAborted, osImageCrashDumpCompleted, osImageCrashDumpInitiated,
                   componentPfa, powerSubsystemLowFuel, powerSubsystemOverCurrent, powerSubsystemOverPower,
                   powerSubsystemRedundancy, powerSupplyFailed, powerSupplyInserted, powerSupplyRemoved,
                   serverPowerOff, serverPowerOn, serverPowerState, serviceProcessorActive,
                   serviceProcessorConfiguration, serviceProcessorInserted, serviceProcessorLog,
                   serviceProcessorNetworkStack, serviceProcessorRedundancy, serviceProcessorRemoteLogin,
                   serviceProcessorRemoved, serviceProcessorRestart, serviceProcessorTest,
                   smpExpansionModuleDisabled, usbInserted, usbOwner, usbRemoved, vrmFailed, hardDiskDrive,
                   multipleFanFailure, powerFailureEpow, powerFailureFailed, powerFailureRemoved,
                   criticalTamper, criticalTemperatureAmbient, criticalTemperaturePci,
                   criticalTemperaturePlanar, criticalTwelveVoltsHigh, criticalTwelveVoltsLow,
                   criticalTwelveVoltsFaultA, criticalTwelveVoltsFaultB, criticalTwelveVoltsFaultC,
                   criticalTwelveVoltsFaultD, criticalOneVoltHigh, criticalOneVoltLow,
                   criticalTwoVoltsHigh, criticalTwoVoltsLow, criticalThreeVoltsHigh,
                   criticalThreeVoltsLow, criticalThreeVoltsPciHigh, criticalThreeVoltsPciLow,
                   criticalThreeVoltsStandbyHigh, criticalThreeVoltsStandbyLow, criticalFiveVoltsHigh,
                   criticalFiveVoltsLow, criticalFiveVoltsFaultHigh, criticalFiveVoltsFaultLow,
                   criticalFiveVoltsPciHigh, criticalFiveVoltsPciLow, criticalFiveVoltsStandbyHigh,
                   criticalFiveVoltsStandbyLow, criticalNTwelveVoltsHigh, criticalNTwelveVoltsLow,
                   voltageRegulatorModuleFailure, deploymentBoot, deploymentLoader, deploymentOs,
                   deploymentPost, environmentalTemperature, environmentalVoltage, nonCriticalFanRemoved,
                   redundantPower, singleFanFailure, nonCriticalTemperatureAmbient, nonCriticalTemperaturePci,
                   nonCriticalTemperaturePlanar, nonCriticalTwelveVoltsHigh, nonCriticalTwelveVoltsLow,
                   nonCriticalOneVoltHigh, nonCriticalOneVoltLow, nonCriticalTwoVoltsHigh, nonCriticalTwoVoltsLow,
                   nonCriticalThreeVoltsHigh, nonCriticalThreeVoltsLow, nonCriticalThreeVoltsPciHigh,
                   nonCriticalThreeVoltsPciLow, nonCriticalThreeVoltsStandbyHigh, nonCriticalThreeVoltsStandbyLow,
                   nonCriticalFiveVoltsHigh, nonCriticalFiveVoltsLow, nonCriticalFiveVoltsPciHigh,
                   nonCriticalFiveVoltsPciLow, nonCriticalFiveVoltsStandbyHigh, nonCriticalFiveVoltsStandbyLow,
                   nonCriticalNTwelveVoltsHigh, nonCriticalNTwelveVoltsLow, scalableNodeModeNullOrUnknown,
                   scalableNodeModePrimary, scalableNodeModeSecondary, scalableNodeModeStandalone, primary, resetPrimary,
                   resetSecondary, secondary, scalablePartitionAlert, scalablePartitionNullOrUnknown,
                   scalablePartitionPoweredOff, scalablePartitionPoweringOn, scalablePartitionResetting,
                   scalablePartitionShuttingDown, bootFailure, fuelGaugeLowFuel, fuelGaugeNotRedundant, fuelGaugeOverCurrent,
                   systemLoaderTimeout, systemOsTimeout, fanSystemPfa, systemPostTimeout, systemPowerOff,
                   systemPowerOn, systemRedundantPower, systemTamper, unknown, sensorCaseIntrusion,
                   sensorCurrent, sensorFan, sensorPowerSupply, sensorTemperature, sensorVoltage,
                   progress, cableInterconnect, drivebay, moduleBoard, monitorAsicIc, network, watchdog1,
                   watchdog2, petFamilySystemOsBoot, heartbeat, vendorUnsupported, notFound }
   STATUS  current
   DESCRIPTION
      "The notification group for notifications defined in this module."
   ::= { directorTrapsGroups 2 }


 END
