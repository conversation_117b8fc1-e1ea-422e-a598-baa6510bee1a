-- <PERSON><PERSON> created 12/11/98 18:26:10, by
--   SM<PERSON> (the next generation) version 1.6.29, November 22, 1994.

IBMESCONCUB-MIB DEFINITIONS ::= BEGIN

-- From file: "ibmesconcub.mi2"
-- Compile options "G A T M"

IMPORTS
    enterprises, Counter32, Integer32, IpAddress
            FROM SNMPv2-SMI-v1
    OBJECT-TYPE
            FROM RFC-1212
    MacAddress
            FROM SNMPv2-TC-v1
    ifIndex
            FROM RFC1213-MIB;

ibmIROCescon OBJECT IDENTIFIER ::= { enterprises ibm(2) ibmProd(6) ibmIROC(119) ibmIROCrouting(4) 14 }
-- MODULE-IDENTITY
--  LastUpdated
--    9810221200Z
--  OrgName
--    IBM
--  ContactInfo
--    <PERSON>
--     IBM Corporation
--     800 Park Offices Drive
--     V28A/664
--     P.O. Box 12195
--     Research Triangle Park, NC 27709, USA
--     Tel:    **************
--     E-mail: <EMAIL>
--  Descr
--    MI<PERSON> for managing activity on an ESCON channel from its
--    secondary end.
--    
--    New capabilities provided by the ESCON adapter microcode
--    required a change in table indexing. Therefore, this new
--    MIB has been created and is based on the architecture
--    MIB defined in ibmescon.txt.
--    
--    'ESCON' is a trademark of the IBM Corporation.

esconPortData   OBJECT IDENTIFIER ::= { ibmIROCescon 1 }
esconLinkData   OBJECT IDENTIFIER ::= { ibmIROCescon 2 }
esconStationData OBJECT IDENTIFIER ::= { ibmIROCescon 3 }
esconConformance OBJECT IDENTIFIER ::= { ibmIROCescon 4 }

esconPortTable OBJECT-TYPE
    SYNTAX SEQUENCE OF EsconPortEntry
    ACCESS not-accessible
    STATUS mandatory
    DESCRIPTION
        "Table of objects that describe an ESCON channel port."
    ::= { esconPortData 1 }

esconPortEntry OBJECT-TYPE
    SYNTAX EsconPortEntry
    ACCESS not-accessible
    STATUS mandatory
    DESCRIPTION
        "Table of objects that describe an ESCON channel port.  This
        table is indexed by ifIndex from MIB-II."
    INDEX { ifIndex }
    ::= { esconPortTable 1 }

EsconPortEntry ::= SEQUENCE {
    esconPortInFiberStatus INTEGER,
    esconPortOutFiberStatus INTEGER
    }

esconPortInFiberStatus OBJECT-TYPE
    SYNTAX INTEGER {
        inLoff(1),
        inOls(2),
        inIdle(3),
        inUnknown(4)
        }
    ACCESS read-only
    STATUS mandatory
    DESCRIPTION
        "Status of the fiber into this device from the host:
        
               inLoff       = the light is off on the fiber into
                              this device from the host
               inOls        = the fiber into this device from the
                              host is in an intermediate state between
                              light-off and light-on
               inIdle       = the fiber into this device from the
                              host is in the light-on state, and is
                              ready to transfer data from the host to
                              this device
               inUnknown    = the agent cannot determine the status of
                              the fiber into this device from the host"
    ::= { esconPortEntry 1 }

esconPortOutFiberStatus OBJECT-TYPE
    SYNTAX INTEGER {
        outDisableReq(1),
        outDisableForced(2),
        outLoffForced(3),
        outOls(4),
        outOlsForced(5),
        outEnable(6),
        outError(7)
        }
    ACCESS read-only
    STATUS mandatory
    DESCRIPTION
        "Status of the fiber out of this device to the host:
        
               outDisableReq    = out disable obtained; the fiber
                                  out of this device into the host
                                  is not in the light-on state
               outDisableForced = out ESCON emits OLS; the fiber
                                  out of this device into the host
                                  is not in the light-on state
               outLoffForced    = out ESCON forced light-off; the fiber
                                  out of this device into the host
                                  is not in the light-on state
               outOls           = the fiber out of this device into the
                                  host is in an intermediate state
                                  between light-off and light-on
               outOlsforced     = out ESCON forced OLS; the fiber
                                  out of this device into the host
                                  is not in the light-on state
               outEnable        = the fiber out of this device into the
                                  host is in the light-on state, and is
                                  ready to transfer data from this
                                  device to the host
               outError         = the status of the fiber out of this
                                  device to the host is none of those
                                  listed above.  This is a state that
                                  should not occur"
    ::= { esconPortEntry 2 }

esconLinkTable OBJECT-TYPE
    SYNTAX SEQUENCE OF EsconLinkEntry
    ACCESS not-accessible
    STATUS mandatory
    DESCRIPTION
        "Table of objects that describe an ESCON channel link."
    ::= { esconLinkData 1 }

esconLinkEntry OBJECT-TYPE
    SYNTAX EsconLinkEntry
    ACCESS not-accessible
    STATUS mandatory
    DESCRIPTION
        "Table of objects that describe an ESCON channel link.  This
        table is indexed by ifIndex from MIB-II, by host link address,
        (host) control unit address, and by (host) partition number."
    INDEX { ifIndex, esconLinkHostLinkAddress, esconLinkControlUnitAddress, esconLinkPartitionNumber }
    ::= { esconLinkTable 1 }

EsconLinkEntry ::= SEQUENCE {
    esconLinkHostLinkAddress OCTET STRING,
    esconLinkControlUnitAddress OCTET STRING,
    esconLinkPartitionNumber OCTET STRING,
    esconLinkStatus INTEGER
    }

esconLinkHostLinkAddress OBJECT-TYPE
    SYNTAX OCTET STRING(SIZE(1))
    ACCESS not-accessible
    STATUS mandatory
    DESCRIPTION
        "This address identifies the ESCON Director port to which the
        optical fiber between the ESCON Director and the host is
        attached."
    ::= { esconLinkEntry 1 }

esconLinkControlUnitAddress OBJECT-TYPE
    SYNTAX OCTET STRING(SIZE(1))
    ACCESS not-accessible
    STATUS mandatory
    DESCRIPTION
        "A number identifying a logical control unit within an actual host."
    ::= { esconLinkEntry 2 }

esconLinkPartitionNumber OBJECT-TYPE
    SYNTAX OCTET STRING(SIZE(1))
    ACCESS not-accessible
    STATUS mandatory
    DESCRIPTION
        "A number identifying a logical host within an actual host."
    ::= { esconLinkEntry 3 }

esconLinkStatus OBJECT-TYPE
    SYNTAX INTEGER {
        hlpNotEstab(1),
        hlpEstab(2),
        hlpError(3)
        }
    ACCESS read-only
    STATUS mandatory
    DESCRIPTION
        "Gives the status of the link:
        
               hlpNotEstab = Host Logical Path not established
               hlpEstab = Host Logical Path established
               hlpError = Host Logical Path error"
    ::= { esconLinkEntry 4 }

esconStationTable OBJECT-TYPE
    SYNTAX SEQUENCE OF EsconStationEntry
    ACCESS not-accessible
    STATUS mandatory
    DESCRIPTION
        "Table of objects that describe an ESCON channel station."
    ::= { esconStationData 1 }

esconStationEntry OBJECT-TYPE
    SYNTAX EsconStationEntry
    ACCESS not-accessible
    STATUS mandatory
    DESCRIPTION
        "Table of objects that describe an ESCON channel station.  This
        table is indexed by ifIndex from MIB-II, by host link address,
        by (host) control unit address, by (host) partition number, and
        by ESCON device address."
    INDEX { ifIndex, esconStationHostLinkAddress, esconStationControlUnitAddress, esconStationPartitionNumber, esconStationDeviceAddress }
    ::= { esconStationTable 1 }

EsconStationEntry ::= SEQUENCE {
    esconStationHostLinkAddress OCTET STRING,
    esconStationControlUnitAddress OCTET STRING,
    esconStationPartitionNumber OCTET STRING,
    esconStationDeviceAddress OCTET STRING,
    esconStationState INTEGER,
    esconStationAttentionDelay INTEGER,
    esconStationAttentionTimeOut INTEGER,
    esconStationMaxBfru INTEGER,
    esconStationUnitSize INTEGER,
    esconStationMaxMsgSizeReceived INTEGER,
    esconStationMaxMsgSizeSent INTEGER,
    esconStationDataPacketsOkReceived Counter32,
    esconStationDataPacketsKoReceived Counter32,
    esconStationDataPacketsSent Counter32,
    esconStationTotalFramesSent Counter32,
    esconStationDataPacketsRetransmitted Counter32,
    esconStationPositiveAckDataPackets Counter32,
    esconStationSecondChanceAttentions Counter32,
    esconStationCommandsRetried Counter32
    }

esconStationHostLinkAddress OBJECT-TYPE
    SYNTAX OCTET STRING(SIZE(1))
    ACCESS not-accessible
    STATUS mandatory
    DESCRIPTION
        "This address identifies the ESCON Director port to which the
        optical fiber between the ESCON Director and the host is
        attached."
    ::= { esconStationEntry 1 }

esconStationControlUnitAddress OBJECT-TYPE
    SYNTAX OCTET STRING(SIZE(1))
    ACCESS not-accessible
    STATUS mandatory
    DESCRIPTION
        "A number identifying a logical control unit within an actual host."
    ::= { esconStationEntry 2 }

esconStationPartitionNumber OBJECT-TYPE
    SYNTAX OCTET STRING(SIZE(1))
    ACCESS not-accessible
    STATUS mandatory
    DESCRIPTION
        "A number identifying a logical host within an actual host."
    ::= { esconStationEntry 3 }

esconStationDeviceAddress OBJECT-TYPE
    SYNTAX OCTET STRING(SIZE(1))
    ACCESS not-accessible
    STATUS mandatory
    DESCRIPTION
        "A unique hexadecimal number allocated to each station on the
        same host link."
    ::= { esconStationEntry 4 }

esconStationState OBJECT-TYPE
    SYNTAX INTEGER {
        idle(1),
        cpDefined(2),
        cpReset(3),
        cpActive(4),
        cpDelete(5),
        cpAbend(6),
        cldpWait(7),
        cldpDefinedl(8),
        cldpError(9),
        cldpLoad(10),
        cldpDump(11),
        deletePending(12),
        deleted(13),
        cpXidExpected(14)
        }
    ACCESS read-only
    STATUS mandatory
    DESCRIPTION
        "The current state of the station."
    ::= { esconStationEntry 5 }

esconStationAttentionDelay OBJECT-TYPE
    SYNTAX INTEGER(0..420)
--  Units
--    seconds
    ACCESS read-write
    STATUS mandatory
    DESCRIPTION
        "Specifies the amount of time in seconds that elapses
        from the receipt of a packet at an ESCON station (when
        no other packets are queued) before that station sends
        buffered data to the Host.
        
        An update to this object takes effect the next time the station
        establishes communications with the host."
    ::= { esconStationEntry 6 }

esconStationAttentionTimeOut OBJECT-TYPE
    SYNTAX INTEGER(10..840)
--  Units
--    seconds
    ACCESS read-write
    STATUS mandatory
    DESCRIPTION
        "Specifies the amount of time in seconds that the station is to
        wait for a response to an attention signal it sent to the host
        before initiating channel disconnect.
        
        An update to this object takes effect the next time the station
        establishes communications with the host."
    ::= { esconStationEntry 7 }

esconStationMaxBfru OBJECT-TYPE
    SYNTAX INTEGER(1..65535)
    ACCESS read-only
    STATUS mandatory
    DESCRIPTION
        "Number of buffers in the host buffer pool for receiving data
        from this station."
    ::= { esconStationEntry 8 }

esconStationUnitSize OBJECT-TYPE
    SYNTAX INTEGER(64..4000)
--  Units
--    bytes
    ACCESS read-only
    STATUS mandatory
    DESCRIPTION
        "Maximum size of a buffer, in bytes, that the host can receive
        from this station."
    ::= { esconStationEntry 9 }

esconStationMaxMsgSizeReceived OBJECT-TYPE
    SYNTAX INTEGER(0..65535)
    ACCESS read-write
    STATUS mandatory
    DESCRIPTION
        "The maximum length of a message that can be received on this
        station.
        
        An update to this object takes effect the next time the station
        establishes communications with the host."
    ::= { esconStationEntry 10 }

esconStationMaxMsgSizeSent OBJECT-TYPE
    SYNTAX INTEGER(0..65535)
    ACCESS read-write
    STATUS mandatory
    DESCRIPTION
        "The maximum length of a message that can be sent from this
        station to the host.
        
        An update to this object takes effect the next time the station
        establishes communications with the host."
    ::= { esconStationEntry 11 }

esconStationDataPacketsOkReceived OBJECT-TYPE
    SYNTAX Counter32
    ACCESS read-only
    STATUS mandatory
    DESCRIPTION
        "The number of data packets received from the host by this
          station without Data Check."
    ::= { esconStationEntry 12 }

esconStationDataPacketsKoReceived OBJECT-TYPE
    SYNTAX Counter32
    ACCESS read-only
    STATUS mandatory
    DESCRIPTION
        "The number of data packets received from the host by this station
        with Data Check."
    ::= { esconStationEntry 13 }

esconStationDataPacketsSent OBJECT-TYPE
    SYNTAX Counter32
    ACCESS read-only
    STATUS mandatory
    DESCRIPTION
        "The number of data packets sent to the host by this station."
    ::= { esconStationEntry 14 }

esconStationTotalFramesSent OBJECT-TYPE
    SYNTAX Counter32
    ACCESS read-only
    STATUS mandatory
    DESCRIPTION
        "The number of data packets and control packets sent to the host
        by this station."
    ::= { esconStationEntry 15 }

esconStationDataPacketsRetransmitted OBJECT-TYPE
    SYNTAX Counter32
    ACCESS read-only
    STATUS mandatory
    DESCRIPTION
        "The number of data packets retransmitted by this station"
    ::= { esconStationEntry 16 }

esconStationPositiveAckDataPackets OBJECT-TYPE
    SYNTAX Counter32
    ACCESS read-only
    STATUS mandatory
    DESCRIPTION
        "The number of data packets sent by this station to the host that
        the host has positively acknowledged.  When the host sends a
        positive acknowledgement for a group of n data packets, this
        counter is incremented by n."
    ::= { esconStationEntry 17 }

esconStationSecondChanceAttentions OBJECT-TYPE
    SYNTAX Counter32
    ACCESS read-only
    STATUS mandatory
    DESCRIPTION
        "The number of times this station has sent a Second Chance
        Attention signal to the host."
    ::= { esconStationEntry 18 }

esconStationCommandsRetried OBJECT-TYPE
    SYNTAX Counter32
    ACCESS read-only
    STATUS mandatory
    DESCRIPTION
        "The number of times a command has been retried on this
        station"
    ::= { esconStationEntry 19 }


END

