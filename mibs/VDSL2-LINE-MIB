VDSL2-LINE-MIB DEFINITIONS ::= BEGIN

IMPORTS
   MODULE-IDENTITY,
   OBJECT-TYPE,
   transmission,
   Unsigned32,
   NOTIFICATION-TYPE,
   Integer32,
   Counter32
      FROM SNMPv2-SMI

   ifIndex
      FROM IF-MIB

   TruthValue,
   RowStatus
       FROM SNMPv2-TC
   SnmpAdminString
      FROM SNMP-FRAMEWORK-MIB
	  
   Adsl2LConfProfPmMode
      FROM ADSL2-LINE-TC-MIB

   <PERSON>erf<PERSON>ntervalThreshold,
   HCPerfTimeElapsed
      FROM  HC-PerfHist-TC-MIB   -- [RFC3705]

   Xdsl2Unit,
   Xdsl2Direction,
   Xdsl2Band,
   Xdsl2TransmissionModeType,
   Xdsl2RaMode,
   Xdsl2InitResult,
   Xdsl2OperationModes,
   Xdsl2PowerMngState,
   Xdsl2ConfPmsForce,
   Xdsl2LinePmMode,
   <PERSON><PERSON><PERSON>2<PERSON><PERSON><PERSON><PERSON><PERSON>,
   X<PERSON><PERSON>2<PERSON>dsfResult,
   <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,

   <PERSON><PERSON>l2<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
   Xdsl2<PERSON>ine<PERSON><PERSON><PERSON>,
   Xdsl2SymbolProtection,
   Xdsl2SymbolProtection8,
   Xdsl2MaxBer,
   Xdsl2ChInitPolicy,
   Xdsl2ScMaskDs,
   Xdsl2ScMaskUs,
   Xdsl2CarMask,
   Xdsl2RfiBands,
   Xdsl2PsdMaskDs,
   Xdsl2PsdMaskUs,
   Xdsl2Tssi,
   Xdsl2LastTransmittedState,
   Xdsl2LineStatus,
   Xdsl2ChInpReport,
   Xdsl2ChAtmStatus,
   Xdsl2ChPtmStatus,
   Xdsl2UpboKLF,
   Xdsl2BandUs,
   Xdsl2LineProfiles,
   Xdsl2LineUs0Mask,
   Xdsl2LineClassMask,
   Xdsl2LineLimitMask,
   Xdsl2LineUs0Disable,
   Xdsl2LinePsdMaskSelectUs,
   Xdsl2LineCeFlag,
   Xdsl2LineSnrMode,
   Xdsl2LineTxRefVnDs,
   Xdsl2LineTxRefVnUs,
   Xdsl2BitsAlloc,
   Xdsl2MrefPsdDs,
   Xdsl2MrefPsdUs
   
          FROM   VDSL2-LINE-TC-MIB       -- [This document]

   MODULE-COMPLIANCE,
   OBJECT-GROUP,
   NOTIFICATION-GROUP
      FROM SNMPv2-CONF;

vdsl2MIB MODULE-IDENTITY
   LAST-UPDATED "200909300000Z" -- September 30, 2009
   ORGANIZATION "ADSLMIB Working Group"
   CONTACT-INFO "WG-email:  <EMAIL>
   Info:      https://www1.ietf.org/mailman/listinfo/adslmib

             Chair:     Mike Sneed
                        Sand Channel Systems
             Postal:    P.O. Box 37324
                        Raleigh NC 27627-732
             Email:     <EMAIL>
             Phone:     ****** 600 7022

             Co-Chair:  Menachem Dodge
                        ECI Telecom Ltd.
             Postal:    30 Hasivim St.
                        Petach Tikva 49517,
                        Israel.
             Email:     <EMAIL>
             Phone:     +972 3 926 8421

             Co-editor: Moti Morgenstern
                        ECI Telecom Ltd.
             Postal:    30 Hasivim St.
                        Petach Tikva 49517,
                        Israel.
             Email:     <EMAIL>
             Phone:     +972 3 926 6258

             Co-editor: Scott Baillie
                        NEC Australia
             Postal:    649-655 Springvale Road,
                        Mulgrave, Victoria 3170,
                        Australia.
             Email:     <EMAIL>
             Phone:     +61 3 9264 3986

             Co-editor: Umberto Bonollo
                        NEC Australia
             Postal:    649-655 Springvale Road,
                        Mulgrave, Victoria 3170,
                        Australia.
             Email:     <EMAIL>
             Phone:     +61 3 9264 3385
            "
   DESCRIPTION
        "
         This document defines a Management Information Base (MIB)
         module for use with network management protocols in the
         Internet community for the purpose of managing VDSL2, ADSL,
         ADSL2, and ADSL2+ lines.

         The MIB module described in RFC 2662 [RFC2662] defines
         objects used for managing Asymmetric Bit-Rate DSL (ADSL)

         interfaces per [T1E1.413], [G.992.1], and [G.992.2].
         These object descriptions are based upon the specifications
         for the ADSL Embedded Operations Channel (EOC) as defined
         in American National Standards Institute (ANSI) T1E1.413
         [T1E1.413] and International Telecommunication Union (ITU-T)
         G.992.1 [G.992.1] and G.992.2 [G.992.2].

         The MIB module described in RFC 4706 [RFC4706] defines
         objects used for managing ADSL2 interfaces per [G.992.3]
         and [G.992.4], and ADSL2+ interfaces per [G.992.5].  That MIB
         is also capable of managing ADSL interfaces per [T1E1.413],
         [G.992.1], and [G.992.2].

         This document does not obsolete RFC 2662 [RFC2662] or
         RFC 4706 [RFC4706], but rather provides a more comprehensive
         management model that manages VDSL2 interfaces per G.993.2
         [G.993.2] as well as ADSL, ADSL2, and ADSL2+ technologies
         per T1E1.413, G.992.1, G.992.2, G.992.3, G.992.4, and
         G.992.5
         ([T1E1.413], [G.992.1], [G.992.2], [G.992.3], [G.992.4], and
         [G.992.5], respectively).

         Additionally, the management framework for VDSL2 lines
         specified by the Digital Subscriber Line Forum
         (DSLF) has been taken into consideration [TR-129].  That
         framework is based on the ITU-T G.997.1 standard [G.997.1] and
         its amendment 1 [G.997.1-Am1].

         The MIB module is located in the MIB tree under MIB 2
         transmission, as discussed in the MIB-2 Integration (RFC 2863
         [RFC2863]) section of this document.

         Copyright (c) 2009 IETF Trust and the persons identified
         as authors of the code.  All rights reserved.

         Redistribution and use in source and binary forms, with
         or without modification, are permitted provided that the
         following conditions are met:

         - Redistributions of source code must retain the above
           copyright notice, this list of conditions and the
           following disclaimer.

         - Redistributions in binary form must reproduce the above
           copyright notice, this list of conditions and the
           following disclaimer in the documentation and/or other
           materials provided with the distribution.

         - Neither the name of Internet Society, IETF or IETF Trust,
           nor the names of specific contributors, may be used to
           endorse or promote products derived from this software
           without specific prior written permission.

         THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND
         CONTRIBUTORS 'AS IS' AND ANY EXPRESS OR IMPLIED WARRANTIES,
         INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
         MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
         DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR
         CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
         SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
         NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
         LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
         HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
         CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
         OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
         SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

         This version of this MIB module is part of RFC 5650;
         see the RFC itself for full legal notices."

   REVISION "200909300000Z" -- September 30, 2009
   DESCRIPTION "Initial version, published as RFC 5650."
      ::= { transmission 251 }

  xdsl2Notifications OBJECT IDENTIFIER ::= { vdsl2MIB 0 }
  xdsl2Objects       OBJECT IDENTIFIER ::= { vdsl2MIB 1 }
  xdsl2Conformance   OBJECT IDENTIFIER ::= { vdsl2MIB 2 }
  ------------------------------------------------
  xdsl2Line          OBJECT IDENTIFIER ::= { xdsl2Objects 1 }
  xdsl2Status        OBJECT IDENTIFIER ::= { xdsl2Objects 2 }
  xdsl2Inventory     OBJECT IDENTIFIER ::= { xdsl2Objects 3 }
  xdsl2PM            OBJECT IDENTIFIER ::= { xdsl2Objects 4 }
  xdsl2Profile       OBJECT IDENTIFIER ::= { xdsl2Objects 5 }
  xdsl2Scalar        OBJECT IDENTIFIER ::= { xdsl2Objects 6 }
  ------------------------------------------------
  xdsl2PMLine      OBJECT IDENTIFIER ::= { xdsl2PM 1 }
  xdsl2PMChannel   OBJECT IDENTIFIER ::= { xdsl2PM 2 }
  ------------------------------------------------
  xdsl2ProfileLine      OBJECT IDENTIFIER ::= { xdsl2Profile 1 }
  xdsl2ProfileChannel   OBJECT IDENTIFIER ::= { xdsl2Profile 2 }
  xdsl2ProfileAlarmConf OBJECT IDENTIFIER ::= { xdsl2Profile 3 }
  ------------------------------------------------
  xdsl2ScalarSC         OBJECT IDENTIFIER ::= { xdsl2Scalar 1 }
  ------------------------------------------------

------------------------------------------------

--          xdsl2LineTable                    --
------------------------------------------------

xdsl2LineTable  OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2LineEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2LineTable contains configuration, command and
       status parameters of the VDSL2/ADSL/ADSL2 or ADSL2+ line.

       Several objects in this table MUST be maintained in a persistent
       manner."
   ::= { xdsl2Line 1 }

xdsl2LineEntry  OBJECT-TYPE
   SYNTAX      Xdsl2LineEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The index of this table is an interface index where the
       interface has an ifType of vdsl2(251)."
   INDEX  { ifIndex }
   ::= { xdsl2LineTable 1 }

Xdsl2LineEntry  ::=
   SEQUENCE {
      xdsl2LineConfTemplate            SnmpAdminString,
      xdsl2LineConfFallbackTemplate    SnmpAdminString,
      xdsl2LineAlarmConfTemplate       SnmpAdminString,
      xdsl2LineCmndConfPmsf            Xdsl2ConfPmsForce,
      xdsl2LineCmndConfLdsf            Xdsl2LineLdsf,
      xdsl2LineCmndConfLdsfFailReason  Xdsl2LdsfResult,
      xdsl2LineCmndConfBpsc            Xdsl2LineBpsc,
      xdsl2LineCmndConfBpscFailReason  Xdsl2BpscResult,
      xdsl2LineCmndConfBpscRequests    Counter32,
      xdsl2LineCmndAutomodeColdStart   TruthValue,
      xdsl2LineCmndConfReset           Xdsl2LineReset,
      xdsl2LineStatusActTemplate       SnmpAdminString,
      xdsl2LineStatusXtuTransSys       Xdsl2TransmissionModeType,
      xdsl2LineStatusPwrMngState       Xdsl2PowerMngState,
      xdsl2LineStatusInitResult        Xdsl2InitResult,
      xdsl2LineStatusLastStateDs       Xdsl2LastTransmittedState,
      xdsl2LineStatusLastStateUs       Xdsl2LastTransmittedState,
      xdsl2LineStatusXtur              Xdsl2LineStatus,
      xdsl2LineStatusXtuc              Xdsl2LineStatus,
      xdsl2LineStatusAttainableRateDs  Unsigned32,
      xdsl2LineStatusAttainableRateUs  Unsigned32,

      xdsl2LineStatusActPsdDs          Integer32,
      xdsl2LineStatusActPsdUs          Integer32,
      xdsl2LineStatusActAtpDs          Integer32,
      xdsl2LineStatusActAtpUs          Integer32,
      xdsl2LineStatusActProfile        Xdsl2LineProfiles,
      xdsl2LineStatusActLimitMask      Xdsl2LineLimitMask,
      xdsl2LineStatusActUs0Mask        Xdsl2LineUs0Mask,
      xdsl2LineStatusActSnrModeDs      Xdsl2LineSnrMode,
      xdsl2LineStatusActSnrModeUs      Xdsl2LineSnrMode,
      xdsl2LineStatusElectricalLength  Unsigned32,
      xdsl2LineStatusTssiDs            Xdsl2Tssi,
      xdsl2LineStatusTssiUs            Xdsl2Tssi,
      xdsl2LineStatusMrefPsdDs         Xdsl2MrefPsdDs,
      xdsl2LineStatusMrefPsdUs         Xdsl2MrefPsdUs,
      xdsl2LineStatusTrellisDs         TruthValue,
      xdsl2LineStatusTrellisUs         TruthValue,
      xdsl2LineStatusActualCe          Unsigned32
   }

xdsl2LineConfTemplate  OBJECT-TYPE
   SYNTAX      SnmpAdminString (SIZE(1..32))
   MAX-ACCESS  read-write
   STATUS      current
   DESCRIPTION
      "The value of this object identifies the row in the xDSL2
       Line Configuration Template Table, xdsl2LineConfTemplateTable,
       that applies for this line.

       This object MUST be maintained in a persistent manner."
   REFERENCE    "DSL Forum TR-129, paragraph #5.1"
   DEFVAL       { "DEFVAL" }
   ::= { xdsl2LineEntry 1 }

xdsl2LineConfFallbackTemplate  OBJECT-TYPE
   SYNTAX      SnmpAdminString (SIZE(0..32))
   MAX-ACCESS  read-write
   STATUS      current
   DESCRIPTION
      "This object is used to identify the template that will be
       used if the xDSL2 line fails to operate using the primary
       template.  The primary template is identified using the
       xdsl2LineConfTemplate object.

       For example, a xDSL2 line may fall back to a template with a
       lower rate if the rate specified in the primary template
       cannot be achieved.

       The value of this object identifies a row in the xDSL2 Line

       Configuration Template Table, xdsl2LineConfTemplateTable.
       Any row in the xdsl2LineConfTemplateTable table may be used as a
       fall-back template.

       If the xDSL2 line fails to operate using the fall-back template,
       then the primary template should be retried.
       The xTU-C should continue to alternate between the primary and
       fall-back templates until one of them succeeds.

       If the value of this object is a zero-length string, then no
       fall-back template is defined and only the primary template will
       be used.

       Note that implementation of this object is not mandatory.
       If this object is not supported, any attempt to modify this
       object should result in the SET request being rejected.

       This object MUST be maintained in a persistent manner."
   ::= { xdsl2LineEntry 2 }

xdsl2LineAlarmConfTemplate  OBJECT-TYPE
   SYNTAX      SnmpAdminString (SIZE(1..32))
   MAX-ACCESS  read-write
   STATUS      current
   DESCRIPTION
      "The value of this object identifies the row in the xDSL2
       Line Alarm Configuration Template Table,
       xdsl2LineAlarmConfTemplateTable, which applies to this line.

       This object MUST be maintained in a persistent manner."
   REFERENCE    "DSL Forum TR-129, paragraph #5.1"
   DEFVAL       { "DEFVAL" }
   ::= { xdsl2LineEntry 3 }

xdsl2LineCmndConfPmsf  OBJECT-TYPE
   SYNTAX      Xdsl2ConfPmsForce
   MAX-ACCESS  read-write
   STATUS      current
   DESCRIPTION
      "Power management state forced (PMSF).  Defines the line
       states to be forced by the near-end xTU on this line.
       This object MUST be maintained in a persistent manner."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.3 (PMSF)"
   DEFVAL       { l3toL0 }
   ::= { xdsl2LineEntry 4 }

xdsl2LineCmndConfLdsf  OBJECT-TYPE
   SYNTAX      Xdsl2LineLdsf

   MAX-ACCESS  read-write
   STATUS      current
   DESCRIPTION
      "Loop diagnostic state forced (LDSF).
       Defines whether the line should be forced into the loop
       diagnostics mode by the near-end xTU of this line.  Note that
       a loop diagnostic may be initiated by the far-end xTU at any
       time.

       Only when the xdsl2LineStatusPwrMngState object is in the
       'l3' state and the xdsl2LineCmndConfPmsf object is in the
       'l0orL2toL3' state, can the line be forced into loop diagnostic
       mode procedures.  Upon successful completion of the loop
       diagnostic mode procedures, the Access Node shall set this
       object to 'inhibit', and xdsl2LineStatusPwrMngState will
       remain in the 'l3' state.  The loop diagnostic data shall be
       available at least until xdsl2LineCmndConfPmsf is set to the
       'l3toL0' state.

       The results of the loop diagnostic procedure are stored in the
       tables xdsl2SCStatusTable, xdsl2SCStatusBandTable, and
       xdsl2SCStatusSegmentTable.  The status of the loop diagnostic
       procedure is indicated by xdsl2LineCmndConfLdsfFailReason.

       As long as loop diagnostic procedures are not completed
       successfully, attempts shall be made to do so, until the loop
       diagnostic mode is no longer forced on the line through this
       configuration parameter."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.8 (LDSF)"
   DEFVAL       { inhibit }
   ::= { xdsl2LineEntry 5 }

xdsl2LineCmndConfLdsfFailReason  OBJECT-TYPE
   SYNTAX      Xdsl2LdsfResult
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The status of the most recent occasion when the loop
       diagnostics state forced (LDSF) command was issued for the
       associated line."
   DEFVAL       { none }
   ::= { xdsl2LineEntry 6 }

xdsl2LineCmndConfBpsc  OBJECT-TYPE
   SYNTAX      Xdsl2LineBpsc
   MAX-ACCESS  read-write
   STATUS      current
   DESCRIPTION

      "Request a bits-per-subcarrier measurement to be made.

       A request for a bits-per-subcarrier measurement is made by
       setting this object to the value of 'measure'.  Upon
       completion of the measurement request, the Access Node shall set
       this object to 'idle'.

       The SNMP agent should allow initiating a bits-per-subcarrier
       measurement process only if there is no other bits-per-subcarrier
       measurement already running, and respond with an SNMP error
       (e.g., wrongValue) otherwise.

       Note that a bits-per-subcarrier measurement is also performed
       during a line diagnostic procedure.  This object provides an
       additional mechanism to fetch the bits-per-subcarrier data.  This
       additional mechanism is provided so that bits-per-subcarrier
       data may be fetched without forcing the line into no power state.
       This is useful because the bits-per-subcarrier allocation may be
       adjusted at show time due to rate adaption and bit swapping.

       The implementation of this additional mechanism for measuring
       bits per subcarrier is not mandatory.

       The results of the bits-per-subcarrier measurement are stored in
       xdsl2LineSegmentTable.  The status of the bits-per-subcarrier
       measurement is indicated by
       xdsl2LineCmndConfBpscFailReason."
   DEFVAL       { idle }
   ::= { xdsl2LineEntry 7 }

xdsl2LineCmndConfBpscFailReason  OBJECT-TYPE
   SYNTAX      Xdsl2BpscResult
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The status of the most recent bits-per-subcarrier
       measurement request issued for the associated line."
   DEFVAL       { none }
   ::= { xdsl2LineEntry 8 }

xdsl2LineCmndConfBpscRequests  OBJECT-TYPE
   SYNTAX      Counter32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Measurement request counter.
       This counter is incremented by one every time a request for a
       bits-per-subcarrier measurement is made.  A measurement request

       is made by modifying the xdsl2LineCmndConfBpsc object from
       idle(1) to the value measure(2).

       The measurement results may be very large and will not fit
       into a single PDU; hence, multiple SNMP GET requests may be
       required to fetch the measurement results.
       Because the measurement results cannot be fetched atomically,
       it is possible for a second manager to start a new measurement
       before a first manager has fetched all of its results.
       An SNMP manager can use this object to ensure that the
       measurement results retrieved using one or more GET requests
       all belong to the measurement initiated by that manager.

       The following steps are suggested in order for the SNMP
       manager to initiate the bits-per-subcarrier measurement:

       1. Wait for xdsl2LineCmndConfBpsc value to be idle(1).
       2. Perform an SNMP GET for xdsl2LineCmndConfBpscRequests.
       3. Wait a short delay (4 -> 8 seconds).
       4. Perform an SNMP SET on xdsl2LineCmndConfBpsc with
          the value measure(2).
       5. If step 4 returns an error, then go to step 1.
       6. Wait for xdsl2LineCmndConfBpsc value to be idle(1).
       7. Fetch measurement results using one or more GET PDUs.
       8. Perform an SNMP GET for xdsl2LineCmndConfBpscRequests.
       9. Compute the difference between the two values of
          xdsl2LineCmndConfBpscRequests.  If the value is one,
          then the results are valid, else go to step 1."
   ::= { xdsl2LineEntry 9 }

xdsl2LineCmndAutomodeColdStart   OBJECT-TYPE
   SYNTAX      TruthValue
   MAX-ACCESS  read-write
   STATUS      current
   DESCRIPTION
      "Automode cold start forced.  This parameter is defined in
       order to improve testing of the performance of xTUs supporting
       automode when it is enabled in the MIB.
       Change the value of this parameter to 'true' to indicate a change
       in loop conditions applied to the devices under the test.  The
       xTUs shall reset any historical information used for automode
       and for shortening G.994.1 handshake and initialization.

       Automode is the case where multiple operation-modes are enabled
       through the xdsl2LConfProfXtuTransSysEna object in the line
       configuration profile being used for the line, and where the
       selection of the actual operation-mode depends not only on the
       common capabilities of both xTUs (as exchanged in G.994.1), but

       also on achievable data rates under given loop conditions."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.10
                (Automode Cold Start Forced)"
   DEFVAL       { false }
   ::= { xdsl2LineEntry 10 }

xdsl2LineCmndConfReset   OBJECT-TYPE
      SYNTAX      Xdsl2LineReset
      MAX-ACCESS  read-write
      STATUS      current
      DESCRIPTION
         "Request a line reset to occur.
          If this object is set to the value of 'reset', then force
          the line to reset (i.e., the modems will retrain).
          When the line has successfully reset, the SNMP agent will
          set the value of this object to 'idle'.

          Note that the xdsl2LineCmndConfPmsf object will always take
          precedence over this object.
          If the xdsl2LineCmndConfPmsf object is set to the value
          'l0orL2toL3', then the line MUST NOT return to the Showtime
          state due to a reset request action performed using this
          object."
   DEFVAL       { idle }
      ::= { xdsl2LineEntry 11 }

xdsl2LineStatusActTemplate  OBJECT-TYPE
   SYNTAX      SnmpAdminString (SIZE(0..32))
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "This object is used to identify the template that is
       currently in use for this line.
       This object is updated when a successful line initialization
       occurs.
       This object indicates if the primary template
       (xdsl2LineConfTemplate) is in use or the fall-back template
       (xdsl2LineConfFallbackTemplate) is in use.
       If the line is not successfully initialized, then the value of
       this object will be a zero-length string."
   ::= { xdsl2LineEntry 12 }

xdsl2LineStatusXtuTransSys  OBJECT-TYPE
   SYNTAX      Xdsl2TransmissionModeType
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The xTU Transmission System (xTS) in use.

       It is coded in a bitmap representation with one bit set to
       '1' (the selected coding for the DSL line).  This
       parameter may be derived from the handshaking procedures defined
       in Recommendation G.994.1.  A set of xDSL line transmission
       modes, with one bit per mode."
   REFERENCE    "ITU-T G.997.1, paragraph #*******
                (xDSL transmission system)"
   DEFVAL       { {} }
   ::= { xdsl2LineEntry 13 }

xdsl2LineStatusPwrMngState  OBJECT-TYPE
   SYNTAX      Xdsl2PowerMngState
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The current power management state."
   REFERENCE    "ITU-T G.997.1, paragraph #*******
                (Line power management state)"
   DEFVAL       { l3 }
      ::= { xdsl2LineEntry 14 }

xdsl2LineStatusInitResult  OBJECT-TYPE
   SYNTAX      Xdsl2InitResult
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Indicates the result of the last full initialization
       performed on the line."
   REFERENCE    "ITU-T G.997.1, paragraph #*******
                (Initialization success/failure cause)"
   DEFVAL       { noFail }
   ::= { xdsl2LineEntry 15 }

xdsl2LineStatusLastStateDs  OBJECT-TYPE
   SYNTAX      Xdsl2LastTransmittedState
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The last successful transmitted initialization state in
       the downstream direction in the last full initialization
       performed on the line."
   REFERENCE    "ITU-T G.997.1, paragraph #*******
                (Downstream last transmitted state)"
   DEFVAL       { atucG9941 }
   ::= { xdsl2LineEntry 16 }

xdsl2LineStatusLastStateUs  OBJECT-TYPE
   SYNTAX      Xdsl2LastTransmittedState

   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The last successful transmitted initialization state in the
       upstream direction in the last full initialization performed on
       the line."
   REFERENCE    "ITU-T G.997.1, paragraph #*******
                (Upstream last transmitted state)"
   DEFVAL       { aturG9941 }
   ::= { xdsl2LineEntry 17 }

xdsl2LineStatusXtur  OBJECT-TYPE
   SYNTAX      Xdsl2LineStatus
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Indicates the current state (existing failures) of the xTU-R.
       This is a bitmap of possible conditions."
   REFERENCE    "ITU-T G.997.1, paragraph #*******
                (Line far-end failures)"
   DEFVAL       { { noDefect } }
   ::= { xdsl2LineEntry 18 }

xdsl2LineStatusXtuc  OBJECT-TYPE
   SYNTAX      Xdsl2LineStatus
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Indicates the current state (existing failures) of the xTU-C.
       This is a bitmap of possible conditions."
   REFERENCE    "ITU-T G.997.1, paragraph #*******
                (Line near-end failures)"
   DEFVAL       { { noDefect } }
   ::= { xdsl2LineEntry 19 }

xdsl2LineStatusAttainableRateDs  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "bits/second"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Maximum Attainable Data Rate Downstream.
       The maximum downstream net data rate currently attainable by
       the xTU-C transmitter and the xTU-R receiver, coded in
       bit/s."
   REFERENCE    "ITU-T G.997.1, paragraph #*******9 (ATTNDRds)"
   DEFVAL       { 0 }
   ::= { xdsl2LineEntry 20 }

xdsl2LineStatusAttainableRateUs  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "bits/second"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Maximum Attainable Data Rate Upstream.
       The maximum upstream net data rate currently attainable by the
       xTU-R transmitter and the xTU-C receiver, coded in bit/s."
   REFERENCE    "ITU-T G.997.1, paragraph #******** (ATTNDRus)"
   DEFVAL       { 0 }
   ::= { xdsl2LineEntry 21 }

xdsl2LineStatusActPsdDs OBJECT-TYPE
   SYNTAX      Integer32 (-900..0 | 2147483647)
   UNITS       "0.1 dBm/Hz"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Actual Power Spectral Density (PSD) Downstream.  The average
       downstream transmit PSD over the subcarriers used for downstream.
       It ranges from -900 to 0 units of 0.1 dBm/Hz (physical values are
       -90 to 0 dBm/Hz).
       A value of 0x7FFFFFFF (2147483647) indicates the measurement is
       out of range to be represented."
   REFERENCE    "ITU-T G.997.1, paragraph #******** (ACTPSDds)"
   DEFVAL       { 2147483647 }
   ::= { xdsl2LineEntry 22 }

xdsl2LineStatusActPsdUs OBJECT-TYPE
   SYNTAX      Integer32 (-900..0 | 2147483647)
   UNITS       "0.1 dBm/Hz"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Actual Power Spectral Density (PSD) Upstream.  The average
       upstream transmit PSD over the subcarriers used for upstream.
       It ranges from -900 to 0 units of 0.1 dBm/Hz (physical values are
       -90 to 0 dBm/Hz).
       A value of 0x7FFFFFFF (2147483647) indicates the measurement is
       out of range to be represented."
   REFERENCE    "ITU-T G.997.1, paragraph #******** (ACTPSDus)"
   DEFVAL       { 2147483647 }
   ::= { xdsl2LineEntry 23 }

xdsl2LineStatusActAtpDs  OBJECT-TYPE
   SYNTAX      Integer32 (-310..310 | 2147483647)
   UNITS       "0.1 dBm"

   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Actual Aggregate Transmit Power Downstream.
       The total amount of transmit power delivered by the xTU-C at
       the U-C reference point, at the instant of measurement.  It
       ranges from -310 to 310 units of 0.1 dBm (physical values are -31
       to 31 dBm).
       A value of 0x7FFFFFFF (2147483647) indicates the measurement is
       out of range to be represented."
   REFERENCE    "ITU-T G.997.1, paragraph #******** (ACTATPds)"
   DEFVAL       { 2147483647 }
   ::= { xdsl2LineEntry 24 }

xdsl2LineStatusActAtpUs  OBJECT-TYPE
   SYNTAX      Integer32 (-310..310 | 2147483647)
   UNITS       "0.1 dBm"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Actual Aggregate Transmit Power Upstream.
       The total amount of transmit power delivered by the xTU-R at the
       U-R reference point, at the instant of measurement.  It ranges
       from -310 to 310 units of 0.1 dBm (physical values are -31
       to 31 dBm).
       A value of 0x7FFFFFFF (2147483647) indicates the measurement is
       out of range to be represented."
   REFERENCE    "ITU-T G.997.1, paragraph #******** (ACTATPus)"
   DEFVAL       { 2147483647 }
   ::= { xdsl2LineEntry 25 }

xdsl2LineStatusActProfile  OBJECT-TYPE
   SYNTAX      Xdsl2LineProfiles
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The G.993.2 profile in use.
       The configuration parameter xdsl2LConfProfProfiles defines
       the set of allowed G.993.2 profiles.  This parameter indicates
       the profile in use on this line.
       This parameter may be derived from the handshaking procedures
       defined in ITU-T Recommendation G.994.1."
   REFERENCE    "ITU-T G.997.1, paragraph #******* (VDSL2 Profile)"
   DEFVAL       { {} }
   ::= { xdsl2LineEntry 26 }

xdsl2LineStatusActLimitMask  OBJECT-TYPE
   SYNTAX      Xdsl2LineLimitMask

   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The Limit PSD mask and band plan in use.
       The configuration parameter xdsl2LConfProfLimitMask defines
       the set of allowed G.993.2 limit PSD masks.
       This parameter indicates the limit PSD mask in use on this
       line."
   REFERENCE    "ITU-T G.997.1, paragraph #*******
                (VDSL2 Limit PSD Mask and Band plan)"
   DEFVAL       { {} }
   ::= { xdsl2LineEntry 27 }

xdsl2LineStatusActUs0Mask  OBJECT-TYPE
   SYNTAX      Xdsl2LineUs0Mask
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The US0 PSD mask in use.
       The configuration parameter xdsl2LConfProfUs0Mask defines
       the set of allowed US0 PSD masks.
       This parameter indicates the US0 PSD mask in use on this line.
       This parameter may be derived from the handshaking procedures
       defined in ITU-T Recommendation G.994.1."
   REFERENCE    "ITU-T G.997.1, paragraph #*******
                (VDSL2 US0 PSD Mask)"
   DEFVAL       { {} }
   ::= { xdsl2LineEntry 28 }

xdsl2LineStatusActSnrModeDs  OBJECT-TYPE
   SYNTAX      Xdsl2LineSnrMode
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "This parameter indicates if the transmitter-referred
       virtual noise is active on the line in the downstream
       direction.
       The configuration parameter xdsl2LConfProfSnrModeDs is used to
       configure referred virtual noise."
   REFERENCE    "ITU-T G.997.1, paragraph #*******5 (ACTSNRMODEds)"
   DEFVAL       { virtualNoiseDisabled }
   ::= { xdsl2LineEntry 29 }

xdsl2LineStatusActSnrModeUs  OBJECT-TYPE
   SYNTAX      Xdsl2LineSnrMode
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION

      "This parameter indicates if the transmitter-referred virtual
       noise is active on the line in the upstream direction.
       The configuration parameter xdsl2LConfProfSnrModeUs is used to
       configure referred virtual noise."
   REFERENCE    "ITU-T G.997.1, paragraph #*******8 (ACTSNRMODEus)"
   DEFVAL       { virtualNoiseDisabled }
   ::= { xdsl2LineEntry 30 }

xdsl2LineStatusElectricalLength  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..1280)
   UNITS       "0.1 dB"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "This parameter contains the estimated electrical length
       expressed in dB at 1 MHz, kl0.  This is the final electrical
       length that would have been sent from the VTU-O to VTU-R if the
       electrical length was not forced by the CO-MIB.
       The value ranges from 0 to 128 dB in steps of 0.1 dB."
   REFERENCE    "ITU-T G.997.1, paragraph #*******3 (UPBOKLE)"
   DEFVAL       { 0 }
   ::= { xdsl2LineEntry 31 }

xdsl2LineStatusTssiDs  OBJECT-TYPE
     SYNTAX      Xdsl2Tssi
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "The transmit spectrum shaping (TSSi) breakpoints expressed
      as the set of breakpoints exchanged
      during G.994.1 (Downstream)."
   REFERENCE  "ITU-T G.997.1, paragraph #********.5 (TSSpsds)"
     ::= { xdsl2LineEntry 32 }

xdsl2LineStatusTssiUs  OBJECT-TYPE
     SYNTAX      Xdsl2Tssi
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "The transmit spectrum shaping (TSSi) breakpoints expressed
      as the set of breakpoints exchanged
      during G.994.1 (Upstream)."
   REFERENCE  "ITU-T G.997.1, paragraph #********.6 (TSSpsus)"
     ::= { xdsl2LineEntry 33 }

xdsl2LineStatusMrefPsdDs  OBJECT-TYPE
     SYNTAX      Xdsl2MrefPsdDs
     MAX-ACCESS  read-only

     STATUS      current
     DESCRIPTION
     "The MEDLEY Reference PSD status parameters
      in the downstream
      direction expressed as the set of breakpoints exchanged at
      initialization."
   REFERENCE  "ITU-T G.997.1, paragraph #********.7 (MREFPSDds)"
     ::= { xdsl2LineEntry 34 }

xdsl2LineStatusMrefPsdUs  OBJECT-TYPE
     SYNTAX      Xdsl2MrefPsdUs
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "The MEDLEY Reference PSD status parameters in the
      upstream direction expressed as the set of breakpoints
      exchanged at initialization."
   REFERENCE  "ITU-T G.997.1, paragraph #********.8 (MREFPSDus)"
     ::= { xdsl2LineEntry 35 }

xdsl2LineStatusTrellisDs  OBJECT-TYPE
   SYNTAX      TruthValue
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "This parameter reports whether trellis coding is in use in
      the downstream direction."
   REFERENCE    "ITU-T G.997.1, paragraph #*******0 (TRELLISds)"
   DEFVAL       { false }
   ::= { xdsl2LineEntry 36 }

xdsl2LineStatusTrellisUs  OBJECT-TYPE
   SYNTAX      TruthValue
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "This parameter reports whether trellis coding is in use in
      the upstream direction."
   REFERENCE    "ITU-T G.997.1, paragraph #*******1 (TRELLISus)"
   DEFVAL       { false }
   ::= { xdsl2LineEntry 37 }

xdsl2LineStatusActualCe  OBJECT-TYPE
   SYNTAX      Unsigned32 (2..16)
   UNITS       "N/32 samples"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION

      "(ACTUALCE)
       This parameter reports the cyclic extension used on the line.  It
       is coded as an unsigned integer from 2 to 16 in units of N/32
       samples, where 2N is the Inverse Discrete Fourier Transform
       (IDFT) size."
   REFERENCE    "ITU-T G.997.1, paragraph #*******2 (ACTUALCE)"
   DEFVAL       { 2 }
   ::= { xdsl2LineEntry 38 }

------------------------------------------------
--          xdsl2LineSegmentTable             --
------------------------------------------------

xdsl2LineSegmentTable  OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2LineSegmentEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2LineSegmentTable contains status parameters
       of VDSL2/ADSL/ADSL2 and ADSL2+ subcarriers.
       The parameters in this table are updated when a measurement
       request is made using the xdsl2LineCmndConfBpsc object.

       Note that a bits-per-subcarrier measurement is also performed
       during a line diagnostic procedure.  This table provides an
       additional mechanism to fetch the bits-per-subcarrier data.  This
       additional mechanism is provided so that bits-per-subcarrier
       data may be fetched without forcing the line into no power state.
       This is useful because the bits-per-subcarrier allocation may be
       adjusted at Showtime due to rate adaption and bit swapping.

       The implementation of this additional mechanism for measuring
       bits per subcarrier is not mandatory."
   ::= { xdsl2Status 1 }

xdsl2LineSegmentEntry  OBJECT-TYPE
   SYNTAX      Xdsl2LineSegmentEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2LineSegmentEntry contains status parameters
       of VDSL2/ADSL/ADSL2 and ADSL2+ subcarriers.

       Objects in the table refer to NSus and NSds.  For G.993.2, the
       value of NSus and NSds are, respectively, the indices of the
       highest supported upstream and downstream subcarriers according
       to the selected implementation profile.  For ADSL, NSus is equal
       to NSCus-1 and NSds is equal to NSCds-1.

       One index of this table is an interface index where the interface
       has an ifType of vdsl2(251).  A second index of this table is the
       transmission direction.  A third index identifies the specific
       segment of the subcarriers status addressed."
   INDEX  { ifIndex,
            xdsl2LineSegmentDirection,
            xdsl2LineSegment   }
   ::= { xdsl2LineSegmentTable 1 }

Xdsl2LineSegmentEntry  ::=
   SEQUENCE {
      xdsl2LineSegmentDirection         Xdsl2Direction,
      xdsl2LineSegment                  Unsigned32,
      xdsl2LineSegmentBitsAlloc         Xdsl2BitsAlloc,
      xdsl2LineSegmentRowStatus         RowStatus
   }

xdsl2LineSegmentDirection  OBJECT-TYPE
     SYNTAX      Xdsl2Direction
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
     "The direction of the subcarrier either
      upstream or downstream."
     ::= { xdsl2LineSegmentEntry 1 }

xdsl2LineSegment  OBJECT-TYPE
     SYNTAX      Unsigned32(1..8)
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
     "The segment of the subcarriers status information
      provided by this row.
      Status parameters in this table are retrieved in segments.
      The first segment of the status information is retrieved with
      xdsl2LineSegment=1, the second segment is retrieved with
      xdsl2LineSegment=2, and so on.  When a status parameter is
      retrieved in n segments where n<8) then, for that parameter,
      GET operations for the remaining segment numbers (n+1 to 8) will
      respond with a zero-length OCTET STRING."
     ::= { xdsl2LineSegmentEntry 2 }

xdsl2LineSegmentBitsAlloc  OBJECT-TYPE
     SYNTAX      Xdsl2BitsAlloc
     UNITS       "bits"
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION

     "The bits allocation per subcarrier.  An array of 256 octets
      (512 nibbles), designed for supporting up to 512 (downstream)
      subcarriers.  When more than 512 subcarriers are supported, the
      status information is reported through multiple (up to 8)
      segments.  The first segment is then used for the first 512
      subcarriers.  The second segment is used for the subcarriers
      512 to 1023 and so on.
      The aggregate number of utilized nibbles in the downstream
      direction (in all segments) depends on NSds; in the
      upstream direction, it depends on NSus.
      This value is referred to here as NS.  The segment number is in
      xdsl2SCStatusSegment.
      Nibble i (0 <= i < MIN((NS+1)-(segment-1)*512,512)) in each
      segment is set to a value in the range 0 to 15 to indicate that
      the respective downstream or upstream subcarrier j
      (j=(segement-1)*512+i) has the same amount of bits
      allocation."
   REFERENCE    "ITU-T G.997.1, paragraph #********.1 (BITSpsds)
                 and paragraph #********.2 (BITSpsus)"
     ::= { xdsl2LineSegmentEntry 3 }

xdsl2LineSegmentRowStatus  OBJECT-TYPE
     SYNTAX      RowStatus
     MAX-ACCESS  read-write
     STATUS      current
     DESCRIPTION
     "Row Status.  The SNMP agent will create a row in this table
      for storing the results of a measurement performed on the
      associated line, if the row does not already exist.

      The SNMP manager is not permitted to create rows in this table or
      set the row status to 'notInService'.  In the first case,
      if the SNMP manager tries to create a new row, the SNMP agent
      responds with the value 'noCreation' in the error status
      field of the response-PDU.  In the latter case, the SNMP agent
      responds with the value 'wrongValue' in the error status
      field of the response-PDU.

      The SNMP agent may have limited resources; therefore, if multiple
      rows coexist in this table, it may fail to add new rows to this
      table or allocate memory resources.
      If that occurs, the SNMP agent responds with the value
      'noResources' (for the xdsl2LineCmndConfBpscFailReason
      object in xdsl2LineTable).

      The management system (the operator) may delete rows from this
      table according to any scheme.  For example, after retrieving
      the results.

      When the SNMP manager deletes any row in this table, the SNMP
      agent MUST delete all rows in this table that have the same
      ifIndex value."
     ::= { xdsl2LineSegmentEntry 4 }

------------------------------------------------
--          xdsl2LineBandTable                --
------------------------------------------------

xdsl2LineBandTable  OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2LineBandEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2LineBandTable contains the, per-band line
       status parameters of the VDSL2/ADSL/ADSL2 or ADSL2+ line.
       The parameters in this table are updated at line initialization
       time and at Showtime."
   ::= { xdsl2Line 2 }

xdsl2LineBandEntry  OBJECT-TYPE
   SYNTAX      Xdsl2LineBandEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
       "One index of this table is an interface index where the
       interface
       has an ifType of vdsl2(251).  A second index of this table is a
       per-band index covering both VDSL2 and ADSL/ADSL2/ADSL2+."
   INDEX  { ifIndex, xdsl2LineBand }
   ::= { xdsl2LineBandTable 1 }

Xdsl2LineBandEntry  ::=
   SEQUENCE {
      xdsl2LineBand                        Xdsl2Band,
      xdsl2LineBandStatusLnAtten           Unsigned32,
      xdsl2LineBandStatusSigAtten          Unsigned32,
      xdsl2LineBandStatusSnrMargin         Integer32
   }

xdsl2LineBand OBJECT-TYPE
     SYNTAX      Xdsl2Band
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
     "Identifies the band(s) associated with this line.
      For ADSL/ADSL2/ADSL2+, the values 'upstream' and 'downstream'
      will always be present.

      For VDSL2, a subset of {'us0', 'ds1', 'us1' ... 'ds4', 'us4' }
      will always be present, together with rows for
      'upstream' and 'downstream', in which only the
      xdsl2LineBandStatusSnrMargin object is expected to hold a valid
      (average) measurement."
     ::= { xdsl2LineBandEntry 1 }

xdsl2LineBandStatusLnAtten  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..1270 | 2147483646 | 2147483647)
   UNITS       "0.1 dB"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Line Attenuation.
       When referring to a band in the downstream direction, it is
       the measured difference in the total power transmitted by the
       xTU-C and the total power received by the xTU-R over all
       subcarriers of that band during initialization.

       When referring to a band in the upstream direction, it is the
       measured difference in the total power transmitted by the xTU-R
       and the total power received by the xTU-C over all subcarriers of
       that band during initialization.

       Values range from 0 to 1270 in units of 0.1 dB (physical values
       are 0 to 127 dB).
       A special value of 0x7FFFFFFF (2147483647) indicates the line
       attenuation is out of range to be represented.
       A special value of 0x7FFFFFFE (2147483646) indicates the line
       attenuation measurement is unavailable."
   REFERENCE    "ITU-T G.997.1, paragraph #******* (LATNds)
               and paragraph #*******0 (LATNus)6"
   DEFVAL       { 2147483646 }
   ::= { xdsl2LineBandEntry 2 }

xdsl2LineBandStatusSigAtten  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..1270 | 2147483646 | 2147483647)
   UNITS       "0.1 dB"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Signal Attenuation.
       When referring to a band in the downstream direction, it is
       the measured difference in the total power transmitted by the
       xTU-C and the total power received by the xTU-R over all
       subcarriers of that band during Showtime.

       When referring to a band in the upstream direction, it is the

       measured difference in the total power transmitted by the xTU-R
       and the total power received by the xTU-C over all subcarriers of
       that band during Showtime.

       Values range from 0 to 1270 in units of 0.1 dB (physical values
       are 0 to 127 dB).
       A special value of 0x7FFFFFFF (2147483647) indicates the line
       attenuation is out of range to be represented.
       A special value of 0x7FFFFFFE (2147483646) indicates the line
       attenuation measurement is unavailable."
   REFERENCE    "ITU-T G.997.1, paragraph #*******1 (SATNds)
                 and paragraph #*******2 (SATNus)"
   DEFVAL       { 2147483646 }
   ::= { xdsl2LineBandEntry 3 }

xdsl2LineBandStatusSnrMargin  OBJECT-TYPE
   SYNTAX      Integer32 (-640..630 | 2147483646 | 2147483647)
   UNITS       "0.1 dB"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "SNR Margin is the maximum increase in dB of the noise power
       received at the xTU (xTU-R for a band in the downstream direction
       and xTU-C for a band in the upstream direction), such that the
       BER requirements are met for all bearer channels received at the
       xTU.  Values range from -640 to 630 in units of 0.1 dB (physical
       values are -64 to 63 dB).
       A special value of 0x7FFFFFFF (2147483647) indicates the SNR
       Margin is out of range to be represented.
       A special value of 0x7FFFFFFE (2147483646) indicates the SNR
       Margin measurement is currently unavailable."
   REFERENCE    "ITU-T G.997.1, paragraph #*******3 (SNRMds)
                 and paragraph #*******4 (SNRMpbds)
                 and paragraph #*******6 (SNRMus)
                 and paragraph #*******7 (SNRMpbus)"
   DEFVAL       { 2147483646 }
   ::= { xdsl2LineBandEntry 4 }

------------------------------------------------
--        xdsl2ChannelStatusTable             --
------------------------------------------------

xdsl2ChannelStatusTable  OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2ChannelStatusEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2ChannelStatusTable contains status

       parameters of VDSL2/ADSL/ADSL2 or ADSL2+ channel.
       This table contains live data from equipment."
   ::= { xdsl2Status 2 }

xdsl2ChannelStatusEntry  OBJECT-TYPE
   SYNTAX      Xdsl2ChannelStatusEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
       "One index of this table is an interface index where the
        interface has an ifType of a DSL channel.  A second index of
        this table is the termination unit."
   INDEX  { ifIndex, xdsl2ChStatusUnit }
   ::= { xdsl2ChannelStatusTable 1 }

Xdsl2ChannelStatusEntry  ::=
   SEQUENCE {
      xdsl2ChStatusUnit                Xdsl2Unit,
      xdsl2ChStatusActDataRate         Unsigned32,
      xdsl2ChStatusPrevDataRate        Unsigned32,
      xdsl2ChStatusActDelay            Unsigned32,
      xdsl2ChStatusActInp              Unsigned32,
      xdsl2ChStatusInpReport           Xdsl2ChInpReport,
      xdsl2ChStatusNFec                Unsigned32,
      xdsl2ChStatusRFec                Unsigned32,
      xdsl2ChStatusLSymb               Unsigned32,
      xdsl2ChStatusIntlvDepth          Unsigned32,
      xdsl2ChStatusIntlvBlock          Unsigned32,
      xdsl2ChStatusLPath               Unsigned32,
      xdsl2ChStatusAtmStatus           Xdsl2ChAtmStatus,
      xdsl2ChStatusPtmStatus           Xdsl2ChPtmStatus
   }

xdsl2ChStatusUnit  OBJECT-TYPE
   SYNTAX      Xdsl2Unit
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The termination unit."
   ::= { xdsl2ChannelStatusEntry 1 }

xdsl2ChStatusActDataRate  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "bits/second"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The actual net data rate at which the bearer channel is

       operating, if in L0 power management state.  In L1 or L2
       states, it relates to the previous L0 state.  The data rate is
       coded in bit/s."
   REFERENCE    "ITU-T G.997.1, paragraph #*******
                (Actual data rate)"
   DEFVAL       { 0 }
   ::= { xdsl2ChannelStatusEntry 2 }

xdsl2ChStatusPrevDataRate  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "bits/second"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The previous net data rate that the bearer channel was
       operating at just before the latest rate change event.  This
       could be a full or short initialization, fast retrain, DRA or
       power management transitions, excluding transitions between L0
       state and L1 or L2 states.  The data rate is coded in
       bit/s."
   REFERENCE    "ITU-T G.997.1, paragraph #*******
                (Previous data rate)"
   DEFVAL       { 0 }
   ::= { xdsl2ChannelStatusEntry 3 }

xdsl2ChStatusActDelay  OBJECT-TYPE
   SYNTAX      Unsigned32(0..8176)
   UNITS       "milliseconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The actual one-way interleaving delay introduced by the
       PMS-TC in the direction of the bearer channel, if in L0 power
       management state.  In L1 or L2 states, it relates to the previous
       L0 state.  It is coded in ms (rounded to the nearest ms)."
   REFERENCE    "ITU-T G.997.1, paragraph #*******
                (Actual interleaving delay)"
   DEFVAL       { 0 }
   ::= { xdsl2ChannelStatusEntry 4 }

xdsl2ChStatusActInp  OBJECT-TYPE
   SYNTAX      Unsigned32(0..255)
   UNITS       "0.1 symbols"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Actual impulse noise protection.
       This parameter reports the actual impulse noise protection (INP)

       on the bearer channel in the L0 state.  In the L1 or L2 state,
       the parameter contains the INP in the previous L0 state.  For
       ADSL, this value is computed according to the formula specified
       in the relevant Recommendation based on the actual framing
       parameters.  For ITU-T Recommendation G.993.2, the method to
       report this value is according to the INPREPORT parameter.
       The value is coded in fractions of DMT symbols with a
       granularity of 0.1 symbols.  The range is from 0 to 25.4.
       The special value of 255 indicates an ACTINP higher
       than 25.4."
   REFERENCE    "ITU-T G.997.1, paragraph #******* (ACTINP)"
   DEFVAL       { 0 }
   ::= { xdsl2ChannelStatusEntry 5 }

xdsl2ChStatusInpReport  OBJECT-TYPE
   SYNTAX      Xdsl2ChInpReport
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Impulse noise protection reporting mode."
   REFERENCE    "ITU-T G.997.1 Amendment 1, paragraph #*******
                (INPREPORT)"
   DEFVAL       { inpComputedUsingFormula }
   ::= { xdsl2ChannelStatusEntry 6 }

xdsl2ChStatusNFec  OBJECT-TYPE
   SYNTAX      Unsigned32(0..255)
   UNITS       "bytes"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Actual size of Reed-Solomon codeword.
       This parameter reports the actual number of Reed-Solomon
       redundancy bytes per codeword used in the latency path in which
       the bearer channel is transported.  The value is coded in bytes.
       It ranges from 0 to 16.
       The value 0 indicates no Reed-Solomon coding."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1 (NFEC)"
   DEFVAL       { 0 }
   ::= { xdsl2ChannelStatusEntry 7 }

xdsl2ChStatusRFec  OBJECT-TYPE
   SYNTAX      Unsigned32(0..16)
   UNITS       "bits"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Actual number of Reed-Solomon redundancy bytes.

       This parameter reports the actual number of Reed-Solomon
       redundancy bytes per codeword used in the latency path in which
       the bearer channel is transported.  The value is coded in bytes.
       It ranges from 0 to 16.
       The value 0 indicates no Reed-Solomon coding."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.2 (RFEC)"
   DEFVAL       { 0 }
   ::= { xdsl2ChannelStatusEntry 8 }

xdsl2ChStatusLSymb  OBJECT-TYPE
   SYNTAX      Unsigned32(0..65535)
   UNITS       "bits"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Actual number of bits per symbol.
       This parameter reports the actual number of bits per symbol
       assigned to the latency path in which the bearer channel is
       transported.  This value does not include trellis overhead.  The
       value is coded in bits.
       It ranges from 0 to 65535."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.3 (LSYMB)"
   DEFVAL       { 0 }
   ::= { xdsl2ChannelStatusEntry 9 }

xdsl2ChStatusIntlvDepth  OBJECT-TYPE
   SYNTAX      Unsigned32(1..4096)
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Actual interleaving depth.
       This parameter reports the actual depth of the interleaver used
       in the latency path in which the bearer channel is transported.
       The value ranges from 1 to 4096 in steps of 1.
       The value 1 indicates no interleaving."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4 (INTLVDEPTH)"
   DEFVAL       { 1 }
   ::= { xdsl2ChannelStatusEntry 10 }

xdsl2ChStatusIntlvBlock  OBJECT-TYPE
   SYNTAX      Unsigned32(4..255)
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Actual interleaving block length.
       This parameter reports the actual block length of the interleaver
       used in the latency path in which the bearer channel is
       transported.

       The value ranges from 4 to 255 in steps of 1."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.5 (INTLVBLOCK)"
   DEFVAL       { 4 }
   ::= { xdsl2ChannelStatusEntry 11 }

xdsl2ChStatusLPath  OBJECT-TYPE
   SYNTAX      Unsigned32(0..3)
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Actual latency path.
       This parameter reports the index of the actual latency path in
       which the bearer is transported.
       The valid values are 0, 1, 2 and 3.
       For G.992.1, the FAST path shall be mapped to the latency
       index 0, and the INTERLEAVED path shall be mapped to the latency
       index 1."
   REFERENCE    "ITU-T G.997.1 amendment 1, paragraph #*******
                 (LPATH)"
   DEFVAL       { 0 }
   ::= { xdsl2ChannelStatusEntry 12 }

xdsl2ChStatusAtmStatus  OBJECT-TYPE
   SYNTAX      Xdsl2ChAtmStatus
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Indicates current state (existing failures) of the DSL
       channel in case its Data Path is ATM.  This is a bitmap of
       possible conditions.
       In case the channel is not of ATM Data Path, the object is set
       to '0'."
   REFERENCE    "ITU-T G.997.1, paragraph #7.1.4
                 (ATM data path failures)"
   DEFVAL       { { noDefect } }
   ::= { xdsl2ChannelStatusEntry 13 }

xdsl2ChStatusPtmStatus  OBJECT-TYPE
   SYNTAX      Xdsl2ChPtmStatus
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Indicates current state (existing failures) of the DSL
       channel in case its Data Path is PTM (Packet Transfer Mode).
       This is a bitmap of possible conditions.
      In case the channel is not of PTM Data Path, the object is set
      to '0'."
   REFERENCE    "ITU-T G.997.1, paragraph #7.1.5

                 (PTM Data Path failures)"
   DEFVAL       { { noDefect } }
   ::= { xdsl2ChannelStatusEntry 14 }

------------------------------------------------
--    Scalars that relate to the SC Status Tables
------------------------------------------------

xdsl2ScalarSCMaxInterfaces  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "This value determines the maximum number of
       interfaces supported by xdsl2SCStatusTable,
       xdsl2SCStatusBandTable, and xdsl2SCStatusSegmentTable."
   ::= { xdsl2ScalarSC 1 }

xdsl2ScalarSCAvailInterfaces  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "This value determines the currently available number of
       interfaces listed in xdsl2SCStatusTable,
       xdsl2SCStatusBandTable, and xdsl2SCStatusSegmentTable."
   ::= { xdsl2ScalarSC 2 }

------------------------------------------------
--        xdsl2SCStatusTable                  --
------------------------------------------------

xdsl2SCStatusTable  OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2SCStatusEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2SCStatusTable contains
       status parameters for VDSL2/ADSL/ADSL2 and ADSL2+ that
       provide information about the size of parameters in
       xdsl2SCStatusSegmentTable.
       The parameters in this table MUST be updated after a loop
       diagnostic procedure, MAY be updated after a line
       initialization, and MAY be updated at Showtime."
   ::= { xdsl2Status 3 }

xdsl2SCStatusEntry  OBJECT-TYPE
   SYNTAX      Xdsl2SCStatusEntry

   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
       "One index of this table is an interface index where the
        interface has an ifType of vdsl2(251).  A second index of this
        table is the transmission direction."
   INDEX  { ifIndex, xdsl2SCStatusDirection }
   ::= { xdsl2SCStatusTable 1 }

Xdsl2SCStatusEntry  ::=
   SEQUENCE {
      xdsl2SCStatusDirection         Xdsl2Direction,
      xdsl2SCStatusLinScale          Unsigned32,
      xdsl2SCStatusLinScGroupSize    Unsigned32,
      xdsl2SCStatusLogMt             Unsigned32,
      xdsl2SCStatusLogScGroupSize    Unsigned32,
      xdsl2SCStatusQlnMt             Unsigned32,
      xdsl2SCStatusQlnScGroupSize    Unsigned32,
      xdsl2SCStatusSnrMtime          Unsigned32,
      xdsl2SCStatusSnrScGroupSize    Unsigned32,
      xdsl2SCStatusAttainableRate    Unsigned32,
      xdsl2SCStatusRowStatus         RowStatus
   }

xdsl2SCStatusDirection  OBJECT-TYPE
     SYNTAX      Xdsl2Direction
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
     "The direction of the subcarrier either
      upstream or downstream."
     ::= { xdsl2SCStatusEntry 1 }

xdsl2SCStatusLinScale  OBJECT-TYPE
     SYNTAX      Unsigned32 (1..65535)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "The scale factor to be applied to the H(f) linear
      representation values for the respective transmission direction.
      This parameter is only available after a loop diagnostic
      procedure.  It is represented as an unsigned integer in the range
      from 1 to 2^16-1."
   REFERENCE  "ITU-T G.997.1, paragraph #*******6.1 (HLINSCds)
               and paragraph #*******6.7 (HLINSCus)"
     ::= { xdsl2SCStatusEntry 2 }

xdsl2SCStatusLinScGroupSize OBJECT-TYPE

     SYNTAX      Unsigned32(1 | 2 | 4 | 8)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "Number of subcarriers per group used to report the H(f)
      linear representation values for the respective transmission
      direction.  The valid values are 1, 2, 4, and 8.  For ADSL, this
      parameter is equal to one and, for VDSL2, it is equal to the size
      of a subcarrier group used to compute these parameters.
      This parameter is only available after a loop diagnostic
      procedure."
   REFERENCE  "ITU-T G.997.1, paragraph #*******6.2 (HLINGds)
               and paragraph #*******6.8 (HLINGus)"
     ::= { xdsl2SCStatusEntry 3 }

xdsl2SCStatusLogMt  OBJECT-TYPE
     SYNTAX      Unsigned32 (1..65535)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "This parameter contains the number of symbols used to
      measure the Hlog(f) values.  It is represented as an unsigned
      integer in the range from 1 to 2^16-1.
      After a loop diagnostic procedure, this parameter shall contain
      the number of symbols used to measure the Hlog(f).  It should
      correspond to the value specified in the Recommendation (e.g., the
      number of symbols in 1 s time interval for ITU-T Recommendation.
      G.992.3)."
   REFERENCE  "ITU-T G.997.1, paragraph #*******6.4 (HLOGMTds)
               and paragraph #*******6.10 (HLOGMTus)"
     ::= { xdsl2SCStatusEntry 4 }

xdsl2SCStatusLogScGroupSize OBJECT-TYPE
     SYNTAX      Unsigned32(1 | 2 | 4 | 8)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "Number of subcarriers per group used to report the H(f)
      logarithmic representation values for the respective
      transmission direction.  The valid values are 1, 2, 4, and 8.
      For ADSL, this parameter is equal to 1, and for VDSL2, it is
      equal to the size of a subcarrier group used to compute these
      parameters."
   REFERENCE  "ITU-T G.997.1, paragraph #*******6.5 (HLOGGds)
               and paragraph #*******6.11 (HLOGGus)"
     ::= { xdsl2SCStatusEntry 5 }

xdsl2SCStatusQlnMt  OBJECT-TYPE

     SYNTAX      Unsigned32 (1..65535)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "This parameter contains the number of symbols used to
      measure the QLN(f) values.  It is an unsigned integer in the range
      from 1 to 2^16-1.  After a loop diagnostic procedure, this
      parameter shall contain the number of symbols used to measure the
      QLN(f).  It should correspond to the value specified in the
      Recommendation (e.g., the number of symbols in 1 s time interval
      for ITU-T Recommendation G.992.3)."
   REFERENCE  "ITU-T G.997.1, paragraph #*******7.1 (QLNMTds)
               and paragraph #*******7.4 (QLNMTus)"
     ::= { xdsl2SCStatusEntry 6 }

xdsl2SCStatusQlnScGroupSize OBJECT-TYPE
     SYNTAX      Unsigned32(1 | 2 | 4 | 8)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "Number of subcarriers per group used to report the Quiet
      Line Noise values for the respective transmission direction.
      The valid values are 1, 2, 4, and 8.
      For ADSL, this parameter is equal to 1, and for VDSL2, it is
      equal to the size of a subcarrier group used to compute these
      parameters."
   REFERENCE  "ITU-T G.997.1, paragraph #*******7.2 (QLNGds)
               and paragraph #*******7.5 (QLNGus)"
     ::= { xdsl2SCStatusEntry 7 }

xdsl2SCStatusSnrMtime  OBJECT-TYPE
     SYNTAX      Unsigned32 (1..65535)
     UNITS       "symbols"
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "This parameter contains the number of symbols used to measure
      the SNR(f) values.  It is an unsigned integer in the range from 1
      to 2^16-1.  After a loop diagnostic procedure, this parameter
      shall contain the number of symbols used to measure the SNR(f).
      It should correspond to the value specified in the Recommendation
      (e.g., the number of symbols in 1 s time interval for ITU-T
      Recommendation G.992.3)."
   REFERENCE    "ITU-T G.997.1, paragraph #*******8.1 (SNRMTds)
                 and paragraph #*******8.4 (SNRMTus)"
     ::= { xdsl2SCStatusEntry 8 }

xdsl2SCStatusSnrScGroupSize OBJECT-TYPE

     SYNTAX      Unsigned32(1 | 2 | 4 | 8)
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "Number of subcarriers per group used to report the SNR values
      on the respective transmission direction.
      The valid values are 1, 2, 4, and 8.
      For ADSL, this parameter is equal to 1, and for VDSL2, it is
      equal to the size of a subcarrier group used to compute these
      parameters."
   REFERENCE  "ITU-T G.997.1, paragraph #*******8.2 (SNRGds)
               and paragraph #*******8.5 (SNRGus)"
     ::= { xdsl2SCStatusEntry 9 }

xdsl2SCStatusAttainableRate  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "bits/second"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Maximum Attainable Data Rate.  The maximum net data rate
       currently attainable by the xTU-C transmitter and xTU-R receiver
       (when referring to downstream direction) or by the xTU-R
       transmitter and xTU-C receiver (when referring to upstream
       direction).  Value is coded in bits/s.
       This object reflects the value of the parameter following the
       most recent DELT performed on the associated line.  Once the DELT
       process is over, the parameter no longer changes until the row is
       deleted or a new DELT process is initiated."
   REFERENCE  "ITU-T G.997.1, paragraph #*******9 (ATTNDRds)
               and paragraph #******** (ATTNDRus)"
   ::= { xdsl2SCStatusEntry 10 }

xdsl2SCStatusRowStatus  OBJECT-TYPE
     SYNTAX      RowStatus
     MAX-ACCESS  read-write
     STATUS      current
     DESCRIPTION
     "Row Status.  The SNMP agent will create a row in this table
      for storing the results of a DELT performed on the associated
      line, if the row does not already exist.

      When a row is created in this table, the SNMP agent should also
      create corresponding rows in the tables xdsl2SCStatusBandTable and
      xdsl2SCStatusSegmentTable.

      The SNMP manager is not permitted to create rows in this table or
      set the row status to 'notInService'.  In the first case,

      if the SNMP manager tries to create a new row, the SNMP agent
      responds with the value 'noCreation' in the error status
      field of the response-PDU.  In the latter case the SNMP agent
      responds with the value 'wrongValue' in the error status
      field of the response-PDU.

      When a row is deleted in this table, the SNMP agent should also
      delete corresponding rows in the tables xdsl2SCStatusBandTable and
      xdsl2SCStatusSegmentTable.

      The SNMP agent may have limited resources; therefore, if multiple
      rows coexist in this table, it may fail to add new rows to this
      table or allocate memory resources for a new DELT process.  If
      that occurs, the SNMP agent responds with either the value
      'tableFull' or the value 'noResources' (for
      the xdsl2LineCmndConfLdsfFailReason object in xdsl2LineTable).

      The management system (the operator) may delete rows from this
      table according to any scheme.  For example, after retrieving the
      results."
     ::= { xdsl2SCStatusEntry 11 }

------------------------------------------------
--        xdsl2SCStatusBandTable              --
------------------------------------------------

xdsl2SCStatusBandTable  OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2SCStatusBandEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2SCStatusBandTable contains subcarrier status
       parameters for VDSL2/ADSL/ADSL2 and ADSL2+ that are grouped per-
       band.
       For ADSL/ADSL2/ADSL2+, there is a single upstream band and a
       single downstream band.  For VDSL2, there are several downstream
       bands and several upstream bands.
       The parameters in this table are only available after a loop
       diagnostic procedure."
   ::= { xdsl2Status 4 }

xdsl2SCStatusBandEntry  OBJECT-TYPE
   SYNTAX      Xdsl2SCStatusBandEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
       "One index of this table is an interface index where the
       interface

       has an ifType of vdsl2(251).  A second index of this table is the
       transmission band."
   INDEX  { ifIndex, xdsl2SCStatusBand }
   ::= { xdsl2SCStatusBandTable 1 }

Xdsl2SCStatusBandEntry  ::=
   SEQUENCE {
      xdsl2SCStatusBand                  Xdsl2Band,
      xdsl2SCStatusBandLnAtten           Unsigned32,
      xdsl2SCStatusBandSigAtten          Unsigned32
   }

xdsl2SCStatusBand OBJECT-TYPE
     SYNTAX      Xdsl2Band
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
     "The transmission band."
     ::= { xdsl2SCStatusBandEntry 1 }

xdsl2SCStatusBandLnAtten  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..1270 | 2147483646 | 2147483647)
   UNITS       "0.1 dB"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "When referring to a band in the downstream direction, it is
       the measured difference in the total power transmitted by the
       xTU-C and the total power received by the xTU-R over all
       subcarriers during diagnostics mode.
       When referring to a band in the upstream direction, it is the
       measured difference in the total power transmitted by the xTU-R
       and the total power received by the xTU-C over all subcarriers
       during diagnostics mode.
       It ranges from 0 to 1270 units of 0.1 dB (physical values are 0
       to 127 dB).
       A special value of 0x7FFFFFFF (2147483647) indicates the line
       attenuation is out of range to be represented.
       A special value of 0x7FFFFFFE (2147483646) indicates the line
       attenuation measurement is unavailable.
       This object reflects the value of the parameter following the
       most recent DELT performed on the associated line.  Once the DELT
       process is over, the parameter no longer changes until the row is
       deleted or a new DELT process is initiated."
   REFERENCE  "ITU-T G.997.1, paragraph #******* (LATNds)
               and paragraph #*******0 (LATNus)"
   DEFVAL       { 2147483646 }
   ::= { xdsl2SCStatusBandEntry 2 }

xdsl2SCStatusBandSigAtten  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..1270 | 2147483646 | 2147483647)
   UNITS       "0.1 dB"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "When referring to a band in the downstream direction, it is the
       measured difference in the total power transmitted by the xTU-C
       and the total power received by the xTU-R over all subcarriers
       during Showtime after the diagnostics mode.
       When referring to the upstream direction, it is the measured
       difference in the total power transmitted by the xTU-R and the
       total power received by the xTU-C over all subcarriers during
       Showtime after the diagnostics mode.
       It ranges from 0 to 1270 units of 0.1 dB (physical values are 0
       to 127 dB).
       A special value of 0x7FFFFFFF (2147483647) indicates the line
       attenuation is out of range to be represented.
       A special value of 0x7FFFFFFE (2147483646) indicates the line
       attenuation measurement is unavailable.
       This object reflects the value of the parameter following the
       most recent DELT performed on the associated line.  Once the DELT
       process is over, the parameter no longer changes until the row is
       deleted or a new DELT process is initiated."
   REFERENCE  "ITU-T G.997.1, paragraph #*******1 (SATNds)
               and paragraph #*******2 (SATNus)"
   DEFVAL       { 2147483646 }
   ::= { xdsl2SCStatusBandEntry 3 }

------------------------------------------------
--        xdsl2SCStatusSegmentTable           --
------------------------------------------------

xdsl2SCStatusSegmentTable  OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2SCStatusSegmentEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2SCStatusSegmentTable contains status
       parameters of VDSL2/ADSL/ADSL2 and ADSL2+ subcarriers.

       Several objects in the table refer to NSus and NSds.  For
       G.993.2, the value of NSus and NSds are, respectively, the
       indices of the highest supported upstream and downstream
       subcarriers according to the selected implementation profile.
       For ADSL, NSus is equal to NSCus-1 and NSds is equal to NSCds-1.

       The parameters in this table MUST be updated after a loop

       diagnostic procedure and MAY be updated after a line
       initialization and MAY be updated at Showtime."
   ::= { xdsl2Status 5 }

xdsl2SCStatusSegmentEntry  OBJECT-TYPE
   SYNTAX      Xdsl2SCStatusSegmentEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "One index of this table is an interface index where the
       interface has an ifType of vdsl2(251).  A second index of this
       table is the transmission direction.  A third index identifies
       the specific segment of the subcarriers status addressed."
   INDEX  { ifIndex,
            xdsl2SCStatusDirection,
            xdsl2SCStatusSegment   }
   ::= { xdsl2SCStatusSegmentTable 1 }

Xdsl2SCStatusSegmentEntry  ::=
   SEQUENCE {
      xdsl2SCStatusSegment                  Unsigned32,
      xdsl2SCStatusSegmentLinReal           OCTET STRING,
      xdsl2SCStatusSegmentLinImg            OCTET STRING,
      xdsl2SCStatusSegmentLog               OCTET STRING,
      xdsl2SCStatusSegmentQln               OCTET STRING,
      xdsl2SCStatusSegmentSnr               OCTET STRING,
      xdsl2SCStatusSegmentBitsAlloc         Xdsl2BitsAlloc,
      xdsl2SCStatusSegmentGainAlloc         OCTET STRING
   }

xdsl2SCStatusSegment  OBJECT-TYPE
     SYNTAX      Unsigned32(1..8)
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
     "The segment of the subcarriers status information provided by
      this row.
      Several status parameters in this table are retrieved in segments.
      The first segment of the status information is retrieved with
      xdsl2SCStatusSegment=1, the second segment is retrieved with
      xdsl2SCStatusSegment=2, and so on.  When any status parameter is
      retrieved in n segments where n<8), then for that parameter,
      GET operations for the remaining segment numbers (n+1 to 8) will
      respond with a zero-length OCTET STRING."
     ::= { xdsl2SCStatusSegmentEntry 1 }

xdsl2SCStatusSegmentLinReal  OBJECT-TYPE
     SYNTAX      OCTET STRING  (SIZE(0..1024))

     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "An array of up to 512 complex H(f) linear representation
      values in linear scale for the respective transmission direction.
      It is designed to support up to 512 (downstream) subcarrier
      groups and can be retrieved in a single segment.
      The number of utilized values in the downstream direction depends
      on NSds; in the upstream direction, it depends on NSus.  This
      value is referred to here as NS.
      Each array entry represents the real component (referred to here
      as a(i)) of Hlin(f = i*Df) value for a particular subcarrier
      group index i (0 <= i <= NS).
      Hlin(f) is represented as ((scale/2^15)*((a(i)+j*b(i))/2^15)),
      where scale is xdsl2SCStatusLinScale and a(i) and b(i)
      (provided by the xdsl2SCStatusSegmentLinImg object) are in the
      range (-2^15+1) to (+2^15-1).
      A special value a(i)=b(i)= -2^15 indicates that no measurement
      could be done for the subcarrier group because it is out of the
      passband or that the attenuation is out of range to be
      represented.  This parameter is only available after a loop
      diagnostic procedure.
      Each value in this array is 16 bits wide and is stored in big
      endian format."
   REFERENCE  "ITU-T G.997.1, paragraph #*******6.3 (HLINpsds)
               and paragraph #*******6.9 (HLINpsus)"
     ::= { xdsl2SCStatusSegmentEntry 2 }

xdsl2SCStatusSegmentLinImg  OBJECT-TYPE
     SYNTAX      OCTET STRING  (SIZE(0..1024))
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "An array of up to 512 complex H(f) linear representation
      values in linear scale for the respective transmission direction.
      It is designed to support up to 512 (downstream) subcarrier
      groups and can be retrieved in a single segment.
      The number of utilized values in the downstream direction depends
      on NSds; in the upstream direction, it depends on NSus.  This
      value is referred to here as NS.
      Each array entry represents the imaginary component (referred to
      here as b(i)) of Hlin(f = i*Df) value for a particular
      subcarrier group index i (0 <= i <= NS).
      Hlin(f) is represented as ((scale/2^15)*((a(i)+j*b(i))/2^15)),
      where scale is xdsl2SCStatusLinScale and a(i) (provided by the
      xdsl2SCStatusSegmentLinReal object) and b(i) are in the range
      (-2^15+1) to (+2^15-1).
      A special value a(i)=b(i)= -2^15 indicates that no measurement

      could be done for the subcarrier group because it is out of the
      passband or that the attenuation is out of range to be
      represented.  This parameter is only available after a loop
      diagnostic procedure.
      Each value in this array is 16 bits wide and is stored in big
      endian format."
   REFERENCE  "ITU-T G.997.1, paragraph #*******6.3 (HLINpsds)
               and paragraph #*******6.9 (HLINpsus)"
     ::= { xdsl2SCStatusSegmentEntry 3 }

xdsl2SCStatusSegmentLog  OBJECT-TYPE
     SYNTAX      OCTET STRING  (SIZE(0..1024))
     UNITS       "dB"
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "An array of up to 512 real H(f) logarithmic representation
      values in dB for the respective transmission direction.  It is
      designed to support up to 512 (downstream) subcarrier groups
      and can be retrieved in a single segment.
      The number of utilized values in the downstream direction depends
      on NSds; in the upstream direction, it depends on NSus.  This
      value is referred to here as NS.
      Each array entry represents the real Hlog(f = i*Df) value for a
      particular subcarrier group index i, (0 <= i <= NS).
      The real Hlog(f) value is represented as (6-m(i)/10), with m(i)
      in the range 0 to 1022.  A special value m=1023 indicates that
      no measurement could be done for the subcarrier group because
      it is out of the passband or that the attenuation is out of
      range to be represented.  This parameter is applicable in loop
      diagnostic procedure and initialization.
      Each value in this array is 16 bits wide and is stored in big
      endian format."
   REFERENCE  "ITU-T G.997.1, paragraph #*******6.6 (HLOGpsds)
               and paragraph #*******6.12 (HLOGpsus)"
     ::= { xdsl2SCStatusSegmentEntry 4 }

xdsl2SCStatusSegmentQln  OBJECT-TYPE
     SYNTAX      OCTET STRING  (SIZE(0..512))
     UNITS       "dBm/Hz"
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "An array of up to 512 real Quiet Line Noise values in dBm/Hz
      for the respective transmission direction.  It is designed for up
      to 512 (downstream) subcarrier groups and can be retrieved in a
      single segment.
      The number of utilized values in the downstream direction depends

      on NSds; in the upstream direction, it depends on NSus.  This
      value is referred to here as NS.
      Each array entry represents the QLN(f = i*Df) value for a
      particular subcarrier index i, (0 <= i <= NS).
      The QLN(f) is represented as ( -23-n(i)/2), with n(i) in the range
      0 to 254.  A special value n(i)=255 indicates that no measurement
      could be done for the subcarrier group because it is out of the
      passband or that the noise PSD is out of range to be represented.
      This parameter is applicable in loop diagnostic procedure and
      initialization.  Each value in this array is 8 bits wide."
   REFERENCE  "ITU-T G.997.1, paragraph #*******7.3 (QLNpsds)
               and paragraph #*******7.6 (QLNpsus)"
     ::= { xdsl2SCStatusSegmentEntry 5 }

xdsl2SCStatusSegmentSnr  OBJECT-TYPE
     SYNTAX      OCTET STRING  (SIZE(0..512))
     UNITS       "0.5 dB"
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "The SNR Margin per subcarrier group, expressing the ratio
      between the received signal power and received noise power per
      subscriber group.  It is an array of 512 octets, designed for
      supporting up to 512 (downstream) subcarrier groups and can be
      retrieved in a single segment.
      The number of utilized octets in the downstream direction depends
      on NSds; in the upstream direction, it depends on NSus.  This
      value is referred to here as NS.
      Octet i (0 <= i <= NS) is set to a value in the range 0 to
      254 to indicate that the respective downstream or upstream
      subcarrier group i has an SNR of:
      (-32 + xdsl2SCStatusSegmentSnr(i)/2) in dB (i.e., -32 to 95 dB).
      The special value 255 means that no measurement could be done for
      the subcarrier group because it is out of the PSD mask passband or
      that the noise PSD is out of range to be represented.  Each value
      in this array is 8 bits wide."
   REFERENCE    "ITU-T G.997.1, paragraph #*******8.3 (SNRpsds)
                 and paragraph #*******8.6 (SNRpsus)"
     ::= { xdsl2SCStatusSegmentEntry 6 }

xdsl2SCStatusSegmentBitsAlloc  OBJECT-TYPE
     SYNTAX      Xdsl2BitsAlloc
     UNITS       "bits"
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "The bits allocation per subcarrier.  An array of 256 octets
      (512 nibbles) designed for supporting up to 512 (downstream)

      subcarriers.  When more than 512 subcarriers are supported, the
      status information is reported through multiple (up to 8)
      segments.  The first segment is then used for the first 512
      subcarriers.  The second segment is used for the subcarriers
      512 to 1023 and so on.
      The aggregate number of utilized nibbles in the downstream
      direction (in all segments) depends on NSds; in the upstream
      direction, it depends on NSus.
      This value is referred to here as NS.  The segment number is in
      xdsl2SCStatusSegment.
      Nibble i (0 <= i < MIN((NS+1)-(segment-1)*512,512)) in each
      segment is set to a value in the range 0 to 15 to indicate that
      the respective downstream or upstream subcarrier j
      (j=(segement-1)*512+i) has the same amount of bits
      allocation."
   REFERENCE    "ITU-T G.997.1, paragraph #********.1 (BITSpsds)
                 and paragraph #********.2 (BITSpsus)"
     ::= { xdsl2SCStatusSegmentEntry 7 }

xdsl2SCStatusSegmentGainAlloc  OBJECT-TYPE
     SYNTAX      OCTET STRING  (SIZE(0..1024))
     MAX-ACCESS  read-only
     STATUS      current
     DESCRIPTION
     "The gain allocation per subcarrier.  An array of 512 16-bit
      values, designed for supporting up to 512 (downstream)
      subcarriers.  When more then 512 subcarriers are supported, the
      status information is reported through multiple (up to 8)
      segments.  The first segment is then used for the first 512
      subcarriers.  The second segment is used for the subcarriers 512
      to 1023 and so on.
      The aggregate number of utilized octets in the downstream
      direction depends on NSds; in the upstream direction, it depends
      on NSus.  This value is referred to here as NS.  The segment
      number is in xdsl2SCStatusSegment.
      Value i (0 <= i < MIN((NS+1)-(segment-1)*512,512)) in each
      segment is set to a value in the range 0 to 4093 to indicate that
      the respective downstream or upstream subcarrier j
      (j=(segement-1)*512+i) has the same amount of gain value.
      The gain value is represented as a multiple of 1/512 on a linear
      scale.  Each value in this array is 16 bits wide and is stored in
      big endian format."
   REFERENCE    "ITU-T G.997.1, paragraph #********.3 (GAINSpsds)
                 and paragraph #********.4 (GAINSpsus)"
     ::= { xdsl2SCStatusSegmentEntry 8 }

------------------------------------------------
--        xdsl2LineInventoryTable             --

------------------------------------------------

xdsl2LineInventoryTable  OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2LineInventoryEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2LineInventoryTable contains an inventory of the
       DSL termination unit."
   ::= { xdsl2Inventory 1 }

xdsl2LineInventoryEntry  OBJECT-TYPE
   SYNTAX      Xdsl2LineInventoryEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
       "One index of this table is an interface index where the
       interface
       has an ifType of vdsl2(251).  A second index of this table is the
       termination unit."
   INDEX  { ifIndex, xdsl2LInvUnit }
   ::= { xdsl2LineInventoryTable 1 }

Xdsl2LineInventoryEntry  ::=
   SEQUENCE {
      xdsl2LInvUnit                      Xdsl2Unit,
      xdsl2LInvG994VendorId              OCTET STRING,
      xdsl2LInvSystemVendorId            OCTET STRING,
      xdsl2LInvVersionNumber             OCTET STRING,
      xdsl2LInvSerialNumber              OCTET STRING,
      xdsl2LInvSelfTestResult            Unsigned32,
      xdsl2LInvTransmissionCapabilities  Xdsl2TransmissionModeType
   }

xdsl2LInvUnit  OBJECT-TYPE
   SYNTAX      Xdsl2Unit
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The termination unit."
   ::= { xdsl2LineInventoryEntry 1 }

xdsl2LInvG994VendorId  OBJECT-TYPE
   SYNTAX      OCTET STRING  (SIZE(8))
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The ADSL Transceiver Unit (ATU) G.994.1 Vendor ID as

       inserted in the G.994.1 CL/CLR message.
       It consists of 8 binary octets, including a country
       code followed by a (regionally allocated) provider code, as
       defined in Recommendation T.35."
   REFERENCE    "ITU-T G.997.1, paragraph #7.4.1-7.4.2"
   ::= { xdsl2LineInventoryEntry 2 }

xdsl2LInvSystemVendorId  OBJECT-TYPE
   SYNTAX      OCTET STRING  (SIZE(8))
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The ATU System Vendor ID (identifies the xTU system
       integrator) as inserted in the Overhead Messages (both xTUs for
       G.992.3, G.992.4, G.992.5, and G.993.2) or in the Embedded
       Operations Channel (xTU-R in G.992.1 and G.992.2).
       It consists of 8 binary octets, with same format as used for
       Xdsl2InvG994VendorId."
   REFERENCE    "ITU-T G.997.1, paragraph #7.4.3-7.4.4"
   ::= { xdsl2LineInventoryEntry 3 }

xdsl2LInvVersionNumber  OBJECT-TYPE
   SYNTAX      OCTET STRING  (SIZE(0..16))
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The xTU version number (vendor-specific information) as
       inserted in the Overhead Messages (both xTUs for G.992.3,
       G.992.4, G.992.5, and G.993.2) or in the Embedded Operations
       Channel (xTU-R in G.992.1 and G.992.2).  It consists of up to 16
       binary octets."
   REFERENCE    "ITU-T G.997.1, paragraph #7.4.5-7.4.6"
   ::= { xdsl2LineInventoryEntry 4 }

xdsl2LInvSerialNumber  OBJECT-TYPE
   SYNTAX      OCTET STRING  (SIZE(0..32))
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The xTU serial number (vendor-specific information) as
       inserted in the Overhead Messages (both xTUs for G.992.3,
       G.992.4, G.992.5, and G.993.2) or in the Embedded Operations
       Channel (xTU-R in G.992.1 and G.992.2).  It is vendor-specific
       information consisting of up to 32 ASCII characters."
   REFERENCE    "ITU-T G.997.1, paragraph #7.4.7-7.4.8"
   ::= { xdsl2LineInventoryEntry 5 }

xdsl2LInvSelfTestResult  OBJECT-TYPE

   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The xTU self-test result, coded as a 32-bit value.  The
       most significant octet of the result is '0' if the
       self-test passed, and '1' if the self-test failed.  The
       interpretation of the other octets is vendor discretionary."
   REFERENCE    "ITU-T G.997.1, paragraph #7.4.9-7.4.10"
   DEFVAL       { 0 }
   ::= { xdsl2LineInventoryEntry 6 }

xdsl2LInvTransmissionCapabilities  OBJECT-TYPE
   SYNTAX      Xdsl2TransmissionModeType
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The xTU transmission system capability list of the different
       coding types.  It is coded in a bitmap representation with 1 or
       more bits set.  A bit set to '1' means that the xTU
       supports the respective coding.  The value may be derived from
       the handshaking procedures defined in G.994.1.  A set of xDSL
       line transmission modes, with one bit per mode."
   REFERENCE    "ITU-T G.997.1, paragraph #7.4.11-7.4.12"
   ::= { xdsl2LineInventoryEntry 7 }

------------------------------------------------
--        xdsl2LineConfTemplateTable          --
------------------------------------------------

xdsl2LineConfTemplateTable  OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2LineConfTemplateEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2LineConfTemplateTable contains VDSL2/ADSL/
       ADSL2 and ADSL2+ line configuration templates.

       Note that this table is also used to configure the number of
       bearer channels.
       When the number of bearer channels is increased, the SNMP agent
       SHOULD create rows in all tables indexed by a channel index.
       When the number of bearer channels is decreased, the SNMP agent
       SHOULD delete rows in all tables indexed by a channel index.
       For example, if the value of xdsl2LConfTempChan4ConfProfile is
       set to a non-null value, then rows SHOULD be created in
       xdsl2ChannelStatusTable, xdsl2PMChCurrTable, and all other tables
       indexed by a channel index.

       For example, if the value of xdsl2LConfTempChan2ConfProfile is
       set to a null value, then rows SHOULD be deleted in
       xdsl2ChannelStatusTable, xdsl2PMChCurrTable, and all other
       tables indexed by a channel index.

       Entries in this table MUST be maintained in a persistent
       manner."
   ::= { xdsl2ProfileLine 1 }

xdsl2LineConfTemplateEntry  OBJECT-TYPE
   SYNTAX      Xdsl2LineConfTemplateEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "A default template with an index of 'DEFVAL' will always
       exist, and its parameters will be set to vendor-specific values,
       unless otherwise specified in this document."
   INDEX  { xdsl2LConfTempTemplateName }
   ::= { xdsl2LineConfTemplateTable 1 }

Xdsl2LineConfTemplateEntry  ::=
   SEQUENCE {
      xdsl2LConfTempTemplateName      SnmpAdminString,
      xdsl2LConfTempLineProfile       SnmpAdminString,
      xdsl2LConfTempChan1ConfProfile  SnmpAdminString,
      xdsl2LConfTempChan1RaRatioDs    Unsigned32,
      xdsl2LConfTempChan1RaRatioUs    Unsigned32,
      xdsl2LConfTempChan2ConfProfile  SnmpAdminString,
      xdsl2LConfTempChan2RaRatioDs    Unsigned32,
      xdsl2LConfTempChan2RaRatioUs    Unsigned32,
      xdsl2LConfTempChan3ConfProfile  SnmpAdminString,
      xdsl2LConfTempChan3RaRatioDs    Unsigned32,
      xdsl2LConfTempChan3RaRatioUs    Unsigned32,
      xdsl2LConfTempChan4ConfProfile  SnmpAdminString,
      xdsl2LConfTempChan4RaRatioDs    Unsigned32,
      xdsl2LConfTempChan4RaRatioUs    Unsigned32,
      xdsl2LConfTempRowStatus         RowStatus
   }

xdsl2LConfTempTemplateName  OBJECT-TYPE
   SYNTAX      SnmpAdminString (SIZE(1..32))
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "This object identifies a row in this table."
   REFERENCE    "DSL Forum TR-129, paragraph #5.4"
   ::= { xdsl2LineConfTemplateEntry 1 }

xdsl2LConfTempLineProfile  OBJECT-TYPE
   SYNTAX      SnmpAdminString (SIZE(1..32))
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The value of this object identifies the row in the
       VDSL2/ADSL/ADSL2 and ADSL2+ line configuration Profile Table
       (xdsl2LineConfProfTable) that applies for this DSL line."
   REFERENCE    "DSL Forum TR-129, paragraph #5.4"
   DEFVAL       { "DEFVAL" }
   ::= { xdsl2LineConfTemplateEntry 2 }

xdsl2LConfTempChan1ConfProfile  OBJECT-TYPE
   SYNTAX      SnmpAdminString (SIZE(1..32))
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The value of this object identifies the row in the VDSL2/
       ADSL/ADSL2 and ADSL2+ channel configuration Profile Table
       (xdsl2ChConfProfileTable) that applies to DSL bearer channel #1.
       The channel profile name specified here MUST match the name of an
       existing row in the xdsl2ChConfProfileTable table."
   DEFVAL       { "DEFVAL" }
   ::= { xdsl2LineConfTemplateEntry 3 }

xdsl2LConfTempChan1RaRatioDs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..100)
   UNITS       "percent"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Rate Adaptation Ratio.  The ratio (in percent) that should be
       taken into account for the bearer channel #1 when performing rate
       adaptation on Downstream.  The ratio refers to the available data
       rate in excess of the Minimum Data Rate, summed over all bearer
       channels.
       Also, the 100 - xdsl2LConfTempChan1RaRatioDs is the ratio of
       excess data rate to be assigned to all other bearer channels on
       Downstream direction.  The sum of rate adaptation ratios over all
       bearers on the same direction shall be equal to 100%."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4
                (Rate adaptation ratio)"
   DEFVAL       { 100 }
   ::= { xdsl2LineConfTemplateEntry 4 }

xdsl2LConfTempChan1RaRatioUs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..100)
   UNITS       "percent"

   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Rate Adaptation Ratio.  The ratio (in percent) that should be
       taken into account for the bearer channel #1 when performing
       rate adaptation on Upstream.  The ratio refers to the available
       data rate in excess of the Minimum Data Rate, summed over all
       bearer channels.
       Also, the 100 - xdsl2LConfTempChan1RaRatioUs is the ratio of
       excess data rate to be assigned to all other bearer channels on
       Upstream direction.  The sum of rate adaptation ratios over all
       bearers on the same direction shall be equal to 100%."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4
                 (Rate adaptation ratio)"
   DEFVAL       { 100 }
   ::= { xdsl2LineConfTemplateEntry 5 }

xdsl2LConfTempChan2ConfProfile  OBJECT-TYPE
   SYNTAX      SnmpAdminString (SIZE(0..32))
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The value of this object identifies the row in the VDSL2/
       ADSL/ADSL2 and ADSL2+ channel configuration Profile Table
       (xdsl2ChConfProfileTable) that applies to DSL bearer channel #2.
       If the channel is unused, then the object is set to a zero-length
       string.
       This object may be set to a zero-length string only if
       xdsl2LConfTempChan3ConfProfile contains a zero-length
       string."
   DEFVAL       { "" }
   ::= { xdsl2LineConfTemplateEntry 6 }

xdsl2LConfTempChan2RaRatioDs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..100)
   UNITS       "percent"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Rate Adaptation Ratio.  The ratio (in percent) that should be
       taken into account for the bearer channel #2 when performing
       rate adaptation on Downstream.  The ratio refers to the available
       data rate in excess of the Minimum Data Rate, summed over all
       bearer channels.
       Also, the 100 - xdsl2LConfTempChan2RaRatioDs is the ratio of
       excess data rate to be assigned to all other bearer channels on
       Downstream direction.  The sum of rate adaptation ratios over all
       bearers on the same direction shall be equal to

       100%."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4
                 (Rate adaptation ratio)"
   DEFVAL       { 0 }
   ::= { xdsl2LineConfTemplateEntry 7 }

xdsl2LConfTempChan2RaRatioUs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..100)
   UNITS       "percent"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Rate Adaptation Ratio.  The ratio (in percent) that should be
       taken into account for the bearer channel #2 when performing
       rate adaptation on Upstream.  The ratio refers to the available
       data rate in excess of the Minimum Data Rate, summed over all
       bearer channels.
       Also, the 100 - xdsl2LConfTempChan2RaRatioUs is the ratio of
       excess data rate to be assigned to all other bearer channels on
       Upstream direction.  The sum of rate adaptation ratios over all
       bearers on the same direction shall be equal to 100%."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4
                 (Rate adaptation ratio)"
   DEFVAL       { 0 }
   ::= { xdsl2LineConfTemplateEntry 8 }

xdsl2LConfTempChan3ConfProfile  OBJECT-TYPE
   SYNTAX      SnmpAdminString (SIZE(0..32))
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The value of this object identifies the row in the VDSL2/
       ADSL/ADSL2 and ADSL2+ channel configuration Profile Table
       (xdsl2ChConfProfileTable) that applies to DSL bearer channel #3.
       If the channel is unused, then the object is set to a zero-length
       string.
       This object may be set to a zero-length string only if
       xdsl2LConfTempChan4ConfProfile contains a zero-length string.
       This object may be set to a non-zero-length string only if
       xdsl2LConfTempChan2ConfProfile contains a non-zero-length
       string."
   DEFVAL       { "" }
   ::= { xdsl2LineConfTemplateEntry 9 }

xdsl2LConfTempChan3RaRatioDs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..100)
   UNITS       "percent"
   MAX-ACCESS  read-create

   STATUS      current
   DESCRIPTION
      "Rate Adaptation Ratio.  The ratio (in percent) that should be
       taken into account for the bearer channel #3 when performing
       rate adaptation on Downstream.  The ratio refers to the available
       data rate in excess of the Minimum Data Rate, summed over all
       bearer channels.
       Also, the 100 - xdsl2LConfTempChan3RaRatioDs is the ratio of
       excess data rate to be assigned to all other bearer channels on
       Downstream direction.  The sum of rate adaptation ratios over all
       bearers on the same direction shall be equal to 100%."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4
                 (Rate adaptation ratio)"
   DEFVAL       { 0 }
   ::= { xdsl2LineConfTemplateEntry 10 }

xdsl2LConfTempChan3RaRatioUs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..100)
   UNITS       "percent"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Rate Adaptation Ratio.  The ratio (in percent) that should be
       taken into account for the bearer channel #3 when performing
       rate adaptation on Upstream.  The ratio refers to the available
       data rate in excess of the Minimum Data Rate, summed over all
       bearer channels.
       Also, the 100 - xdsl2LConfTempChan3RaRatioUs is the ratio of
       excess data rate to be assigned to all other bearer channels on
       Upstream direction.  The sum of rate adaptation ratios over all
       bearers on the same direction shall be equal to 100%."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4
                 (Rate adaptation ratio)"
   DEFVAL       { 0 }
   ::= { xdsl2LineConfTemplateEntry 11 }

xdsl2LConfTempChan4ConfProfile  OBJECT-TYPE
   SYNTAX      SnmpAdminString (SIZE(0..32))
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The value of this object identifies the row in the VDSL2/
       ADSL/ADSL2 and ADSL2+ channel configuration Profile Table
       (xdsl2ChConfProfileTable) that applies to DSL bearer channel #4.
       If the channel is unused, then the object is set to a zero-length
       string.
       This object may be set to a non-zero-length string only if
       xdsl2LConfTempChan3ConfProfile contains a non-zero-length

       string."
   DEFVAL       { "" }
   ::= { xdsl2LineConfTemplateEntry 12 }

xdsl2LConfTempChan4RaRatioDs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..100)
   UNITS       "percent"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Rate Adaptation Ratio.  The ratio (in percent) that should be
       taken into account for the bearer channel #4 when performing rate
       adaptation on Downstream.  The ratio refers to the available data
       rate in excess of the Minimum Data Rate, summed over all bearer
       channels.
       Also, the 100 - xdsl2LConfTempChan4RaRatioDs is the ratio of
       excess data rate to be assigned to all other bearer channels.
       The sum of rate adaptation ratios over all bearers on the same
       direction shall sum to 100%."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4
                 (Rate adaptation ratio)"
   DEFVAL       { 0 }
   ::= { xdsl2LineConfTemplateEntry 13 }

xdsl2LConfTempChan4RaRatioUs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..100)
   UNITS       "percent"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Rate Adaptation Ratio.  The ratio (in percent) that should be
       taken into account for the bearer channel #4 when performing rate
       adaptation on Upstream.  The ratio refers to the available data
       rate in excess of the Minimum Data Rate, summed over all bearer
       channels.
       Also, the 100 - xdsl2LConfTempChan4RaRatioUs is the ratio of
       excess data rate to be assigned to all other bearer channels.
       The sum of rate adaptation ratios over all bearers on the same
       direction shall sum to 100%."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4
                 (Rate adaptation ratio)"
   DEFVAL       { 0 }
   ::= { xdsl2LineConfTemplateEntry 14 }

xdsl2LConfTempRowStatus  OBJECT-TYPE
   SYNTAX      RowStatus
   MAX-ACCESS  read-create
   STATUS      current

   DESCRIPTION
      "This object is used to create a new row or to modify or
       delete an existing row in this table.
       A template is activated by setting this object to 'active'.
       Before a profile can be deleted or taken out of service (by
       setting this object to 'destroy' or 'notInService'), it MUST be
       first unreferenced from all associated lines.
       A row in this table is said to be unreferenced when there is no
       instance of xdsl2LineConfTemplate or
       xdsl2LineConfFallbackTemplate that refers to the row."
   ::= { xdsl2LineConfTemplateEntry 15 }

------------------------------------------
--        xdsl2LineConfProfTable        --
------------------------------------------

xdsl2LineConfProfTable  OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2LineConfProfEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2LineConfProfTable contains VDSL2/ADSL/
       ADSL2 and ADSL2+ line configuration profiles.

       Entries in this table MUST be maintained in a persistent
       manner."
   ::= { xdsl2ProfileLine 2 }

xdsl2LineConfProfEntry  OBJECT-TYPE
   SYNTAX      Xdsl2LineConfProfEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "A default profile with an index of 'DEFVAL' will always
      exist, and its parameters will be set to vendor-specific values,
      unless otherwise specified in this document."
   INDEX  { xdsl2LConfProfProfileName }
   ::= { xdsl2LineConfProfTable 1 }

Xdsl2LineConfProfEntry  ::=
   SEQUENCE {
      xdsl2LConfProfProfileName          SnmpAdminString,
      xdsl2LConfProfScMaskDs             Xdsl2ScMaskDs,
      xdsl2LConfProfScMaskUs             Xdsl2ScMaskUs,
      xdsl2LConfProfVdsl2CarMask         Xdsl2CarMask,
      xdsl2LConfProfRfiBands             Xdsl2RfiBands,
      xdsl2LConfProfRaModeDs             Xdsl2RaMode,
      xdsl2LConfProfRaModeUs             Xdsl2RaMode,

      xdsl2LConfProfRaUsNrmDs            Unsigned32,
      xdsl2LConfProfRaUsNrmUs            Unsigned32,
      xdsl2LConfProfRaUsTimeDs           Unsigned32,
      xdsl2LConfProfRaUsTimeUs           Unsigned32,
      xdsl2LConfProfRaDsNrmDs            Unsigned32,
      xdsl2LConfProfRaDsNrmUs            Unsigned32,
      xdsl2LConfProfRaDsTimeDs           Unsigned32,
      xdsl2LConfProfRaDsTimeUs           Unsigned32,
      xdsl2LConfProfTargetSnrmDs         Unsigned32,
      xdsl2LConfProfTargetSnrmUs         Unsigned32,
      xdsl2LConfProfMaxSnrmDs            Unsigned32,
      xdsl2LConfProfMaxSnrmUs            Unsigned32,
      xdsl2LConfProfMinSnrmDs            Unsigned32,
      xdsl2LConfProfMinSnrmUs            Unsigned32,
      xdsl2LConfProfMsgMinUs             Unsigned32,
      xdsl2LConfProfMsgMinDs             Unsigned32,
      xdsl2LConfProfCeFlag               Xdsl2LineCeFlag,
      xdsl2LConfProfSnrModeDs            Xdsl2LineSnrMode,
      xdsl2LConfProfSnrModeUs            Xdsl2LineSnrMode,
      xdsl2LConfProfTxRefVnDs            Xdsl2LineTxRefVnDs,
      xdsl2LConfProfTxRefVnUs            Xdsl2LineTxRefVnUs,
      xdsl2LConfProfXtuTransSysEna       Xdsl2TransmissionModeType,
      xdsl2LConfProfPmMode               Xdsl2LinePmMode,
      xdsl2LConfProfL0Time               Unsigned32,
      xdsl2LConfProfL2Time               Unsigned32,
      xdsl2LConfProfL2Atpr               Unsigned32,
      xdsl2LConfProfL2Atprt              Unsigned32,
      xdsl2LConfProfProfiles             Xdsl2LineProfiles,
      xdsl2LConfProfDpboEPsd             Xdsl2PsdMaskDs,
      xdsl2LConfProfDpboEsEL             Unsigned32,
      xdsl2LConfProfDpboEsCableModelA    Unsigned32,
      xdsl2LConfProfDpboEsCableModelB    Unsigned32,
      xdsl2LConfProfDpboEsCableModelC    Unsigned32,
      xdsl2LConfProfDpboMus              Unsigned32,
      xdsl2LConfProfDpboFMin             Unsigned32,
      xdsl2LConfProfDpboFMax             Unsigned32,
      xdsl2LConfProfUpboKL               Unsigned32,
      xdsl2LConfProfUpboKLF              Xdsl2UpboKLF,
      xdsl2LConfProfUs0Mask              Xdsl2LineUs0Mask,
      xdsl2LConfProfForceInp             TruthValue,
      xdsl2LConfProfRowStatus            RowStatus
   }

xdsl2LConfProfProfileName  OBJECT-TYPE
   SYNTAX      SnmpAdminString (SIZE(1..32))
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION

      "This object identifies a row in this table."
     ::= { xdsl2LineConfProfEntry 1 }

xdsl2LConfProfScMaskDs  OBJECT-TYPE
   SYNTAX      Xdsl2ScMaskDs
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Subcarrier mask.  A bitmap of 4096 bits that allows masking
       up to 4096 downstream subcarriers.  If bit i (0 <= i <
       NSCds) is set to '1', the respective downstream
       subcarrier is masked, and if set to '0', the respective
       subcarrier is unmasked.
       Note that there should always be unmasked subcarriers (i.e.,
       this object cannot be all 1's).
       Also note that if NSCds < 4096, all bits i
       (NSCds < i <= 4096) should be set to '1'."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.6 (CARMASKds)"
   ::= { xdsl2LineConfProfEntry 2 }

xdsl2LConfProfScMaskUs  OBJECT-TYPE
   SYNTAX      Xdsl2ScMaskUs
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Subcarrier mask.  A bitmap of 4096 bits that allows masking
       up to 4096 upstream subcarriers.  If bit i (0 <= i < NSCus)
       is set to '1', the respective upstream subcarrier is
       masked, and if set to '0', the respective subcarrier
       is unmasked.
       Note that there should always be unmasked subcarriers (i.e.,
       this object cannot be all 1's).
       Also note that if NSCus < 4096, all bits i
       (NSCus < i <= 4096) should be set to '1'."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.7 (CARMASKus)"
   ::= { xdsl2LineConfProfEntry 3 }

xdsl2LConfProfVdsl2CarMask  OBJECT-TYPE
   SYNTAX      Xdsl2CarMask
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "VDSL2-specific subcarrier mask.  This configuration
       parameter defines the restrictions, additional to the band plan,
       to determine the set of subcarriers allowed for transmission in
       both the upstream and downstream directions.
       The parameter shall describe the not masked subcarriers as one or
       more frequency bands.  Each band is represented by start and stop

       subcarrier indices with a subcarrier spacing of 4.3125 kHz.  The
       valid range of subcarrier indices runs from 0 to at least the
       index of the highest allowed subcarrier in both transmission
       directions among all profiles enabled by the parameter
       xdsl2LConfProfProfiles.
       Up to 32 bands may be specified.  Other subcarriers shall be
       masked."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.8 (VDSL2-
                 CARMASK)"
   ::= { xdsl2LineConfProfEntry 4 }

xdsl2LConfProfRfiBands  OBJECT-TYPE
   SYNTAX      Xdsl2RfiBands
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "For ITU-T Recommendation G.992.5, this configuration
       parameter defines
       the subset of downstream PSD mask breakpoints, as specified in
       xdsl2LConfProfPsdMaskDs (PSDMASKds), that shall be used to notch
       an RFI band.  This subset consists of pairs of consecutive
       subcarrier indices belonging to breakpoints: [ti; ti + 1],
       corresponding to the low level of the notch.
       The specific interpolation around these points is defined in the
       relevant Recommendations (e.g., ITU-T Recommendation G.992.5).
       The CO-MIB shall define the RFI notches using breakpoints in
       xdsl2LConfProfPsdMaskDs (PSDMASKds) as specified in the relevant
       Recommendations (e.g., ITU-T Recommendation G.992.5).

       For ITU-T Recommendation G.993.2, this configuration parameter
       defines the bands where the PSD shall be reduced as
       specified in #*******/G.993.2.  Each band shall be represented
       by a start and stop subcarrier indices with a subcarrier
       spacing of 4.3125 kHz.  Up to 16 bands may be specified.
       This parameter defines the RFI bands for both the upstream
       and downstream directions."
   REFERENCE   "ITU-T G.997.1, paragraph #*******.10 (RFIBANDS)"
   ::= { xdsl2LineConfProfEntry 5 }

xdsl2LConfProfRaModeDs  OBJECT-TYPE
   SYNTAX      Xdsl2RaMode
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The mode of operation of a rate-adaptive xTU-C in the
       transmit direction."
   REFERENCE   "ITU-T G.997.1, paragraph #*******.1 (RA-MODEds)"
   DEFVAL      { manual }

   ::= { xdsl2LineConfProfEntry 6 }

xdsl2LConfProfRaModeUs  OBJECT-TYPE
   SYNTAX      Xdsl2RaMode
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The mode of operation of a rate-adaptive xTU-R in the
       transmit direction."
   REFERENCE   "ITU-T G.997.1, paragraph #*******.2 (RA-MODEus)"
   DEFVAL      { manual }
   ::= { xdsl2LineConfProfEntry 7 }

xdsl2LConfProfRaUsNrmDs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..310)
   UNITS       "0.1 dB"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The Downstream Up-Shift Noise Margin value, to be used when
       xdsl2LConfProfRaModeDs is set to 'dynamicRa'.  If the downstream
       noise margin is above this value, and stays above it,
       for more than the time specified by the
       xdsl2LConfProfRaUsTimeDs, the xTU-R shall attempt to increase
       the downstream net data rate.  The Downstream Up-Shift Noise
       Margin ranges from 0 to 310 units of 0.1 dB (physical values
       are 0 to 31 dB)."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.3 (RA-USNRMds)"
   DEFVAL       { 10 }
   ::= { xdsl2LineConfProfEntry 8 }

xdsl2LConfProfRaUsNrmUs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..310)
   UNITS       "0.1 dB"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The Upstream Up-Shift Noise Margin value, to be used when
       xdsl2LConfProfRaModeUs is set to 'dynamicRa'.  If the upstream
       noise margin is above this value, and stays above it,
       for more than
       the time specified by the xdsl2LConfProfRaUsTimeUs, the xTU-C
       shall attempt to increase the upstream net data rate.
       The Upstream Up-Shift Noise Margin ranges from 0 to 310 units of
       0.1 dB (physical values are 0 to 31 dB)."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4 (RA-USNRMus)"
   DEFVAL       { 10 }
   ::= { xdsl2LineConfProfEntry 9 }

xdsl2LConfProfRaUsTimeDs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..16383)
   UNITS       "seconds"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The Downstream Up-Shift Time Interval, to be used when
       xdsl2LConfProfRaModeDs is set to 'dynamicRa'.  The interval of
       time that the downstream noise margin should stay above the
       Downstream Up-Shift Noise Margin before the xTU-R shall attempt
       to increase the downstream net data rate.  The time interval
       ranges from 0 to 16383 seconds."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.5 (RA-UTIMEds)"
   DEFVAL       { 3600 }
   ::= { xdsl2LineConfProfEntry 10 }

xdsl2LConfProfRaUsTimeUs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..16383)
   UNITS       "seconds"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The Upstream Up-Shift Time Interval, to be used when
       xdsl2LConfProfRaModeUs is set to 'dynamicRa'.  The interval of
       time the upstream noise margin should stay above the Upstream
       Up-Shift Noise Margin before the xTU-C shall attempt to increase
       the upstream net data rate.  The time interval ranges from 0 to
       16383 seconds."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.6 (RA-UTIMEus)"
   DEFVAL       { 3600 }
   ::= { xdsl2LineConfProfEntry 11 }

xdsl2LConfProfRaDsNrmDs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..310)
   UNITS       "0.1 dB"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The Downstream Down-Shift Noise Margin value, to be used
       when xdsl2LConfProfRaModeDs is set to 'dynamicRa'.  If the
       downstream noise margin is below this value and stays
       below that value, for more than the time specified by the
       xdsl2LConfProfRaDsTimeDs, the xTU-R shall attempt to decrease
       the downstream net data rate.  The Downstream Down-Shift Noise
       Margin ranges from 0 to 310 units of 0.1 dB (physical values
       are 0 to 31 dB)."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.7 (RA-DSNRMds)"
   DEFVAL       { 10 }

   ::= { xdsl2LineConfProfEntry 12 }

xdsl2LConfProfRaDsNrmUs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..310)
   UNITS       "0.1 dB"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The Upstream Downshift Noise Margin value, to be used when
       xdsl2LConfProfRaModeUs is set to 'dynamicRa'.  If the upstream
       noise margin is below this value and stays below that value,
       for more than the time specified by the xdsl2LConfProfRaDsTimeUs,
       the xTU-C shall attempt to decrease the upstream net data rate.
       The Upstream Down-Shift Noise Margin ranges from 0 to 310 units
       of 0.1 dB (physical values are 0 to 31 dB)."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.8 (RA-DSNRMus)"
   DEFVAL       { 10 }
   ::= { xdsl2LineConfProfEntry 13 }

xdsl2LConfProfRaDsTimeDs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..16383)
   UNITS       "seconds"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The Downstream Downshift Time Interval, to be used when
       xdsl2LConfProfRaModeDs is set to 'dynamicRa'.  The interval of
       time the downstream noise margin should stay below the Downstream
       Down-Shift Noise Margin before the xTU-R shall attempt to
       decrease the downstream net data rate.  The time interval ranges
       from 0 to 16383 seconds."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.9 (RA-DTIMEds)"
   DEFVAL       { 3600 }
   ::= { xdsl2LineConfProfEntry 14 }

xdsl2LConfProfRaDsTimeUs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..16383)
   UNITS       "seconds"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The Upstream Down-Shift Time Interval, to be used when
       xdsl2LConfProfRaModeUs is set to 'dynamicRa'.  The interval of
       time the upstream noise margin should stay below the Upstream
       Down-Shift Noise Margin before the xTU-C shall attempt to
       decrease the upstream net data rate.  The time interval ranges
       from 0 to 16383 seconds."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.10 (RA-DTIMEus)"

   DEFVAL       { 3600 }
   ::= { xdsl2LineConfProfEntry 15 }

xdsl2LConfProfTargetSnrmDs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..310)
   UNITS       "0.1 dB"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The minimum Noise Margin the xTU-R receiver shall achieve,
       relative to the BER requirement for each of the downstream bearer
       channels, to successfully complete initialization.
       The target noise margin ranges from 0 to 310 units of 0.1 dB
       (physical values are 0 to 31 dB)."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1 (TARSNRMds)"
   DEFVAL       { 60 }
   ::= { xdsl2LineConfProfEntry 16 }

xdsl2LConfProfTargetSnrmUs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..310)
   UNITS       "0.1 dB"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The minimum Noise Margin the xTU-C receiver shall achieve,
       relative to the BER requirement for each of the upstream bearer
       channels, to successfully complete initialization.
       The target noise margin ranges from 0 to 310 units of 0.1 dB
       (physical values are 0 to 31 dB)."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.2 (TARSNRMus)"
   DEFVAL       { 60 }
   ::= { xdsl2LineConfProfEntry 17 }

xdsl2LConfProfMaxSnrmDs  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..310 | 2147483647)
   UNITS       "0.1 dB"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The maximum Noise Margin the xTU-R receiver shall try to
       sustain.  If the Noise Margin is above this level, the xTU-R
       shall request that the xTU-C reduce the xTU-C transmit power to
       get a noise margin below this limit (if this functionality is
       supported).  The maximum noise margin ranges from 0 to 310 units
       of 0.1 dB (physical values are 0 to 31 dB).  A value of
       0x7FFFFFFF (2147483647) means that there is no maximum."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.3 (MAXSNRMds)"
   DEFVAL       { 310 }

   ::= { xdsl2LineConfProfEntry 18 }

xdsl2LConfProfMaxSnrmUs  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..310 | 2147483647)
   UNITS       "0.1 dB"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The maximum Noise Margin the xTU-C receiver shall try to
       sustain.  If the Noise Margin is above this level, the xTU-C
       shall request that the xTU-R reduce the xTU-R transmit power to
       get a noise margin below this limit (if this functionality is
       supported).  The maximum noise margin ranges from 0 to 310 units
       of 0.1 dB (physical values are 0 to 31 dB).  A value of
       0x7FFFFFFF (2147483647) means that there is no maximum."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4 (MAXSNRMus)"
   DEFVAL       { 310 }
   ::= { xdsl2LineConfProfEntry 19 }

xdsl2LConfProfMinSnrmDs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..310)
   UNITS       "0.1 dB"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The minimum Noise Margin the xTU-R receiver shall tolerate.
       If the noise margin falls below this level, the xTU-R shall
       request that the xTU-C increase the xTU-C transmit power.
       If an increase to xTU-C transmit power is not possible, a loss-
       of-margin (LOM) defect occurs, the xTU-R shall fail and attempt
       to reinitialize and the NMS shall be notified.  The minimum noise
       margin ranges from 0 to 310 units of 0.1 dB (physical values are
       0 to 31 dB).  A value of 0 means that there is no minimum."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.5 (MINSNRMds)"
   DEFVAL       { 10 }
   ::= { xdsl2LineConfProfEntry 20 }

xdsl2LConfProfMinSnrmUs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..310)
   UNITS       "0.1 dB"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The minimum Noise Margin the xTU-C receiver shall tolerate.
       If the noise margin falls below this level, the xTU-C shall
       request that the xTU-R increase the xTU-R transmit power.
       If an increase of xTU-R transmit power is not possible, a loss-
       of-margin (LOM) defect occurs, the xTU-C shall fail and attempt

       to re-initialize and the NMS shall be notified.  The minimum
       noise margin ranges from 0 to 310 units of 0.1 dB (physical
       values are 0 to 31 dB).  A value of 0 means that there is no
       minimum."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.6 (MINSNRMus)"
   DEFVAL       { 10 }
   ::= { xdsl2LineConfProfEntry 21 }

xdsl2LConfProfMsgMinUs  OBJECT-TYPE
   SYNTAX      Unsigned32(4000..248000)
   UNITS       "bits/second"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Minimum Overhead Rate Upstream.  Defines the minimum rate of
       the message-based overhead that shall be maintained by the xTU in
       upstream direction.  Expressed in bits per second and ranges from
       4000 to 248000 bits/s."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1 (MSGMINus)"
   DEFVAL       { 4000 }
  ::= { xdsl2LineConfProfEntry 22 }

xdsl2LConfProfMsgMinDs  OBJECT-TYPE
   SYNTAX      Unsigned32(4000..248000)
   UNITS       "bits/second"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Minimum Overhead Rate Downstream.  Defines the minimum rate
       of the message-based overhead that shall be maintained by the xTU
       in the downstream direction.  Expressed in bits per second and
       ranges from 4000 to 248000 bits/s."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.2 (MSGMINds)"
   DEFVAL       { 4000 }
   ::= { xdsl2LineConfProfEntry 23 }

xdsl2LConfProfCeFlag  OBJECT-TYPE
   SYNTAX      Xdsl2LineCeFlag
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This parameter is a bit that enables the use of the optional
       cyclic extension values."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1 (CEFLAG)"
   DEFVAL       { { } }
   ::= { xdsl2LineConfProfEntry 24 }

xdsl2LConfProfSnrModeDs  OBJECT-TYPE

   SYNTAX      Xdsl2LineSnrMode
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This parameter enables the transmitter-referred virtual
       noise in the downstream direction."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1 (SNRMODEds)"
   DEFVAL       { virtualNoiseDisabled }
   ::= { xdsl2LineConfProfEntry 25 }

xdsl2LConfProfSnrModeUs  OBJECT-TYPE
   SYNTAX      Xdsl2LineSnrMode
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This parameter enables the transmitter-referred virtual
       noise in the upstream direction."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.2 (SNRMODEus)"
   DEFVAL       { virtualNoiseDisabled }
   ::= { xdsl2LineConfProfEntry 26 }

xdsl2LConfProfTxRefVnDs  OBJECT-TYPE
   SYNTAX      Xdsl2LineTxRefVnDs
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This configuration parameter defines the downstream
       transmitter-referred virtual noise.
       The TXREFVNds shall be specified through a set of breakpoints.
       Each breakpoint shall consist of a subcarrier index t, with a
       subcarrier spacing of 4.3125 kHz, and a noise PSD level
       (expressed in dBm/Hz) at that subcarrier.  The set of breakpoints
       can then be represented as:
       [(t1,PSD1), (t2, PSD2), ... , (tN, PSDN)]."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.3 (TXREFVNds)"
   ::= { xdsl2LineConfProfEntry 27 }

xdsl2LConfProfTxRefVnUs  OBJECT-TYPE
   SYNTAX      Xdsl2LineTxRefVnUs
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This configuration parameter defines the upstream
       transmitter-referred virtual noise.
       The TXREFVNus shall be specified through a set of breakpoints.
       Each breakpoint shall consist of a subcarrier index t, with a
       subcarrier spacing of 4.3125 kHz, and a noise PSD level
       (expressed in dBm/Hz) at that subcarrier.  The set of breakpoints

       can then be represented as:
       [(t1, PSD1), (t2, PSD2), ... , (tN, PSDN)]."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4 (TXREFVNus)"
   ::= { xdsl2LineConfProfEntry 28 }

xdsl2LConfProfXtuTransSysEna  OBJECT-TYPE
   SYNTAX      Xdsl2TransmissionModeType
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "xTU Transmission System Enabling (XTSE).  A list of the
       different coding types enabled in this profile.  It is coded in a
       bitmap representation with 1 or more bits set.  A bit set to
       '1' means that the xTUs may apply the respective
       coding for the DSL line.  A bit set to '0' means that
       the xTUs cannot apply the respective coding for the ADSL line.
       All 'reserved' bits should be set to '0'."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1 (XTSE)"
   ::= { xdsl2LineConfProfEntry 29 }

xdsl2LConfProfPmMode  OBJECT-TYPE
   SYNTAX      Xdsl2LinePmMode
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Power management state Enabling (PMMode).  Defines the power
       states the xTU-C or xTU-R may autonomously transition to on
       this line.
       This is a set of bits, where any bit with a '1' value
       means that the xTU is allowed to transit into the respective
       state and any bit with a '0' value means that the xTU
       is not allowed to transit into the respective state."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4 (PMMode)"
   DEFVAL  { { allowTransitionsToIdle, allowTransitionsToLowPower } }
   ::= { xdsl2LineConfProfEntry 30 }

xdsl2LConfProfL0Time  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..255)
   UNITS       "seconds"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The minimum time (in seconds) between an Exit from the L2
       state and the next Entry into the L2 state.
       It ranges from 0 to 255 seconds."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.5 (L0-TIME)"
   DEFVAL       { 255 }
   ::= { xdsl2LineConfProfEntry 31 }

xdsl2LConfProfL2Time  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..255)
   UNITS       "seconds"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The minimum time (in seconds) between an Entry into the
       L2 state and the first Power Trim in the L2 state and between two
       consecutive Power Trims in the L2 state.
       It ranges from 0 to 255 seconds."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.6 (L2-TIME)"
   DEFVAL       { 255 }
   ::= { xdsl2LineConfProfEntry 32 }

xdsl2LConfProfL2Atpr  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..31)
   UNITS       "dB"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The maximum aggregate transmit power reduction (in dB) that
       can be performed at transition of L0 to L2 state or through a
       single Power Trim in the L2 state.
       It ranges from 0 dB to 31 dB."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.7 (L2-ATPR)"
   DEFVAL       { 10 }
   ::= { xdsl2LineConfProfEntry 33 }

xdsl2LConfProfL2Atprt  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..31)
   UNITS       "dB"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The total maximum aggregate transmit power reduction (in dB)
       that can be performed in an L2 state.  This is the sum of all
       reductions of L2 Requests (i.e., at transition of L0 to L2 state)
       and Power Trims."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.9 (L2-ATPRT)"
   DEFVAL       { 31 }
   ::= { xdsl2LineConfProfEntry 34 }

xdsl2LConfProfProfiles  OBJECT-TYPE
   SYNTAX      Xdsl2LineProfiles
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The configuration parameter contains the G.993.2 profiles

       to be allowed by the near-end xTU on this line.
       It is coded in a bitmap representation (0 if not allowed, 1 if
       allowed)."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.11 (PROFILES)"
   DEFVAL       { { profile8a,  profile8b,  profile8c,
                    profile8d,  profile12a, profile12b,
                    profile17a, profile30a } }
   ::= { xdsl2LineConfProfEntry 35 }

xdsl2LConfProfDpboEPsd  OBJECT-TYPE
   SYNTAX      Xdsl2PsdMaskDs
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This configuration parameter defines the PSD mask that is
       assumed to be permitted at the exchange.  This parameter shall
       use the same format as xdsl2LConfProfPsdMaskDs (PSDMASKds).
       The maximum number of breakpoints for xdsl2LConfProfDpboEPsd
       is 16."
   REFERENCE   "ITU-T G.997.1, paragraph #*******.13 (DPBOEPSD)"
   ::= { xdsl2LineConfProfEntry 36 }

xdsl2LConfProfDpboEsEL  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..511)
   UNITS       "0.5 dB"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This configuration parameter defines the assumed electrical
       length of cables (E-side cables) connecting exchange-based DSL
       services to a remote flexibility point (cabinet), that hosts the
       xTU-C that is subject to spectrally shaped downstream power back-
       off (DPBO) depending on this length.  The electrical length is
       defined as the loss (in dB) of an equivalent length of
       hypothetical cable at a reference frequency defined by the
       network operator or in spectrum management regulations.
       This parameter shall be coded as an unsigned integer representing
       an electrical length from 0 dB (coded as 0) to 255.5 dB (coded as
       511) in steps of 0.5 dB.  All values in the range are valid.  If
       this parameter is set to '0', the DPBO shall be disabled."
   REFERENCE   "ITU-T G.997.1, paragraph #*******.13 (DPBOESEL)"
   DEFVAL      { 0 }
   ::= { xdsl2LineConfProfEntry 37 }

xdsl2LConfProfDpboEsCableModelA  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..640)
   UNITS       "2^-8"
   MAX-ACCESS  read-create

   STATUS      current
   DESCRIPTION
      "The E-side Cable Model parameter A (DPBOESCMA) of the cable
       model (DPBOESCM) for cables connecting exchange-based DSL
       services to a remote flexibility point (cabinet), that hosts the
       xTU-C that is subject to spectrally shaped downstream power back-
       off (DPBO) depending on this value.
       The cable model is in terms of three scalars
       xdsl2LConfProfDpboEsCableModelA (DPBOESCMA),
       xdsl2LConfProfDpboEsCableModelB(DPBOESCMB), and
       xdsl2LConfProfDpboEsCableModelC (DPBOESCMC), that are used to
       estimate the frequency dependent loss of E-side cables calculated
       from the xdsl2LConfProfDpboEsEL (DPBOESEL) parameter.  Possible
       values shall be coded as unsigned integers representing a scalar
       value from -1 (coded as 0) to 1.5 (coded as 640) in steps of
       2^-8.  All values in the range are valid.  This parameter is used
       only for G.993.2."
   REFERENCE   "ITU-T G.997.1, paragraph #*******.13 (DPBOESCMA)"
   DEFVAL      { 0 }
   ::= { xdsl2LineConfProfEntry 38 }

xdsl2LConfProfDpboEsCableModelB  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..640)
   UNITS       "2^-8"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The E-side Cable Model parameter B (DPBOESCMB) of the cable
       model (DPBOESCM) for cables connecting exchange-based DSL
       services to a remote flexibility point (cabinet), that hosts the
       xTU-C that is subject to spectrally shaped downstream power back-
       off (DPBO) depending on this value.
       The cable model is in terms of three scalars
       dsl2LConfProfDpboEsCableModelA (DPBOESCMA),
       xdsl2LConfProfDpboEsCableModelB(DPBOESCMB), and
       xdsl2LConfProfDpboEsCableModelC (DPBOESCMC), that are used to
       estimate the frequency dependent loss of E-side cables calculated
       from the xdsl2LConfProfDpboEsEL (DPBOESEL) parameter.  Possible
       values shall be coded as unsigned integers representing a scalar
       value from -1 (coded as 0) to 1.5 (coded as 640) in steps of
       2^-8.  All values in the range are valid.  This parameter is used
       only for G.993.2."
   REFERENCE   "ITU-T G.997.1, paragraph #*******.13 (DPBOESCMB)"
   DEFVAL      { 0 }
   ::= { xdsl2LineConfProfEntry 39 }

xdsl2LConfProfDpboEsCableModelC  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..640)

   UNITS       "2^-8"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The E-side Cable Model parameter C (DPBOESCMC) of the cable
       model (DPBOESCM) for cables connecting exchange-based DSL
       services to a remote flexibility point (cabinet), that hosts the
       xTU-C that is subject to spectrally shaped downstream power back-
       off (DPBO) depending on this value.
       The cable model is in terms of three scalars
       xdsl2LConfProfDpboEsCableModelA (DPBOESCMA),
       xdsl2LConfProfDpboEsCableModelB(DPBOESCMB), and
       xdsl2LConfProfDpboEsCableModelC (DPBOESCMC), that are used to
       estimate the frequency dependent loss of E-side cables calculated
       from the xdsl2LConfProfDpboEsEL (DPBOESEL) parameter.  Possible
       values shall be coded as unsigned integers representing a scalar
       value from -1 (coded as 0) to 1.5 (coded as 640) in steps of
       2^-8.  All values in the range are valid.  This parameter is used
       only for G.993.2."
   REFERENCE   "ITU-T G.997.1, paragraph #*******.13 (DPBOESCMC)"
   DEFVAL      { 0 }
   ::= { xdsl2LineConfProfEntry 40 }

xdsl2LConfProfDpboMus OBJECT-TYPE
   SYNTAX      Unsigned32 (0..255)
   UNITS       "0.5 dBm/Hz"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This configuration parameter defines the assumed Minimum
       Usable receive PSD mask (in dBm/Hz) for exchange-based services,
       used to modify parameter xdsl2LConfProfDpboFMax (DPBOFMAX)
       defined below (to determine the DPBO).  It shall be coded as an
       unsigned integer representing a PSD mask level from 0 dBm/Hz
       (coded as 0) to -127.5 dBm/Hz (coded as 255) in steps of 0.5
       dBm/Hz.  All values in the range are valid.
       NOTE - The PSD mask level is 3.5 dB above the signal PSD level.
       This parameter is used only for G.993.2."
   REFERENCE   "ITU-T G.997.1, paragraph #*******.13 (DPBOMUS)"
   DEFVAL      { 0 }
   ::= { xdsl2LineConfProfEntry 41 }

xdsl2LConfProfDpboFMin OBJECT-TYPE
   SYNTAX      Unsigned32 (0..2048)
   UNITS       "4.3125 kHz"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION

      "This configuration parameter defines the minimum frequency
       from which the DPBO shall be applied.  It ranges from 0 kHz
       (coded as 0) to 8832 kHz (coded as 2048) in steps of
       4.3125 kHz.  This parameter is used only for G.993.2."
   REFERENCE   "ITU-T G.997.1, paragraph #*******.13 (DPBOFMIN)"
   DEFVAL      { 32 }
   ::= { xdsl2LineConfProfEntry 42 }

xdsl2LConfProfDpboFMax OBJECT-TYPE
   SYNTAX      Unsigned32 (32..6956)
   UNITS       "4.3125 kHz"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This configuration parameter defines the maximum frequency
       at which DPBO may be applied.  It ranges from 138 kHz (coded as
       32) to 29997.75 kHz (coded as 6956) in steps of 4.3125 kHz.
       This parameter is used only for G.993.2."
   REFERENCE   "ITU-T G.997.1, paragraph #*******.13 (DPBOFMAX)"
   DEFVAL      { 512 }
   ::= { xdsl2LineConfProfEntry 43 }

xdsl2LConfProfUpboKL  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..1280)
   UNITS       "0.1 dB"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This configuration parameter defines the electrical length
       expressed in dB at 1 MHz, kl0, configured by the CO-MIB.
       The value ranges from 0 (coded as 0) to 128 dB (coded as 1280) in
       steps of 0.1 dB.  This parameter is relevant only if
       xdsl2LConfProfUpboKLF is set to 'override(2)', which indicates
       that this parameter's value will override the VTUs'
       determination of the electrical length.
       If xdsl2LConfProfUpboKLF is set either to auto(1) or
       disableUpbo(3), then this parameter will be ignored."
   REFERENCE   "ITU-T G.997.1, paragraph #*******.14 (UPBOKL)"
   DEFVAL      { 0 }
   ::= { xdsl2LineConfProfEntry 44 }

xdsl2LConfProfUpboKLF OBJECT-TYPE
   SYNTAX      Xdsl2UpboKLF
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Defines the upstream power backoff force mode."
   REFERENCE   "ITU-T G.997.1, paragraph #*******.14 (UPBOKLF)

   "
   DEFVAL      { disableUpbo }
   ::= { xdsl2LineConfProfEntry 45 }

xdsl2LConfProfUs0Mask  OBJECT-TYPE
   SYNTAX      Xdsl2LineUs0Mask
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The configuration parameter contains the US0 PSD masks to be
       allowed by the near-end xTU on the line.  This parameter is only
       defined for G.993.2 Annex A.  It is represented as a bitmap (0
       if not allowed and 1 if allowed)."
   REFERENCE    "ITU-T G.997.1 Amendment 1, paragraph #*******.18
                 (US0MASK)"
   DEFVAL       { {} }
   ::= { xdsl2LineConfProfEntry 46 }

xdsl2LConfProfForceInp  OBJECT-TYPE
   SYNTAX      TruthValue
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
       "This parameter, when set to 'true' indicates that the framer
        settings of the bearer shall be selected such that the impulse
        noise protection computed according to the formula specified in
        the relevant Recommendation is greater than or equal to the
        minimal impulse noise protection requirement.
        This flag shall have the same value for all the bearers of one
        line in the same direction."
   REFERENCE    "ITU-T G.997.1, paragraph #******* (FORCEINP)"
   DEFVAL       { false }
   ::= { xdsl2LineConfProfEntry 47 }

xdsl2LConfProfRowStatus  OBJECT-TYPE
   SYNTAX      RowStatus
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This object is used to create a new row or to modify or
       delete an existing row in this table.

       A profile is activated by setting this object to 'active'.

       Before a profile can be deleted or taken out of service (by
       setting this object to 'destroy' or 'notInService'), it MUST be
       first unreferenced from all templates.

       A row in this table is said to be unreferenced when there is no
       instance of xdsl2LConfTempLineProfile that refers to the row.

       When a row is created in this table, the SNMP agent should also
       create corresponding rows in the tables
       xdsl2LineConfProfModeSpecTable and
       xdsl2LineConfProfModeSpecBandUsTable.
       When a row is deleted in this table, the SNMP agent should also
       delete corresponding rows in the tables
       xdsl2LineConfProfModeSpecTable and
       xdsl2LineConfProfModeSpecBandUsTable."
   ::= { xdsl2LineConfProfEntry 48 }

------------------------------------------
--    xdsl2LineConfProfModeSpecTable    --
------------------------------------------

xdsl2LineConfProfModeSpecTable  OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2LineConfProfModeSpecEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2LineConfProfModeSpecTable extends the DSL
       line configuration profile by xDSL Mode-Specific parameters.
       A row in this table that has an index of xdsl2LConfProfXdslMode
       == defMode(1), is called a 'mandatory' row or 'default' row.
       A row in this table that has an index such that
       xdsl2LConfProfXdslMode is not equal to defMode(1), is called an
       'optional' row or 'mode-specific' row.
       When a row in the xdsl2LineConfProfTable table (the parent row)
       is created, the SNMP agent will automatically create a
       'mandatory' row in this table.
       When the parent row is deleted, the SNMP agent will automatically
       delete all associated rows in this table.
       Any attempt to delete the 'mandatory' row using the
       xdsl2LConfProfModeSpecRowStatus object will be rejected by the
       SNMP agent.
       The manager MAY create an 'optional' row in this table using the
       xdsl2LConfProfModeSpecRowStatus object if the parent row
       exists.
       The manager MAY delete an 'optional' row in this table using the
       xdsl2LConfProfModeSpecRowStatus object at any time.
       If the actual transmission mode of a DSL line does not match one
       of the 'optional' rows in this table, then the line will use the
       PSD configuration from the 'mandatory' row.

       Entries in this table MUST be maintained in a persistent
       manner."

   ::= { xdsl2ProfileLine 3 }

xdsl2LineConfProfModeSpecEntry  OBJECT-TYPE
   SYNTAX      Xdsl2LineConfProfModeSpecEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2LineConfProfModeSpecTable extends the
       DSL line configuration profile by DSL Mode-Specific
       parameters."
   INDEX  { xdsl2LConfProfProfileName, xdsl2LConfProfXdslMode }
   ::= { xdsl2LineConfProfModeSpecTable 1 }

Xdsl2LineConfProfModeSpecEntry  ::=
   SEQUENCE {
      xdsl2LConfProfXdslMode             Xdsl2OperationModes,
      xdsl2LConfProfMaxNomPsdDs          Integer32,
      xdsl2LConfProfMaxNomPsdUs          Integer32,
      xdsl2LConfProfMaxNomAtpDs          Unsigned32,
      xdsl2LConfProfMaxNomAtpUs          Unsigned32,
      xdsl2LConfProfMaxAggRxPwrUs        Integer32,
      xdsl2LConfProfPsdMaskDs            Xdsl2PsdMaskDs,
      xdsl2LConfProfPsdMaskUs            Xdsl2PsdMaskUs,
      xdsl2LConfProfPsdMaskSelectUs      Xdsl2LinePsdMaskSelectUs,
      xdsl2LConfProfClassMask            Xdsl2LineClassMask,
      xdsl2LConfProfLimitMask            Xdsl2LineLimitMask,
      xdsl2LConfProfUs0Disable           Xdsl2LineUs0Disable,
      xdsl2LConfProfModeSpecRowStatus    RowStatus
   }

xdsl2LConfProfXdslMode    OBJECT-TYPE
   SYNTAX      Xdsl2OperationModes
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The DSL Mode is a way of categorizing the various xDSL
       transmission modes into groups, each group (xDSL Mode) shares
       the same PSD configuration.
       There should be multiple entries in this table for a given line
       profile in case multiple bits are set in
       xdsl2LConfProfXtuTransSysEna for that profile."
   REFERENCE    "DSL Forum TR-129, paragraph #5.5"
   ::= { xdsl2LineConfProfModeSpecEntry 1 }

xdsl2LConfProfMaxNomPsdDs  OBJECT-TYPE
   SYNTAX      Integer32(-600..-300)
   UNITS       "0.1 dBm/Hz"
   MAX-ACCESS  read-create

   STATUS      current
   DESCRIPTION
      "The maximum nominal transmit PSD in the downstream direction
       during initialization and Showtime.  It ranges from -600 to -300
       units of 0.1 dBm/Hz (physical values are -60 to -30
       dBm/Hz)."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1 (MAXNOMPSDds)"
   DEFVAL       { -300 }
  ::= { xdsl2LineConfProfModeSpecEntry 2 }

xdsl2LConfProfMaxNomPsdUs  OBJECT-TYPE
   SYNTAX      Integer32(-600..-300)
   UNITS       "0.1 dBm/Hz"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The maximum nominal transmit PSD in the upstream direction
       during initialization and Showtime.  It ranges from -600 to
       -300 units of 0.1 dBm/Hz (physical values are -60 to -30
       dBm/Hz)."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.2 (MAXNOMPSDus)"
   DEFVAL       { -300 }
   ::= { xdsl2LineConfProfModeSpecEntry 3 }

xdsl2LConfProfMaxNomAtpDs  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..255)
   UNITS       "0.1 dBm"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The maximum nominal aggregate to transmit power in the
       downstream direction during initialization and Showtime.  It
       ranges from 0 to 255 units of 0.1 dBm (physical values are 0
       to 25.5 dBm)."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.3 (MAXNOMATPds)"
   DEFVAL       { 255 }
   ::= { xdsl2LineConfProfModeSpecEntry 4 }

xdsl2LConfProfMaxNomAtpUs  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..255)
   UNITS       "0.1 dBm"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The maximum nominal aggregate transmit power in the upstream
       direction during initialization and Showtime.  It ranges from
       0 to 255 units of 0.1 dBm (physical values are 0 to 25.5
       dBm)."

   REFERENCE    "ITU-T G.997.1, paragraph #*******.4 (MAXNOMATPus)"
   DEFVAL       { 255 }
   ::= { xdsl2LineConfProfModeSpecEntry 5 }

xdsl2LConfProfMaxAggRxPwrUs  OBJECT-TYPE
   SYNTAX      Integer32(-255..255 | 2147483647)
   UNITS       "0.1 dBm"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The maximum upstream aggregate receive power over the
       relevant set of subcarriers.  The xTU-C should verify that the
       upstream power cutback is such that this maximum aggregate
       receive power value is honored.  It ranges from -255 to 255
       units of 0.1 dBm (physical values are -25.5 to 25.5 dBm).
       A value of 0x7FFFFFFF (2147483647) means that there is no
       limit."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.5 (MAXRXPWRus)"
   DEFVAL       { 255 }
   ::= { xdsl2LineConfProfModeSpecEntry 6 }

xdsl2LConfProfPsdMaskDs   OBJECT-TYPE
   SYNTAX      Xdsl2PsdMaskDs
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
     "The downstream PSD mask applicable at the U-C2 reference
      point.
      This parameter is used only for G.992.5 and it may impose PSD
      restrictions (breakpoints) in addition to the Limit PSD mask
      defined in G.992.5.
      This is a string of 32 pairs of values in the following
      structure:
      Octets 0-1 - Index of the first subcarrier used in the context of
                   a first breakpoint.
      Octet 2    - The PSD reduction for the subcarrier indicated in
                   octets 0 and 1.
      Octets 3-5 - Same, for a second breakpoint.
      Octets 6-8 - Same, for a third breakpoint.
      This architecture continues until octets 94-95, which are
      associated with a 32nd breakpoint.
      Each subcarrier index is an unsigned number in the range 0 and
      NSCds-1.  Each PSD reduction value is in the range 0 (0 dBm/Hz) to
      255 (-127.5 dBm/Hz) with steps of 0.5 dBm/Hz.  Valid values are in
      the range 0 to 190 (0 to -95 dBm/Hz).
      When the number of breakpoints is less than 32, all remaining
      octets are set to the value '0'.  Note that the content of this
      object should be correlated with the subcarrier mask and with

      the RFI setup."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.9 (PSDMASKds)"
     ::= { xdsl2LineConfProfModeSpecEntry 7 }

xdsl2LConfProfPsdMaskUs   OBJECT-TYPE
   SYNTAX      Xdsl2PsdMaskUs
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
     "The upstream PSD mask applicable at the U-R2 reference
      point.
      This parameter is used only for G.992.5, and it may impose PSD
      restrictions (breakpoints) in addition to the Limit PSD mask
      defined in G.992.5.
      This is a string of 16 pairs of values in the following
      structure:
      Octets 0-1 - Index of the first subcarrier used in the context of
                   a first breakpoint.
      Octet 2    - The PSD reduction for the subcarrier indicated in
                   octets 0 and 1.
      Octets 3-5 - Same, for a second breakpoint.
      Octets 6-8 - Same, for a third breakpoint.
      This architecture continues until octets 9-47, which are
      associated with a 16th breakpoint.
      Each subcarrier index is an unsigned number in the range 0 and
      NSCus-1.  Each PSD reduction value is in the range 0 (0 dBm/Hz) to
      255 (-127.5 dBm/Hz) with steps of 0.5 dBm/Hz.  Valid values are in
      the range 0 to 190 (0 to -95 dBm/Hz).
      When the number of breakpoints is less than 16, all remaining
      octets are set to the value '0'.  Note that the content of this
      object should be correlated with the subcarrier mask and with
      the RFI setup."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.12 (PSDMASKus)"
     ::= { xdsl2LineConfProfModeSpecEntry 8 }

xdsl2LConfProfPsdMaskSelectUs  OBJECT-TYPE
   SYNTAX      Xdsl2LinePsdMaskSelectUs
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
     "The selected upstream PSD mask.  This parameter is used only
      for Annexes J and M of G.992.3 and G.992.5, and the same
      selection is used for all relevant enabled bits in
      xdsl2LConfProfXtuTransSysEna."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.11
                (Upstream PSD mask selection)"
   DEFVAL       { adlu32Eu32 }
    ::= { xdsl2LineConfProfModeSpecEntry 9 }

xdsl2LConfProfClassMask  OBJECT-TYPE
   SYNTAX      Xdsl2LineClassMask
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "In order to reduce the number of configuration
       possibilities, the limit Power Spectral Density masks (see
       LIMITMASK) are grouped in PSD mask classes.
       Each class is designed such that the PSD levels of each limit PSD
       mask of a specific class are equal in their respective passband
       above 552 kHz.
       This parameter is defined per VDSL2 Annex enabled in the
       xdsl2LConfProfXtuTransSysEna object.  It selects a single PSD
       mask class per Annex that is activated at the VTU-O."
   REFERENCE    "ITU-T G.997.1 Amendment 1, paragraph #*******.15
                (CLASSMASK)"
   DEFVAL       { a998ORb997M1cORc998B }
   ::= { xdsl2LineConfProfModeSpecEntry 10 }

xdsl2LConfProfLimitMask  OBJECT-TYPE
   SYNTAX      Xdsl2LineLimitMask
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This configuration parameter contains the G.993.2 limit
       PSD masks of the selected PSD mask class, enabled by the near-end
       xTU on this line for each class of profiles.
       This parameter is defined per VDSL2 Annex enabled in the
       xdsl2LConfProfXtuTransSysEna object.
       Through this parameter, several limit PSD masks of the selected
       PSD mask class (xdsl2LConfProfClassMask) may be enabled.  The
       enabling parameter is coded in a bitmap representation (0 if the
       associated mask is not allowed, 1 if it is allowed)."
   REFERENCE    "ITU-T G.997.1 Amendment 1, paragraph #*******.16
                 (LIMITMASK)"
   DEFVAL       { {} }
   ::= { xdsl2LineConfProfModeSpecEntry 11 }

xdsl2LConfProfUs0Disable  OBJECT-TYPE
   SYNTAX      Xdsl2LineUs0Disable
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This configuration parameter indicates if the use of the US0 is
       disabled for each limit PSD mask enabled in the
       xdsl2LConfProfLimitMask parameter.
       This parameter is defined per VDSL2 Annex enabled in the
       xdsl2LConfProfXtuTransSysEna object.

       For each limit PSD mask enabled in the xdsl2LConfProfLimitMask
       parameter, a bit shall indicate if the US0 is disabled.  The
       disabling parameter is coded as a bitmap.  The bit is set to '1'
       if the US0 is disabled for the associated limit mask.
       This parameter and the xdsl2LConfProfLimitMask parameter use the
       same structure."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.17 (US0DISABLE)"
   DEFVAL       { {} }
   ::= { xdsl2LineConfProfModeSpecEntry 12 }

xdsl2LConfProfModeSpecRowStatus  OBJECT-TYPE
   SYNTAX      RowStatus
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This object is used to create a new row or to modify or
       delete an existing row in this table.

       This row is activated by setting this object to 'active'.

       A 'mandatory' row, as defined in the DESCRIPTION clause of
       xdsl2LineConfProfModeSpecTable, cannot be deleted at all.

       A 'mandatory' row can be taken out of service
       (by setting this object to 'notInService') if the parent
       row in the xdsl2LineConfProfTable table is not in
       the 'active' state.

       An 'optional' row (or 'mode-specific' row) can be deleted or
       taken out of service (by setting this object to 'destroy' or
       'notInService') at any time."

   ::= { xdsl2LineConfProfModeSpecEntry 13 }

----------------------------------------------
--   xdsl2LineConfProfModeSpecBandUsTable   --
----------------------------------------------

xdsl2LineConfProfModeSpecBandUsTable  OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2LineConfProfModeSpecBandUsEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2LineConfProfModeSpecBandUsTable extends
       xdsl2LineConfProfModeSpecTable with upstream-band-specific
       parameters for VDSL2, such as upstream power back-off
       parameters xdsl2LConfProfUpboPsdA and xdsl2LConfProfUpboPsdB
       (UPBOPSD-pb).

       When a parent 'mandatory row' is created in
       xdsl2LineConfProfModeSpecTable, the SNMP agent will automatically
       create several 'mandatory' rows in this table -- one for each
       upstream band:
       Note: A mandatory row is one where xdsl2LConfProfXdslMode =
       defMode(1).  When the parent row is deleted, the SNMP agent will
       automatically delete all associated rows in this table.  Any
       attempt to delete a 'mandatory' row using the
       xdsl2LConfProfModeSpecBandUsRowStatus object will be rejected
       by the SNMP agent.  The manager MAY create a new 'optional'
       row in this table using the xdsl2LConfProfModeSpecBandUsRowStatus
       object if the associated parent row exists, and the
       value of xdsl2LConfProfXdslMode is a G.993.2 value.  The manager
       MAY delete an 'optional' row in this table using the
       xdsl2LConfProfModeSpecBandUsRowStatus object at any time.

       With respect to the xdsl2LConfProfUpboPsdA and
       xdsl2LConfProfUpboPsdB parameters, for a given upstream band,
       if an optional row is missing from this table, then that
       means upstream power back-off is disabled for that upstream
       band.

       Entries in this table MUST be maintained in a persistent
       manner."
   ::= { xdsl2ProfileLine 4 }

xdsl2LineConfProfModeSpecBandUsEntry  OBJECT-TYPE
   SYNTAX      Xdsl2LineConfProfModeSpecBandUsEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2LineConfProfModeSpecBandUsTable extends
       xdsl2LineConfProfModeSpecTable with upstream-band-specific
       parameters for VDSL2, such as upstream power back-off parameters
       xdsl2LConfProfUpboPsdA and xdsl2LConfProfUpboPsdB (UPBOPSD-
       pb)."
   INDEX       { xdsl2LConfProfProfileName, xdsl2LConfProfXdslMode,
                 xdsl2LConfProfXdslBandUs}
   ::= { xdsl2LineConfProfModeSpecBandUsTable 1 }

Xdsl2LineConfProfModeSpecBandUsEntry  ::=
   SEQUENCE {
      xdsl2LConfProfXdslBandUs                 Xdsl2BandUs,
      xdsl2LConfProfUpboPsdA                   Integer32,
      xdsl2LConfProfUpboPsdB                   Integer32,
      xdsl2LConfProfModeSpecBandUsRowStatus    RowStatus
   }

xdsl2LConfProfXdslBandUs    OBJECT-TYPE
   SYNTAX      Xdsl2BandUs
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "Each value identifies a specific band in the upstream
       transmission direction (excluding the US0 band)."
   REFERENCE   "ITU-T G.997.1, paragraph #*******.14"
   ::= { xdsl2LineConfProfModeSpecBandUsEntry 1 }

xdsl2LConfProfUpboPsdA  OBJECT-TYPE
   SYNTAX      Integer32(4000..8095)
   UNITS       "0.01 dBm/Hz"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This configuration parameter defines the 'a' reference
       parameter of the UPBO reference PSD used to compute the
       upstream power back-off for the upstream band.  A UPBO PSD
       defined for each band shall consist of two parameters [a, b].
       Parameter 'a' (xdsl2LConfProfUpboPsdA) ranges from 40 dBm/Hz
       (coded as 4000) to 80.95 dBm/Hz (coded as 8095) in steps of 0.01
       dBm/Hz; and parameter 'b' (xdsl2LConfProfUpboPsdB) ranges from 0
       dBm/Hz (coded as 0) to 40.95 dBm/Hz (coded as 4095) in steps of
       0.01 dBm/Hz.  The UPBO reference PSD at the frequency 'f'
       expressed in MHz shall be equal to '-a-b(SQRT(f))'.  Setting
       xdsl2LConfProfUpboPsdA to 4000 and xdsl2LConfProfUpboPsdB to 0 is
       a special configuration to disable UPBO in the respective
       upstream band."
   REFERENCE   "ITU-T G.997.1, paragraph #*******.14 (UPBOPSD-pb)"
   DEFVAL      { 4000 }
  ::= { xdsl2LineConfProfModeSpecBandUsEntry 2 }

xdsl2LConfProfUpboPsdB  OBJECT-TYPE
   SYNTAX      Integer32(0..4095)
   UNITS       "0.01 dBm/Hz"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This configuration parameter defines the 'b' reference
       parameter of the UPBO reference PSD used to compute the
       upstream power back-off for the upstream band.  A UPBO PSD
       defined for each band shall consist of two parameters [a, b].
       Parameter 'a' (xdsl2LConfProfUpboPsdA) ranges from 40 dBm/Hz
       (coded as 4000) to 80.95 dBm/Hz (coded as 8095) in steps of 0.01
       dBm/Hz; and parameter 'b' (xdsl2LConfProfUpboPsdB) ranges from 0
       dBm/Hz (coded as 0) to 40.95 dBm/Hz (coded as 4095) in steps of
       0.01 dBm/Hz.  The UPBO reference PSD at the frequency 'f'

       expressed in MHz shall be equal to '-a-b(SQRT(f))'.  Setting
       xdsl2LConfProfUpboPsdA to 4000 and xdsl2LConfProfUpboPsdB to 0 is
       a special configuration to disable UPBO in the respective
       upstream band."
   REFERENCE   "ITU-T G.997.1, paragraph #*******.14 (UPBOPSD-pb)"
   DEFVAL      { 0 }
  ::= { xdsl2LineConfProfModeSpecBandUsEntry 3 }

xdsl2LConfProfModeSpecBandUsRowStatus  OBJECT-TYPE
   SYNTAX      RowStatus
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This object is used to create a new row or to modify or
       delete an existing row in this table.

       This row is activated by setting this object to 'active'.

       A 'mandatory' row, as defined in the DESCRIPTION clause of
       xdsl2LineConfProfModeSpecBandUsTable, cannot be deleted at all.

       A 'mandatory' row can be taken out of service
       (by setting this object to 'notInService') if the parent
       row in the xdsl2LineConfProfModeSpecTable table is not in
       the 'active' state.

       An 'optional' row (or 'mode-specific' row) can be deleted or
       taken out of service (by setting this object to 'destroy' or
       'notInService') at any time."
   ::= { xdsl2LineConfProfModeSpecBandUsEntry 4 }

------------------------------------------------
--          xdsl2ChConfProfileTable           --
------------------------------------------------

xdsl2ChConfProfileTable  OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2ChConfProfileEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2ChConfProfileTable contains DSL channel
       profile configuration.

       Entries in this table MUST be maintained in a persistent
       manner."
   ::= { xdsl2ProfileChannel 1 }

xdsl2ChConfProfileEntry  OBJECT-TYPE

   SYNTAX      Xdsl2ChConfProfileEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "A default profile with an index of 'DEFVAL' will always
      exist, and its parameters will be set to vendor-specific values,
      unless otherwise specified in this document."
   INDEX  { xdsl2ChConfProfProfileName }
   ::= { xdsl2ChConfProfileTable 1 }

Xdsl2ChConfProfileEntry  ::=
   SEQUENCE {
      xdsl2ChConfProfProfileName          SnmpAdminString,
      xdsl2ChConfProfMinDataRateDs        Unsigned32,
      xdsl2ChConfProfMinDataRateUs        Unsigned32,
      xdsl2ChConfProfMinResDataRateDs     Unsigned32,
      xdsl2ChConfProfMinResDataRateUs     Unsigned32,
      xdsl2ChConfProfMaxDataRateDs        Unsigned32,
      xdsl2ChConfProfMaxDataRateUs        Unsigned32,
      xdsl2ChConfProfMinDataRateLowPwrDs  Unsigned32,
      xdsl2ChConfProfMinDataRateLowPwrUs  Unsigned32,
      xdsl2ChConfProfMaxDelayDs           Unsigned32,
      xdsl2ChConfProfMaxDelayUs           Unsigned32,
      xdsl2ChConfProfMinProtectionDs      Xdsl2SymbolProtection,
      xdsl2ChConfProfMinProtectionUs      Xdsl2SymbolProtection,
      xdsl2ChConfProfMinProtection8Ds     Xdsl2SymbolProtection8,
      xdsl2ChConfProfMinProtection8Us     Xdsl2SymbolProtection8,
      xdsl2ChConfProfMaxBerDs             Xdsl2MaxBer,
      xdsl2ChConfProfMaxBerUs             Xdsl2MaxBer,
      xdsl2ChConfProfUsDataRateDs         Unsigned32,
      xdsl2ChConfProfDsDataRateDs         Unsigned32,
      xdsl2ChConfProfUsDataRateUs         Unsigned32,
      xdsl2ChConfProfDsDataRateUs         Unsigned32,
      xdsl2ChConfProfImaEnabled           TruthValue,
      xdsl2ChConfProfMaxDelayVar          Unsigned32,
      xdsl2ChConfProfInitPolicy           Xdsl2ChInitPolicy,
      xdsl2ChConfProfRowStatus            RowStatus
   }

xdsl2ChConfProfProfileName  OBJECT-TYPE
   SYNTAX      SnmpAdminString (SIZE(1..32))
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "This object identifies a row in this table."
   ::= { xdsl2ChConfProfileEntry 1 }

xdsl2ChConfProfMinDataRateDs  OBJECT-TYPE

   SYNTAX      Unsigned32
   UNITS       "bits/second"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Minimum Data Rate on Downstream direction.  The minimum net
       data rate for the bearer channel, coded in bit/s."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1
                 (Minimum data rate)"
   ::= { xdsl2ChConfProfileEntry 2 }

xdsl2ChConfProfMinDataRateUs  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "bits/second"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Minimum Data Rate on Upstream direction.  The minimum net
       data rate for the bearer channel, coded in bit/s."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1
                 (Minimum data rate)"
   ::= { xdsl2ChConfProfileEntry 3 }

xdsl2ChConfProfMinResDataRateDs  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "bits/second"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Minimum Reserved Data Rate on Downstream direction.  The
       minimum reserved net data rate for the bearer channel, coded
       in bit/s.  This parameter is used only if the Rate Adaptation
       Mode in the direction of the bearer channel (i.e.,
       xdsl2LConfProfRaModeDs) is set to 'dynamicRa'."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.2
                 (Minimum reserved data rate)"
   ::= { xdsl2ChConfProfileEntry 4 }

xdsl2ChConfProfMinResDataRateUs  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "bits/second"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Minimum Reserved Data Rate on Upstream direction.  The
       minimum reserved net data rate for the bearer channel, coded in
       bit/s.  This parameter is used only if the Rate Adaptation Mode
       in the direction of the bearer channel (i.e.,

       xdsl2LConfProfRaModeUs) is set to 'dynamicRa'."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.2
                 (Minimum reserved data rate)"
   ::= { xdsl2ChConfProfileEntry 5 }

xdsl2ChConfProfMaxDataRateDs  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "bits/second"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Maximum Data Rate on Downstream direction.  The maximum net
       data rate for the bearer channel, coded in bit/s."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.3
                 (Maximum data rate)"
   ::= { xdsl2ChConfProfileEntry 6 }

xdsl2ChConfProfMaxDataRateUs  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "bits/second"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Maximum Data Rate on Upstream direction.  The maximum net
       data rate for the bearer channel, coded in bit/s."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.3
                 (Maximum data rate)"
   ::= { xdsl2ChConfProfileEntry 7 }

xdsl2ChConfProfMinDataRateLowPwrDs  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "bits/second"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This parameter specifies the minimum net data rate for
       the bearer channel as desired by the operator of the system
       during the low power state (L1/L2).  The power management low
       power states L1 and L2 are defined in ITU-T Recommendations
       G.992.2 and G.992.3, respectively.
       The data rate is coded in steps of bit/s."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.5
                 (Minimum Data Rate in low power state)"
   ::= { xdsl2ChConfProfileEntry 8 }

xdsl2ChConfProfMinDataRateLowPwrUs  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "bits/second"

   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This parameter specifies the minimum net data rate for
       the bearer channel as desired by the operator of the system
       during the low power state (L1/L2).  The power management low
       power states L1 and L2 are defined in ITU-T Recommendations
       G.992.2 and G.992.3, respectively.
       The data rate is coded in steps of bit/s."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.5
                 (Minimum Data Rate in low power state)"
   ::= { xdsl2ChConfProfileEntry 9 }

xdsl2ChConfProfMaxDelayDs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..63)
   UNITS       "milliseconds"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Maximum Interleave Delay on Downstream direction.  The
       maximum one-way interleaving delay introduced by the PMS-TC on
       Downstream direction.  The xTUs shall choose the S (factor) and D
       (depth) values such that the actual one-way interleaving delay
       (Xdsl2ChStatusActDelay) is as close as possible to, but less than
       or equal to, xdsl2ChConfProfMaxDelayDs.  The delay is coded in
       ms, with the value 0 indicating no delay bound is being
       imposed."
   REFERENCE    "ITU-T G.997.1, paragraph #*******
                 (Maximum interleaving delay)"
   ::= { xdsl2ChConfProfileEntry 10 }

xdsl2ChConfProfMaxDelayUs  OBJECT-TYPE
   SYNTAX      Unsigned32(0..63)
   UNITS       "milliseconds"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Maximum Interleave Delay on Upstream direction.  The maximum
       one-way interleaving delay introduced by the PMS-TC on Upstream
       direction.  The xTUs shall choose the S (factor) and D (depth)
       values such that the actual one-way interleaving delay
       (Xdsl2ChStatusActDelay) is as close as possible to, but less than
       or equal to, xdsl2ChConfProfMaxDelayUs.  The delay is coded in
       ms, with the value 0 indicating no delay bound is being
       imposed."
   REFERENCE    "ITU-T G.997.1, paragraph #*******
                 (Maximum interleaving delay)"
   ::= { xdsl2ChConfProfileEntry 11 }

xdsl2ChConfProfMinProtectionDs  OBJECT-TYPE
   SYNTAX      Xdsl2SymbolProtection
   UNITS       "symbols"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This parameter specifies the minimum impulse noise
       protection for the bearer channel if it is transported over DMT
       symbols with a subcarrier spacing of 4.3125 kHz.  The impulse
       noise protection is expressed in DMT symbols with a subcarrier
       spacing of 4.3125 kHz and can take the values 1/2 and any integer
       from 0 to 16, inclusive.  If the xTU does not support the
       configured INPMIN value, it shall use the nearest supported
       impulse noise protection greater than INPMIN."
   REFERENCE    "ITU-T G.997.1, paragraph #******* (INPMINds)"
   DEFVAL       { noProtection }
   ::= { xdsl2ChConfProfileEntry 12 }

xdsl2ChConfProfMinProtectionUs  OBJECT-TYPE
   SYNTAX      Xdsl2SymbolProtection
   UNITS       "symbols"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This parameter specifies the minimum impulse noise
       protection for the bearer channel if it is transported over DMT
       symbols with a subcarrier spacing of 4.3125 kHz.  The impulse
       noise protection is expressed in DMT symbols with a subcarrier
       spacing of 4.3125 kHz and can take the values 1/2 and any integer
       from 0 to 16, inclusive.  If the xTU does not support the
       configured INPMIN value, it shall use the nearest supported
       impulse noise protection greater than INPMIN."
   REFERENCE    "ITU-T G.997.1, paragraph #******* (INPMINus)"
   DEFVAL       { noProtection }
   ::= { xdsl2ChConfProfileEntry 13 }

xdsl2ChConfProfMinProtection8Ds  OBJECT-TYPE
   SYNTAX      Xdsl2SymbolProtection8
   UNITS       "symbols"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This parameter specifies the minimum impulse noise
       protection for the bearer channel if it is transported over DMT
       symbols with a subcarrier spacing of 8.625 kHz.  The impulse
       noise protection is expressed in DMT symbols with a subcarrier
       spacing of 8.625 kHz."
   REFERENCE    "ITU-T G.997.1, paragraph #******* (INPMIN8ds)"

   DEFVAL       { noProtection }
   ::= { xdsl2ChConfProfileEntry 14 }

xdsl2ChConfProfMinProtection8Us  OBJECT-TYPE
   SYNTAX      Xdsl2SymbolProtection8
   UNITS       "symbols"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This parameter specifies the minimum impulse noise
       protection for the bearer channel if it is transported over DMT
       symbols with a subcarrier spacing of 8.625 kHz.  The impulse
       noise protection is expressed in DMT symbols with a subcarrier
       spacing of 8.625 kHz."
   REFERENCE    "ITU-T G.997.1, paragraph #******* (INPMIN8us)"
   DEFVAL       { noProtection }
   ::= { xdsl2ChConfProfileEntry 15 }

xdsl2ChConfProfMaxBerDs  OBJECT-TYPE
   SYNTAX      Xdsl2MaxBer
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Maximum Bit Error Ratio on Downstream direction.  The
       maximum bit error ratio for the bearer channel."
   REFERENCE    "ITU-T G.997.1, paragraph #*******
                 (Maximum bit error ratio)"
   DEFVAL       { eminus5 }
  ::= { xdsl2ChConfProfileEntry 16 }

xdsl2ChConfProfMaxBerUs  OBJECT-TYPE
   SYNTAX      Xdsl2MaxBer
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Maximum Bit Error Ratio on Upstream direction.  The maximum
       bit error ratio for the bearer channel."
   REFERENCE    "ITU-T G.997.1, paragraph #*******
                 (Maximum bit error ratio)"
   DEFVAL       { eminus5 }
   ::= { xdsl2ChConfProfileEntry 17 }

xdsl2ChConfProfUsDataRateDs  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "bits/second"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION

      "Data Rate Threshold Upshift for Downstream direction.  An
       'Up-Shift rate change' event is triggered when the
       actual downstream data rate exceeds, by more than the threshold,
       the data rate at the last entry into Showtime.  The parameter is
       coded in bit/s."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1
                 (Data rate threshold upshift)"
   ::= { xdsl2ChConfProfileEntry 18 }

xdsl2ChConfProfDsDataRateDs  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "bits/second"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Data Rate Threshold Downshift for Downstream direction.  A
       'Down-Shift rate change' event is triggered when the
       actual downstream data rate is below the data rate at the last
       entry into Showtime, by more than the threshold.  The parameter
       is coded in bit/s."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.2
                 (Data rate threshold downshift)"
   ::= { xdsl2ChConfProfileEntry 19 }

xdsl2ChConfProfUsDataRateUs  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "bits/second"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Data Rate Threshold Upshift for Upstream direction.  An
       'Up-Shift rate change' event is triggered when the
       actual upstream data rate exceeds, by more than the threshold,
       the data rate at the last entry into Showtime.  The parameter is
       coded in bit/s."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1
                 (Data rate threshold upshift)"
   ::= { xdsl2ChConfProfileEntry 20 }

xdsl2ChConfProfDsDataRateUs  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "bits/second"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Data Rate Threshold Downshift for Upstream direction.  A
       'Down-Shift rate change' event is triggered when the
       actual upstream data rate is below the data rate at the last

       entry into Showtime, by more than the threshold.  The parameter
       is coded in bit/s."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.2
                 (Data rate threshold downshift)"
   ::= { xdsl2ChConfProfileEntry 21 }

xdsl2ChConfProfImaEnabled  OBJECT-TYPE
   SYNTAX      TruthValue
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "IMA Mode Enable.  The parameter enables the IMA operation
       mode in the ATM Data Path.  Relevant only if the channel is of
       ATM Data Path.  When in 'enable' state, the ATM Data
       Path should comply with the requirements for IMA
       transmission."
   REFERENCE    "ITU-T G.997.1, paragraph #*******
                 (IMA operation mode enable parameter)"
   DEFVAL       { false }
 ::= { xdsl2ChConfProfileEntry 22 }

xdsl2ChConfProfMaxDelayVar  OBJECT-TYPE
   SYNTAX      Unsigned32(1..255)
   UNITS       "0.1 milliseconds"
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Maximum delay variation (DVMAX).
       This optional VDSL2-specific parameter specifies the maximum
       value for the delay variation allowed in an OLR procedure.
       It is ranges from 1 to 254 units of 0.1 milliseconds (i.e., 0.1
       to 25.4 milliseconds) with the special value 255, which indicates
       that no delay variation bound is imposed."
   REFERENCE    "ITU-T G.997.1 Amendment 1, paragraph #*******
                 (DVMAX)"
   DEFVAL       { 255 }
 ::= { xdsl2ChConfProfileEntry 23 }

xdsl2ChConfProfInitPolicy  OBJECT-TYPE
   SYNTAX      Xdsl2ChInitPolicy
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "Channel Initialization Policy Selection (CIPOLICY).
       This optional parameter indicates which policy shall be applied
       to determine the transceiver configuration parameters at
       initialization.  Those policies are defined in the respective
       Recommendations."

   REFERENCE    "ITU-T G.997.1 Amendment 1, paragraph #********
                 (CIPOLICY)"
   DEFVAL       { policy0 }
 ::= { xdsl2ChConfProfileEntry 24 }

xdsl2ChConfProfRowStatus  OBJECT-TYPE
   SYNTAX      RowStatus
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This object is used to create a new row or to modify or
       delete an existing row in this table.

       A profile is activated by setting this object to 'active'.

       Before a profile can be deleted or taken out of service (by
       setting this object to 'destroy' or 'notInService'), it MUST be
       first unreferenced from all associated templates.

       A row in xdsl2ChConfProfTable is said to be unreferenced when
       there is no instance of xdsl2LConfTempChan1ConfProfile,
       xdsl2LConfTempChan2ConfProfile, xdsl2LConfTempChan3ConfProfile,
       or xdsl2LConfTempChan4ConfProfile that refers to
       the row."
   ::= { xdsl2ChConfProfileEntry 25 }

------------------------------------------------
--      xdsl2LineAlarmConfTemplateTable       --
------------------------------------------------

xdsl2LineAlarmConfTemplateTable  OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2LineAlarmConfTemplateEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2LineAlarConfTemplateTable contains DSL
       line alarm configuration templates.

        Entries in this table MUST be maintained in a persistent
        manner."
   ::= { xdsl2ProfileAlarmConf 1 }

xdsl2LineAlarmConfTemplateEntry  OBJECT-TYPE
   SYNTAX      Xdsl2LineAlarmConfTemplateEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "A default template with an index of 'DEFVAL' will always

       exist, and its parameters will be set to vendor-specific values,
       unless otherwise specified in this document."
   INDEX  { xdsl2LAlarmConfTempTemplateName }
   ::= { xdsl2LineAlarmConfTemplateTable 1 }

Xdsl2LineAlarmConfTemplateEntry  ::=
   SEQUENCE {
      xdsl2LAlarmConfTempTemplateName      SnmpAdminString,
      xdsl2LAlarmConfTempLineProfile       SnmpAdminString,
      xdsl2LAlarmConfTempChan1ConfProfile  SnmpAdminString,
      xdsl2LAlarmConfTempChan2ConfProfile  SnmpAdminString,
      xdsl2LAlarmConfTempChan3ConfProfile  SnmpAdminString,
      xdsl2LAlarmConfTempChan4ConfProfile  SnmpAdminString,
      xdsl2LAlarmConfTempRowStatus         RowStatus
   }

xdsl2LAlarmConfTempTemplateName  OBJECT-TYPE
   SYNTAX      SnmpAdminString (SIZE(1..32))
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "This object identifies a row in this table."
   ::= { xdsl2LineAlarmConfTemplateEntry 1 }

xdsl2LAlarmConfTempLineProfile  OBJECT-TYPE
   SYNTAX      SnmpAdminString (SIZE(1..32))
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The value of this object identifies the row in the DSL Line
       Thresholds Configuration Profile Table
       (xdsl2LineAlarmConfProfileTable) that applies to this line."
   REFERENCE    "DSL Forum TR-129, paragraph #8.2"
   DEFVAL       { "DEFVAL" }
   ::= { xdsl2LineAlarmConfTemplateEntry 2 }

xdsl2LAlarmConfTempChan1ConfProfile  OBJECT-TYPE
   SYNTAX      SnmpAdminString (SIZE(1..32))
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The value of this object identifies the row in the DSL
       Channel Thresholds Configuration Profile Table
       (xdsl2ChAlarmConfProfileTable) that applies for DSL bearer
       channel #1.  The channel profile name specified here MUST match
       the name of an existing row in the xdsl2ChAlarmConfProfileTable
       table."
   REFERENCE    "DSL Forum TR-129, paragraph #8.4"

   DEFVAL       { "DEFVAL" }
   ::= { xdsl2LineAlarmConfTemplateEntry 3 }

xdsl2LAlarmConfTempChan2ConfProfile  OBJECT-TYPE
   SYNTAX      SnmpAdminString (SIZE(0..32))
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The value of this object identifies the row in the DSL
       Channel Thresholds Configuration Profile Table
       (xdsl2ChAlarmConfProfileTable) that applies for DSL bearer
       channel #2.  The channel profile name specified here MUST match
       the name of an existing row in the xdsl2ChAlarmConfProfileTable
       table.  If the channel is unused, then the object is set to a
       zero-length string."
   REFERENCE    "DSL Forum TR-129, paragraph #8.4"
   DEFVAL       { "" }
   ::= { xdsl2LineAlarmConfTemplateEntry 4 }

xdsl2LAlarmConfTempChan3ConfProfile  OBJECT-TYPE
   SYNTAX      SnmpAdminString (SIZE(0..32))
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The value of this object identifies the row in the DSL
       Channel Thresholds Configuration Profile Table
       (xdsl2ChAlarmConfProfileTable) that applies for DSL bearer
       channel #3.  The channel profile name specified here MUST match
       the name of an existing row in the xdsl2ChAlarmConfProfileTable
       table.
       This object may be set to a non-zero-length string only if
       xdsl2LAlarmConfTempChan2ConfProfile contains a non-zero-length
       string."
   REFERENCE    "DSL Forum TR-129, paragraph #8.4"
   DEFVAL       { "" }
   ::= { xdsl2LineAlarmConfTemplateEntry 5 }

xdsl2LAlarmConfTempChan4ConfProfile  OBJECT-TYPE
   SYNTAX      SnmpAdminString (SIZE(0..32))
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "The value of this object identifies the row in the DSL
       Channel Thresholds Configuration Profile Table
       (xdsl2ChAlarmConfProfileTable) that applies for DSL bearer
       channel #4.  The channel profile name specified here MUST match
       the name of an existing row in the xdsl2ChAlarmConfProfileTable
       table.

       This object may be set to a non-zero-length string only if
       xdsl2LAlarmConfTempChan3ConfProfile contains a non-zero-length
       string."
   REFERENCE    "DSL Forum TR-129, paragraph #8.4"
   DEFVAL       { "" }
   ::= { xdsl2LineAlarmConfTemplateEntry 6 }

xdsl2LAlarmConfTempRowStatus  OBJECT-TYPE
   SYNTAX      RowStatus
   MAX-ACCESS  read-create
   STATUS      current
   DESCRIPTION
      "This object is used to create a new row or to modify or
       delete an existing row in this table.

       A template is activated by setting this object to 'active'.

       Before a template can be deleted or taken out of service (by
       setting this object to 'destroy' or 'notInService'), it MUST be
       first unreferenced from all associated lines.

       A row in this table is said to be unreferenced when there is no
       instance of xdsl2LineAlarmConfTemplate that refers to the
       row."
   ::= { xdsl2LineAlarmConfTemplateEntry 7 }

------------------------------------------------
--      xdsl2LineAlarmConfProfileTable        --
------------------------------------------------

xdsl2LineAlarmConfProfileTable  OBJECT-TYPE
     SYNTAX      SEQUENCE  OF  Xdsl2LineAlarmConfProfileEntry
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
      "The table xdsl2LineAlarmConfProfileTable contains DSL
       line performance threshold values.

       If a performance counter exceeds the threshold value specified
       in this table, then the SNMP agent will issue a threshold trap.
       Each performance counter has a unique trap type
       (see NOTIFICATION-TYPE definitions below).
       One trap will be sent per interval, per interface, per trap type.
       A value of 0 will disable the trap.

       Entries in this table MUST be maintained in a persistent
       manner."
     ::= { xdsl2ProfileAlarmConf 2 }

xdsl2LineAlarmConfProfileEntry  OBJECT-TYPE
     SYNTAX      Xdsl2LineAlarmConfProfileEntry
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
      "A default profile with an index of 'DEFVAL' will always
       exist, and its parameters will be set to vendor-specific values,
       unless otherwise specified in this document."
     INDEX  { xdsl2LineAlarmConfProfileName }
     ::= { xdsl2LineAlarmConfProfileTable 1 }

Xdsl2LineAlarmConfProfileEntry ::=
     SEQUENCE {
     xdsl2LineAlarmConfProfileName                SnmpAdminString,
     xdsl2LineAlarmConfProfileXtucThresh15MinFecs
                                          HCPerfIntervalThreshold,
     xdsl2LineAlarmConfProfileXtucThresh15MinEs
                                          HCPerfIntervalThreshold,
     xdsl2LineAlarmConfProfileXtucThresh15MinSes
                                           HCPerfIntervalThreshold,
     xdsl2LineAlarmConfProfileXtucThresh15MinLoss
                                           HCPerfIntervalThreshold,
     xdsl2LineAlarmConfProfileXtucThresh15MinUas
                                           HCPerfIntervalThreshold,
     xdsl2LineAlarmConfProfileXturThresh15MinFecs
                                           HCPerfIntervalThreshold,
     xdsl2LineAlarmConfProfileXturThresh15MinEs
                                           HCPerfIntervalThreshold,
     xdsl2LineAlarmConfProfileXturThresh15MinSes
                                           HCPerfIntervalThreshold,
     xdsl2LineAlarmConfProfileXturThresh15MinLoss
                                           HCPerfIntervalThreshold,
     xdsl2LineAlarmConfProfileXturThresh15MinUas
                                           HCPerfIntervalThreshold,

     xdsl2LineAlarmConfProfileThresh15MinFailedFullInt   Unsigned32,
     xdsl2LineAlarmConfProfileThresh15MinFailedShrtInt   Unsigned32,

     xdsl2LineAlarmConfProfileRowStatus                   RowStatus
     }

xdsl2LineAlarmConfProfileName  OBJECT-TYPE
     SYNTAX      SnmpAdminString (SIZE(1..32))
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
     "This object identifies a row in this table."
     ::= { xdsl2LineAlarmConfProfileEntry 1 }

xdsl2LineAlarmConfProfileXtucThresh15MinFecs  OBJECT-TYPE
     SYNTAX      HCPerfIntervalThreshold
     UNITS       "seconds"
     MAX-ACCESS  read-create
     STATUS      current
     DESCRIPTION
     "A threshold for the xdsl2PMLCurr15MFecs counter, when
      xdsl2PMLCurrUnit is xtuc {1}.
      The value 0 means that no threshold is specified for the
      associated counter."
   REFERENCE    "ITU-T G.997.1, paragraph #*******"
   DEFVAL       { 0 }
     ::= { xdsl2LineAlarmConfProfileEntry 2 }

xdsl2LineAlarmConfProfileXtucThresh15MinEs  OBJECT-TYPE
     SYNTAX      HCPerfIntervalThreshold
     UNITS       "seconds"
     MAX-ACCESS  read-create
     STATUS      current
     DESCRIPTION
     "A threshold for the xdsl2PMLCurr15MEs counter, when
      xdsl2PMLCurrUnit is xtuc {1}.
      The value 0 means that no threshold is specified for the
      associated counter."
   REFERENCE    "ITU-T G.997.1, paragraph #*******"
   DEFVAL       { 0 }
     ::= { xdsl2LineAlarmConfProfileEntry 3 }

xdsl2LineAlarmConfProfileXtucThresh15MinSes  OBJECT-TYPE
     SYNTAX      HCPerfIntervalThreshold
     UNITS       "seconds"
     MAX-ACCESS  read-create
     STATUS      current
     DESCRIPTION
     "A threshold for the xdsl2PMLCurr15MSes counter, when
      xdsl2PMLCurrUnit is xtuc {1}.
      The value 0 means that no threshold is specified for the
      associated counter."
   REFERENCE    "ITU-T G.997.1, paragraph #*******"
   DEFVAL       { 0 }
     ::= { xdsl2LineAlarmConfProfileEntry 4 }

xdsl2LineAlarmConfProfileXtucThresh15MinLoss  OBJECT-TYPE
     SYNTAX      HCPerfIntervalThreshold
     UNITS       "seconds"
     MAX-ACCESS  read-create
     STATUS      current
     DESCRIPTION

     "A threshold for the xdsl2PMLCurr15MLoss counter, when
      xdsl2PMLCurrUnit is xtuc {1}.
      The value 0 means that no threshold is specified for the
      associated counter."
   REFERENCE    "ITU-T G.997.1, paragraph #*******"
   DEFVAL       { 0 }
     ::= { xdsl2LineAlarmConfProfileEntry 5 }

xdsl2LineAlarmConfProfileXtucThresh15MinUas  OBJECT-TYPE
     SYNTAX      HCPerfIntervalThreshold
     UNITS       "seconds"
     MAX-ACCESS  read-create
     STATUS      current
     DESCRIPTION
     "A threshold for the xdsl2PMLCurr15MUas counter, when
      xdsl2PMLCurrUnit is xtuc {1}.
      The value 0 means that no threshold is specified for the
      associated counter."
   REFERENCE    "ITU-T G.997.1, paragraph #*******"
   DEFVAL       { 0 }
     ::= { xdsl2LineAlarmConfProfileEntry 6 }

xdsl2LineAlarmConfProfileXturThresh15MinFecs  OBJECT-TYPE
     SYNTAX      HCPerfIntervalThreshold
     UNITS       "seconds"
     MAX-ACCESS  read-create
     STATUS      current
     DESCRIPTION
     "A threshold for the xdsl2PMLCurr15MFecs counter, when
      xdsl2PMLCurrUnit is xtur {2}.
      The value 0 means that no threshold is specified for the
      associated counter."
   REFERENCE    "ITU-T G.997.1, paragraph #*******"
   DEFVAL       { 0 }
     ::= { xdsl2LineAlarmConfProfileEntry 7 }

xdsl2LineAlarmConfProfileXturThresh15MinEs  OBJECT-TYPE
     SYNTAX      HCPerfIntervalThreshold
     UNITS       "seconds"
     MAX-ACCESS  read-create
     STATUS      current
     DESCRIPTION
     "A threshold for the xdsl2PMLCurr15MEs counter, when
      xdsl2PMLCurrUnit is xtur {2}.
      The value 0 means that no threshold is specified for the
      associated counter."
   REFERENCE    "ITU-T G.997.1, paragraph #*******"
   DEFVAL       { 0 }

     ::= { xdsl2LineAlarmConfProfileEntry 8 }

xdsl2LineAlarmConfProfileXturThresh15MinSes  OBJECT-TYPE
     SYNTAX      HCPerfIntervalThreshold
     UNITS       "seconds"
     MAX-ACCESS  read-create
     STATUS      current
     DESCRIPTION
     "A threshold for the xdsl2PMLCurr15MSes counter, when
      xdsl2PMLCurrUnit is xtur {2}.
      The value 0 means that no threshold is specified for the
      associated counter."
   REFERENCE    "ITU-T G.997.1, paragraph #*******"
   DEFVAL       { 0 }
     ::= { xdsl2LineAlarmConfProfileEntry 9 }

xdsl2LineAlarmConfProfileXturThresh15MinLoss  OBJECT-TYPE
     SYNTAX      HCPerfIntervalThreshold
     UNITS       "seconds"
     MAX-ACCESS  read-create
     STATUS      current
     DESCRIPTION
     "A threshold for the xdsl2PMLCurr15MLoss counter, when
      xdsl2PMLCurrUnit is xtur {2}.
      The value 0 means that no threshold is specified for the
      associated counter."
   REFERENCE    "ITU-T G.997.1, paragraph #*******"
   DEFVAL       { 0 }
     ::= { xdsl2LineAlarmConfProfileEntry 10 }

xdsl2LineAlarmConfProfileXturThresh15MinUas  OBJECT-TYPE
     SYNTAX      HCPerfIntervalThreshold
     UNITS       "seconds"
     MAX-ACCESS  read-create
     STATUS      current
     DESCRIPTION
     "A threshold for the xdsl2PMLCurr15MUas counter, when
      xdsl2PMLCurrUnit is xtur {2}.
      The value 0 means that no threshold is specified for the
      associated counter."
   REFERENCE    "ITU-T G.997.1, paragraph #*******"
   DEFVAL       { 0 }
     ::= { xdsl2LineAlarmConfProfileEntry 11 }

xdsl2LineAlarmConfProfileThresh15MinFailedFullInt  OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-create
     STATUS      current

     DESCRIPTION
     "A threshold for the xdsl2PMLInitCurr15MfailedFullInits
      counter.
      The value 0 means that no threshold is specified for the
      associated counter."
   REFERENCE    "ITU-T G.997.1, paragraph #*******"
   DEFVAL       { 0 }
     ::= { xdsl2LineAlarmConfProfileEntry 12 }

xdsl2LineAlarmConfProfileThresh15MinFailedShrtInt  OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-create
     STATUS      current
     DESCRIPTION
     "A threshold for the xdsl2PMLInitCurr15MFailedShortInits
      counter.
      The value 0 means that no threshold is specified for the
      associated counter."
   REFERENCE    "ITU-T G.997.1, paragraph #*******"
   DEFVAL       { 0 }
     ::= { xdsl2LineAlarmConfProfileEntry 13 }

xdsl2LineAlarmConfProfileRowStatus  OBJECT-TYPE
     SYNTAX      RowStatus
     MAX-ACCESS  read-create
     STATUS      current
     DESCRIPTION
      "This object is used to create a new row or to modify or
       delete an existing row in this table.

       A profile is activated by setting this object to 'active'.

       Before a profile can be deleted or taken out of service (by
       setting this object to 'destroy' or 'notInService'), it MUST be
       first unreferenced from all associated templates.

       A row in this table is said to be unreferenced when there is no
       instance of xdsl2LAlarmConfTempLineProfile that refers to the
       row."
     ::= { xdsl2LineAlarmConfProfileEntry 14 }

------------------------------------------------
--       xdsl2ChAlarmConfProfileTable         --
------------------------------------------------

xdsl2ChAlarmConfProfileTable  OBJECT-TYPE
     SYNTAX      SEQUENCE  OF  Xdsl2ChAlarmConfProfileEntry
     MAX-ACCESS  not-accessible

     STATUS      current
     DESCRIPTION
      "The table xdsl2ChAlarmConfProfileTable contains DSL channel
       performance threshold values.

       If a performance counter exceeds the threshold value specified
       in this table, then the SNMP agent will issue a threshold trap.
       Each performance counter has a unique trap type
       (see NOTIFICATION-TYPE definitions below).
       One trap will be sent per interval per interface per trap type.
       A value of 0 will disable the trap.

       Entries in this table MUST be maintained in a persistent
       manner."
     ::= { xdsl2ProfileAlarmConf 3 }

xdsl2ChAlarmConfProfileEntry  OBJECT-TYPE
     SYNTAX      Xdsl2ChAlarmConfProfileEntry
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
      "A default profile with an index of 'DEFVAL' will always
       exist, and its parameters will be set to vendor-specific values,
       unless otherwise specified in this document."
     INDEX  { xdsl2ChAlarmConfProfileName }
     ::= { xdsl2ChAlarmConfProfileTable 1 }

Xdsl2ChAlarmConfProfileEntry ::=
     SEQUENCE {
     xdsl2ChAlarmConfProfileName
                                                     SnmpAdminString,
     xdsl2ChAlarmConfProfileXtucThresh15MinCodingViolations
                                                     Unsigned32,
     xdsl2ChAlarmConfProfileXtucThresh15MinCorrected Unsigned32,
     xdsl2ChAlarmConfProfileXturThresh15MinCodingViolations
                                                     Unsigned32,
     xdsl2ChAlarmConfProfileXturThresh15MinCorrected Unsigned32,
     xdsl2ChAlarmConfProfileRowStatus                RowStatus
     }

xdsl2ChAlarmConfProfileName  OBJECT-TYPE
     SYNTAX      SnmpAdminString (SIZE(1..32))
     MAX-ACCESS  not-accessible
     STATUS      current
     DESCRIPTION
     "This object identifies a row in this table."
     ::= { xdsl2ChAlarmConfProfileEntry 1 }

xdsl2ChAlarmConfProfileXtucThresh15MinCodingViolations OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-create
     STATUS      current
     DESCRIPTION
     "A threshold for the xdsl2PMChCurr15MCodingViolations
      counter, when xdsl2PMChCurrUnit is xtuc {1}.
      The value 0 means that no threshold is specified for the
      associated counter."
   REFERENCE    "ITU-T G.997.1, paragraph #*******"
   DEFVAL       { 0 }
     ::= { xdsl2ChAlarmConfProfileEntry 2 }

xdsl2ChAlarmConfProfileXtucThresh15MinCorrected  OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-create
     STATUS      current
     DESCRIPTION
     "A threshold for the xdsl2PMChCurr15MCorrectedBlocks
      counter, when xdsl2PMChCurrUnit is xtuc {1}.
      The value 0 means that no threshold is specified for the
      associated counter."
   REFERENCE    "ITU-T G.997.1, paragraph #*******"
   DEFVAL       { 0 }
     ::= { xdsl2ChAlarmConfProfileEntry 3 }

xdsl2ChAlarmConfProfileXturThresh15MinCodingViolations  OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-create
     STATUS      current
     DESCRIPTION
     "A threshold for the xdsl2PMChCurr15MCodingViolations
      counter, when xdsl2PMChCurrUnit is xtur {2}.
      The value 0 means that no threshold is specified for the
      associated counter."
   REFERENCE    "ITU-T G.997.1, paragraph #*******"
   DEFVAL       { 0 }
     ::= { xdsl2ChAlarmConfProfileEntry 4 }

xdsl2ChAlarmConfProfileXturThresh15MinCorrected  OBJECT-TYPE
     SYNTAX      Unsigned32
     MAX-ACCESS  read-create
     STATUS      current
     DESCRIPTION
     "A threshold for the xdsl2PMChCurr15MCorrectedBlocks
      counter, when xdsl2PMChCurrUnit is xtur {2}.
      The value 0 means that no threshold is specified for the
      associated counter."

   REFERENCE    "ITU-T G.997.1, paragraph #*******"
   DEFVAL       { 0 }
     ::= { xdsl2ChAlarmConfProfileEntry 5 }

xdsl2ChAlarmConfProfileRowStatus  OBJECT-TYPE
     SYNTAX      RowStatus
     MAX-ACCESS  read-create
     STATUS      current
     DESCRIPTION
      "This object is used to create a new row or to modify or
       delete an existing row in this table.

       A profile is activated by setting this object to 'active'.

       Before a profile can be deleted or taken out of service (by
       setting this object to 'destroy' or 'notInService'), it MUST be
       first unreferenced from all associated templates.

       A row in xdsl2ChConfProfTable is said to be unreferenced when
       there is no instance of xdsl2LAlarmConfTempChan1ConfProfile,
       xdsl2LAlarmConfTempChan2ConfProfile,
       xdsl2LAlarmConfTempChan3ConfProfile, or
       xdsl2LAlarmConfTempChan4ConfProfile that refers to
       the row."
     ::= { xdsl2ChAlarmConfProfileEntry 6 }

------------------------------------------------
--          PM line current counters          --
------------------------------------------------

xdsl2PMLineCurrTable  OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2PMLineCurrEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2PMLineCurrTable contains current Performance
       Monitoring results for DSL lines."
   ::= { xdsl2PMLine 1 }

xdsl2PMLineCurrEntry  OBJECT-TYPE
   SYNTAX      Xdsl2PMLineCurrEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
       "One index of this table is an interface index where the
        interface has an ifType of vdsl2(251).  A second index of this
        table is the termination unit."
   INDEX  { ifIndex, xdsl2PMLCurrUnit }

   ::= { xdsl2PMLineCurrTable 1 }

Xdsl2PMLineCurrEntry  ::=
   SEQUENCE {
      xdsl2PMLCurrUnit                    Xdsl2Unit,
      xdsl2PMLCurr15MValidIntervals       Unsigned32,
      xdsl2PMLCurr15MInvalidIntervals     Unsigned32,
      xdsl2PMLCurr15MTimeElapsed          HCPerfTimeElapsed,
      xdsl2PMLCurr15MFecs                 Counter32,
      xdsl2PMLCurr15MEs                   Counter32,
      xdsl2PMLCurr15MSes                  Counter32,
      xdsl2PMLCurr15MLoss                 Counter32,
      xdsl2PMLCurr15MUas                  Counter32,
      xdsl2PMLCurr1DayValidIntervals      Unsigned32,
      xdsl2PMLCurr1DayInvalidIntervals    Unsigned32,
      xdsl2PMLCurr1DayTimeElapsed         HCPerfTimeElapsed,
      xdsl2PMLCurr1DayFecs                Counter32,
      xdsl2PMLCurr1DayEs                  Counter32,
      xdsl2PMLCurr1DaySes                 Counter32,
      xdsl2PMLCurr1DayLoss                Counter32,
      xdsl2PMLCurr1DayUas                 Counter32
   }

xdsl2PMLCurrUnit  OBJECT-TYPE
   SYNTAX      Xdsl2Unit
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The termination unit."
   ::= { xdsl2PMLineCurrEntry 1 }

xdsl2PMLCurr15MValidIntervals  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..96)
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The number of 15-minute PM intervals for which data
       was collected.  The value will typically be equal to the maximum
       number of 15-minute intervals the implementation is planned to
       store (i.e., beyond the scope of this MIB module) unless the
       measurement was (re-)started recently, in which case the value
       will be the number of complete 15-minute intervals for which
       the agent has at least some data.  In certain cases (e.g., in
       the case where the agent is a proxy), it is possible that some
       intervals are unavailable.  In this case, this interval is the
       maximum interval number for which data is available."
   ::= { xdsl2PMLineCurrEntry 2 }

xdsl2PMLCurr15MInvalidIntervals  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..96)
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The number of 15-minute PM intervals for which no data is
       available.  The value will typically be zero except in cases
       where the data for some intervals are not available (e.g.,
       in proxy situations)."
   ::= { xdsl2PMLineCurrEntry 3 }

xdsl2PMLCurr15MTimeElapsed  OBJECT-TYPE
   SYNTAX      HCPerfTimeElapsed
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Total elapsed seconds in this interval."
   ::= { xdsl2PMLineCurrEntry 4 }

xdsl2PMLCurr15MFecs  OBJECT-TYPE
   SYNTAX      Counter32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of seconds during this interval that there was at
       least one FEC correction event for one or more bearer channels in
       this line.  This parameter is inhibited during UAS or SES."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1 (FECS-L)
                 and paragraph #*******.1 (FECS-LFE)"
   ::= { xdsl2PMLineCurrEntry 5 }

xdsl2PMLCurr15MEs  OBJECT-TYPE
   SYNTAX      Counter32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of seconds during this interval that there was:
          xTU-C: CRC-8 >= 1 for one or more bearer channels OR
                 LOS >= 1 OR SEF >=1 OR LPR >= 1.
          xTU-R: FEBE >= 1 for one or more bearer channels OR
                 LOS-FE >=1 OR RDI >=1 OR LPR-FE >=1.
       This parameter is inhibited during UAS."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.2 (ES-L)
                 and paragraph #*******.2 (ES-LFE)"
   ::= { xdsl2PMLineCurrEntry 6 }

xdsl2PMLCurr15MSes  OBJECT-TYPE
   SYNTAX      Counter32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of seconds during this interval that there was:
          xTU-C: (CRC-8 anomalies in one or more of the
                 received bearer channels) >= 18 OR LOS >= 1
                 OR SEF >= 1 OR LPR >= 1.
          xTU-R: (FEBE anomalies in one or more of the
                 received bearer channels) >= 18 OR LOS-FE >= 1
                 OR RDI >= 1 OR LPR-FE >= 1.
       This parameter is inhibited during UAS."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.3 (SES-L)
                 and paragraph #*******.3 (SES-LFE)"
   ::= { xdsl2PMLineCurrEntry 7 }

xdsl2PMLCurr15MLoss  OBJECT-TYPE
   SYNTAX      Counter32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of seconds during this interval that there was LOS (or
       LOS-FE for xTU-R)."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4 (LOSS-L)
                 and paragraph #*******.4 (LOSS-LFE)"
   ::= { xdsl2PMLineCurrEntry 8 }

xdsl2PMLCurr15MUas  OBJECT-TYPE
   SYNTAX      Counter32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of seconds in Unavailability State during this
       interval.  Unavailability begins at the onset of 10 contiguous
       severely errored seconds, and ends at the onset of 10 contiguous
       seconds with no severely errored seconds."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.5 (UAS-L)
                 and paragraph #*******.5 (UAS-LFE)"
   ::= { xdsl2PMLineCurrEntry 9 }

xdsl2PMLCurr1DayValidIntervals  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..30)
   MAX-ACCESS  read-only
   STATUS      current

   DESCRIPTION
      "The number of 24-hour PM intervals for which data was
       collected.  The value will typically be equal to the maximum
       number of 24-hour intervals the implementation is planned to
       store (i.e., beyond the scope of this MIB module) unless the
       measurement was (re-)started recently, in which case the value
       will be the number of complete 24-hour intervals for which
       the agent has at least some data.  In certain cases (e.g., in
       the case where the agent is a proxy), it is possible that some
       intervals are unavailable.  In this case, this interval is the
       maximum interval number for which data is available."
   ::= { xdsl2PMLineCurrEntry 10 }

xdsl2PMLCurr1DayInvalidIntervals  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..30)
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The number of 24-hour PM intervals for which no data is
       available.  The value will typically be zero except in cases
       where the data for some intervals are not available (e.g.,
       in proxy situations)."
   ::= { xdsl2PMLineCurrEntry 11 }

xdsl2PMLCurr1DayTimeElapsed  OBJECT-TYPE
   SYNTAX      HCPerfTimeElapsed
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Total elapsed seconds in this interval."
   ::= { xdsl2PMLineCurrEntry 12 }

xdsl2PMLCurr1DayFecs  OBJECT-TYPE
   SYNTAX      Counter32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of seconds during this interval that there was at
       least one FEC correction event for one or more bearer channels in
       this line.  This parameter is inhibited during UAS or SES."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1 (FECS-L)
                 and paragraph #*******.1 (FECS-LFE)"
   ::= { xdsl2PMLineCurrEntry 13 }

xdsl2PMLCurr1DayEs  OBJECT-TYPE
   SYNTAX      Counter32

   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of seconds during this interval that there was:
          xTU-C: CRC-8 >= 1 for one or more bearer channels OR
                 LOS >= 1 OR SEF >= 1 OR LPR >= 1.
          xTU-R: FEBE >= 1 for one or more bearer channels OR
                 LOS-FE >= 1 OR RDI >= 1 OR LPR-FE >= 1.
       This parameter is inhibited during UAS."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.2 (ES-L)
                 and paragraph #*******.2 (ES-LFE)"
   ::= { xdsl2PMLineCurrEntry 14 }

xdsl2PMLCurr1DaySes  OBJECT-TYPE
   SYNTAX      Counter32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of seconds during this interval that there was:
          xTU-C: (CRC-8 anomalies in one or more of the
                 received bearer channels) >= 18 OR LOS >= 1
                 OR SEF >= 1 OR LPR >= 1.
          xTU-R: (FEBE anomalies in one or more of the
                 received bearer channels) >= 18 OR LOS-FE >= 1.
                 OR RDI >= 1 OR LPR-FE >= 1.
       This parameter is inhibited during UAS."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.3 (SES-L)
                 and paragraph #*******.3 (SES-LFE)"
   ::= { xdsl2PMLineCurrEntry 15 }

xdsl2PMLCurr1DayLoss  OBJECT-TYPE
   SYNTAX      Counter32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of seconds during this interval that there was LOS (or
       LOS-FE for xTU-R)."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4 (LOSS-L)
                 and paragraph #*******.4 (LOSS-LFE)"
   ::= { xdsl2PMLineCurrEntry 16 }

xdsl2PMLCurr1DayUas  OBJECT-TYPE
   SYNTAX      Counter32
   UNITS       "seconds"
   MAX-ACCESS  read-only

   STATUS      current
   DESCRIPTION
      "Count of seconds in Unavailability State during this
       interval.
       Unavailability begins at the onset of 10 contiguous severely
       errored seconds, and ends at the onset of 10 contiguous seconds
       with no severely errored seconds."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.5 (UAS-L)
                 and paragraph #*******.5 (UAS-LFE)"
   ::= { xdsl2PMLineCurrEntry 17 }

------------------------------------------------
--          PM line init current counters     --
------------------------------------------------

xdsl2PMLineInitCurrTable   OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2PMLineInitCurrEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2PMLineInitCurrTable contains current
       initialization counters for DSL lines."
   ::= { xdsl2PMLine 2 }

xdsl2PMLineInitCurrEntry  OBJECT-TYPE
   SYNTAX      Xdsl2PMLineInitCurrEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The index of this table is an interface index where the
       interface has an ifType of vdsl2(251)."
   INDEX  { ifIndex }
   ::= { xdsl2PMLineInitCurrTable 1 }

Xdsl2PMLineInitCurrEntry  ::=
   SEQUENCE {
      xdsl2PMLInitCurr15MValidIntervals       Unsigned32,
      xdsl2PMLInitCurr15MInvalidIntervals     Unsigned32,
      xdsl2PMLInitCurr15MTimeElapsed          Unsigned32,
      xdsl2PMLInitCurr15MFullInits            Unsigned32,
      xdsl2PMLInitCurr15MFailedFullInits      Unsigned32,
      xdsl2PMLInitCurr15MShortInits           Unsigned32,
      xdsl2PMLInitCurr15MFailedShortInits     Unsigned32,
      xdsl2PMLInitCurr1DayValidIntervals      Unsigned32,
      xdsl2PMLInitCurr1DayInvalidIntervals    Unsigned32,
      xdsl2PMLInitCurr1DayTimeElapsed         Unsigned32,
      xdsl2PMLInitCurr1DayFullInits           Unsigned32,
      xdsl2PMLInitCurr1DayFailedFullInits     Unsigned32,

      xdsl2PMLInitCurr1DayShortInits          Unsigned32,
      xdsl2PMLInitCurr1DayFailedShortInits    Unsigned32
   }

xdsl2PMLInitCurr15MValidIntervals  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..96)
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The number of 15-minute PM intervals for which data
       was collected.  The value will typically be equal to the maximum
       number of 15-minute intervals the implementation is planned to
       store (i.e., beyond the scope of this MIB module) unless the
       measurement was (re-)started recently, in which case the value
       will be the number of complete 15-minute intervals for which
       the agent has at least some data.  In certain cases (e.g., in
       the case where the agent is a proxy), it is possible that some
       intervals are unavailable.  In this case, this interval is the
       maximum interval number for which data is available."
   ::= { xdsl2PMLineInitCurrEntry 1 }

xdsl2PMLInitCurr15MInvalidIntervals  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..96)
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The number of 15-minute PM intervals for which no data is
       available.  The value will typically be zero except in cases
       where the data for some intervals are not available (e.g.,
       in proxy situations)."
   ::= { xdsl2PMLineInitCurrEntry 2 }

xdsl2PMLInitCurr15MTimeElapsed  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Total elapsed seconds in this interval."
   ::= { xdsl2PMLineInitCurrEntry 3 }

xdsl2PMLInitCurr15MFullInits  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of full initializations attempted on the line
       (successful and failed) during this interval."

   REFERENCE    "ITU-T G.997.1, paragraph #*******.1"
   ::= { xdsl2PMLineInitCurrEntry 4 }

xdsl2PMLInitCurr15MFailedFullInits  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of failed full initializations on the line during this
       interval."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.2"
   ::= { xdsl2PMLineInitCurrEntry 5 }

xdsl2PMLInitCurr15MShortInits  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of short initializations attempted on the line
       (successful and failed) during this interval."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.3"
   ::= { xdsl2PMLineInitCurrEntry 6 }

xdsl2PMLInitCurr15MFailedShortInits  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of failed short initializations on the line during
       this interval."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4"
   ::= { xdsl2PMLineInitCurrEntry 7 }

xdsl2PMLInitCurr1DayValidIntervals  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..30)
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The number of 24-hour PM intervals for which data was
       collected.  The value will typically be equal to the maximum
       number of 24-hour intervals the implementation is planned to
       store (i.e., beyond the scope of this MIB module) unless the
       measurement was (re-)started recently, in which case the value
       will be the number of complete 24-hour intervals for which
       the agent has at least some data.  In certain cases (e.g., in
       the case where the agent is a proxy), it is possible that some
       intervals are unavailable.  In this case, this interval is the
       maximum interval number for which data is available."

   ::= { xdsl2PMLineInitCurrEntry 8 }

xdsl2PMLInitCurr1DayInvalidIntervals  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..30)
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The number of 24-hour PM intervals for which no data is
       available.  The value will typically be zero except in cases
       where the data for some intervals are not available (e.g.,
       in proxy situations)."
   ::= { xdsl2PMLineInitCurrEntry 9 }

xdsl2PMLInitCurr1DayTimeElapsed  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Total elapsed seconds in this interval."
   ::= { xdsl2PMLineInitCurrEntry 10 }

xdsl2PMLInitCurr1DayFullInits  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of full initializations attempted on the line
       (successful and failed) during this interval."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1"
   ::= { xdsl2PMLineInitCurrEntry 11 }

xdsl2PMLInitCurr1DayFailedFullInits  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of failed full initializations on the line during this
       interval."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.2"
   ::= { xdsl2PMLineInitCurrEntry 12 }

xdsl2PMLInitCurr1DayShortInits  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of short initializations attempted on the line

       (successful and failed) during this interval."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.3"
   ::= { xdsl2PMLineInitCurrEntry 13 }

xdsl2PMLInitCurr1DayFailedShortInits  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of failed short initializations on the line during
       this interval."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4"
   ::= { xdsl2PMLineInitCurrEntry 14 }

-------------------------------------------
--       PM line history 15 Minutes      --
-------------------------------------------

xdsl2PMLineHist15MinTable    OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2PMLineHist15MinEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2PMLineHist15MinTable contains PM line history
       for 15-minute intervals of DSL line."
   ::= { xdsl2PMLine 3 }

xdsl2PMLineHist15MinEntry  OBJECT-TYPE
   SYNTAX      Xdsl2PMLineHist15MinEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "One index of this table is an interface index where the
      interface has an ifType of vdsl2(251).  A second index of this
      table is the transmission unit.  The third index is the interval
      number."
   INDEX  { ifIndex,
            xdsl2PMLHist15MUnit,
            xdsl2PMLHist15MInterval }
   ::= { xdsl2PMLineHist15MinTable 1 }

Xdsl2PMLineHist15MinEntry  ::=
   SEQUENCE {
      xdsl2PMLHist15MUnit                 Xdsl2Unit,
      xdsl2PMLHist15MInterval             Unsigned32,
      xdsl2PMLHist15MMonitoredTime        Unsigned32,
      xdsl2PMLHist15MFecs                 Counter32,
      xdsl2PMLHist15MEs                   Counter32,

      xdsl2PMLHist15MSes                  Counter32,
      xdsl2PMLHist15MLoss                 Counter32,
      xdsl2PMLHist15MUas                  Counter32,
      xdsl2PMLHist15MValidInterval        TruthValue
   }

xdsl2PMLHist15MUnit  OBJECT-TYPE
   SYNTAX      Xdsl2Unit
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The termination unit."
   ::= { xdsl2PMLineHist15MinEntry 1 }

xdsl2PMLHist15MInterval  OBJECT-TYPE
   SYNTAX      Unsigned32 (1..96)
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The interval number."
   ::= { xdsl2PMLineHist15MinEntry 2 }

xdsl2PMLHist15MMonitoredTime  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Total seconds monitored in this interval."
   ::= { xdsl2PMLineHist15MinEntry 3 }

xdsl2PMLHist15MFecs  OBJECT-TYPE
   SYNTAX      Counter32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of seconds during this interval that there was at
       least one FEC correction event for one or more bearer channels in
       this line.  This parameter is inhibited during UAS or SES."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1 (FECS-L)
                 and paragraph #*******.1 (FECS-LFE)"
   ::= { xdsl2PMLineHist15MinEntry 4 }

xdsl2PMLHist15MEs  OBJECT-TYPE
   SYNTAX      Counter32
   UNITS       "seconds"
   MAX-ACCESS  read-only

   STATUS      current
   DESCRIPTION
      "Count of seconds during this interval that there was:
          xTU-C: CRC-8 >= 1 for one or more bearer channels OR
                 LOS >= 1 OR SEF >= 1 OR LPR >= 1.
          xTU-R: FEBE >= 1 for one or more bearer channels OR
                 LOS-FE >= 1 OR RDI >= 1 OR LPR-FE >= 1.
       This parameter is inhibited during UAS."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.2 (ES-L)
                 and paragraph #*******.2 (ES-LFE)"
   ::= { xdsl2PMLineHist15MinEntry 5 }

xdsl2PMLHist15MSes  OBJECT-TYPE
   SYNTAX      Counter32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of seconds during this interval that there was:
          xTU-C: (CRC-8 anomalies in one or more of the
                 received bearer channels) >= 18 OR LOS >= 1
                 OR SEF >= 1 OR LPR >= 1.
          xTU-R: (FEBE anomalies in one or more of the
                 received bearer channels) >= 18 OR LOS-FE >= 1
                 OR RDI >= 1 OR LPR-FE >= 1.
       This parameter is inhibited during UAS."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.3 (SES-L)
                 and paragraph #*******.3 (SES-LFE)"
   ::= { xdsl2PMLineHist15MinEntry 6 }

xdsl2PMLHist15MLoss  OBJECT-TYPE
   SYNTAX      Counter32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of seconds during this interval that there was LOS (or
       LOS-FE for xTU-R)."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4 (LOSS-L)
                 and paragraph #*******.4 (LOSS-LFE)"
   ::= { xdsl2PMLineHist15MinEntry 7 }

xdsl2PMLHist15MUas  OBJECT-TYPE
   SYNTAX      Counter32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION

      "Count of seconds in Unavailability State during this
       interval.
       Unavailability begins at the onset of 10 contiguous severely
       errored seconds, and ends at the onset of 10 contiguous seconds
       with no severely errored seconds."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.5 (UAS-L)
                 and paragraph #*******.5 (UAS-LFE)"
   ::= { xdsl2PMLineHist15MinEntry 8 }

xdsl2PMLHist15MValidInterval  OBJECT-TYPE
   SYNTAX      TruthValue
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "This variable indicates if the data for this interval is
       valid."
   ::= { xdsl2PMLineHist15MinEntry 9 }

---------------------------------------
--       PM line history 1 Day       --
---------------------------------------

xdsl2PMLineHist1DayTable     OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2PMLineHist1DayEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2PMLineHist1DayTable contains PM line history
       for 24-hour intervals of DSL line."
   ::= { xdsl2PMLine 4 }

xdsl2PMLineHist1DayEntry  OBJECT-TYPE
   SYNTAX      Xdsl2PMLineHist1DayEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "One index of this table is an interface index where the
      interface has an ifType of vdsl2(251).  A second index of this
      table is the transmission unit.The third index is the interval
      number."
   INDEX  { ifIndex,
            xdsl2PMLHist1DUnit,
            xdsl2PMLHist1DInterval }
   ::= { xdsl2PMLineHist1DayTable 1 }

Xdsl2PMLineHist1DayEntry  ::=
   SEQUENCE {
      xdsl2PMLHist1DUnit              Xdsl2Unit,

      xdsl2PMLHist1DInterval          Unsigned32,
      xdsl2PMLHist1DMonitoredTime     Unsigned32,
      xdsl2PMLHist1DFecs              Counter32,
      xdsl2PMLHist1DEs                Counter32,
      xdsl2PMLHist1DSes               Counter32,
      xdsl2PMLHist1DLoss              Counter32,
      xdsl2PMLHist1DUas               Counter32,
      xdsl2PMLHist1DValidInterval     TruthValue
   }

xdsl2PMLHist1DUnit  OBJECT-TYPE
   SYNTAX      Xdsl2Unit
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The termination unit."
   ::= { xdsl2PMLineHist1DayEntry 1 }

xdsl2PMLHist1DInterval  OBJECT-TYPE
   SYNTAX      Unsigned32 (1..30)
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The interval number."
   ::= { xdsl2PMLineHist1DayEntry 2 }

xdsl2PMLHist1DMonitoredTime  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Total seconds monitored in this interval."
   ::= { xdsl2PMLineHist1DayEntry 3 }

xdsl2PMLHist1DFecs  OBJECT-TYPE
   SYNTAX      Counter32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of seconds during this interval that there was at
       least one FEC correction event for one or more bearer channels in
       this line.  This parameter is inhibited during UAS or SES."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1 (FECS-L)
                 and paragraph #*******.1 (FECS-LFE)"
   ::= { xdsl2PMLineHist1DayEntry 4 }

xdsl2PMLHist1DEs  OBJECT-TYPE
   SYNTAX      Counter32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of seconds during this interval that there was:
          xTU-C: CRC-8 >= 1 for one or more bearer channels OR
                 LOS >= 1 OR SEF >= 1 OR LPR >= 1.
          xTU-R: FEBE >= 1 for one or more bearer channels OR
                 LOS-FE >= 1 OR RDI >= 1 OR LPR-FE >= 1.
       This parameter is inhibited during UAS."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.2 (ES-L)
                 and paragraph #*******.2 (ES-LFE)"
   ::= { xdsl2PMLineHist1DayEntry 5 }

xdsl2PMLHist1DSes  OBJECT-TYPE
   SYNTAX      Counter32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of seconds during this interval that there was:
          xTU-C: (CRC-8 anomalies in one or more of the
                 received bearer channels) >= 18 OR LOS >= 1
                 OR SEF >= 1 OR LPR >= 1.
          xTU-R: (FEBE anomalies in one or more of the
                 received bearer channels) >= 18 OR LOS-FE >= 1
                 OR RDI >= 1 OR LPR-FE >= 1.
       This parameter is inhibited during UAS."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.3 (SES-L)
                 and paragraph #*******.3 (SES-LFE)"
   ::= { xdsl2PMLineHist1DayEntry 6 }

xdsl2PMLHist1DLoss  OBJECT-TYPE
   SYNTAX      Counter32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of seconds during this interval that there was LOS (or
       LOS-FE for xTU-R)."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4 (LOSS-L)
                 and paragraph #*******.4 (LOSS-LFE)"
   ::= { xdsl2PMLineHist1DayEntry 7 }

xdsl2PMLHist1DUas  OBJECT-TYPE
   SYNTAX      Counter32

   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of seconds in Unavailability State during this
       interval.
       Unavailability begins at the onset of 10 contiguous severely
       errored seconds, and ends at the onset of 10 contiguous seconds
       with no severely errored seconds."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.5 (UAS-L)
                 and paragraph #*******.5 (UAS-LFE)"
   ::= { xdsl2PMLineHist1DayEntry 8 }

xdsl2PMLHist1DValidInterval  OBJECT-TYPE
   SYNTAX      TruthValue
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "This variable indicates if the data for this interval is
       valid."
   ::= { xdsl2PMLineHist1DayEntry 9 }

-------------------------------------------
--     PM line init history 15 Minutes   --
-------------------------------------------

xdsl2PMLineInitHist15MinTable      OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2PMLineInitHist15MinEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2PMLineInitHist15MinTable contains PM line
       initialization history for 15-minute intervals of DSL
       line."
   ::= { xdsl2PMLine 5 }

xdsl2PMLineInitHist15MinEntry  OBJECT-TYPE
   SYNTAX      Xdsl2PMLineInitHist15MinEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
       "One index of this table is an interface index where the
        interface has an ifType of vdsl2(251).  A second index is the
        interval number."
   INDEX  { ifIndex,
            xdsl2PMLInitHist15MInterval }
   ::= { xdsl2PMLineInitHist15MinTable 1 }

Xdsl2PMLineInitHist15MinEntry  ::=
   SEQUENCE {
      xdsl2PMLInitHist15MInterval              Unsigned32,
      xdsl2PMLInitHist15MMonitoredTime         Unsigned32,
      xdsl2PMLInitHist15MFullInits             Unsigned32,
      xdsl2PMLInitHist15MFailedFullInits       Unsigned32,
      xdsl2PMLInitHist15MShortInits            Unsigned32,
      xdsl2PMLInitHist15MFailedShortInits      Unsigned32,
      xdsl2PMLInitHist15MValidInterval         TruthValue
   }

xdsl2PMLInitHist15MInterval  OBJECT-TYPE
   SYNTAX      Unsigned32 (1..96)
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The interval number."
   ::= { xdsl2PMLineInitHist15MinEntry 1 }

xdsl2PMLInitHist15MMonitoredTime  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Total seconds monitored in this interval."
   ::= { xdsl2PMLineInitHist15MinEntry 2 }

xdsl2PMLInitHist15MFullInits  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of full initializations attempted on the line
       (successful and failed) during this interval."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1"
   ::= { xdsl2PMLineInitHist15MinEntry 3 }

xdsl2PMLInitHist15MFailedFullInits  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of failed full initializations on the line during this
       interval."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.2"
   ::= { xdsl2PMLineInitHist15MinEntry 4 }

xdsl2PMLInitHist15MShortInits  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of short initializations attempted on the line
       (successful and failed) during this interval."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.3"
   ::= { xdsl2PMLineInitHist15MinEntry 5 }

xdsl2PMLInitHist15MFailedShortInits  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of failed short initializations on the line during
       this interval."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4"
   ::= { xdsl2PMLineInitHist15MinEntry 6 }

xdsl2PMLInitHist15MValidInterval  OBJECT-TYPE
   SYNTAX      TruthValue
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "This variable indicates if the data for this interval is
       valid."
   ::= { xdsl2PMLineInitHist15MinEntry 7 }

-------------------------------------------
--       PM line init history 1 Day      --
-------------------------------------------

xdsl2PMLineInitHist1DayTable       OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2PMLineInitHist1DayEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2PMLineInitHist1DayTable contains PM line
       initialization history for 24-hour intervals for DSL
       lines."
   ::= { xdsl2PMLine 6 }

xdsl2PMLineInitHist1DayEntry  OBJECT-TYPE
   SYNTAX      Xdsl2PMLineInitHist1DayEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION

       "One index of this table is an interface index where the
        interface has an ifType of vdsl2(251).  A second index is the
        interval number."
   INDEX  { ifIndex,
            xdsl2PMLInitHist1DInterval }
   ::= { xdsl2PMLineInitHist1DayTable 1 }

Xdsl2PMLineInitHist1DayEntry  ::=
   SEQUENCE {
      xdsl2PMLInitHist1DInterval              Unsigned32,
      xdsl2PMLInitHist1DMonitoredTime         Unsigned32,
      xdsl2PMLInitHist1DFullInits             Unsigned32,
      xdsl2PMLInitHist1DFailedFullInits       Unsigned32,
      xdsl2PMLInitHist1DShortInits            Unsigned32,
      xdsl2PMLInitHist1DFailedShortInits      Unsigned32,
      xdsl2PMLInitHist1DValidInterval         TruthValue
   }

xdsl2PMLInitHist1DInterval  OBJECT-TYPE
   SYNTAX      Unsigned32 (1..30)
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The interval number."
   ::= { xdsl2PMLineInitHist1DayEntry 1 }

xdsl2PMLInitHist1DMonitoredTime  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Total seconds monitored in this interval."
   ::= { xdsl2PMLineInitHist1DayEntry 2 }

xdsl2PMLInitHist1DFullInits  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of full initializations attempted on the line
       (successful and failed) during this interval."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1"
    ::= { xdsl2PMLineInitHist1DayEntry 3 }

xdsl2PMLInitHist1DFailedFullInits  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only

   STATUS      current
   DESCRIPTION
      "Count of failed full initializations on the line during this
       interval."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.2"
   ::= { xdsl2PMLineInitHist1DayEntry 4 }

xdsl2PMLInitHist1DShortInits  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of short initializations attempted on the line
       (successful and failed) during this interval."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.3"
   ::= { xdsl2PMLineInitHist1DayEntry 5 }

xdsl2PMLInitHist1DFailedShortInits  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of failed short initializations on the line during
       this interval."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.4"
   ::= { xdsl2PMLineInitHist1DayEntry 6 }

xdsl2PMLInitHist1DValidInterval  OBJECT-TYPE
   SYNTAX      TruthValue
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "This variable indicates if the data for this interval is
       valid."
   ::= { xdsl2PMLineInitHist1DayEntry 7 }

---------------------------------------------------
--          PM channel current counters          --
---------------------------------------------------

xdsl2PMChCurrTable        OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2PMChCurrEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2PMChCurrTable contains current Performance
       Monitoring results for DSL channels."
   ::= { xdsl2PMChannel 1 }

xdsl2PMChCurrEntry  OBJECT-TYPE
   SYNTAX      Xdsl2PMChCurrEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
       "One index of this table is an interface index where the
       interface has an ifType of a DSL channel.  A second index of
       this table is the termination unit."
   INDEX  { ifIndex, xdsl2PMChCurrUnit }
   ::= { xdsl2PMChCurrTable 1 }

Xdsl2PMChCurrEntry  ::=
   SEQUENCE {
      xdsl2PMChCurrUnit                     Xdsl2Unit,
      xdsl2PMChCurr15MValidIntervals        Unsigned32,
      xdsl2PMChCurr15MInvalidIntervals      Unsigned32,
      xdsl2PMChCurr15MTimeElapsed           HCPerfTimeElapsed,
      xdsl2PMChCurr15MCodingViolations      Unsigned32,
      xdsl2PMChCurr15MCorrectedBlocks       Unsigned32,
      xdsl2PMChCurr1DayValidIntervals       Unsigned32,
      xdsl2PMChCurr1DayInvalidIntervals     Unsigned32,
      xdsl2PMChCurr1DayTimeElapsed          HCPerfTimeElapsed,
      xdsl2PMChCurr1DayCodingViolations     Unsigned32,
      xdsl2PMChCurr1DayCorrectedBlocks      Unsigned32
   }

xdsl2PMChCurrUnit  OBJECT-TYPE
   SYNTAX      Xdsl2Unit
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
   "The termination unit."
   ::= { xdsl2PMChCurrEntry 1 }

xdsl2PMChCurr15MValidIntervals  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..96)
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The number of 15-minute PM intervals for which data
       was collected.  The value will typically be equal to the maximum
       number of 15-minute intervals the implementation is planned to
       store (i.e., beyond the scope of this MIB module) unless the
       measurement was (re-)started recently, in which case the value
       will be the number of complete 15-minute intervals for which
       the agent has at least some data.  In certain cases (e.g., in
       the case where the agent is a proxy), it is possible that some
       intervals are unavailable.  In this case, this interval is the

       maximum interval number for which data is available."
   ::= { xdsl2PMChCurrEntry 2 }

xdsl2PMChCurr15MInvalidIntervals  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..96)
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The number of 15-minute PM intervals for which no data is
       available.  The value will typically be zero except in cases
       where the data for some intervals are not available (e.g.,
       in proxy situations)."
    ::= { xdsl2PMChCurrEntry 3 }

xdsl2PMChCurr15MTimeElapsed  OBJECT-TYPE
   SYNTAX      HCPerfTimeElapsed
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Total elapsed seconds in this interval."
   ::= { xdsl2PMChCurrEntry 4 }

xdsl2PMChCurr15MCodingViolations  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of CRC-8 (FEBE for xTU-R) anomalies occurring in the
       channel during the interval.  This parameter is inhibited during
       UAS or SES.  If the CRC is applied over multiple channels, then
       each related CRC-8 (or FEBE) anomaly SHOULD increment each of the
       counters related to the individual channels."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1 (CV-C)
                 and paragraph #*******.1 (CV-CFE)"
  ::= { xdsl2PMChCurrEntry 5 }

xdsl2PMChCurr15MCorrectedBlocks  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of FEC (FFEC for xTU-R) anomalies (corrected code
       words) occurring in the channel during the interval.  This
       parameter is inhibited during UAS or SES.  If the FEC is applied
       over multiple channels, then each related FEC (or FFEC) anomaly
       SHOULD increment each of the counters related to the individual
       channels."

   REFERENCE    "ITU-T G.997.1, paragraph #*******.2 (FEC-C)
                 and paragraph #*******.2 (FEC-CFE)"
   ::= { xdsl2PMChCurrEntry 6 }

xdsl2PMChCurr1DayValidIntervals  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..30)
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The number of 24-hour PM intervals for which data was
       collected.  The value will typically be equal to the maximum
       number of 24-hour intervals the implementation is planned to
       store (i.e., beyond the scope of this MIB module) unless the
       measurement was (re-)started recently, in which case the value
       will be the number of complete 24-hour intervals for which
       the agent has at least some data.  In certain cases (e.g., in
       the case where the agent is a proxy), it is possible that some
       intervals are unavailable.  In this case, this interval is the
       maximum interval number for which data is available."
   ::= { xdsl2PMChCurrEntry 7 }

xdsl2PMChCurr1DayInvalidIntervals  OBJECT-TYPE
   SYNTAX      Unsigned32 (0..30)
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "The number of 24-hour PM intervals for which no data is
       available.  The value will typically be zero except in cases
       where the data for some intervals are not available (e.g.,
       in proxy situations)."
   ::= { xdsl2PMChCurrEntry 8 }

xdsl2PMChCurr1DayTimeElapsed  OBJECT-TYPE
   SYNTAX      HCPerfTimeElapsed
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Total elapsed seconds in this interval."
   ::= { xdsl2PMChCurrEntry 9 }

xdsl2PMChCurr1DayCodingViolations  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of CRC-8 (FEBE for xTU-R) anomalies occurring in the
       channel during the interval.  This parameter is inhibited during

       UAS or SES.  If the CRC is applied over multiple channels, then
       each related CRC-8 (or FEBE) anomaly SHOULD increment each of the
       counters related to the individual channels."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1 (CV-C)
                 and paragraph #*******.1 (CV-CFE)"
   ::= { xdsl2PMChCurrEntry 10 }

xdsl2PMChCurr1DayCorrectedBlocks  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of FEC (FFEC for xTU-R) anomalies (corrected code
       words) occurring in the channel during the interval.  This
       parameter is inhibited during UAS or SES.  If the FEC is applied
       over multiple channels, then each related FEC (or FFEC) anomaly
       SHOULD increment each of the counters related to the individual
       channels."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.2 (FEC-C)
                 and paragraph #*******.2 (FEC-CFE)"
   ::= { xdsl2PMChCurrEntry 11 }

-------------------------------------------
--    PM channel history 15 Minutes      --
-------------------------------------------

xdsl2PMChHist15MinTable         OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2PMChHist15MinEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2PMChHist15MinTable contains Performance
       Monitoring (PM) history for 15-minute intervals for DSL channels
       PM."
   ::= { xdsl2PMChannel 2 }

xdsl2PMChHist15MinEntry  OBJECT-TYPE
   SYNTAX      Xdsl2PMChHist15MinEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "One index of this table is an interface index where the
       interface has an ifType of a DSL channel.  A second index of
       this table is the transmission unit.  The third index is the
       interval number."
   INDEX  { ifIndex,
            xdsl2PMChHist15MUnit,
            xdsl2PMChHist15MInterval }

   ::= { xdsl2PMChHist15MinTable 1 }

Xdsl2PMChHist15MinEntry  ::=
   SEQUENCE {
      xdsl2PMChHist15MUnit                     Xdsl2Unit,
      xdsl2PMChHist15MInterval                 Unsigned32,
      xdsl2PMChHist15MMonitoredTime            Unsigned32,
      xdsl2PMChHist15MCodingViolations         Unsigned32,
      xdsl2PMChHist15MCorrectedBlocks          Unsigned32,
      xdsl2PMChHist15MValidInterval            TruthValue
   }

xdsl2PMChHist15MUnit  OBJECT-TYPE
   SYNTAX      Xdsl2Unit
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The termination unit."
   ::= { xdsl2PMChHist15MinEntry 1 }

xdsl2PMChHist15MInterval  OBJECT-TYPE
   SYNTAX      Unsigned32 (1..96)
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The interval number."
   ::= { xdsl2PMChHist15MinEntry 2 }

xdsl2PMChHist15MMonitoredTime  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Total seconds monitored in this interval."
   ::= { xdsl2PMChHist15MinEntry 3 }

xdsl2PMChHist15MCodingViolations  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of CRC-8 (FEBE for xTU-R) anomalies occurring in the
       channel during the interval.  This parameter is inhibited during
       UAS or SES.  If the CRC is applied over multiple channels, then
       each related CRC-8 (or FEBE) anomaly SHOULD increment each of the
       counters related to the individual channels."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1 (CV-C)

                 and paragraph #*******.1 (CV-CFE)"
  ::= { xdsl2PMChHist15MinEntry 4 }

xdsl2PMChHist15MCorrectedBlocks  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of FEC (FFEC for xTU-R) anomalies (corrected code
       words) occurring in the channel during the interval.  This
       parameter is inhibited during UAS or SES.  If the FEC is applied
       over multiple channels, then each related FEC (or FFEC) anomaly
       SHOULD increment each of the counters related to the individual
       channels."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.2 (FEC-C)
                 and paragraph #*******.2 (FEC-CFE)"
   ::= { xdsl2PMChHist15MinEntry 5 }

xdsl2PMChHist15MValidInterval  OBJECT-TYPE
   SYNTAX      TruthValue
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "This variable indicates if the data for this interval is
       valid."
   ::= { xdsl2PMChHist15MinEntry 6 }

------------------------------------------
--        PM channel history 1 Day      --
------------------------------------------

xdsl2PMChHist1DTable         OBJECT-TYPE
   SYNTAX      SEQUENCE  OF  Xdsl2PMChHist1DEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The table xdsl2PMChHist1DTable contains Performance
       Monitoring (PM) history for 1-day intervals for DSL channels
       PM."
   ::= { xdsl2PMChannel 3 }

xdsl2PMChHist1DEntry  OBJECT-TYPE
   SYNTAX      Xdsl2PMChHist1DEntry
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "One index of this table is an interface index where the
       interface has an ifType of a DSL channel.  A second index of

       this table is the transmission unit.  The third index is the
       interval number."
   INDEX  { ifIndex,
            xdsl2PMChHist1DUnit,
            xdsl2PMChHist1DInterval }
   ::= { xdsl2PMChHist1DTable 1 }

Xdsl2PMChHist1DEntry  ::=
   SEQUENCE {
      xdsl2PMChHist1DUnit                      Xdsl2Unit,
      xdsl2PMChHist1DInterval                  Unsigned32,
      xdsl2PMChHist1DMonitoredTime             Unsigned32,
      xdsl2PMChHist1DCodingViolations          Unsigned32,
      xdsl2PMChHist1DCorrectedBlocks           Unsigned32,
      xdsl2PMChHist1DValidInterval             TruthValue
   }

xdsl2PMChHist1DUnit  OBJECT-TYPE
   SYNTAX      Xdsl2Unit
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The termination unit."
    ::= { xdsl2PMChHist1DEntry 1 }

xdsl2PMChHist1DInterval  OBJECT-TYPE
   SYNTAX      Unsigned32 (1..30)
   MAX-ACCESS  not-accessible
   STATUS      current
   DESCRIPTION
      "The interval number."
   ::= { xdsl2PMChHist1DEntry 2 }

xdsl2PMChHist1DMonitoredTime  OBJECT-TYPE
   SYNTAX      Unsigned32
   UNITS       "seconds"
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Total seconds monitored in this interval."
   ::= { xdsl2PMChHist1DEntry 3 }

xdsl2PMChHist1DCodingViolations  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of CRC-8 (FEBE for xTU-R) anomalies occurring in the

       channel during the interval.  This parameter is inhibited during
       UAS or SES.  If the CRC is applied over multiple channels, then
       each related CRC-8 (or FEBE) anomaly SHOULD increment each of the
       counters related to the individual channels."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.1 (CV-C)
                 and paragraph #*******.1 (CV-CFE)"
   ::= { xdsl2PMChHist1DEntry 4 }

xdsl2PMChHist1DCorrectedBlocks  OBJECT-TYPE
   SYNTAX      Unsigned32
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "Count of FEC (FFEC for xTU-R) anomalies (corrected code
       words) occurring in the channel during the interval.  This
       parameter is inhibited during UAS or SES.  If the FEC is applied
       over multiple channels, then each related FEC (or FFEC) anomaly
       SHOULD increment each of the counters related to the individual
       channels."
   REFERENCE    "ITU-T G.997.1, paragraph #*******.2 (FEC-C)
                 and paragraph #*******.2 (FEC-CFE)"
   ::= { xdsl2PMChHist1DEntry 5 }

xdsl2PMChHist1DValidInterval  OBJECT-TYPE
   SYNTAX      TruthValue
   MAX-ACCESS  read-only
   STATUS      current
   DESCRIPTION
      "This variable indicates if the data for this interval is
       valid."
   ::= { xdsl2PMChHist1DEntry 6 }

-------------------------------------------
--          Notifications Group          --
-------------------------------------------

xdsl2LinePerfFECSThreshXtuc NOTIFICATION-TYPE
   OBJECTS
   {
   xdsl2PMLCurr15MFecs,
   xdsl2LineAlarmConfProfileXtucThresh15MinFecs
   }
   STATUS     current
   DESCRIPTION
     "This notification indicates that the FEC seconds threshold
      has been reached/exceeded for the referred xTU-C."
   ::= { xdsl2Notifications 1 }

xdsl2LinePerfFECSThreshXtur NOTIFICATION-TYPE
   OBJECTS
   {
   xdsl2PMLCurr15MFecs,
   xdsl2LineAlarmConfProfileXturThresh15MinFecs
   }
   STATUS     current
   DESCRIPTION
     "This notification indicates that the FEC seconds threshold
      has been reached/exceeded for the referred xTU-R."
   ::= { xdsl2Notifications 2 }

xdsl2LinePerfESThreshXtuc NOTIFICATION-TYPE
   OBJECTS
   {
   xdsl2PMLCurr15MEs,
   xdsl2LineAlarmConfProfileXtucThresh15MinEs
   }
   STATUS     current
   DESCRIPTION
     "This notification indicates that the errored seconds
      threshold has been reached/exceeded for the referred xTU-C."
   ::= { xdsl2Notifications 3 }

xdsl2LinePerfESThreshXtur NOTIFICATION-TYPE
   OBJECTS
   {
   xdsl2PMLCurr15MEs,
   xdsl2LineAlarmConfProfileXturThresh15MinEs
   }
   STATUS     current
   DESCRIPTION
     "This notification indicates that the errored seconds
      threshold has been reached/exceeded for the referred xTU-R."
   ::= { xdsl2Notifications 4 }

xdsl2LinePerfSESThreshXtuc NOTIFICATION-TYPE
   OBJECTS
   {
   xdsl2PMLCurr15MSes,
   xdsl2LineAlarmConfProfileXtucThresh15MinSes
   }
   STATUS     current
   DESCRIPTION
     "This notification indicates that the severely errored seconds
      threshold has been reached/exceeded for the referred xTU-C."
   ::= { xdsl2Notifications 5 }

xdsl2LinePerfSESThreshXtur NOTIFICATION-TYPE
   OBJECTS
   {
   xdsl2PMLCurr15MSes,
   xdsl2LineAlarmConfProfileXturThresh15MinSes
   }
   STATUS     current
   DESCRIPTION
     "This notification indicates that the severely errored seconds
      threshold has been reached/exceeded for the referred xTU-R."
   ::= { xdsl2Notifications 6 }

xdsl2LinePerfLOSSThreshXtuc NOTIFICATION-TYPE
   OBJECTS
   {
   xdsl2PMLCurr15MLoss,
   xdsl2LineAlarmConfProfileXtucThresh15MinLoss
   }
   STATUS     current
   DESCRIPTION
     "This notification indicates that the LOS seconds
      threshold has been reached/exceeded for the referred xTU-C."
   ::= { xdsl2Notifications 7 }

xdsl2LinePerfLOSSThreshXtur NOTIFICATION-TYPE
   OBJECTS
   {
   xdsl2PMLCurr15MLoss,
   xdsl2LineAlarmConfProfileXturThresh15MinLoss
   }
   STATUS     current
   DESCRIPTION
     "This notification indicates that the LOS seconds
      threshold has been reached/exceeded for the referred xTU-R."
   ::= { xdsl2Notifications 8 }

xdsl2LinePerfUASThreshXtuc NOTIFICATION-TYPE
   OBJECTS
   {
   xdsl2PMLCurr15MUas,
   xdsl2LineAlarmConfProfileXtucThresh15MinUas
   }
   STATUS     current
   DESCRIPTION
     "This notification indicates that the unavailable seconds
      threshold has been reached/exceeded for the referred xTU-C."
   ::= { xdsl2Notifications 9 }

xdsl2LinePerfUASThreshXtur NOTIFICATION-TYPE
   OBJECTS
   {
   xdsl2PMLCurr15MUas,
   xdsl2LineAlarmConfProfileXturThresh15MinUas
   }
   STATUS     current
   DESCRIPTION
     "This notification indicates that the unavailable seconds
      threshold has been reached/exceeded for the referred xTU-R."
   ::= { xdsl2Notifications 10 }

xdsl2LinePerfCodingViolationsThreshXtuc NOTIFICATION-TYPE
   OBJECTS
   {
   xdsl2PMChCurr15MCodingViolations,
   xdsl2ChAlarmConfProfileXtucThresh15MinCodingViolations
   }
   STATUS     current
   DESCRIPTION
     "This notification indicates that the coding violations
      threshold has been reached/exceeded for the referred xTU-C."
   ::= { xdsl2Notifications 11 }

xdsl2LinePerfCodingViolationsThreshXtur NOTIFICATION-TYPE
   OBJECTS
   {
   xdsl2PMChCurr15MCodingViolations,
   xdsl2ChAlarmConfProfileXturThresh15MinCodingViolations
   }
   STATUS     current
   DESCRIPTION
     "This notification indicates that the coding violations
      threshold has been reached/exceeded for the referred xTU-R."
   ::= { xdsl2Notifications 12 }

xdsl2LinePerfCorrectedThreshXtuc NOTIFICATION-TYPE
   OBJECTS
   {
   xdsl2PMChCurr15MCorrectedBlocks,
   xdsl2ChAlarmConfProfileXtucThresh15MinCorrected
   }
   STATUS     current
   DESCRIPTION
     "This notification indicates that the corrected blocks
      (FEC events) threshold has been reached/exceeded for the
      referred xTU-C."
   ::= { xdsl2Notifications 13 }

xdsl2LinePerfCorrectedThreshXtur NOTIFICATION-TYPE
   OBJECTS
   {
   xdsl2PMChCurr15MCorrectedBlocks,
   xdsl2ChAlarmConfProfileXturThresh15MinCorrected
   }
   STATUS     current
   DESCRIPTION
     "This notification indicates that the corrected blocks
      (FEC events) threshold has been reached/exceeded for the
      referred xTU-R."
   ::= { xdsl2Notifications 14 }

xdsl2LinePerfFailedFullInitThresh NOTIFICATION-TYPE
   OBJECTS
   {
   xdsl2PMLInitCurr15MFailedFullInits,
   xdsl2LineAlarmConfProfileThresh15MinFailedFullInt
   }
   STATUS     current
   DESCRIPTION
     "This notification indicates that the failed full
      initializations threshold has been reached/exceeded for the
      referred ADSL/ADSL2 or ADSL2 line."
   ::= { xdsl2Notifications 15 }

xdsl2LinePerfFailedShortInitThresh NOTIFICATION-TYPE
   OBJECTS
   {
   xdsl2PMLInitCurr15MFailedShortInits,
   xdsl2LineAlarmConfProfileThresh15MinFailedShrtInt
   }
   STATUS     current
   DESCRIPTION
     "This notification indicates that the failed short
      initializations threshold has been reached/exceeded for the
      referred VDSL2/ADSL/ADSL2 or ADSL2+ line."
   ::= { xdsl2Notifications 16 }

xdsl2LineStatusChangeXtuc NOTIFICATION-TYPE
   OBJECTS
   {
   xdsl2LineStatusXtuc
   }
   STATUS     current
   DESCRIPTION
     "This notification indicates that a status change is
      detected for the referred xTU-C."

   ::= { xdsl2Notifications 17 }

xdsl2LineStatusChangeXtur NOTIFICATION-TYPE
   OBJECTS
   {
   xdsl2LineStatusXtur
   }
   STATUS     current
   DESCRIPTION
     "This notification indicates that a status change is
      detected for the referred xTU-R."
   ::= { xdsl2Notifications 18 }

   -- conformance information

   xdsl2Groups OBJECT IDENTIFIER ::= { xdsl2Conformance 1 }
   xdsl2Compliances OBJECT IDENTIFIER ::= { xdsl2Conformance 2 }

   xdsl2LineMibCompliance MODULE-COMPLIANCE
      STATUS  current
      DESCRIPTION
         "The compliance statement for SNMP entities which
          manage VDSL2/ADSL/ADSL2 and ADSL2+ interfaces."
      MODULE  -- this module
      MANDATORY-GROUPS
          {
          xdsl2LineGroup,
          xdsl2ChannelStatusGroup,
          xdsl2SCStatusGroup,
          xdsl2LineInventoryGroup,
          xdsl2LineConfTemplateGroup,
          xdsl2LineConfProfGroup,
          xdsl2LineConfProfModeSpecGroup,
          xdsl2LineConfProfModeSpecBandUsGroup,
          xdsl2ChConfProfileGroup,
          xdsl2LineAlarmConfTemplateGroup,
          xdsl2PMLineCurrGroup,
          xdsl2PMLineInitCurrGroup,
          xdsl2PMLineHist15MinGroup,
          xdsl2PMLineHist1DayGroup,
          xdsl2PMLineInitHist15MinGroup,
          xdsl2PMLineInitHist1DayGroup,
          xdsl2PMChCurrGroup,
          xdsl2PMChHist15MinGroup,
          xdsl2PMChHist1DGroup
          }

   GROUP  xdsl2LineFallbackGroup
      DESCRIPTION
        "The group of configuration, status, and commands
         objects on the line level that are associated with the fallback
         feature."

   GROUP  xdsl2LineBpscGroup
      DESCRIPTION
        "The group of configuration, status, and commands objects
         on the line level that are associated with requesting a bits
         per subcarrier measurement."

   GROUP  xdsl2LineSegmentGroup
      DESCRIPTION
        "The group of status and commands objects on the line
         level that are used to hold the results of the
         bits-per-subcarrier measurement."

   GROUP  xdsl2ChannelStatusAtmGroup
      DESCRIPTION
        "The group of status objects required when the data path
         is ATM."

   GROUP  xdsl2ChannelStatusPtmGroup
      DESCRIPTION
        "The group of status objects required when the data path
         is PTM."

   GROUP  xdsl2LineConfProfRaGroup
      DESCRIPTION
        "The group of objects required for controlling the
         rate-adaptive behavior of the line."

   GROUP  xdsl2LineConfProfMsgMinGroup
      DESCRIPTION
        "The group of objects required for controlling the rate
         reserved for Overhead traffic."

   GROUP  xdsl2LineAlarmConfProfileGroup
      DESCRIPTION
        "The group of objects that define the alarm thresholds
         on line-level PM counters."

   GROUP  xdsl2ChAlarmConfProfileGroup
      DESCRIPTION
        "The group of objects that define the alarm thresholds
        on channel-level PM counters."

   GROUP  xdsl2ChConfProfileAtmGroup
      DESCRIPTION
        "The group of configuration objects required when the data
         path is ATM."

   GROUP  xdsl2ChConfProfileMinResGroup
      DESCRIPTION
        "The group of configuration objects required for the
         reserved data rate."

   GROUP  xdsl2ChConfProfileOptAttrGroup
      DESCRIPTION
        "The group of various optional channel configuration
        objects."

   GROUP  xdsl2PMLineInitCurrShortGroup
      DESCRIPTION
        "The group of PM counters for the current intervals short
         initializations."

   GROUP  xdsl2PMLineInitHist15MinShortGroup
      DESCRIPTION
        "The group of PM counters for the previous 15-minute
         intervals short initializations."

   GROUP  xdsl2PMLineInitHist1DayShortGroup
      DESCRIPTION
        "The group of PM counters for the previous 24-hour
         intervals short initializations."

   GROUP  xdsl2ScalarSCGroup
      DESCRIPTION
        "The group of objects that report the available memory
         resources for the DELT processes."

   GROUP  xdsl2ThreshNotificationGroup
      DESCRIPTION
        "The group of thresholds crossing notifications."

   GROUP  xdsl2StatusChangeNotificationGroup
      DESCRIPTION
        "The group of status change notifications."

      ::= { xdsl2Compliances 1 }

   -- units of conformance

   xdsl2LineGroup OBJECT-GROUP

      OBJECTS
          {
          xdsl2LineConfTemplate,
          xdsl2LineAlarmConfTemplate,
          xdsl2LineCmndConfPmsf,
          xdsl2LineCmndConfLdsf,
          xdsl2LineCmndConfLdsfFailReason,
          xdsl2LineCmndAutomodeColdStart,
          xdsl2LineCmndConfReset,
          xdsl2LineStatusXtuTransSys,
          xdsl2LineStatusPwrMngState,
          xdsl2LineStatusInitResult,
          xdsl2LineStatusLastStateDs,
          xdsl2LineStatusLastStateUs,
          xdsl2LineStatusXtur,
          xdsl2LineStatusXtuc,
          xdsl2LineStatusAttainableRateDs,
          xdsl2LineStatusAttainableRateUs,
          xdsl2LineStatusActPsdDs,
          xdsl2LineStatusActPsdUs,
          xdsl2LineStatusActAtpDs,
          xdsl2LineStatusActAtpUs,
          xdsl2LineStatusActProfile,
          xdsl2LineStatusActLimitMask,
          xdsl2LineStatusActUs0Mask,
          xdsl2LineStatusActSnrModeDs,
          xdsl2LineStatusActSnrModeUs,
          xdsl2LineStatusElectricalLength,
          xdsl2LineStatusTssiDs,
          xdsl2LineStatusTssiUs,
          xdsl2LineStatusMrefPsdDs,
          xdsl2LineStatusMrefPsdUs,
          xdsl2LineStatusTrellisDs,
          xdsl2LineStatusTrellisUs,
          xdsl2LineStatusActualCe,
          xdsl2LineBandStatusLnAtten,
          xdsl2LineBandStatusSigAtten,
          xdsl2LineBandStatusSnrMargin
           }
      STATUS     current
      DESCRIPTION
         "The group of configuration, status, and commands objects
          on the line level."
      ::= { xdsl2Groups 1 }

   xdsl2LineFallbackGroup OBJECT-GROUP
      OBJECTS
          {

          xdsl2LineConfFallbackTemplate,
          xdsl2LineStatusActTemplate
           }
      STATUS     current
      DESCRIPTION
         "The group of configuration, status, and commands
          objects on the line level that are associated with the
          fallback feature."
      ::= { xdsl2Groups 2 }

   xdsl2LineBpscGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2LineCmndConfBpsc,
          xdsl2LineCmndConfBpscFailReason,
          xdsl2LineCmndConfBpscRequests
           }
      STATUS     current
      DESCRIPTION
         "The group of configuration, status, and commands
          objects on the line level that are associated with requesting
          a bits-per-subcarrier measurement."
      ::= { xdsl2Groups 3 }

   xdsl2LineSegmentGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2LineSegmentBitsAlloc,
          xdsl2LineSegmentRowStatus
           }
      STATUS     current
      DESCRIPTION
         "The group of status and commands objects on the line
          level that are used to hold the results of the
          bits-per-subcarrier measurement."
      ::= { xdsl2Groups 4 }

   xdsl2ChannelStatusGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2ChStatusActDataRate,
          xdsl2ChStatusPrevDataRate,
          xdsl2ChStatusActDelay,
          xdsl2ChStatusActInp,
          xdsl2ChStatusInpReport,
          xdsl2ChStatusNFec,
          xdsl2ChStatusRFec,
          xdsl2ChStatusLSymb,

          xdsl2ChStatusIntlvDepth,
          xdsl2ChStatusIntlvBlock,
          xdsl2ChStatusLPath
          }
      STATUS     current
      DESCRIPTION
          "The group of status objects on the channel level."
      ::= { xdsl2Groups 5 }

   xdsl2ChannelStatusAtmGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2ChStatusAtmStatus
          }
      STATUS     current
      DESCRIPTION
         "The group of status objects on the data path level
          when it is ATM."
      ::= { xdsl2Groups 6 }

   xdsl2ChannelStatusPtmGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2ChStatusPtmStatus
          }
      STATUS     current
      DESCRIPTION
         "The group of status objects on the data path level
          when it is PTM."
      ::= { xdsl2Groups 7 }

   xdsl2SCStatusGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2SCStatusLinScale,
          xdsl2SCStatusLinScGroupSize,
          xdsl2SCStatusLogMt,
          xdsl2SCStatusLogScGroupSize,
          xdsl2SCStatusQlnMt,
          xdsl2SCStatusQlnScGroupSize,
          xdsl2SCStatusSnrMtime,
          xdsl2SCStatusSnrScGroupSize,
          xdsl2SCStatusBandLnAtten,
          xdsl2SCStatusBandSigAtten,
          xdsl2SCStatusAttainableRate,
          xdsl2SCStatusRowStatus,
          xdsl2SCStatusSegmentLinReal,
          xdsl2SCStatusSegmentLinImg,

          xdsl2SCStatusSegmentLog,
          xdsl2SCStatusSegmentQln,
          xdsl2SCStatusSegmentSnr,
          xdsl2SCStatusSegmentBitsAlloc,
          xdsl2SCStatusSegmentGainAlloc
          }
      STATUS     current
      DESCRIPTION
         "The group of status objects on the subcarrier level.
          They are updated as a result of a DELT process."
      ::= { xdsl2Groups 8 }

   xdsl2LineInventoryGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2LInvG994VendorId,
          xdsl2LInvSystemVendorId,
          xdsl2LInvVersionNumber,
          xdsl2LInvSerialNumber,
          xdsl2LInvSelfTestResult,
          xdsl2LInvTransmissionCapabilities
          }
      STATUS     current
      DESCRIPTION
          "The group of inventory objects per xTU."
      ::= { xdsl2Groups 9 }

   xdsl2LineConfTemplateGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2LConfTempLineProfile,
          xdsl2LConfTempChan1ConfProfile,
          xdsl2LConfTempChan1RaRatioDs,
          xdsl2LConfTempChan1RaRatioUs,
          xdsl2LConfTempChan2ConfProfile,
          xdsl2LConfTempChan2RaRatioDs,
          xdsl2LConfTempChan2RaRatioUs,
          xdsl2LConfTempChan3ConfProfile,
          xdsl2LConfTempChan3RaRatioDs,
          xdsl2LConfTempChan3RaRatioUs,
          xdsl2LConfTempChan4ConfProfile,
          xdsl2LConfTempChan4RaRatioDs,
          xdsl2LConfTempChan4RaRatioUs,
          xdsl2LConfTempRowStatus
          }
      STATUS     current
      DESCRIPTION
         "The group of objects in a line configuration

          template."
      ::= { xdsl2Groups 10 }

   xdsl2LineConfProfGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2LConfProfScMaskDs,
          xdsl2LConfProfScMaskUs,
          xdsl2LConfProfVdsl2CarMask,
          xdsl2LConfProfRfiBands,
          xdsl2LConfProfRaModeDs,
          xdsl2LConfProfRaModeUs,
          xdsl2LConfProfTargetSnrmDs,
          xdsl2LConfProfTargetSnrmUs,
          xdsl2LConfProfMaxSnrmDs,
          xdsl2LConfProfMaxSnrmUs,
          xdsl2LConfProfMinSnrmDs,
          xdsl2LConfProfMinSnrmUs,
          xdsl2LConfProfCeFlag,
          xdsl2LConfProfSnrModeDs,
          xdsl2LConfProfSnrModeUs,
          xdsl2LConfProfTxRefVnDs,
          xdsl2LConfProfTxRefVnUs,
          xdsl2LConfProfXtuTransSysEna,
          xdsl2LConfProfPmMode,
          xdsl2LConfProfL0Time,
          xdsl2LConfProfL2Time,
          xdsl2LConfProfL2Atpr,
          xdsl2LConfProfL2Atprt,
          xdsl2LConfProfProfiles,
          xdsl2LConfProfDpboEPsd,
          xdsl2LConfProfDpboEsEL,
          xdsl2LConfProfDpboEsCableModelA,
          xdsl2LConfProfDpboEsCableModelB,
          xdsl2LConfProfDpboEsCableModelC,
          xdsl2LConfProfDpboMus,
          xdsl2LConfProfDpboFMin,
          xdsl2LConfProfDpboFMax,
          xdsl2LConfProfUpboKL,
          xdsl2LConfProfUpboKLF,
          xdsl2LConfProfUs0Mask,
          xdsl2LConfProfForceInp,
          xdsl2LConfProfRowStatus
          }
      STATUS     current
      DESCRIPTION
         "The group of objects in a line configuration
          profile."

      ::= { xdsl2Groups 11 }

   xdsl2LineConfProfRaGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2LConfProfRaUsNrmDs,
          xdsl2LConfProfRaUsNrmUs,
          xdsl2LConfProfRaUsTimeDs,
          xdsl2LConfProfRaUsTimeUs,
          xdsl2LConfProfRaDsNrmDs,
          xdsl2LConfProfRaDsNrmUs,
          xdsl2LConfProfRaDsTimeDs,
          xdsl2LConfProfRaDsTimeUs
          }
      STATUS     current
      DESCRIPTION
         "The group of objects required for controlling the
          rate-adaptive behavior of the line."
      ::= { xdsl2Groups 12 }

   xdsl2LineConfProfMsgMinGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2LConfProfMsgMinUs,
          xdsl2LConfProfMsgMinDs
          }
      STATUS     current
      DESCRIPTION
         "The group of objects required for controlling the rate
          reserved for Overhead traffic."
      ::= { xdsl2Groups 13 }

   xdsl2LineConfProfModeSpecGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2LConfProfMaxNomPsdDs,
          xdsl2LConfProfMaxNomPsdUs,
          xdsl2LConfProfMaxNomAtpDs,
          xdsl2LConfProfMaxNomAtpUs,
          xdsl2LConfProfMaxAggRxPwrUs,
          xdsl2LConfProfPsdMaskDs,
          xdsl2LConfProfPsdMaskUs,
          xdsl2LConfProfPsdMaskSelectUs,
          xdsl2LConfProfClassMask,
          xdsl2LConfProfLimitMask,
          xdsl2LConfProfUs0Disable,
          xdsl2LConfProfModeSpecRowStatus
          }

      STATUS     current
      DESCRIPTION
         "The group of objects in a line configuration profile
          that have an instance for each operation mode allowed."
      ::= { xdsl2Groups 14 }

   xdsl2LineConfProfModeSpecBandUsGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2LConfProfUpboPsdA,
          xdsl2LConfProfUpboPsdB,
          xdsl2LConfProfModeSpecBandUsRowStatus
          }
      STATUS     current
      DESCRIPTION
         "The group of objects in a line configuration profile
          that have several per-upstream-band instances for each
          operation mode allowed."
      ::= { xdsl2Groups 15 }

   xdsl2ChConfProfileGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2ChConfProfMinDataRateDs,
          xdsl2ChConfProfMinDataRateUs,
          xdsl2ChConfProfMaxDataRateDs,
          xdsl2ChConfProfMaxDataRateUs,
          xdsl2ChConfProfMinDataRateLowPwrDs,
          xdsl2ChConfProfMinDataRateLowPwrUs,
          xdsl2ChConfProfMaxDelayDs,
          xdsl2ChConfProfMaxDelayUs,
          xdsl2ChConfProfMinProtectionDs,
          xdsl2ChConfProfMinProtectionUs,
          xdsl2ChConfProfMinProtection8Ds,
          xdsl2ChConfProfMinProtection8Us,
          xdsl2ChConfProfMaxBerDs,
          xdsl2ChConfProfMaxBerUs,
          xdsl2ChConfProfUsDataRateDs,
          xdsl2ChConfProfDsDataRateDs,
          xdsl2ChConfProfUsDataRateUs,
          xdsl2ChConfProfDsDataRateUs,
          xdsl2ChConfProfRowStatus
          }
      STATUS     current
      DESCRIPTION
         "The group of objects in a channel configuration
          profile."
      ::= { xdsl2Groups 16 }

   xdsl2ChConfProfileAtmGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2ChConfProfImaEnabled,
          xdsl2ChStatusAtmStatus
          }
      STATUS     current
      DESCRIPTION
         "The group of configuration objects required when the data
          path is ATM."
      ::= { xdsl2Groups 17 }

   xdsl2ChConfProfileMinResGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2ChConfProfMinResDataRateDs,
          xdsl2ChConfProfMinResDataRateUs
          }
      STATUS     current
      DESCRIPTION
         "The group of configuration objects required for the
          reserved data rate."
      ::= { xdsl2Groups 18 }
   xdsl2ChConfProfileOptAttrGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2ChConfProfMaxDelayVar,
          xdsl2ChConfProfInitPolicy
          }
      STATUS     current
      DESCRIPTION
         "The group of various optional channel configuration
          parameters."
      ::= { xdsl2Groups 19 }

   xdsl2LineAlarmConfTemplateGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2LAlarmConfTempLineProfile,
          xdsl2LAlarmConfTempChan1ConfProfile,
          xdsl2LAlarmConfTempChan2ConfProfile,
          xdsl2LAlarmConfTempChan3ConfProfile,
          xdsl2LAlarmConfTempChan4ConfProfile,
          xdsl2LAlarmConfTempRowStatus
          }
      STATUS     current
      DESCRIPTION
         "The group of objects in a line alarm template."

      ::= { xdsl2Groups 20 }

   xdsl2LineAlarmConfProfileGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2LineAlarmConfProfileXtucThresh15MinFecs,
          xdsl2LineAlarmConfProfileXtucThresh15MinEs,
          xdsl2LineAlarmConfProfileXtucThresh15MinSes,
          xdsl2LineAlarmConfProfileXtucThresh15MinLoss,
          xdsl2LineAlarmConfProfileXtucThresh15MinUas,
          xdsl2LineAlarmConfProfileXturThresh15MinFecs,
          xdsl2LineAlarmConfProfileXturThresh15MinEs,
          xdsl2LineAlarmConfProfileXturThresh15MinSes,
          xdsl2LineAlarmConfProfileXturThresh15MinLoss,
          xdsl2LineAlarmConfProfileXturThresh15MinUas,
          xdsl2LineAlarmConfProfileThresh15MinFailedFullInt,
          xdsl2LineAlarmConfProfileThresh15MinFailedShrtInt,
          xdsl2LineAlarmConfProfileRowStatus
          }
      STATUS     current
      DESCRIPTION
         "The group of objects in a line alarm profile."
      ::= { xdsl2Groups 21 }

   xdsl2ChAlarmConfProfileGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2ChAlarmConfProfileXtucThresh15MinCodingViolations,
          xdsl2ChAlarmConfProfileXtucThresh15MinCorrected,
          xdsl2ChAlarmConfProfileXturThresh15MinCodingViolations,
          xdsl2ChAlarmConfProfileXturThresh15MinCorrected,
          xdsl2ChAlarmConfProfileRowStatus
          }
      STATUS     current
      DESCRIPTION
         "The group of objects in a channel alarm profile."
      ::= { xdsl2Groups 22 }

   xdsl2PMLineCurrGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2PMLCurr15MValidIntervals,
          xdsl2PMLCurr15MInvalidIntervals,
          xdsl2PMLCurr15MTimeElapsed,
          xdsl2PMLCurr15MFecs,
          xdsl2PMLCurr15MEs,
          xdsl2PMLCurr15MSes,
          xdsl2PMLCurr15MLoss,

          xdsl2PMLCurr15MUas,
          xdsl2PMLCurr1DayValidIntervals,
          xdsl2PMLCurr1DayInvalidIntervals,
          xdsl2PMLCurr1DayTimeElapsed,
          xdsl2PMLCurr1DayFecs,
          xdsl2PMLCurr1DayEs,
          xdsl2PMLCurr1DaySes,
          xdsl2PMLCurr1DayLoss,
          xdsl2PMLCurr1DayUas
          }
      STATUS     current
      DESCRIPTION
         "The group of objects that report the line-level
          counters for current PM intervals."
      ::= { xdsl2Groups 23 }

   xdsl2PMLineInitCurrGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2PMLInitCurr15MValidIntervals,
          xdsl2PMLInitCurr15MInvalidIntervals,
          xdsl2PMLInitCurr15MTimeElapsed,
          xdsl2PMLInitCurr15MFullInits,
          xdsl2PMLInitCurr15MFailedFullInits,
          xdsl2PMLInitCurr1DayValidIntervals,
          xdsl2PMLInitCurr1DayInvalidIntervals,
          xdsl2PMLInitCurr1DayTimeElapsed,
          xdsl2PMLInitCurr1DayFullInits,
          xdsl2PMLInitCurr1DayFailedFullInits
          }
      STATUS     current
      DESCRIPTION
         "The group of objects that report the full
          initialization counters for current PM intervals."
      ::= { xdsl2Groups 24 }

   xdsl2PMLineInitCurrShortGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2PMLInitCurr15MShortInits,
          xdsl2PMLInitCurr15MFailedShortInits,
          xdsl2PMLInitCurr1DayShortInits,
          xdsl2PMLInitCurr1DayFailedShortInits
          }
      STATUS     current
      DESCRIPTION
         "The group of objects that report the short
          initialization counters for current PM intervals."

      ::= { xdsl2Groups 25 }

   xdsl2PMLineHist15MinGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2PMLHist15MMonitoredTime,
          xdsl2PMLHist15MFecs,
          xdsl2PMLHist15MEs,
          xdsl2PMLHist15MSes,
          xdsl2PMLHist15MLoss,
          xdsl2PMLHist15MUas,
          xdsl2PMLHist15MValidInterval
          }
      STATUS     current
      DESCRIPTION
         "The group of line-level PM counters for the previous
          15-minute intervals."
      ::= { xdsl2Groups 26 }

   xdsl2PMLineHist1DayGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2PMLHist1DMonitoredTime,
          xdsl2PMLHist1DFecs,
          xdsl2PMLHist1DEs,
          xdsl2PMLHist1DSes,
          xdsl2PMLHist1DLoss,
          xdsl2PMLHist1DUas,
          xdsl2PMLHist1DValidInterval
          }
      STATUS     current
      DESCRIPTION
         "The group of line-level PM counters for the previous
          24-hour intervals."
      ::= { xdsl2Groups 27 }

   xdsl2PMLineInitHist15MinGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2PMLInitHist15MMonitoredTime,
          xdsl2PMLInitHist15MFullInits,
          xdsl2PMLInitHist15MFailedFullInits,
          xdsl2PMLInitHist15MValidInterval
          }
      STATUS     current
      DESCRIPTION
         "The group of PM counters for the previous 15-minute
          interval full initializations."

      ::= { xdsl2Groups 28 }

   xdsl2PMLineInitHist15MinShortGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2PMLInitHist15MShortInits,
          xdsl2PMLInitHist15MFailedShortInits
          }
      STATUS     current
      DESCRIPTION
         "The group of PM counters for the previous 15-minute
          interval short initializations."
      ::= { xdsl2Groups 29 }

   xdsl2PMLineInitHist1DayGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2PMLInitHist1DMonitoredTime,
          xdsl2PMLInitHist1DFullInits,
          xdsl2PMLInitHist1DFailedFullInits,
          xdsl2PMLInitHist1DValidInterval
          }
      STATUS     current
      DESCRIPTION
         "The group of PM counters for the previous 24-hour
          interval full initializations."
      ::= { xdsl2Groups 30 }

   xdsl2PMLineInitHist1DayShortGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2PMLInitHist1DShortInits,
          xdsl2PMLInitHist1DFailedShortInits
          }
      STATUS     current
      DESCRIPTION
         "The group of PM counters for the previous 24-hour
          interval short initializations."
      ::= { xdsl2Groups 31 }

   xdsl2PMChCurrGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2PMChCurr15MValidIntervals,
          xdsl2PMChCurr15MInvalidIntervals,
          xdsl2PMChCurr15MTimeElapsed,
          xdsl2PMChCurr15MCodingViolations,
          xdsl2PMChCurr15MCorrectedBlocks,

          xdsl2PMChCurr1DayValidIntervals,
          xdsl2PMChCurr1DayInvalidIntervals,
          xdsl2PMChCurr1DayTimeElapsed,
          xdsl2PMChCurr1DayCodingViolations,
          xdsl2PMChCurr1DayCorrectedBlocks
          }
      STATUS     current
      DESCRIPTION
         "The group of objects that report the channel-level
          counters for current PM intervals."
      ::= { xdsl2Groups 32 }

   xdsl2PMChHist15MinGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2PMChHist15MMonitoredTime,
          xdsl2PMChHist15MCodingViolations,
          xdsl2PMChHist15MCorrectedBlocks,
          xdsl2PMChHist15MValidInterval
          }
      STATUS     current
      DESCRIPTION
         "The group of objects that report the channel-level
          counters for previous 15-minute PM intervals."
      ::= { xdsl2Groups 33 }

   xdsl2PMChHist1DGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2PMChHist1DMonitoredTime,
          xdsl2PMChHist1DCodingViolations,
          xdsl2PMChHist1DCorrectedBlocks,
          xdsl2PMChHist1DValidInterval
          }
      STATUS     current
      DESCRIPTION
        "The group of objects that report the channel-level
        counters for previous 24-hour PM intervals."
      ::= { xdsl2Groups 34 }

   xdsl2ScalarSCGroup OBJECT-GROUP
      OBJECTS
          {
          xdsl2ScalarSCMaxInterfaces,
          xdsl2ScalarSCAvailInterfaces
          }
      STATUS     current
      DESCRIPTION

         "The group of objects that report the available memory
          resources for DELT processes."
      ::= { xdsl2Groups 35 }

   xdsl2ThreshNotificationGroup NOTIFICATION-GROUP
      NOTIFICATIONS
      {
      xdsl2LinePerfFECSThreshXtuc,
      xdsl2LinePerfFECSThreshXtur,
      xdsl2LinePerfESThreshXtuc,
      xdsl2LinePerfESThreshXtur,
      xdsl2LinePerfSESThreshXtuc,
      xdsl2LinePerfSESThreshXtur,
      xdsl2LinePerfLOSSThreshXtuc,
      xdsl2LinePerfLOSSThreshXtur,
      xdsl2LinePerfUASThreshXtuc,
      xdsl2LinePerfUASThreshXtur,
      xdsl2LinePerfCodingViolationsThreshXtuc,
      xdsl2LinePerfCodingViolationsThreshXtur,
      xdsl2LinePerfCorrectedThreshXtuc,
      xdsl2LinePerfCorrectedThreshXtur,
      xdsl2LinePerfFailedFullInitThresh,
      xdsl2LinePerfFailedShortInitThresh
      }
      STATUS      current
      DESCRIPTION
         "This group supports notifications of significant
          conditions associated with DSL lines."
      ::= { xdsl2Groups 36 }

   xdsl2StatusChangeNotificationGroup NOTIFICATION-GROUP
      NOTIFICATIONS
      {
      xdsl2LineStatusChangeXtuc,
      xdsl2LineStatusChangeXtur
      }
      STATUS      current
      DESCRIPTION
         "This group supports notifications of thresholds crossing
          associated with DSL lines."
      ::= { xdsl2Groups 37 }

END

