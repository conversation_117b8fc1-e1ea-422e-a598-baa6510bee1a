-- =================================================================
-- Copyright (c) 2004-2013 New H3C Tech. Co., Ltd.  All rights reserved.
--
-- Description: Mirror group MIB
-- Reference:
-- Version: V1.1
-- History:
--   2006-01-10 created by xialei
--     Initial version V1.0
-- V1.1 2012/04/28 modified by mouxuanli
--  Modify the description of the object hh3cMGStatus.
--  Modify the description of the object hh3cMGMirrorDirection.
--  Add hh3cMGEgressIfTable, hh3cMGMirrorVlanTable and hh3cMGMirrorCpuTable.
-- =================================================================
-- =================================================================
--
-- Import and definition
--
-- =================================================================
HH3C-MIRRORGROUP-MIB DEFINITIONS ::= BEGIN

IMPORTS

    hh3cCommon
        FROM HH3C-OID-MIB
    RowStatus
        FROM SNMPv2-TC
    Integer32, OBJECT-TYPE, MODULE-IDENTITY, Unsigned32
        FROM SNMPv2-SMI;

    hh3cMirrGroup MODULE-IDENTITY
        LAST-UPDATED "200601131403Z"
        ORGANIZATION
            "New H3C Tech. Co., Ltd."
        CONTACT-INFO
            "Platform Team New H3C Tech. Co., Ltd.
            Hai-Dian District Beijing P.R. China
            http://www.h3c.com
            Zip:100085
            "
        DESCRIPTION
            "This MIB defines objects for managing mirror group."
        REVISION "200601101903Z"
        DESCRIPTION
            "The initial revision of this MIB module."
        ::= { hh3cCommon  68 }

    hh3cMGInfoObjects OBJECT IDENTIFIER ::= {  hh3cMirrGroup 1  }

    hh3cMGObjects OBJECT IDENTIFIER ::= {  hh3cMGInfoObjects 1  }

    hh3cMGTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF Hh3cMGEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A list of mirror group entries."
        ::= { hh3cMGObjects 1 }

    hh3cMGEntry OBJECT-TYPE
        SYNTAX      Hh3cMGEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A list of parameters that describe a mirror group to be created."
        INDEX
        {
            hh3cMGID
        }
      ::= { hh3cMGTable 1 }

    Hh3cMGEntry ::=
        SEQUENCE
        {
            hh3cMGID           Integer32,
            hh3cMGType         INTEGER,
            hh3cMGStatus       INTEGER,
            hh3cMGRowStatus    RowStatus
        }

    hh3cMGID OBJECT-TYPE
        SYNTAX      Integer32 (0..2147483647)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "An index that uniquely identifies an entry in the mirror group table."
        ::={ hh3cMGEntry 1 }

    hh3cMGType OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        local(1),
                        remoteSource(2),
                        remoteDestination(3)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "GroupType."
        ::={ hh3cMGEntry 2 }

    hh3cMGStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        active(1),
                        inactive(2)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
            "The status of a mirror group.  A mirror group is inactive
            when it was created.

            The status of a mirror group can be changed automatically from inactive
            to active when appropriate conditions were met.  Also, it can be changed
            automatically from active to inactive when these conditions disappeared.

            Followings are conditions for a mirror group to become active:
              group type          conditions
              ============================================================================
              local               any source (mirroring-port, mirroring-VLAN, or mirroring-CPU),
                                  monitor-port
              remoteSource        If one of the three conditions is satisfied,
                                  remoteSource group would be active.
                                  1) any source (mirroring-port, mirroring-VLAN, or mirroring-CPU),
                                     remote-probe VLAN,
                                     configurable reflector-port (which is not supported on some devices)
                                  2) any source (mirroring-port, mirroring-VLAN, or mirroring-CPU),
                                     remote-probe VLAN,
                                     egress-port (which is not supported on some devices)
                                  3) any source (mirroring-port, mirroring-VLAN, or mirroring-CPU),
                                     remote-probe VLAN,
                                     fixed reflector-port(which is not supported on some devices)
              remoteDestination   monitor-port, remote-probe VLAN
            "
        ::={ hh3cMGEntry 3 }

    hh3cMGRowStatus OBJECT-TYPE
        SYNTAX      RowStatus
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "RowStatus"
        ::={ hh3cMGEntry 4 }

    hh3cMGMirrorIfObjects OBJECT IDENTIFIER ::= {  hh3cMGInfoObjects 2  }

    hh3cMGMirrorIfTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF Hh3cMGMirrorIfEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A list of mirror group mirroring-port entries."
        ::= { hh3cMGMirrorIfObjects 1 }

    hh3cMGMirrorIfEntry OBJECT-TYPE
        SYNTAX      Hh3cMGMirrorIfEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A list of parameters that describe a mirroring-port to be added to
            a mirror group."
        INDEX
        {
            hh3cMGID,
            hh3cMGMirrorIfIndex,
            hh3cMGMirrorDirection
        }
        ::= { hh3cMGMirrorIfTable 1 }

    Hh3cMGMirrorIfEntry ::=
        SEQUENCE
        {
            hh3cMGMirrorIfIndex      Integer32,
            hh3cMGMirrorDirection    INTEGER,
            hh3cMGMirrorRowStatus    RowStatus
        }

    hh3cMGMirrorIfIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "IfIndex of mirror group mirroring-port."
        ::={ hh3cMGMirrorIfEntry 1 }

    hh3cMGMirrorDirection OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        inbound(1),
                        outbound(2),
                        both(3)
                    }
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Mirror direction of mirroring-port."
        ::={ hh3cMGMirrorIfEntry 2 }

    hh3cMGMirrorRowStatus OBJECT-TYPE
        SYNTAX      RowStatus
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "RowStatus"
        ::={ hh3cMGMirrorIfEntry 3 }

    hh3cMGMonitorIfObjects OBJECT IDENTIFIER ::= {  hh3cMGInfoObjects 3  }

    hh3cMGMonitorIfTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF Hh3cMGMonitorIfEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A list of mirror group monitor-port entries."
        ::= { hh3cMGMonitorIfObjects 1 }

    hh3cMGMonitorIfEntry OBJECT-TYPE
        SYNTAX      Hh3cMGMonitorIfEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A list of parameters that describe a monitor-port to be added to
            a mirror group."
        INDEX
        {
            hh3cMGID,
            hh3cMGMonitorIfIndex
        }
        ::= { hh3cMGMonitorIfTable 1 }

    Hh3cMGMonitorIfEntry ::=
        SEQUENCE
        {
            hh3cMGMonitorIfIndex      Integer32,
            hh3cMGMonitorRowStatus    RowStatus
        }

    hh3cMGMonitorIfIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "IfIndex of mirror group monitor-port."
        ::={ hh3cMGMonitorIfEntry 1 }

    hh3cMGMonitorRowStatus OBJECT-TYPE
        SYNTAX      RowStatus
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "RowStatus"
        ::={ hh3cMGMonitorIfEntry 2 }

    hh3cMGReflectorIfObjects OBJECT IDENTIFIER ::= {  hh3cMGInfoObjects 4  }

    hh3cMGReflectorIfTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF Hh3cMGReflectorIfEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A list of mirror group reflector-port entries."
        ::= { hh3cMGReflectorIfObjects 1 }

    hh3cMGReflectorIfEntry OBJECT-TYPE
        SYNTAX      Hh3cMGReflectorIfEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A list of parameters that describe a reflector-port to be added to
            a mirror group."
        INDEX
        {
            hh3cMGID,
            hh3cMGReflectorIfIndex
        }
        ::= { hh3cMGReflectorIfTable 1 }

    Hh3cMGReflectorIfEntry ::=
        SEQUENCE
        {
            hh3cMGReflectorIfIndex      Integer32,
            hh3cMGReflectorRowStatus    RowStatus
        }

    hh3cMGReflectorIfIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "IfIndex of mirror group reflector-port."
        ::={ hh3cMGReflectorIfEntry 1 }

    hh3cMGReflectorRowStatus OBJECT-TYPE
        SYNTAX      RowStatus
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "RowStatus"
        ::={ hh3cMGReflectorIfEntry 2 }

    hh3cMGRprobeVlanObjects OBJECT IDENTIFIER ::= {  hh3cMGInfoObjects 5  }

    hh3cMGRprobeVlanTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF Hh3cMGRprobeVlanEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A list of mirror group remote-probe vlan entries."
        ::= { hh3cMGRprobeVlanObjects 1 }

    hh3cMGRprobeVlanEntry OBJECT-TYPE
        SYNTAX      Hh3cMGRprobeVlanEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A list of parameters that describe a remote-probe vlan to be added
            to a mirror group.  Details about remote-probe vlan please refer to
            mirror group manual."
        INDEX
        {
            hh3cMGID,
            hh3cMGRprobeVlanID
        }
        ::= { hh3cMGRprobeVlanTable 1 }

    Hh3cMGRprobeVlanEntry ::=
        SEQUENCE
        {
            hh3cMGRprobeVlanID           Integer32,
            hh3cMGRprobeVlanRowStatus    RowStatus
        }

    hh3cMGRprobeVlanID OBJECT-TYPE
        SYNTAX      Integer32(1..4094)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "An index that uniquely identifies an entry in the remote-probe vlan table."
        ::={ hh3cMGRprobeVlanEntry 1 }

    hh3cMGRprobeVlanRowStatus OBJECT-TYPE
        SYNTAX      RowStatus
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "RowStatus"
        ::={ hh3cMGRprobeVlanEntry 2 }

    hh3cMGEgressIfObjects OBJECT IDENTIFIER ::= {  hh3cMGInfoObjects 6  }

    hh3cMGEgressIfTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF Hh3cMGEgressIfEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A list of mirror group egress-port entries."
        ::= { hh3cMGEgressIfObjects 1 }

    hh3cMGEgressIfEntry OBJECT-TYPE
        SYNTAX      Hh3cMGEgressIfEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A list of parameters that describe an egress-port to be added to
            a mirror group."
        INDEX
        {
            hh3cMGID,
            hh3cMGEgressIfIndex
        }
        ::= { hh3cMGEgressIfTable 1 }

    Hh3cMGEgressIfEntry ::=
        SEQUENCE
        {
            hh3cMGEgressIfIndex      Integer32,
            hh3cMGEgressRowStatus    RowStatus
        }

    hh3cMGEgressIfIndex OBJECT-TYPE
        SYNTAX      Integer32 (0..2147483647)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "IfIndex of mirror group egress-port."
        ::={ hh3cMGEgressIfEntry 1 }

    hh3cMGEgressRowStatus OBJECT-TYPE
        SYNTAX      RowStatus
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "RowStatus"
        ::={ hh3cMGEgressIfEntry 2 }

    hh3cMGMirrorVlanObjects OBJECT IDENTIFIER ::= {  hh3cMGInfoObjects 7  }

    hh3cMGMirrorVlanTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF Hh3cMGMirrorVlanEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A list of mirror group mirroring-VLAN entries."
        ::= { hh3cMGMirrorVlanObjects 1 }

    hh3cMGMirrorVlanEntry OBJECT-TYPE
        SYNTAX      Hh3cMGMirrorVlanEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A list of parameters that describe a mirroring-VLAN to be added to
            a mirror group."
        INDEX
        {
            hh3cMGID,
            hh3cMGMirrorVlanID
        }
        ::= { hh3cMGMirrorVlanTable 1 }

    Hh3cMGMirrorVlanEntry ::=
        SEQUENCE
        {
            hh3cMGMirrorVlanID           Integer32,
            hh3cMGMirrorVlanDirection    INTEGER,
            hh3cMGMirrorVlanRowStatus    RowStatus
        }

    hh3cMGMirrorVlanID OBJECT-TYPE
        SYNTAX      Integer32(1..4094)
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Mirror group mirroring-VLAN ID."
        ::={ hh3cMGMirrorVlanEntry 1 }

    hh3cMGMirrorVlanDirection OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        inbound(1),
                        outbound(2),
                        both(3)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "Mirror direction of mirroring-VLAN."
        ::={ hh3cMGMirrorVlanEntry 2 }

    hh3cMGMirrorVlanRowStatus OBJECT-TYPE
        SYNTAX      RowStatus
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "RowStatus"
        ::={ hh3cMGMirrorVlanEntry 3 }

    hh3cMGMirrorCpuObjects OBJECT IDENTIFIER ::= {  hh3cMGInfoObjects 8  }

    hh3cMGMirrorCpuTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF Hh3cMGMirrorCpuEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A list of mirror group mirroring-CPU entries."
        ::= { hh3cMGMirrorCpuObjects 1 }

    hh3cMGMirrorCpuEntry OBJECT-TYPE
        SYNTAX      Hh3cMGMirrorCpuEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "A list of parameters that describe a mirroring-CPU to be added to
            a mirror group."
        INDEX
        {
            hh3cMGID,
            hh3cMGMirrorCpuChassis,
            hh3cMGMirrorCpuSlot
        }
        ::= { hh3cMGMirrorCpuTable 1 }

    Hh3cMGMirrorCpuEntry ::=
        SEQUENCE
        {
            hh3cMGMirrorCpuChassis      Unsigned32,
            hh3cMGMirrorCpuSlot         Unsigned32,
            hh3cMGMirrorCpuDirection    INTEGER,
            hh3cMGMirrorCpuRowStatus    RowStatus
        }

    hh3cMGMirrorCpuChassis OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Chassis ID of mirroring-CPU.  On a centralized device,
             centralized IRF device, or distributed device in standalone mode,
             the value for this node is always zero."
        ::={ hh3cMGMirrorCpuEntry 1 }

    hh3cMGMirrorCpuSlot OBJECT-TYPE
        SYNTAX      Unsigned32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION
            "Slot ID of mirroring-CPU.  On a centralized device, the value for
             this node is always zero."
        ::={ hh3cMGMirrorCpuEntry 2 }

    hh3cMGMirrorCpuDirection OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        inbound(1),
                        outbound(2),
                        both(3)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "Mirror direction of mirroring-CPU."
        ::={ hh3cMGMirrorCpuEntry 3 }

    hh3cMGMirrorCpuRowStatus OBJECT-TYPE
        SYNTAX      RowStatus
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION
            "RowStatus"
        ::={ hh3cMGMirrorCpuEntry 4 }


END
