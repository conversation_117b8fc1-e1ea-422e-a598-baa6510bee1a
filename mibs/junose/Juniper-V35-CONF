
-- *****************************************************************************
-- Juniper-V35-CONF
--
-- SNMP Agent Capabilities definitions for the X.21/V.35 MIB.
--
-- Copyright (c) 2002 Unisphere Networks, Inc.
-- Copyright (c) 2002, 2003 Juniper Networks, Inc.
--   All rights reserved.
-- *****************************************************************************

Juniper-V35-CONF  DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY
        FROM SNMPv2-SMI
    AGENT-CAPABILITIES
        FROM SNMPv2-CONF
    juniAgents
        FROM Juniper-Agents;

juniV35Agent  MODULE-IDENTITY
    LAST-UPDATED "200209061654Z"  -- 06-Sep-02 12:54 PM EDT
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
        "       Juniper Networks, Inc.
        Postal: 10 Technology Park Drive
                Westford, MA  01886-3146
                USA
        Tel:    ****** 589 5800
        E-mail: <EMAIL>"
    DESCRIPTION
        "The agent capabilities definitions for the X.21/V.35 server component
        of the SNMP agent in the Juniper E-series family of products."
    -- Revision History
    REVISION    "200209061654Z"  -- 06-Sep-02 12:54 PM EDT  - JUNOSe 5.0
    DESCRIPTION
        "Replaced Unisphere names with Juniper names."
    REVISION    "200201252143Z"  -- 25-Jan-02 04:43 PM EST  - JUNOSe 4.0
    DESCRIPTION
        "The initial release of this management information module."
    ::= { juniAgents 54 }


-- *****************************************************************************
-- X.21/V.35 SNMP Agent Capabilities definitions
-- *****************************************************************************
juniV35AgentV1  AGENT-CAPABILITIES
    PRODUCT-RELEASE
        "Version 1 of the X.21/V.35 component of the JUNOSe SNMP agent.  This
        version of the X.21/V.35 component is supported in JUNOSe 4.0 and
        subsequent system releases."
    STATUS      current
    DESCRIPTION
        "The MIB supported by the SNMP agent for the X.21/V.35 application in
        JUNOSe."
    SUPPORTS    Juniper-V35-MIB
        INCLUDES {
            juniV35Group }
    ::= { juniV35Agent 1 }

END
