
-- *****************************************************************************
-- Juniper-L2TP-Dialout-Generator-CONF
--
-- SNMP Agent Capabilities definitions for the L2TP Dialout Generator MIB.
--
-- Copyright (c) 2002 Unisphere Networks, Inc.
-- Copyright (c) 2002, 2003 Juniper Networks, Inc.
--   All rights reserved.
-- *****************************************************************************

Juniper-L2TP-Dialout-Generator-CONF  DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY
        FROM SNMPv2-SMI
    AGENT-CAPABILITIES
        FROM SNMPv2-CONF
    juniAgents
        FROM Juniper-Agents;

juniL2tpDialoutGeneratorAgent  MODULE-IDENTITY
    LAST-UPDATED "200211211451Z"  -- 21-Nov-02 09:51 AM EST
    ORGANIZATION "Juniper Networks, Inc."
    CONTACT-INFO
        "       Juniper Networks, Inc.
        Postal: 10 Technology Park Drive
                Westford, MA  01886-3146
                USA
        Tel:    ****** 589 5800
        E-mail: <EMAIL>"
    DESCRIPTION
        "The agent capabilities definitions for the Layer 2 Tunneling Protocol
        (L2TP) Dialout Generator component of the SNMP agent in the Juniper
        E-series family of products."
    -- Revision History
    REVISION    "200211211451Z"  -- 21-Nov-02 09:51 AM EST  - JUNOSe 5.0
    DESCRIPTION
        "The initial release of this management information module."
    ::= { juniAgents 59 }


-- *****************************************************************************
-- L2TP Dialout Generator SNMP Agent Capabilities definitions
-- *****************************************************************************
juniL2tpDialoutGeneratorAgentV1  AGENT-CAPABILITIES
    PRODUCT-RELEASE
        "Version 1 of the L2TP Dialout Generator component of the JUNOSe SNMP
        agent.  This version of the L2TP Dialout Generator component is
        supported in JUNOSe 5.0 and subsequent system releases."
    STATUS      current
    DESCRIPTION
        "The MIB supported by the SNMP agent for the L2TP Dialout Generator
        application in JUNOSe."
    SUPPORTS    Juniper-L2TP-Dialout-MIB
        INCLUDES {
            juniL2tpDialoutTimersGroup,
            juniL2tpDialoutTargetConfigGroup,
            juniL2tpDialoutTriggerBuffersGroup,
            juniL2tpDialoutSessionControlGroup,
            juniL2tpDialoutStatusGroup,
            juniL2tpDialoutSystemStatisticsGroup,
            juniL2tpDialoutSystemWideVRouterStatisticsGroup,
            juniL2tpDialoutSystemWideTargetStatisticsGroup,
            juniL2tpDialoutSystemWideSessionStatisticsGroup,
            juniL2tpDialoutVRouterStatisticsGroup,
            juniL2tpDialoutTargetStatisticsGroup,
            juniL2tpDialoutSessionStatisticsGroup }
    ::= { juniL2tpDialoutGeneratorAgent 1 }

END
