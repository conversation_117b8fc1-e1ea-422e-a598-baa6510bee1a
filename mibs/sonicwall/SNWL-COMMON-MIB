-- *****************************************************************
-- SNWL-COMMON-MIB
--
-- 11-09-07 - Initial Version, <PERSON>
-- 03-10-08 - Removed Imports that are not used to prevent warnings
--            in MIB compilation, <PERSON>y 
-- 04-17-09 - Updated E-mail CONTACT-INFO, Rosa<PERSON> Real
-- 11-11-09 - Updated SonicWall company address, <PERSON>
-- 07-23-12 - Updated company copyright and organization, <PERSON> Uy
-- 01-06-17 - Updated company copyright and organization, <PERSON>
--
-- Copyright (c) 2017 SonicWall Inc.
-- All rights reserved.
-- *****************************************************************

SNWL-COMMON-MIB DEFINITIONS ::= BEGIN


IMPORTS
    DisplayString			 	FROM SNMPv2-TC

    OBJECT-TYPE,
	MODULE-IDENTITY             FROM SNMPv2-SMI

    sonicwallCommon        		FROM SONICWALL-SMI;

snwlCommonModule MODULE-IDENTITY
	LAST-UPDATED "201701060000Z"
	ORGANIZATION "SonicWall"
	CONTACT-INFO
		"	SonicWall Inc.
 
		   Postal: 5455 Great America Parkway
				   Santa Clara, CA 95054
				   USA

		   Tel: ****** 745 9600
		   Fax: ****** 745 9300

		E-mail: <EMAIL>"
	DESCRIPTION
		"This MIB module defines functions and features common
		 across all SonicWall products and platforms."
	REVISION      "201701060000Z"
	DESCRIPTION   "Updated company copyright and organization."

	REVISION      "200711090000Z"
	DESCRIPTION
		"Initial Version"
    ::= { sonicwallCommon 1 }

-- =======================
-- System Group Lists
-- =======================

snwlSys OBJECT IDENTIFIER ::= { snwlCommonModule 1 }

-- Start of System Group --

snwlSysModel OBJECT-TYPE
	SYNTAX DisplayString
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
		"System model name and number"
	::= { snwlSys 1 }

snwlSysSerialNumber OBJECT-TYPE
	SYNTAX DisplayString 
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
		"System serial number for this device"
	::= { snwlSys 2 }

snwlSysFirmwareVersion OBJECT-TYPE
	SYNTAX DisplayString 
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
		"Current system firmware version"
	::= { snwlSys 3 }

snwlSysROMVersion OBJECT-TYPE
	SYNTAX DisplayString 
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
		"Current system ROM version"
	::= { snwlSys 4 }

snwlSysAssetNumber OBJECT-TYPE
	SYNTAX DisplayString 
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
		"user-defined string entry used for asset tracking"
	::= { snwlSys 5 }

-- End of System Group --

END
