
VOLIUS-OT-MIB DEFINITIONS ::= BEGIN

IMPORTS
	NOTIFICATION-TYPE, OBJECT-TYPE, MODULE-IDENTITY, 
	enterprises, TimeTicks, Integer32
		FROM SNMPv2-SMI
	TEXTUAL-CONVENTION, Mac<PERSON><PERSON>ress, <PERSON>AndTime, 
	DisplayString
		FROM SNMPv2-TC
	InetAddressIPv4
		FROM INET-ADDRESS-MIB;

vlsGlobalModule MODULE-IDENTITY
	LAST-UPDATED "201105311534Z"
	ORGANIZATION 
		"Volius"
	CONTACT-INFO 
		""
	DESCRIPTION 
		"This MIB describes all SNMP-enabled hardware produced by Volius,
		such as Fiber optical receivers, Erbium-doped fiber amplifiers,
		Optical switches, CATV optical transmitters."

	REVISION "201105311543Z"
	DESCRIPTION 
		"Initial version"
::= { volius 1 }


VlsDbm ::= TEXTUAL-CONVENTION
	STATUS     current
	DESCRIPTION 
		"Data type representing RF power readings in units of dBm."
	SYNTAX  Integer32


VlsDbuv ::= TEXTUAL-CONVENTION
	DISPLAY-HINT 	"d"
	STATUS     current
	DESCRIPTION 
		"Data type representing RF power readings in units of dBuV."
	SYNTAX  Integer32


VlsDeciCelsius ::= TEXTUAL-CONVENTION
	DISPLAY-HINT 	"d-1"
	STATUS     current
	DESCRIPTION 
		"Data type representing temperature readings in units of 0.1
		degrees Celsius. For example, a temperature of -12.3 degrees
		Celsius will be represented as -123."
	SYNTAX  Integer32


VlsDeciDb ::= TEXTUAL-CONVENTION
	STATUS     current
	DESCRIPTION 
		"Data type representing RF gain in units of 0.1 dB. For
		example, a gain of -2.5 dB will be represented as -25."
	SYNTAX  Integer32


VlsDeciDbm ::= TEXTUAL-CONVENTION
	DISPLAY-HINT 	"d-1"
	STATUS     current
	DESCRIPTION 
		"Data type representing power readings in units of 0.1 dBm. For
		example, a power of -2.5 dBm will be represented as -25."
	SYNTAX  Integer32


VlsDeciDbo ::= TEXTUAL-CONVENTION
	STATUS     current
	DESCRIPTION 
		"Data type representing OMI readings in units of 0.1 dBo.
		For example, an OMI of 10% will be represented as -100
		(meaning -10.0 dBo), 
		an OMI of 1% will be represented
		as -200 (meaning -20.0 dBo)."
	SYNTAX  Integer32


VlsEvent ::= TEXTUAL-CONVENTION
	STATUS     current
	DESCRIPTION 
		""
	SYNTAX  INTEGER {
		vlsEventStartUp (1),
		vlsEventLaserOn (20),
		vlsEventLaserOff (21),
		vlsEventPout1Norm (22),
		vlsEventPout1High (23),
		vlsEventPout1Low (24),
		vlsEventPout2Norm (25),
		vlsEventPout2High (26),
		vlsEventPout2Low (27),
		vlsEventTcaseNorm (28),
		vlsEventTcaseLow (29),
		vlsEventTcaseHigh (30),
		vlsEventRFinNorm (31),
		vlsEventRFinLow (32),
		vlsEventRFinHigh (33),
		vlsEventLaserTempNorm (34),
		vlsEventLaserTempLow (35),
		vlsEventLaserTempHigh (36),
		vlsEventTecCurrentNorm (37),
		vlsEventTecCurrentLow (38),
		vlsEventTecCurrentHigh (39),
		vlsEventLaserCurrentNorm (40),
		vlsEventLaserCurrentLow (41),
		vlsEventLaserCurrentHigh (42),
		vlsEventLaserPowerNorm (43),
		vlsEventLaserPowerLow (44),
		vlsEventLaserPowerHigh (45),
		vlsEventOmiNorm (46),
		vlsEventOmiLow (47),
		vlsEventOmiHigh (48),
		vlsEventFanNorm (49),
		vlsEventFanLow (50)
	}


VlsMilliAmp ::= TEXTUAL-CONVENTION
	STATUS     current
	DESCRIPTION 
		"Data type representing electrical current values in units of mA."
	SYNTAX  Integer32


VlsMillivolt ::= TEXTUAL-CONVENTION
	DISPLAY-HINT 	"d-3"
	STATUS     current
	DESCRIPTION 
		"Data type representing voltage values in units of mV."
	SYNTAX  Integer32


VlsOnOff ::= TEXTUAL-CONVENTION
	STATUS     current
	DESCRIPTION 
		"Data type representing an on-off state."
	SYNTAX  INTEGER {
		vlsOn (1),
		vlsOff (2)
	}


VlsPerMille ::= TEXTUAL-CONVENTION
	STATUS     current
	DESCRIPTION 
		"Data type representing fractions, where percentage would
		normally be used. 1 per mille corresponds to 0.1%.
		For example. 99.9% would be 999 per mille."
	SYNTAX  Integer32


VlsPercent ::= TEXTUAL-CONVENTION
	STATUS     current
	DESCRIPTION 
		"Data type representing fractions in units of 1%."
	SYNTAX  Integer32


VlsRPM ::= TEXTUAL-CONVENTION
	STATUS     current
	DESCRIPTION 
		"Rotational speed in units of revolutions per minute."
	SYNTAX  Integer32

volius               OBJECT IDENTIFIER ::= { enterprises 34652 }
vlsSystem            OBJECT IDENTIFIER ::= { volius 2 }
vlsSystemGeneral     OBJECT IDENTIFIER ::= { vlsSystem 10 }
vlsNetworkServices   OBJECT IDENTIFIER ::= { vlsSystem 11 }
vlsNetworkAddress    OBJECT IDENTIFIER ::= { vlsNetworkServices 5 }
vlsHttp              OBJECT IDENTIFIER ::= { vlsNetworkServices 6 }
vlsSnmp              OBJECT IDENTIFIER ::= { vlsNetworkServices 7 }
vlsSntp              OBJECT IDENTIFIER ::= { vlsNetworkServices 8 }
vlsEventLog          OBJECT IDENTIFIER ::= { vlsSystem 13 }
vlsAlarms            OBJECT IDENTIFIER ::= { vlsSystem 14 }
vlsEdfa              OBJECT IDENTIFIER ::= { volius 3 }
vlsOpticalSwitch     OBJECT IDENTIFIER ::= { volius 4 }
vlsCatvTransmitter   OBJECT IDENTIFIER ::= { volius 5 }
vlsOpticalReceiver   OBJECT IDENTIFIER ::= { volius 6 }
vlsCabinetMonitor    OBJECT IDENTIFIER ::= { volius 7 }
vlsUpsMonitor        OBJECT IDENTIFIER ::= { volius 8 }

vlsModelName  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsSystemGeneral 1 }

vlsSerialNumber  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsSystemGeneral 2 }

vlsFirmwareVersion  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsSystemGeneral 3 }

vlsBootLoaderVersion  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsSystemGeneral 4 }

vlsDateAndTime  OBJECT-TYPE
	SYNTAX     DateAndTime
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsSystemGeneral 5 }

vlsTimeZone  OBJECT-TYPE
	SYNTAX     Integer32 (-720..780)
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Difference between local and UTC in minutes."
	::= { vlsSystemGeneral 6 }

vlsCaseTemperature  OBJECT-TYPE
	SYNTAX     VlsDeciCelsius
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsSystemGeneral 7 }

vlsFanSpeed  OBJECT-TYPE
	SYNTAX     VlsRPM
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsSystemGeneral 8 }

vlsSupplyVoltageTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF VlsSupplyVoltageEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsSystemGeneral 9 }

vlsSupplyVoltageEntry  OBJECT-TYPE
	SYNTAX 	VlsSupplyVoltageEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		""
	INDEX { vlsSupplyVoltageIndex }
	::= { vlsSupplyVoltageTable 1 }

VlsSupplyVoltageEntry ::= SEQUENCE {
	vlsSupplyVoltageIndex
		Integer32,
	vlsSupplyVoltageNominal
		VlsMillivolt,
	vlsSupplyVoltageActual
		VlsMillivolt
}

vlsSupplyVoltageIndex  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsSupplyVoltageEntry 1 }

vlsSupplyVoltageNominal  OBJECT-TYPE
	SYNTAX     VlsMillivolt
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsSupplyVoltageEntry 2 }

vlsSupplyVoltageActual  OBJECT-TYPE
	SYNTAX     VlsMillivolt
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsSupplyVoltageEntry 3 }

vlsMacAddress  OBJECT-TYPE
	SYNTAX     MacAddress
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsNetworkAddress 1 }

vlsIpAddress  OBJECT-TYPE
	SYNTAX     InetAddressIPv4
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsNetworkAddress 2 }

vlsNetMask  OBJECT-TYPE
	SYNTAX     InetAddressIPv4
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsNetworkAddress 3 }

vlsDefaultGateway  OBJECT-TYPE
	SYNTAX     InetAddressIPv4
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsNetworkAddress 4 }

vlsHttpPort  OBJECT-TYPE
	SYNTAX     Integer32 (1..65535)
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsHttp 1 }

vlsHttpEnabled  OBJECT-TYPE
	SYNTAX     VlsOnOff
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsHttp 2 }

vlsHttpPasswordEnabled  OBJECT-TYPE
	SYNTAX     VlsOnOff
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsHttp 3 }

vlsSnmpPort  OBJECT-TYPE
	SYNTAX     Integer32 (1..65535)
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsSnmp 1 }

vlsTrapDestTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF VlsTrapDestEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsSnmp 2 }

vlsTrapDestEntry  OBJECT-TYPE
	SYNTAX 	VlsTrapDestEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		""
	INDEX { vlsTrapDestIndex }
	::= { vlsTrapDestTable 1 }

VlsTrapDestEntry ::= SEQUENCE {
	vlsTrapDestIndex
		Integer32,
	vlsTrapDestAddr
		InetAddressIPv4,
	vlsTrapDestPort
		Integer32,
	vlsTrapDestEnable
		VlsOnOff
}

vlsTrapDestIndex  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsTrapDestEntry 1 }

vlsTrapDestAddr  OBJECT-TYPE
	SYNTAX     InetAddressIPv4
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsTrapDestEntry 2 }

vlsTrapDestPort  OBJECT-TYPE
	SYNTAX     Integer32 (1..65535)
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsTrapDestEntry 3 }

vlsTrapDestEnable  OBJECT-TYPE
	SYNTAX     VlsOnOff
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsTrapDestEntry 4 }

vlsSntpServerAddr  OBJECT-TYPE
	SYNTAX     InetAddressIPv4
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsSntp 1 }

vlsSntpServerPort  OBJECT-TYPE
	SYNTAX     Integer32 (1..65535)
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsSntp 2 }

vlsSntpEnabled  OBJECT-TYPE
	SYNTAX     VlsOnOff
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsSntp 3 }

vlsLastEventCode  OBJECT-TYPE
	SYNTAX     VlsEvent
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsEventLog 1 }

vlsLastEventIndex  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsEventLog 2 }

vlsEventLogSize  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Number of events stored in the log."
	::= { vlsEventLog 3 }

vlsEventLogTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF VlsEventLogEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsEventLog 4 }

vlsEventLogEntry  OBJECT-TYPE
	SYNTAX 	VlsEventLogEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		""
	INDEX { vlsEventIndex }
	::= { vlsEventLogTable 1 }

VlsEventLogEntry ::= SEQUENCE {
	vlsEventIndex
		Integer32,
	vlsEventCode
		VlsEvent,
	vlsEventTimeStamp
		TimeTicks,
	vlsEventDateTime
		DateAndTime,
	vlsEventMessage
		DisplayString
}

vlsEventIndex  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsEventLogEntry 1 }

vlsEventCode  OBJECT-TYPE
	SYNTAX     VlsEvent
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsEventLogEntry 2 }

vlsEventTimeStamp  OBJECT-TYPE
	SYNTAX     TimeTicks
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsEventLogEntry 3 }

vlsEventDateTime  OBJECT-TYPE
	SYNTAX     DateAndTime
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsEventLogEntry 4 }

vlsEventMessage  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsEventLogEntry 5 }

vlsEventTrap  NOTIFICATION-TYPE
	OBJECTS { vlsLastEventCode }
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsEventLog 8 }

vlsAlarmsActiveMask  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"This is a bit mask where all active alarms are combined.
		Alarms are latching, 
		that is an alarm is still active even when
		the condition activating the alarm no longer exists."
	::= { vlsAlarms 1 }

vlsAlarmsResetMask  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"You can write reset mask to reset individual alarms.
		An alarm will be reset only if the activating condition no longer exists."
	::= { vlsAlarms 2 }

vlsAlarmsTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF VlsAlarmsEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsAlarms 3 }

vlsAlarmsEntry  OBJECT-TYPE
	SYNTAX 	VlsAlarmsEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		""
	INDEX { vlsAlarmIndex }
	::= { vlsAlarmsTable 1 }

VlsAlarmsEntry ::= SEQUENCE {
	vlsAlarmIndex
		Integer32,
	vlsAlarmState
		VlsOnOff,
	vlsAlarmMessage
		DisplayString
}

vlsAlarmIndex  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsAlarmsEntry 1 }

vlsAlarmState  OBJECT-TYPE
	SYNTAX     VlsOnOff
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"When read, this OID indicates if the alarm is active.
		When vlsOff is written to this OID, the alarm is reset."
	::= { vlsAlarmsEntry 2 }

vlsAlarmMessage  OBJECT-TYPE
	SYNTAX     DisplayString
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsAlarmsEntry 3 }

vlsTxEmissionState  OBJECT-TYPE
	SYNTAX     VlsOnOff
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Reports whether the laser is on or off."
	::= { vlsCatvTransmitter 1 }

vlsTxOutputTable  OBJECT-TYPE
	SYNTAX SEQUENCE OF VlsTxOutputEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsCatvTransmitter 2 }

vlsTxOutputEntry  OBJECT-TYPE
	SYNTAX 	VlsTxOutputEntry
	MAX-ACCESS not-accessible
	STATUS     current
	DESCRIPTION 
		""
	INDEX { vlsTxOutputIndex }
	::= { vlsTxOutputTable 1 }

VlsTxOutputEntry ::= SEQUENCE {
	vlsTxOutputIndex
		Integer32,
	vlsTxOutputPower
		VlsDeciDbm
}

vlsTxOutputIndex  OBJECT-TYPE
	SYNTAX     Integer32
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Optical output number (1 or 2)."
	::= { vlsTxOutputEntry 1 }

vlsTxOutputPower  OBJECT-TYPE
	SYNTAX     VlsDeciDbm
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Optical output level."
	::= { vlsTxOutputEntry 2 }

vlsTxPoutLowAlarm  OBJECT-TYPE
	SYNTAX     VlsDeciDbm
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Output power low alarm threshold."
	::= { vlsCatvTransmitter 3 }

vlsTxPoutHighAlarm  OBJECT-TYPE
	SYNTAX     VlsDeciDbm
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Output power high alarm threshold."
	::= { vlsCatvTransmitter 4 }

vlsTxInputComposite  OBJECT-TYPE
	SYNTAX     VlsDbm
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Composite RF level at the transmitter input."
	::= { vlsCatvTransmitter 5 }

vlsTxInputPerChannel  OBJECT-TYPE
	SYNTAX     VlsDbuv
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Per-channel RF level at the transmitter input."
	::= { vlsCatvTransmitter 6 }

vlsTxChannelPwrFactor  OBJECT-TYPE
	SYNTAX     VlsDeciDb
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"This factor is used to convert composite RF level to per-channel RF level.
		The factor is expressed in units of 0.1 dB and is always negative or zero.
		For example, if the channel plan is 100 channels all with the same RF level,
		the factor should be -200 (meaning -20.0 dB). Then, if composite RF level
		is 0 dBm (that is 108.75 dBuV), per-channel RF level would be
		108.75 - 20 = 88.75 dBuV."
	::= { vlsCatvTransmitter 7 }

vlsTxOmi  OBJECT-TYPE
	SYNTAX     VlsPerMille
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Composite OMI in units of 0.1%."
	::= { vlsCatvTransmitter 8 }

vlsTxAtt  OBJECT-TYPE
	SYNTAX     VlsDeciDb
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Actual variable attenuator state. The range is 0..25 dB."
	::= { vlsCatvTransmitter 9 }

vlsTxLaserCurrent  OBJECT-TYPE
	SYNTAX     VlsPercent
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		"Laser current.
		100% corresponds to the current set during factory calibration.
		The current is expected to rise slowly after years of operation
		(laser aging)."
	::= { vlsCatvTransmitter 10 }

vlsTxLaserTecCurrent  OBJECT-TYPE
	SYNTAX     VlsPercent
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsCatvTransmitter 11 }

vlsTxLaserPower  OBJECT-TYPE
	SYNTAX     VlsDeciDbm
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsCatvTransmitter 12 }

vlsTxLaserTemperature  OBJECT-TYPE
	SYNTAX     VlsDeciCelsius
	MAX-ACCESS read-only
	STATUS     current
	DESCRIPTION 
		""
	::= { vlsCatvTransmitter 13 }

vlsTxAgc  OBJECT-TYPE
	SYNTAX     VlsOnOff
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"AGC mode on/off control."
	::= { vlsCatvTransmitter 14 }

vlsTxSetOmi  OBJECT-TYPE
	SYNTAX     VlsPerMille
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"OMI setpoint when AGC is on."
	::= { vlsCatvTransmitter 15 }

vlsTxSetAtt  OBJECT-TYPE
	SYNTAX     VlsDeciDb
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"Variable attenuator setpoint when AGC is off."
	::= { vlsCatvTransmitter 16 }

vlsTxSetSbsThreshold  OBJECT-TYPE
	SYNTAX     VlsDbm
	MAX-ACCESS read-write
	STATUS     current
	DESCRIPTION 
		"SBS suppression level."
	::= { vlsCatvTransmitter 17 }
END
