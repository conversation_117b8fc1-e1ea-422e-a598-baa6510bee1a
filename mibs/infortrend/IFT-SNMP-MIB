--
-- Revision: 1.40A01 (2011/11/03)
-- NEW SPEC: Support DS G7
--           Support Advanced LUN
-- Revision: 1.40A02 (2014/01/08)
-- SPEC Modify: New release for additional logical device type and status definition.
-- Revision: 1.40A03 (2014/08/22)
-- SPEC Modify: New release for redundant controller configuration, fibre host channel id base.
-- Revision: 1.40A04 (2014/10/16)
-- SPEC Modify: Support SSD wear life.
-- Revision: 1.40A05 (2015/01/07)
-- SPEC Modify: Change the Fan (RPM) display value in luDevValue.
--              The lunChl changes from logical channel nummber to Physical channel number.
-- Revision: 1.40A06 (2015/01/21)
-- SPEC Modify: HDD Logical ID (hddLdId) can distinguish SSD Cache drive.
-- Revision: 1.40A07 (2015/01/22)
-- SPEC Modify: The extLunChl changes from logical channel nummber to Physical channel number.
-- Revision: 1.40A08 (2015/09/25)
-- SPEC Modify: Add throughput, IOPS for controller and channel.
--              Add lantency for HDD and LD.
-- Revision: 1.40A09 (2017/04/28)
-- SPEC Modify: Add trap mib.
-- Revision: 1.40A10 (2017/05/16)
-- SPEC Modify: modified enterprise and variables object of trap event.
--              modified name of trap event.
-- Revision: 1.40A11 (2017/09/19)
-- SPEC Modify: Replace underscore(_) in identifiers with dash(-).
--              Convert initial character of identifiers to lower case.
--              Shorten identifiers that length is over 64.

IFT-SNMP-MIB DEFINITIONS ::= BEGIN

IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, OBJECT-IDENTITY, enterprises, NOTIFICATION-TYPE,
    Integer32, Counter32
    FROM SNMPv2-SMI

    TEXTUAL-CONVENTION, DisplayString, TruthValue
    FROM SNMPv2-TC;

infortrend MODULE-IDENTITY
    LAST-UPDATED "201709190000Z"
    ORGANIZATION "Infortrend Technology, Inc."
    CONTACT-INFO "Author:     Technical Support Dept.
                  postal:     8F, No. 102 Chung-Shan Rd., Sec. 3 Chung-Ho City, Taipei County  Taiwan, ROC
                  email:      <EMAIL>
                  phone:      886-02-2226-0126
                 "
    DESCRIPTION  "Infortrend SNMP MIB"

    REVISION     "201709190000Z"             -- 19 Sep 2017
    DESCRIPTION  "Revision: 1.40A11 (2017/09/19)
                  Replace underscore(_) in identifiers with dash(-).
                  Convert initial character of identifiers to lower case.
				  Shorten identifiers that length is over 64.
                 "

    REVISION     "201705160000Z"             -- 16 May 2017
    DESCRIPTION  "Revision: 1.40A10 (2017/05/16)
                  modified enterprise and variables object of trap event.
                  modified name of trap event.
                 "

    REVISION     "201704280000Z"             -- 28 Apr 2017
    DESCRIPTION  "Revision: 1.40A09 (2017/04/28)
                  Add trap mib.
                 "

    REVISION     "201509250000Z"             -- 25 Sep 2015
    DESCRIPTION  "Revision: 1.40A08 (2015/09/25)
                  Add throughput, IOPS for controller and channel.
                  Add lantency for HDD and LD.
                 "

    REVISION     "201501220000Z"             -- 22 Jan 2015
    DESCRIPTION  "Revision: 1.40A07 (2015/01/22)
                  The extLunChl changes from logical channel nummber to Physical channel number.
                 "

    REVISION     "201501210000Z"             -- 21 Jan 2015
    DESCRIPTION  "Revision: 1.40A06 (2015/01/21)
                  HDD Logical ID (hddLdId) can distinguish SSD Cache drive.
                 "

    REVISION     "201501070000Z"             -- 07 Jan 2015
    DESCRIPTION  "Revision: 1.40A05 (2015/01/07)
                  Change the Fan (RPM) display value in luDevValue.
                  The lunChl changes from logical channel nummber to Physical channel number.
                 "

    REVISION     "201410160000Z"             -- 16 Oct 2014
    DESCRIPTION  "Revision: 1.40A04 (2014/10/16)
                  Support SSD wear life
                 "
    REVISION     "201408220000Z"             -- 22 Aug 2014
    DESCRIPTION  "Revision: 1.40A03 (2014/08/22)
                  New release for redundant controller configuration, fibre host channel id base
                  and support S model.
                 "
    REVISION     "201401080000Z"             -- 08 Jan 2014
    DESCRIPTION  "Revision: 1.40A02 (2014/01/08)
                  New release for additional logical device type and status definition.
                 "
    REVISION     "201111030000Z"             -- 03 Nov 2011
    DESCRIPTION  "Revision: 1.40A01 (2011/11/03)
                  New release for DS G7.
                 "

    ::= { enterprises 1714 }

-- raid     OBJECT IDENTIFIER ::= { infortrend 1 }

raid OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION     "It's a raid snmp agent"
    ::= { infortrend 1 }

-- SnmpTrap     OBJECT IDENTIFIER ::= { infortrend 2 }



-- extInterface     OBJECT IDENTIFIER ::= { raid 1 }

extInterface OBJECT-IDENTITY
    STATUS          current
    DESCRIPTION     "MIB of Infortrend External Interface Protocol(Draft: 2.26)
                        and Generalized Target Configuration Protocol(Draft: 1.33)"
    ::= { raid 1 }

ctlrConfiguration   OBJECT IDENTIFIER ::= { extInterface 1 }
eventLog            OBJECT IDENTIFIER ::= { extInterface 11 }
sysInformation      OBJECT IDENTIFIER ::= { ctlrConfiguration 1 }
cachingParams       OBJECT IDENTIFIER ::= { ctlrConfiguration 2 }
diskArrayParams     OBJECT IDENTIFIER ::= { ctlrConfiguration 3 }
hostSideParams      OBJECT IDENTIFIER ::= { ctlrConfiguration 4 }
driveSideParams     OBJECT IDENTIFIER ::= { ctlrConfiguration 5 }
redundantParams     OBJECT IDENTIFIER ::= { ctlrConfiguration 6 }

-- ************************************************************************
--                          System Information
-- ************************************************************************
cpuType OBJECT-TYPE
    SYNTAX       DisplayString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "CPU type on RAID controller"
    ::= { sysInformation 1 }

cacheSize OBJECT-TYPE
    SYNTAX       Integer32
    UNITS        "MB"
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Cache size on RAID controller, in MB"
    ::= { sysInformation 2 }

memoryType OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Cache memory type on RAID controller
                  0: FPG, 1: EDO, 2: SDRAM, 3: ECC SDRAM, 4: DDR, 5: ECC DDR"
    ::= { sysInformation 3 }

fwMajorVersion OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Firmware major version"
    ::= { sysInformation 4 }

fwMinorVersion OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Firmware minor version"
    ::= { sysInformation 5 }

fwEngineerVersion OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Firmware engineer version"
    ::= { sysInformation 6 }

brMajorVersion OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Bootrecord major version"
    ::= { sysInformation 7 }

brMinorVersion OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Bootrecord minor version"
    ::= { sysInformation 8 }

brEngineerVersion OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Bootrecord engineer version"
    ::= { sysInformation 9 }

serialNum OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Serial number"
    ::= { sysInformation 10 }

ctlrName OBJECT-TYPE
    SYNTAX       DisplayString
    MAX-ACCESS   read-write
    STATUS       current
    DESCRIPTION  "Controller name"
    ::= { sysInformation 11 }

ctlrCfgModeFlags OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-write
    STATUS       current
    DESCRIPTION  "Controller configuration mode flags
                    BIT 0 : Write Back Status (RW)
                            0: Disabled, 1: Enabled.
                    BIT 1 : Motor Spin Up (RW)
                            0: Disabled, 1: Enabled.
                    BIT 2 : Power Up SCSI Reset (RW)
                            0: Disabled, 1: Enabled.
                    BIT 8 : Battery Backup Supported (RO)
                            0: Not Supported, 1: Supported.
                    BIT 9 : Battery Backup Present (RO)
                            0: Absent, 1: Present.
                    BIT 12 : ECC Function
                            0: Disabled, 1: Enabled
                    BIT 15 : JBOD Mode (RW) - This should be ignored if
                            JBOD Mode Supported is not set.
                            0: Disabled, 1: Enabled.
                    BIT 16 : LUN Assignment by SCSI ID (RO)
                            0: Not Supported, 1: Supported.
                    BIT 17 : Support SCSI LUN > 0  (RO)
                    BIT 18 : Spanning Logical Drive Support
                            0: Not Supported, 1: Supported.
                    BIT 19 : JBOD Mode Supported (RO)
                            0: Not Supported, 1: Supported.
                    BIT 20 -- BIT 32 : Reserved."
    ::= { sysInformation 12 }

privateLogoString OBJECT-TYPE
    SYNTAX       DisplayString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Private logo string"
    ::= { sysInformation 13 }

privateLogoVendor OBJECT-TYPE
    SYNTAX       DisplayString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Private logo vendor string"
    ::= { sysInformation 14 }

privateLogoModel OBJECT-TYPE
    SYNTAX       DisplayString
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Private logo model string"
    ::= { sysInformation 15 }

ctlrUniqueID OBJECT-TYPE
    SYNTAX       DisplayString
    MAX-ACCESS   read-write
    STATUS       current
    DESCRIPTION  "Controller unique identifier"
    ::= { sysInformation 16 }

serialNumSec OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Serial number of Secodary Controller"
    ::= { sysInformation 17 }

-- ************************************************************************
--                          Caching Parameters
-- ************************************************************************
cacheModeFlags OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Caching mode flags
                    BIT 0 : Write Back Status (RW)
                            0: Disabled, 1: Enabled.
                    BIT 1 : Read Ahead Disable (RW)
                            0: Enabled, 1: Disabled.
                    BIT 2 : Enable/Disable keeping LD/LV
                            off-line after controller initialization
                            if cached write data was lost (RW)
                            0: Disabled, 1: Enabled.
                    BIT 3 -- BIT 7 : Reserved (Set to 0).
                    BIT 8 : Cache Optimization Option (RW)
                            0: Small/Random I/Os, 1: Large/Sequential I/Os
                    BIT 9 -- BIT 15 : Reserved (Set to 0).
                    BIT 16 -- BIT 18 : Periodic Cache Sync Period (RW)
                            For interpretation of values, reference
                            Periodic Cache Sync Period Value
                            Cross-Reference List.
                    BIT 19 -- BIT 21 : Cache Flush Initiation
                            Threshold (RW)
                            For interpretation of values, reference
                            Cache Flush Initiation Threshold Valid
                            Cross-Reference List.
                    BIT 22 -- BIT 25 : Cache Flush Termination
                            Threshold (RW)
                            For interpretation of values, reference
                            Cache Flush Termination Threshold Valid
                            Cross-Reference List.
                    BIT 26 -- BIT 31 : Reserved (Set to 0)."
    ::= { cachingParams 1 }

cacheBlkSizeIdx OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Cache block size index
                    BIT 0 -- BIT 3 : Cache Block Size Index
                        Block Size (bytes) = 512 * 2 ** (Block Size Index)
                    BIT 4 -- BIT 7 : Reserved (Set to 0)."
    ::= { cachingParams 2 }

cacheTotal OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Total number of cache blocks"
    ::= { cachingParams 3 }

cacheDirty OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Number of dirty cache blocks"
    ::= { cachingParams 4 }


-- ************************************************************************
--                          Disk Array Parameters
-- ************************************************************************
maxRebPriorityIdx OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Maximum supported rebuild priority index
                  0: Low, 1: Normal, 2: Improved, 3: High"
    ::= { diskArrayParams 1 }

minRebPriorityIdx OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Minimum supported rebuild priority index
                  0: Low, 1: Normal, 2: Improved, 3: High"
    ::= { diskArrayParams 2 }

defRebPriorityIdx OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Default rebuild priority index
                  0: Low, 1: Normal, 2: Improved, 3: High"
    ::= { diskArrayParams 3 }

curRebPriorityIdx OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Current rebuild priority index
                  0: Low, 1: Normal, 2: Improved, 3: High"
    ::= { diskArrayParams 4 }

writeVerifyModeFlags OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Write-verify mode flags
                    BIT 0 : Write-Verify on Normal Writes (RW)
                            0: Disabled, 1: Enabled.
                    BIT 1 : Write-Verify on Initialization Writes (RW)
                            0: Disabled, 1: Enabled.
                    BIT 2 : Write-Verify on Rebuild Writes (RW)
                            0: Disabled, 1: Enabled.
                    BIT 3 -- BIT 7 : Reserved (Set to 0)."
    ::= { diskArrayParams 5 }


-- ************************************************************************
--                      Host-side Interface Parameters
-- ************************************************************************
maxQueuedIOCnt OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Maximum supported host queued I/O count
                    == N (> 0) ==> Count = 2 ** ( N - 1 )."
    ::= { hostSideParams 1 }

minQueuedIOCnt OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Minimum supported host queued I/O count
                    == N (> 0) ==> Count = 2 ** ( N - 1 )."
    ::= { hostSideParams 2 }

defQueuedIOCnt OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Default host queued I/O count
                    == N (> 0) ==> Count = 2 ** ( N - 1 )."
    ::= { hostSideParams 3 }

curQueuedIOCnt OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Current host queued I/O count
                    == 0 - Automatically Computed.
                    == N (> 0) ==> Count = 2 ** ( N - 1 )."
    ::= { hostSideParams 4 }

maxLunNum OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Maximum LUNs per host SCSI ID(SCSI-to-SCSI only)
                    == N ==> LUNs = 2 ** ( N - 1 )"
    ::= { hostSideParams 5 }

minLunNum OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Minimum LUNs per host SCSI ID(SCSI-to-SCSI only)
                    == N ==> LUNs = 2 ** ( N - 1 )"
    ::= { hostSideParams 6 }

defLunNum OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Default LUNs per host SCSI ID(SCSI-to-SCSI only)
                    == N ==> LUNs = 2 ** ( N - 1 )"
    ::= { hostSideParams 7 }

curLunNum OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Current LUNs per host SCSI ID(SCSI-to-SCSI only)
                    == N ==> LUNs = 2 ** ( N - 1 )"
    ::= { hostSideParams 8 }

curReadStatistic OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Current Read Statistic MB/S"
    ::= { hostSideParams 9 }

curWriteStatistic OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Current Write Statistic MB/S"
    ::= { hostSideParams 10 }

curReadRequests OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Current Read Request Req/S"
    ::= { hostSideParams 11 }

curWriteRequests OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Current Write Request Req/S"
    ::= { hostSideParams 12 }


-- ************************************************************************
--                      Drive-side Interface Parameters
-- ************************************************************************
modeFlags OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "mode flags of drive-side interface parameters
                    BIT 0 : Motor Spin Up (RW)
                            0: Disabled, 1: Enabled.
                    BIT 1 : Power Up SCSI Reset (RW)
                            0: Disabled, 1: Enabled.
                    BIT 2 -- BIT 15 : Reserved (Set to 0)
                    BITS 16-17: Drive Failure Prediction (SMART) Mode:
                                == 0 - Disabled.
                                == 1 - Detect only.
                                == 2 - Clone drive and keep drive cloned.
                                == 3 - Clone drive and replace when cloning complete.
                    BIT 18 : Auto Assign Global Spare Drive (RW)
                            0: Disabled, 1: Enabled.
                    BIT 19 -- BIT 31 : Reserved (Set to 0)"
    ::= { driveSideParams 1 }

maxAccessDelayTime OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Maximum time to delay prior to first disk access(in 5 seconds unit)
                    == N ==> Delay = N * 5 seconds."
    ::= { driveSideParams 2 }

minAccessDelayTime OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Minimum time to delay prior to first disk access(in 5 seconds unit)
                    == N ==> Delay = N * 5 seconds."
    ::= { driveSideParams 3 }

defAccessDelayTime OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Default time to delay prior to first disk access(in 5 seconds unit)
                    == N ==> Delay = N * 5 seconds."
    ::= { driveSideParams 4 }

curAccessDelayTime OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Current time to delay prior to first disk access(in 5 seconds unit)
                    == 0 ==> No Delay.
                    == N ==> Delay = N * 5 seconds."
    ::= { driveSideParams 5 }

maxTagCnt OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Maximum drive-side SCSI tags per drive(Tags = 2**(N-1))"
    ::= { driveSideParams 6 }

minTagCnt OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Minimum drive-side SCSI tags per drive(Tags = 2**(N-1))"
    ::= { driveSideParams 7 }

defTagCnt OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Default drive-side SCSI tags per drive(Tags = 2**(N-1))"
    ::= { driveSideParams 8 }

curTagCnt OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Current  drive-side SCSI tags per drive(Tags = 2**(N-1), 0: Disabled)"
    ::= { driveSideParams 9 }

defIOTimeout OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Default drive-side SCSI I/O timeout(in 10ms units)"
    ::= { driveSideParams 10 }

curIOTimeout OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Current drive-side SCSI I/O timeout(in 10ms units)"
    ::= { driveSideParams 11 }

defDrvChkPeriod OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Default drive-side SCSI drive check period(10ms units)"
    ::= { driveSideParams 12 }

curDrvChkPeriod OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Current drive-side SCSI drive check period(10ms units)"
    ::= { driveSideParams 13 }

defSaftePollingPeriod OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Default SAFTE polling period(10ms units)"
    ::= { driveSideParams 14 }

curSaftePollingPeriod OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Current SAFTE polling period(10ms units)"
    ::= { driveSideParams 15 }

defAutoDetectPeriod OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Default auto-detect failed drive swaped check period(10ms units)"
    ::= { driveSideParams 16 }

curAutoDetectPeriod OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Current auto-detect failed drive swaped check period(10ms units)"
    ::= { driveSideParams 17 }

-- ************************************************************************
--                      Redundant Controller Parameters
-- ************************************************************************
redCtlrCfg OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Redundant controller configuration
                  For Reading:
                        == 0xff - Not Supported.
                        == 0 - Disabled.
                        == 1 - Primary.
                        == 2 - Secondary."
    ::= { redundantParams 1 }

redCtlrModeFlags OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Redundant controller mode flags
                    BIT 0 : Cache Synchronization (RW)
                              0 : Cache NOT Synchronized, 1 : Cache Synchronized
                    BIT 1 : Host Channel Failover Mode (RW)
                              0 : Shared host bus, 1 : Independent host bus
                    BIT 2 : Local/Remote Redundant Mode (RW)
                              0 : Local Redundant, 1 : Remote Redundant
                    BITS 2-14 : Reserved.
                    BIT 15 : Secondary RS-232 Port Status (RW)
                              0 : Port Disabled, 1 : Port Enabled"
    ::= { redundantParams 2 }

redCtlrCommType OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Redundant controller communication channel type
                    == 0 - RS-232
                    == 1 - SCSI/Fibre Channel"
    ::= { redundantParams 3 }

redCtlrStatus OBJECT-TYPE
    SYNTAX       Integer32
    MAX-ACCESS   read-only
    STATUS       current
    DESCRIPTION  "Redundant controller status
                    BIT 0 -- BIT 5 : Current Status
                                    == 0 - Inactive.
                                    == 1 - Scanning.
                                    == 0x11 - Detected.
                                    == 0x12 - Enabled.
                                    == 0x3f - Controller Failed.
                    BIT 6 : Original Controller Role (prior to first controller failure):
                            0: Primary, 1: Secondary.
                    BIT 7 : Current Controller Role
                            0: Primary, 1: Secondary."
    ::= { redundantParams 4 }


-- ************************************************************************
--                          Logical Drive Table
-- ************************************************************************
ldTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF LdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Logical drive table"
    ::= { extInterface 2 }

ldEntry OBJECT-TYPE
    SYNTAX      LdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Logical drive entry"
    INDEX   { ldIndex }
    ::= { ldTable  1 }

LdEntry ::= SEQUENCE {
    ldIndex             Integer32,
    ldID                DisplayString,
    ldSize              DisplayString,
    ldBlkSizeIdx        Integer32,
    ldOpModes           Integer32,
    ldStatus            Integer32,
    ldState             Integer32,
    ldTotalDrvCnt       Integer32,
    ldOnlineDrvCnt      Integer32,
    ldSpareDrvCnt       Integer32,
    ldFailedDrvCnt      Integer32,
    ldReadStatistic     DisplayString,
    ldWriteStatistic    DisplayString,
    ldReadLatency       DisplayString,
    ldWriteLatency      DisplayString
}

ldIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Logical drive index"
    ::= { ldEntry 1 }

ldID OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical drive identifier"
    ::= { ldEntry 2 }

ldSize OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical drive size, in Blocks, block size is 512 bytes"
    ::= { ldEntry 3 }

ldBlkSizeIdx OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Stripe Block Size (bytes) = (Stripe Block Size Index) Power of 2"
    ::= { ldEntry 4 }

ldOpModes OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION "Logical drive operating modes
                    BITS 0-3 : RAID Level (RO):
                              0 : Single Drive
                              1 : NON-RAID
                              2 : RAID 0
                              3 : RAID 1
                              4 : RAID 3
                              5 : RAID 4
                              6 : RAID 5
                              7 : RAID 6
                              0xf: NOT DEFINED
                    BITS 4-6 : Reserved (Set to 0).
                    BIT 7 : Logical Drive Assignment (R/W):
                              If CLEAR, Logical Drive is assigned to Primary Controller.
                              If SET, Logical Drive is assigned to Secondary Controller."
    ::= { ldEntry 5 }

ldStatus OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical drive status
                    BITS 0-2 : Status Code (RO):
                              0 : Good
                              1 : Rebuilding
                              2 : Initializing
                              3 : Degraded
                              4 : Dead
                              5 : Invalid
                              6 : Incomplete
                              7 : Drive Missing
                    BITS 3-5 : Reserved.
                    BIT 6 : Logical Drive Data Not Synced
                    BIT 7 : Logical Drive Off-line (RW)."
    ::= { ldEntry 6 }

ldState OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical drive state
                    BIT 0 : If SET, in process of rebuilding
                            (degraded mode) or checking/updating
                            Logical Drive Parity (LD is 'good').
                    BIT 1 : If SET, in process of expanding Logical Drive.
                    BIT 2 : If SET, in process of adding SCSI drives
                            to Logical Drive.
                    BIT 3-5: Reserved.
                    BIT 6 : If SET, add SCSI drives operation is paused.
                    BIT 7 : Reserved."
    ::= { ldEntry 7 }

ldTotalDrvCnt OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Total hard drive count on logical drive"
    ::= { ldEntry 8 }

ldOnlineDrvCnt OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "On-Line hard drvie count on logical drive"
    ::= { ldEntry 9 }

ldSpareDrvCnt OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS  current
    DESCRIPTION "Spare hard drive count on logical drive"
    ::= { ldEntry 10 }

ldFailedDrvCnt OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Failed hard drive count on logical drive"
    ::= { ldEntry 11 }

ldReadStatistic OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical Drive Read Statistic MB/S"
    ::= { ldEntry 12 }

ldWriteStatistic OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical Drive Write Statistic MB/S"
    ::= { ldEntry 13 }

ldReadLatency OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical Drive Read Latency (in microseconds)"
    ::= { ldEntry 14 }

ldWriteLatency OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical Drive Write Latency (in microseconds)"
    ::= { ldEntry 15 }


-- ************************************************************************
--                          Logical Volume Table
-- ************************************************************************
lvTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF LvEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Logical volume table"
    ::= { extInterface 3 }

lvEntry OBJECT-TYPE
    SYNTAX      LvEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Logical volume entry"
    INDEX   { lvIndex }
    ::= { lvTable  1 }

LvEntry ::= SEQUENCE {
    lvIndex     Integer32,
    lvID        DisplayString,
    lvSize      DisplayString,
    lvBlkSizeIdx    Integer32,
    lvOpModes   Integer32,
    lvLdCount   Integer32,
    lvLdList    DisplayString
}

lvIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Logical volume index"
    ::= { lvEntry 1 }

lvID OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical volume identifier"
    ::= { lvEntry 2 }

lvSize OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical volume size, in Blocks, block size is 512 bytes"
    ::= { lvEntry 3 }

lvBlkSizeIdx OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Stripe Block Size (bytes) = (Stripe Block Size Index) Power of 2"
    ::= { lvEntry 4 }

lvOpModes OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION "Logical volume operating modes
                    BITS 0-6 : Reserved (Set to 0).
                    BIT 7 : Logical Volume Assignment (R/W):
                            If CLEAR, Logical Volume is assigned to Primary Controller.
                            If SET, Logical Volume is assigned to Secondary Controller."
    ::= { lvEntry 5 }

lvLdCount OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical drive counts in this logical volume"
    ::= { lvEntry 6 }

lvLdList OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical drive identifiers list in this logical volume"
    ::= { lvEntry 7 }


-- ************************************************************************
--                          Partition Table
-- ************************************************************************
partTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF PartEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Partition table"
    ::= { extInterface 4 }

partEntry OBJECT-TYPE
    SYNTAX      PartEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Partition entry"
    INDEX   { partIndex }
    ::= { partTable  1 }

PartEntry ::= SEQUENCE {
    partIndex   Integer32,
    partLdLvID  DisplayString,
    partOffset  DisplayString,
    partSize    DisplayString
}

partIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..127)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Partition index"
    ::= { partEntry 1 }

partLdLvID OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical drive/Logical volume identifier"
    ::= { partEntry 2 }

partOffset OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Partition base offset"
    ::= { partEntry 3 }

partSize OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Partition size, in Blocks"
    ::= { partEntry 4 }


-- ************************************************************************
--                                 LUN Table
-- ************************************************************************
lunTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF LunEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "LUN table"
    ::= { extInterface 5 }

lunEntry OBJECT-TYPE
    SYNTAX      LunEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "LUN entry"
    INDEX   { lunIndex }
    ::= { lunTable  1 }

LunEntry ::= SEQUENCE {
    lunIndex    Integer32,
    lunChl      Integer32,
    lunID       Integer32,
    lunNum      Integer32,
    lunLdLvID   DisplayString,
    lunPartIdx  Integer32,
    lunSsSiID   DisplayString
}

lunIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "LUN index"
    ::= { lunEntry 1 }

lunChl OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Physical channel number"
    ::= { lunEntry 2 }

lunID OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Host-side SCSI ID"
    ::= { lunEntry 3 }

lunNum OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Host-side SCSI LUN"
    ::= { lunEntry 4 }

lunLdLvID OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical drive/Logical volume identifier"
    ::= { lunEntry 5 }

lunPartIdx OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Partition index of logical drive or logical volume"
    ::= { lunEntry 6 }

lunSsSiID OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Snapshot Set/Snapshot Image identifier"
    ::= { lunEntry 7 }

-- ************************************************************************
--                          Hard Disk Drive Table
-- ************************************************************************
hddTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF HddEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Hard disk drive table"
    ::= { extInterface 6 }

hddEntry OBJECT-TYPE
    SYNTAX      HddEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Hard disk drive entry"
    INDEX   { hddIndex }
    ::= { hddTable  1 }

HddEntry ::= SEQUENCE {
    hddIndex            Integer32,
    hddLogChlNum        Integer32,
    hddPhyChlNum        Integer32,
    hddScsiId           Integer32,
    hddScsiLun          Integer32,
    hddLdId             DisplayString,
    hddSize             DisplayString,
    hddBlkSizeIdx       Integer32,
    hddSpeed            Integer32,
    hddDataWidth        Integer32,
    hddStatus           Integer32,
    hddState            Integer32,
    hddSlotNum          Integer32,
    hddResvSpace        Integer32,
    hddModelStr         DisplayString,
    hddFwRevStr         DisplayString,
    hddSerialNum        DisplayString,
    hddReadStatistic    DisplayString,
    hddWriteStatistic   DisplayString,
    hddSmart1           DisplayString,
    hddSmart2           DisplayString,
    hddSmart3           DisplayString,
    hddSmart4           DisplayString,
    hddSmart5           DisplayString,
    hddSmart6           DisplayString,
    hddSmart7           DisplayString,
    hddSmart8           DisplayString,
    hddSmart9           DisplayString,
    hddSmart10          DisplayString,
    hddSmart11          DisplayString,
    hddSmart12          DisplayString,
    hddSmart13          DisplayString,
    hddSmart14          DisplayString,
    hddSmart15          DisplayString,
    hddSmart16          DisplayString,
    hddSmart17          DisplayString,
    hddSmart18          DisplayString,
    hddSmart19          DisplayString,
    hddSmart20          DisplayString,
    hddSmart21          DisplayString,
    hddSmart22          DisplayString,
    hddSmart23          DisplayString,
    hddSmart24          DisplayString,
    hddSmart25          DisplayString,
    hddSmart26          DisplayString,
    hddSmart27          DisplayString,
    hddSmart28          DisplayString,
    hddSmart29          DisplayString,
    hddSmart30          DisplayString,
    hddWearLife         Integer32,
    hddReadLatency      DisplayString,
    hddWriteLatency     DisplayString
}

hddIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Hard disk drive index"
    ::= { hddEntry 1 }

hddLogChlNum OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical channel number to which hard disk drive is connected"
    ::= { hddEntry 2 }

hddPhyChlNum OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Physical channel number to which hard disk drive is connected"
    ::= { hddEntry 3 }

hddScsiId OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "SCSI ID of hard disk drive"
    ::= { hddEntry 4 }

hddScsiLun OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "SCSI Lun of hard disk drive"
    ::= { hddEntry 5 }

hddLdId OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical drive ID to which hard disk drive is belonged
                 0: If Drive Status == Spare Drive, then drive is Global Spare.
                    Otherwise, indicates drive does not currently belong to any logical drive.
                 1: If Drive Status == Global Spare Drive, then drive is Enclosure Spare drive.
                    Otherwise, indicates drive does not currently belong to any logical drive.
                 2: The drive is SSD Cache drive.
                 else: Display the Logical drive ID."
    ::= { hddEntry 6 }

hddSize OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Hard disk drive size, in Blocks"
    ::= { hddEntry 7 }

hddBlkSizeIdx OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Block Size = (Block Size Index) Power of 2"
    ::= { hddEntry 8 }

hddSpeed OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Hard disk drive data transfer speed, in ns
                 FIBRE HDD: speed (MB) = 3200 / ns
                 SATA, SAS HDD: if ns = 21, speed is 150 MB
                                if ns = 10, speed is 300 MB
                                else speed (MB) = 3200 / ns
                 IDE HDD: if ns = 96, speed is  33 MB
                          if ns = 72, speed is  44 MB
                          if ns = 48, speed is  66 MB
                          if ns = 32, speed is 100 MB
                          if ns = 24, speed is 133 MB
                          if ns = 21, speed is 150 MB
                          if ns =  0, means Auto
                          else speed (MB) = 3200 / ns
                 SCSI HDD: if ns = 250, speed is   1 MB
                           if ns =   8, speed is 160 MB
                           if ns =   9, speed is  80 MB
                           if ns =  10, speed is  40 MB
                           if ns =  11, speed is  33 MB
                           if ns =  12, speed is  20 MB
                           if ns =   0, means Async
                           else speed (MB) = 250 / ns MB
                           if wide transfer is enabled, the speed will be doubled.
    "
    ::= { hddEntry 9 }

hddDataWidth OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Hard disk drive data transfer width
                 0: Narrow 1: Wide"
    ::= { hddEntry 10 }

hddStatus OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Hard disk drive status
                 0 : New Drive
                 1 : On-Line Drive
                 2 : Used Drive
                 3 : Spare Drive
                 4 : Drive Initialization in Progress
                 5 : Drive Rebuild in Progress
                 6 : Add Drive to Logical Drive in Progress
                 9 : Global Spare Drive
                 0x11 : Drive is in process of Cloning another Drive
                 0x12 : Drive is a valid Clone of another Drive
                 0x13 : Drive is in process of Copying from another Drive
                         (for Copy/Replace LD Expansion function)
                 0x3f : Drive Absent
                 0x8x: SCSI Device (Type x)
                 0xfc : Missing Global Spare Drive
                 0xfd : Missing Spare Drive
                 0xfe : Missing Drive
                 0xff : Failed Drive"
    ::= { hddEntry 11 }

hddState OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Hard disk drive state
                 BIT 0: Drive Media Check In-Progress.
                 BITS 1 -- 6: Reserved.
                 BIT 7: Drive off-line (RW)."
    ::= { hddEntry 12 }

hddSlotNum OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Hard disk drive slot number, 0: N/A"
    ::= { hddEntry 13 }

hddResvSpace OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Hard disk drive reserved space
                 Drive Reserved Space Size (64KB units). When set,
                 the index into the 'Valid Value List' should
                 be passed in rather than the size itself.
                 This will trigger a format or unformat
                 operation (depending on the direction of the
                 change) is performed on the drive reserved
                 space. Note that it is an error to try and
                 change a the size of the reserved space
                 without unformatting first or to try and
                 unformat a drive that has been configured as
                 a LD member or as a spare/clone."
    ::= { hddEntry 14 }

hddModelStr OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Hard disk drive model string"
    ::= { hddEntry 15 }

hddFwRevStr OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Hard disk drive firmware revision string"
    ::= { hddEntry 16 }

hddSerialNum OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Hard disk drive serial number"
    ::= { hddEntry 17 }

hddReadStatistic OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Drive Read Statistic MB/S"
    ::= { hddEntry 18 }

hddWriteStatistic OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Drive Write Statistic MB/S"
    ::= { hddEntry 19 }

hddSmart1 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "For WD RE4-GP EP500M(SATA), WD RE4 EX500M(SATA).
---------                                   -------------------     ---------------------------------------------
Attribute                                   Attribute-ID Number     Pre-Failure/Advisory Bit(Status Flags bit 0)1
---------                                   -------------------     ---------------------------------------------
Read Error Rate                             1                       Pre-Failure
Spin-up Time                                3                       Pre-Failure
Start/Stop Count                            4                       Advisory
Re-allocated Sector Count                   5                       Pre-Failure
Seek Error Rate                             7                       Pre-Failure
Power-on Hours Count                        9                       Advisory
Spin-up Retry Count                         10                      Pre-Failure
Drive Calibration Retry Count               11                      Advisory
Drive Power Cycle Count                     12                      Advisory
Emergency Retract Cycles                    192                     Advisory
Load/Unload Cycles                          193                     Advisory
HDA Temperature2                            194                     Advisory
Relocation Event Count                      196                     Advisory
Current Pending Sector Count                197                     Advisory
Offline Scan Uncorrectable Sector Count     198                     Advisory
Ultra ATA CRC Error Rate                    199                     Advisory
Multi-zone Error Rate                       200                     Pre-Failure"
    ::= { hddEntry 20 }

hddSmart2 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 21 }

hddSmart3 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 22 }

hddSmart4 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 23 }

hddSmart5 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 24 }

hddSmart6 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 25 }

hddSmart7 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 26 }

hddSmart8 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 27 }

hddSmart9 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 28 }

hddSmart10 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 29 }

hddSmart11 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 30 }

hddSmart12 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 31 }

hddSmart13 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 32 }

hddSmart14 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 33 }

hddSmart15 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 34 }

hddSmart16 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 35 }

hddSmart17 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 36 }

hddSmart18 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 37 }

hddSmart19 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 38 }

hddSmart20 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 39 }

hddSmart21 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 40 }

hddSmart22 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 41 }

hddSmart23 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 42 }

hddSmart24 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 43 }

hddSmart25 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 44 }

hddSmart26 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 45 }

hddSmart27 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 46 }

hddSmart28 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 47 }

hddSmart29 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 48 }

hddSmart30 OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Same as hddSmart1"
    ::= { hddEntry 49 }

hddWearLife OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "SSD Wear Life Remaining in percentage. -1 is invalid."
    ::= { hddEntry 50 }

hddReadLatency OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Drive Read Latency (in microseconds)"
    ::= { hddEntry 51 }

hddWriteLatency OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Drive Write Latency (in microseconds)"
    ::= { hddEntry 52 }


-- ************************************************************************
--                          SCSI/Fibre Channel Table
-- ************************************************************************
chlTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF ChlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "SCSI/Fibre channel table"
    ::= { extInterface 7 }

chlEntry OBJECT-TYPE
    SYNTAX      ChlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "SCSI/Fibre channel entry"
    INDEX   { chlIndex }
    ::= { chlTable  1 }

ChlEntry ::= SEQUENCE {
    chlIndex            Integer32,
    chlLogChlNum        Integer32,
    chlPhyChlNum        Integer32,
    chlType             Integer32,
    chlChipType         Integer32,
    chlMaxSupId         Integer32,
    chlMaxSupLun        Integer32,
    chlMode             Integer32,
    chlScsiIdBitmap     Integer32,
    chlFibreIdBase      Integer32,
    chlHostIdBitmap     Integer32,
    chlDrvPid           Integer32,
    chlDrvSid           Integer32,
    chlMaxTxPeriod      Integer32,
    chlMinTxPeriod      Integer32,
    chlDefTxPeriod      Integer32,
    chlCurTxPeriod      Integer32,
    chlMaxTxWidth       Integer32,
    chlMinTxWidth       Integer32,
    chlDefTxWidth       Integer32,
    chlCurTxWidth       Integer32,
    chlMaxTagCnt        Integer32,
    chlDefTagCnt        Integer32,
    chlReadStatistic    DisplayString,
    chlWriteStatistic   DisplayString,
    chlReadRequests     DisplayString,
    chlWriteRequests    DisplayString
}

chlIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..127)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "SCSI/Fibre channel index"
    ::= { chlEntry 1 }

chlLogChlNum OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical channel number"
    ::= { chlEntry 2 }

chlPhyChlNum OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Physical channel number"
    ::= { chlEntry 3 }

chlType OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Channel type
                 0 : SCSI
                 1 : PCI
                 2 : FIBRE
                 3 : Parallel-IDE
                 4 : SATA
                 5 : SAS
                 0x11 : Network-IO - LAN
                 0x40 : Fixed Redundant Communication Channel
                 Others : not defined"
    ::= { chlEntry 4 }

chlChipType OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Channel interface chip type"
    ::= { chlEntry 5 }

chlMaxSupId OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Max supported SCSI id"
    ::= { chlEntry 6 }

chlMaxSupLun OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Max supported SCSI lun per id"
    ::= { chlEntry 7 }

chlMode OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Channel mode flags
                 BIT 0 : Channel Mode (RW)
                         0: Drive Mode, 1: Host Mode (or dedicated RCC).
                 BIT 1 : Channel SCSI Termination (SCSI Only) (RW)
                         0: Disabled, 1: Enabled.
                 BIT 2 : SCSI Parity (SCSI Only) (RW)
                         0: Disabled, 1: Enabled.
                 BIT 3 : Support LUN > 0 (RW)
                         0: Disabled, 1: Enabled.
                 BIT 8 : Supports Redundant Channel Communication (RO)
                         0: Does NOT support, 1: Does support.
                 BIT 9 : Redundant Controller Communication Enable (RW)
                         Note that if RCC is enabled on host-mode
                         channels, then the channel is a dedicated
                         RCC channel. If it is enabled on drive-mode
                         channels, then RCC transactions are multiplexed
                         with standard drive-side IOs on the channel.
                         0: Disabled, 1: Enabled.
                 BIT 12 : 'Shelf' Status Input (SCSI Only) (RW)
                         0: Disabled, 1: Enabled.
                 BIT 13 : 'Swap' Status Input (SCSI Only) (RW)
                         0: Disabled, 1: Enabled.
                 BIT 14 : 'Drive Failure' Status Output (SCSI Only) (RW)
                         0: Disabled, 1: Enabled.
                 BIT 15 : Reserved."
    ::= { chlEntry 8 }

chlScsiIdBitmap OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "SCSI host/drive channel id bitmap
                 BITS 0-15 : Primary Controller SCSI ID Bitmap
                 BITS 16-31 : Secondary Controller SCSI ID Bitmap"
    ::= { chlEntry 9 }

chlFibreIdBase OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Fibre host channel id base.
                 Note that this is ignored for SCSI channels"
    ::= { chlEntry 10 }

chlHostIdBitmap OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "SCSI/Fibre host channel id bitmap
                 BITS 0-15 : Primary Controller SCSI ID Bitmap
                 BITS 16-31 : Secondary Controller SCSI ID Bitmap"
    ::= { chlEntry 11 }

chlDrvPid OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Primary controller SCSI/Fibre drive channel id
                 Note: For Fibre Drive Channels, if == 0x7f
                       then ID will be negotiated."
    ::= { chlEntry 12 }

chlDrvSid OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Secondary controller SCSI/Fibre drive channel id
                 Note: For Fibre Drive Channels, if == 0x7f
                       then ID will be negotiated. If == 0xff,
                       then Fibre loop not initialized."
    ::= { chlEntry 13 }

chlMaxTxPeriod OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Maximum synchronized transfer period
                 (SCSI: 4 ns units, 0 : Async; Fibre: 1/32 ns units)"
    ::= { chlEntry 14 }

chlMinTxPeriod OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Minimum synchronized transfer period
                 (SCSI: 4 ns units, 0 : Async; Fibre: 1/32 ns units)"
    ::= { chlEntry 15 }

chlDefTxPeriod OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Default synchronized transfer period
                 (SCSI: 4 ns units, 0 : Async; Fibre: 1/32 ns units, 0 - Auto-negotiate)"
    ::= { chlEntry 16 }

chlCurTxPeriod OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Current synchronized transfer period
                  (SCSI: 4 ns units, 0 : Async; Fibre: 1/32 ns units)"
    ::= { chlEntry 17 }

chlMaxTxWidth OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Maximum synchronized transfer width index(SCSI only)
                (Index - 0:Narrow, 1:Wide)"
    ::= { chlEntry 18 }

chlMinTxWidth OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Minimum synchronized transfer width index(SCSI only)
                 (Index - 0:Narrow, 1:Wide)"
    ::= { chlEntry 19 }

chlDefTxWidth OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Default synchronized transfer width index(SCSI only)
                 (Index - 0:Narrow, 1:Wide)"
    ::= { chlEntry 20 }

chlCurTxWidth OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Current synchronized transfer width index(SCSI only)
                 (Index - 0:Narrow, 1:Wide)"
    ::= { chlEntry 21 }

chlMaxTagCnt OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Maximum tag count"
    ::= { chlEntry 22 }

chlDefTagCnt OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Default tag count"
    ::= { chlEntry 23 }

chlReadStatistic OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Channel Read Statistic MB/S"
    ::= { chlEntry 24 }

chlWriteStatistic OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Channel Write Statistic MB/S"
    ::= { chlEntry 25 }

chlReadRequests OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Channel Read Request Req/S"
    ::= { chlEntry 26 }

chlWriteRequests OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Channel Write Request Req/S"
    ::= { chlEntry 27 }


-- ************************************************************************
--                          Logical Unit Table
-- ************************************************************************
luTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF LuEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Logical unit table"
    ::= { extInterface 8 }

luEntry OBJECT-TYPE
    SYNTAX      LuEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Logical unit entry"
    INDEX       { luIndex }
    ::= { luTable  1 }

LuEntry ::= SEQUENCE {
    luIndex         Integer32,
    luDescriptor    Integer32,
    luClassCode     Integer32,
    luTypeCode      Integer32,
    luVendorID      DisplayString,
    luIDString      DisplayString,
    luHWRev         DisplayString,
    luSWRev         DisplayString,
    luChlNum        Integer32,
    luIDNum         Integer32
}

luIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..127)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Logical unit index"
    ::= { luEntry 1 }

luDescriptor OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit descriptor"
    ::= { luEntry 2 }

luClassCode OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit class code
                 1 - Enclosure Unit: Manages Power supplies, fans, temperature, UPS', and
                     drive slot states.
                 2 - 0xfff: Reserved.
                 0x1000 - 0xffff: Vendor specific."
    ::= { luEntry 3 }

luTypeCode OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit type code
                 1 - SAF-TE Management Device.
                 2 - Controller Peripheral Device.
                 3 - Customable I2C Peripheral Device.
                 4 - SES Management Device.
                 5 - 0xfff: Reserved.
                 0x1000 - 0xffff: Vendor specific."
    ::= { luEntry 4 }

luVendorID OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit vendor ID string"
    ::= { luEntry 5 }

luIDString OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit ID string"
    ::= { luEntry 6 }

luHWRev OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit hardware revision string"
    ::= { luEntry 7 }

luSWRev OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit software revision string"
    ::= { luEntry 8 }

luChlNum OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Channel number to which logical unit is connected"
    ::= { luEntry 9 }

luIDNum OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "ID number for logical unit"
    ::= { luEntry 10 }


-- ************************************************************************
--                          Logical Unit Device Table
-- ************************************************************************
luDevTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF LuDevEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Logical unit device table"
    ::= { extInterface 9 }

luDevEntry OBJECT-TYPE
    SYNTAX      LuDevEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Logical unit device entry"
    INDEX   { luDevTabIdx }
    ::= { luDevTable  1 }

LuDevEntry ::= SEQUENCE {
    luDevTabIdx             Integer32,
    luDeviceDescriptor      Integer32,
    luDeviceClassCode       Integer32,
    luDeviceTypeCode        Integer32,
    luDevDescriptor         Integer32,
    luDevTypeCode           Integer32,
    luDevIndex              Integer32,
    luDevDescription        DisplayString,
    luDevValue              Integer32,
    luDevValueUnit          Integer32,
    luDevChlNum             Integer32,
    luDevIDNum              Integer32,
    luDevStatus             Integer32
}

luDevTabIdx OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Logical unit device table index"
    ::= { luDevEntry 1 }

luDeviceDescriptor OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit descriptor"
    ::= { luDevEntry 2 }

luDeviceClassCode OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit class code
                 1 - Enclosure Unit: Manages Power supplies, fans, temperature, UPS', and
                     drive slot states.
                 2 - 0xfff: Reserved.
                 0x1000 - 0xffff: Vendor specific."
    ::= { luDevEntry 3 }

luDeviceTypeCode OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit type code
                 1 - SAF-TE Management Device.
                 2 - Controller Peripheral Device.
                 3 - Customable I2C Peripheral Device.
                 4 - SES Management Device.
                 5 - 0xfff: Reserved.
                 0x1000 - 0xffff: Vendor specific."
    ::= { luDevEntry 4 }

luDevDescriptor OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit device descriptor"
    ::= { luDevEntry 5 }

luDevTypeCode OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit device type code
                 Enclosure Unit Device Types:
                     1 - Power supply.
                     2 - Fan.
                     3 - Temperature sensor.
                     4 - UPS.
                     5 - Voltage sensors.
                     6 - Current sensors.
                     8 - Temperature Out-of-Range Flags.
                     9 - Door.
                     0xa - Speaker.
                     0xb - Battery-backup battery.
                     0xc - LED.
                     0xd - Cache-Data-Backup Flash Device
                     0xe - Host Board.
                     0xf - Midplane/Backplane
                     0x11 - Slot states.
                     0x12 - Enclosure Drawer.
                     0x1f - Enclosure Management Services Controller"
    ::= { luDevEntry 6 }

luDevIndex OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit device index
                 Can be used to identify device position.
                 If == 0, Index Not Available. For SAF-TE Logical
                 Units, this index corresponds to one greater than
                 the index of the device as presented in the
                 'Read Enclosure Status' data."
    ::= { luDevEntry 7 }

luDevDescription OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit device description string"
    ::= { luDevEntry 8 }

luDevValue OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit device value
                 Fan (RPM):
                    if luDevValue>0 and luDevValueUnit>0, the readable value = luDevValue
                    else if luDevValueUnit == 0
                        luDevValue = 0, means the FAN is in `Normal`
                        luDevValue = 1, means the FAN is in `Lowest speed`
                        luDevValue = 2, means the FAN is in `Second lowest speed`
                        luDevValue = 3, means the FAN is in `Third lowest speed`
                        luDevValue = 4, means the FAN is in `Intermediate speed`
                        luDevValue = 5, means the FAN is in `Third highest speed`
                        luDevValue = 6, means the FAN is in `Second highest speed`
                        luDevValue = 7, means the FAN is in `Highest speed`
                 Voltage Sensor: the readable value = luDevValue * luDevValueUnit / 1000
                 Current Sensor: the readable value = luDevValue * luDevValueUnit / 1000
                 Temperature Sensor: the readable value = (luDevValue * luDevValueUnit / 1000) - 273"
    ::= { luDevEntry 9 }

luDevValueUnit OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit device value unit
                 Fan: in RPM
                 Voltage Sensor: in Millivolt
                 Current Sensor: in Milliamps
                 Temperature Sensor: in 1/1000 Degreeds C(Absolut value)"
    ::= { luDevEntry 10 }

luDevChlNum OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit device(device slot) channel number"
    ::= { luDevEntry 11 }

luDevIDNum OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit device(device slot) id number"
    ::= { luDevEntry 12 }

luDevStatus OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit device status flags
                 Power Supply:
                     BIT 0 - CLEAR:  Power supply functioning normally.
                             SET:    Power supply malfunctioning.
                     BITS 1-5 - Reserved (Set to 0).
                     BIT 6 - CLEAR:  Power supply is ON.
                             SET:    Power supply is OFF.
                     BIT 7 - CLEAR:  Power supply IS present.
                             SET:    Power supply is NOT present.
                     == 0xff - Status unknown.
                 Fan:
                     BIT 0 - CLEAR:  Fan functioning normally.
                             SET:    Fan malfunctioning.
                     BITS 1-5 - Reserved (Set to 0).
                     BIT 6 - CLEAR:  Fan is ON.
                             SET:    Fan is OFF.
                     BIT 7 - CLEAR:  Fan IS present.
                             SET:    Fan is NOT present.
                     == 0xff - Status unknown.
                 Temperature Sensor:
                     BIT 0 - CLEAR:  Temp. sensor functioning normally.
                             SET:    Temp. sensor malfunctioning.
                     BIT 1 - 3: If == 0, Temp. within safe range.
                                If == 2, Cold Temp. Warning.
                                If == 3, Hot Temp. Warning.
                                If == 4, Cold Temp. Limit Exceeded.
                                If == 5, Hot Temp. Limit Exceeded.
                     BIT 6 - CLEAR:  Temp. Sensor is Activated.
                             SET:    Temp. Sensor is NOT Activated.
                     BIT 7 - CLEAR:  Temperature sensor IS present.
                             SET:    Temperature sensor is NOT present.
                     == 0xff - Status unknown.
                 UPS:
                     BIT 0 - CLEAR:  Unit functioning normally.
                             SET:    Unit malfunctioning.
                     BIT 1 - CLEAR:  AC Power present.
                             SET:    AC Power NOT present.
                     BIT 2 - 3: If == 0, battery fully charged.
                                If == 1, battery not fully charged.
                                If == 2, battery charge critically low.
                                If == 3, battery completely drained.
                     BITS 4-5 - Reserved (Set to 0).
                     BIT 6 - CLEAR:  UPS is ON.
                             SET:    UPS is OFF.
                     BIT 7 - CLEAR:  UPS IS present.
                             SET:    UPS is NOT present.
                     == 0xff - Status unknown.
                 Battery:
                     BIT 0 - CLEAR:  Battery functioning normally.
                             SET:    Battery malfunctioning.
                     BIT 1 - CLEAR:  Battery charging OFF (or trickle).
                             SET:    Battery charging ON.
                     BIT 2 - 3: If == 0, battery fully charged.
                                If == 1, battery not fully charged.
                                If == 2, battery charge critically low.
                                If == 3, battery completely drained.
                     BITS 4-5 - Reserved (Set to 0).
                     BIT 6 - CLEAR:  Battery-backup is enabled.
                             SET:    Battery-backup is disabled.
                     BIT 7 - CLEAR:  Battery IS present.
                             SET:    Battery is NOT present.
                     == 0xff - Status unknown.
                 Cache-Data-Backup Flash Device:
                     BIT 0 - CLEAR:  Flash Device functioning normally.
                             SET:    Flash Device malfunctioning.
                     BITS 1-5 - Reserved (Set to 0).
                     BIT 6 - CLEAR:  Flash Device is enabled.
                             SET:    Flash Device is disabled.
                     BIT 7 - CLEAR:  Flash Device IS present.
                             SET:    Flash Device is NOT present.
                     == 0xff - Status unknown.
                 Voltage Sensor:
                     BIT 0 - CLEAR:  Voltage sensor functioning normally.
                             SET:    Voltage sensor malfunctioning.
                     BIT 1 - 3: Current Voltage Sensor State:
                                If == 0, Voltage within acceptable range.
                                If == 2, Low Voltage Warning.
                                If == 3, High Voltage Warning.
                                If == 4, Low Voltage Limit Exceeded.
                                If == 5, High Voltage Limit Exceeded.
                     BIT 4 - 5: Reserved.
                     BIT 6 - CLEAR:  Voltage Sensor is Activated.
                             SET:    Voltage Sensor is NOT Activated.
                     BIT 7 - CLEAR:  Voltage sensor IS present.
                             SET:    Voltage sensor is NOT present.
                     == 0xff - Voltage Sensor Status unknown.
                 Current Sensor:
                     BIT 0 - CLEAR:  Current sensor functioning normally.
                             SET:    Current sensor malfunctioning.
                     BIT 1 - 3: Current Current Sensor State:
                                 If == 0, Current within acceptable range.
                                 If == 3, Over Current Warning.
                                 If == 5, Over Current Limit Exceeded.
                     BIT 4 - 5: Reserved.
                     BIT 6 - CLEAR:  Current Sensor is Activated.
                             SET:    Current Sensor is NOT Activated.
                     BIT 7 - CLEAR:  Current sensor IS present.
                             SET:    Current sensor is NOT present.
                     == 0xff - Current Sensor Status unknown.
                 Door:
                     BIT 0 - CLEAR:  Door OK.
                             SET:    Door, door lock, or door sensor malfunctioning.
                     BIT 1 - CLEAR:  Door is shut.
                             SET:    Door is open.
                     BITS 2-5 - Reserved (Set to 0).
                     BIT 6 - CLEAR:  Door lock engaged.
                             SET:    Door lock NOT engaged.
                     BIT 7 - CLEAR:  Door IS present.
                             SET:    Door is NOT present.
                     == 0xff - Status unknown.
                 Speaker:
                     BIT 0 - CLEAR:  Speaker functioning normally.
                             SET:    Speaker malfunctioning.
                     BITS 1-5 - Reserved (Set to 0).
                     BIT 6 - CLEAR:  Speaker is ON.
                             SET:    Speaker is OFF.
                     BIT 7 - CLEAR:  Speaker IS present.
                             SET:    Speaker is NOT present.
                     == 0xff - Status unknown.
                 Slot:
                     BIT 0 - CLEAR:  Slot sense circuitry functioning normally.
                             SET:    Slot sense circuitry malfunctioning.
                     BIT 1 - CLEAR:  Device in slot has not been marked
                                     'needing replacement' or a replacement
                                     drive has been inserted.
                             SET:    Device in slot has been marked BAD
                                     and is awaiting replacement.
                     BIT 2 - CLEAR:  Slot is activated so that drive can be accessed.
                             SET:    Slot NOT activated.
                     BITS 3-5 - Reserved (Set to 0).
                     BIT 6 - CLEAR:  Slot is NOT ready for insertion/removal.
                             SET:    Slot is ready for insertion/removal.
                     BIT 7 - CLEAR:  Device inserted in slot.
                             SET:    Slot is empty.
                     == 0xff - Status unknown.
                 LED:
                     BITS 0-5 - Reserved (Set to 0).
                     BIT 6 - CLEAR:  LED is active
                             SET:    LED is inactive
                     BIT 7 - CLEAR:  LED IS present.
                             SET:    LED is NOT present.
                     == 0xff - Status unknown.
                 Midplane/Backplane Status:
                     BITS 0-7 - Reserved (Set to 0).
                     == 0xff - Status unknown.
                 Host Board Status:
                     BITS 0-6 - Reserved (Set to 0).
                     BIT 7 - CLEAR:  Host Board IS present.
                             SET:    Host Board is NOT present.
                     == 0xff - Status unknown.
                 Enclosure Drawer Status:
                     BIT 0 - CLEAR:  Enclosre Drawer functioning normally.
                             SET:    Enclosre Drawer malfunctioning.
                     BITS 1-5 - Reserved (Set to 0).
                     BIT 6 - CLEAR:  Enclosure Drawer is closed.
                             SET:    Enclosure Drawer is opened.
                     BIT 7 - CLEAR:  Enclosure Drawer IS present.
                             SET:    Enclosure Drawer is NOT present.
                     == 0xff - Status unknown.
                 Enclosure Management Services Controller Status:
                     BIT 0 - CLEAR:  Enclosre Drawer functioning normally.
                             SET:    Enclosre Drawer malfunctioning.
                     BITS 1-5 - Reserved (Set to 0).
                     BIT 6 - CLEAR:  Enclosure Drawer is closed.
                             SET:    Enclosure Drawer is opened.
                     BIT 7 - CLEAR:  Enclosure Drawer IS present.
                             SET:    Enclosure Drawer is NOT present.
                     == 0xff - Status unknown."
    ::= { luDevEntry 13 }


-- ************************************************************************
--                          Extended LUN Table
-- ************************************************************************
extLunTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF ExtLunEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Extended LUN table"
    ::= { extInterface 10 }

extLunEntry OBJECT-TYPE
    SYNTAX      ExtLunEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Extended LUN entry"
    INDEX   { extLunIndex }
    ::= { extLunTable  1 }

ExtLunEntry ::= SEQUENCE {
    extLunIndex         Integer32,
    extLunGroupName     DisplayString,
    extLunHostIDWWN     DisplayString,
    extLunHostIDMask    DisplayString,
    extLunFilterType    DisplayString,
    extLunAccessMode    DisplayString,
    extLunChl           Integer32,
    extLunID            Integer32,
    extLunNum           Integer32,
    extLunLdLvID        DisplayString,
    extLunPartIdx       Integer32,
    extLunSsSiID        DisplayString
}

extLunIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Extended LUN index"
    ::= { extLunEntry 1 }

extLunGroupName OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Name of this extended LUN definition"
    ::= { extLunEntry 2 }

extLunHostIDWWN OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Host-ID/WWN of this exteded LUN definition"
    ::= { extLunEntry 3 }

extLunHostIDMask OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Host-ID/WWN mask of this extended LUN definition"
    ::= { extLunEntry 10 }

extLunFilterType OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Filtering type of this extended LUN definition"
    ::= { extLunEntry 11 }

extLunAccessMode OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Access mode of this extended LUN definition"
    ::= { extLunEntry 12 }

extLunChl OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Physical channel number"
    ::= { extLunEntry 4 }

extLunID OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Host-side SCSI ID"
    ::= { extLunEntry 5 }

extLunNum OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Host-side SCSI LUN"
    ::= { extLunEntry 6 }

extLunLdLvID OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical drive/Logical volume identifier"
    ::= { extLunEntry 7 }

extLunPartIdx OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Partition index of logical drive or logical volume 1"
    ::= { extLunEntry 8 }

extLunSsSiID OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Snapshot Set/Snapshot Image identifier"
    ::= { extLunEntry 9 }

-- ************************************************************************
--                            All Event Table
-- ************************************************************************
allEvtTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AllEvtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "All event table"
    ::= { eventLog 1 }

allEvtEntry OBJECT-TYPE
    SYNTAX      AllEvtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "All event entry"
    INDEX       { evtTableIdx }
    ::= { allEvtTable  1 }

AllEvtEntry ::= SEQUENCE {
    evtTableIdx Integer32,
    evtSource   DisplayString,
    evtSeverity DisplayString,
    evtIndex    Integer32,
    evtType     DisplayString,
    evtCode     DisplayString,
    evtTime     DisplayString
}

evtTableIdx OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Event table index"
    ::= { allEvtEntry 1 }

evtSource OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event source"
    ::= { allEvtEntry 2 }

evtSeverity OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event severity"
    ::= { allEvtEntry 3 }

evtIndex OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event index"
    ::= { allEvtEntry 4 }

evtType OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event type"
    ::= { allEvtEntry 5 }

evtCode OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event code"
    ::= { allEvtEntry 6 }

evtTime OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event time"
    ::= { allEvtEntry 7 }

-- ************************************************************************
--                      Controller Event Table
-- ************************************************************************
ctlrEvtTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF CtlrEvtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Controller event table"
    ::= { eventLog 2 }

ctlrEvtEntry OBJECT-TYPE
    SYNTAX      CtlrEvtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Controller event entry"
    INDEX       { cevtTableIdx }
    ::= { ctlrEvtTable  1 }

CtlrEvtEntry ::= SEQUENCE {
    cevtTableIdx    Integer32,
    cevtSource      DisplayString,
    cevtSeverity    DisplayString,
    cevtIndex       Integer32,
    cevtType        DisplayString,
    cevtCode        DisplayString,
    cevtTime        DisplayString
}

cevtTableIdx OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Event table index"
    ::= { ctlrEvtEntry 1 }

cevtSource OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event source"
    ::= { ctlrEvtEntry 2 }

cevtSeverity OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event severity"
    ::= { ctlrEvtEntry 3 }

cevtIndex OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event index"
    ::= { ctlrEvtEntry 4 }

cevtType OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event type"
    ::= { ctlrEvtEntry 5 }

cevtCode OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event code"
    ::= { ctlrEvtEntry 6 }

cevtTime OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event time"
    ::= { ctlrEvtEntry 7 }

-- ************************************************************************
--                      Drive Interface Event Table
-- ************************************************************************
drvEvtTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF DrvEvtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Drive interface event table"
    ::= { eventLog 3 }

drvEvtEntry OBJECT-TYPE
    SYNTAX      DrvEvtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Drive event entry"
    INDEX       { evtTableIdx }
    ::= { drvEvtTable  1 }

DrvEvtEntry ::= SEQUENCE {
    devtTableIdx    Integer32,
    devtSource      DisplayString,
    devtSeverity    DisplayString,
    devtIndex       Integer32,
    devtType        DisplayString,
    devtCode        DisplayString,
    evtLdID         DisplayString,
    evtLogChl       Integer32,
    evtID           Integer32,
    evtLun          Integer32,
    devtTime        DisplayString
}

devtTableIdx OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Event table index"
    ::= { drvEvtEntry 1 }

devtSource OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event source"
    ::= { drvEvtEntry 2 }

devtSeverity OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event severity"
    ::= { drvEvtEntry 3 }

devtIndex OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event index"
    ::= { drvEvtEntry 4 }

devtType OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event type"
    ::= { drvEvtEntry 5 }

devtCode OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event code"
    ::= { drvEvtEntry 6 }

evtLdID OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical drive ID"
    ::= { drvEvtEntry 7 }

evtLogChl OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical channel number"
    ::= { drvEvtEntry 8 }

evtID OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Node SCSI ID"
    ::= { drvEvtEntry 9 }

evtLun OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "SCSI LUN"
    ::= { drvEvtEntry 10 }

devtTime OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event time"
    ::= { drvEvtEntry 11 }

-- ************************************************************************
--                      Host Interface Event Table
-- ************************************************************************
hostEvtTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF HostEvtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Host interface event table"
    ::= { eventLog 4 }

hostEvtEntry OBJECT-TYPE
    SYNTAX      HostEvtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Host interface event entry"
    INDEX       { evtTableIdx }
    ::= { hostEvtTable  1 }

HostEvtEntry ::= SEQUENCE {
    hevtTableIdx    Integer32,
    hevtSource      DisplayString,
    hevtSeverity    DisplayString,
    hevtIndex       Integer32,
    hevtType        DisplayString,
    hevtCode        DisplayString,
    evtChl          Integer32,
    hevtID          Integer32,
    hevtLun         Integer32,
    hevtTime        DisplayString
}

hevtTableIdx OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Event table index"
    ::= { hostEvtEntry 1 }

hevtSource OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event source"
    ::= { hostEvtEntry 2 }

hevtSeverity OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event severity"
    ::= { hostEvtEntry 3 }

hevtIndex OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event index"
    ::= { hostEvtEntry 4 }

hevtType OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event type"
    ::= { hostEvtEntry 5 }

hevtCode OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event code"
    ::= { hostEvtEntry 6 }

evtChl OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Host SCSI channel number"
    ::= { hostEvtEntry 7 }

hevtID OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Host SCSI ID"
    ::= { hostEvtEntry 8 }

hevtLun OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Host SCSI LUN"
    ::= { hostEvtEntry 9 }

hevtTime OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event time"
    ::= { hostEvtEntry 10 }

-- ************************************************************************
--                      Logical Drive Event Table
-- ************************************************************************
ldEvtTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF LdEvtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Logical drive event table"
    ::= { eventLog 5 }

ldEvtEntry OBJECT-TYPE
    SYNTAX      LdEvtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Logical drive event entry"
    INDEX   { evtTableIdx }
    ::= { ldEvtTable  1 }

LdEvtEntry ::= SEQUENCE {
    ldevtTableIdx   Integer32,
    ldevtSource     DisplayString,
    ldevtSeverity   DisplayString,
    ldevtIndex      Integer32,
    ldevtType       DisplayString,
    ldevtCode       DisplayString,
    ldevtLdID       DisplayString,
    evtFailedChl    Integer32,
    evtFailedID     Integer32,
    evtFailedLun    Integer32,
    ldevtTime       DisplayString
}

ldevtTableIdx OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Event table index"
    ::= { ldEvtEntry 1 }

ldevtSource OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event source"
    ::= { ldEvtEntry 2 }

ldevtSeverity OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event severity"
    ::= { ldEvtEntry 3 }

ldevtIndex OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event index"
    ::= { ldEvtEntry 4 }

ldevtType OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event type"
    ::= { ldEvtEntry 5 }

ldevtCode OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event code"
    ::= { ldEvtEntry 6 }

ldevtLdID OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical drive ID"
    ::= { ldEvtEntry 7 }

evtFailedChl OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical channel number of the failed hard drive"
    ::= { ldEvtEntry 8 }

evtFailedID OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "SCSI ID of the failed hard drive"
    ::= { ldEvtEntry 9 }

evtFailedLun OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "SCSI LUN of the failed hard drive"
    ::= { ldEvtEntry 10 }

ldevtTime OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event time"
    ::= { ldEvtEntry 11 }

-- ************************************************************************
--                      Generalized Target Event Table
-- ************************************************************************
gtEvtTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF GtEvtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Generalized target event table"
    ::= { eventLog 6 }

gtEvtEntry OBJECT-TYPE
    SYNTAX      GtEvtEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Generalized target event entry"
    INDEX       { evtTableIdx }
    ::= { gtEvtTable  1 }

GtEvtEntry ::= SEQUENCE {
    gtevtTableIdx   Integer32,
    gtevtSource     DisplayString,
    gtevtSeverity   DisplayString,
    gtevtIndex      Integer32,
    gtevtType       DisplayString,
    evtLuDesc       Integer32,
    evtLuDevDesc    Integer32,
    evtLuClass      Integer32,
    evtLuSubClass   Integer32,
    evtLuDevType    DisplayString,
    gtevtCode       DisplayString,
    evtLuDevIdx     Integer32,
    evtEncChl       Integer32,
    evtEncID        Integer32,
    evtEncLun       Integer32,
    gtevtTime       DisplayString
}

gtevtTableIdx OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION "Event table index"
    ::= { gtEvtEntry 1 }

gtevtSource OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event source"
    ::= { gtEvtEntry 2 }

gtevtSeverity OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event severity"
    ::= { gtEvtEntry 3 }

gtevtIndex OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event index"
    ::= { gtEvtEntry 4 }

gtevtType OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event type"
    ::= { gtEvtEntry 5 }

evtLuDesc OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit descriptor"
    ::= { gtEvtEntry 6 }

evtLuDevDesc OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit device descriptor"
    ::= { gtEvtEntry 7 }

evtLuClass OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit class code"
    ::= { gtEvtEntry 8 }

evtLuSubClass OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit subclass code"
    ::= { gtEvtEntry 9 }

gtevtCode OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event code"
    ::= { gtEvtEntry 10 }

evtLuDevType OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit device type"
    ::= { gtEvtEntry 11 }

evtLuDevIdx OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Logical unit device index"
    ::= { gtEvtEntry 12 }

evtEncChl OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Enclosure(SAF-TE/SES) unit SCSI channel number"
    ::= { gtEvtEntry 13 }

evtEncID OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Enclosure(SAF-TE/SES) unit SCSI ID"
    ::= { gtEvtEntry 14 }

evtEncLun OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Enclosure(SAF-TE/SES) unit SCSI LUN"
    ::= { gtEvtEntry 15 }

gtevtTime OBJECT-TYPE
    SYNTAX      DisplayString
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION "Event time"
    ::= { gtEvtEntry 16 }

-- ************************************************************************
--                      SNMP Trap Table
-- ************************************************************************
event		OBJECT IDENTIFIER ::= { raid 8000 }
eventString OBJECT-TYPE
	SYNTAX	DisplayString
	ACCESS	not-accessible
	STATUS	mandatory
	DESCRIPTION
		"event message"
	::= { raid 8001 }

media-scan-for-disk-drive-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 690945

media-scan-for-logical-drive-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 690946

media-scan-for-logical-drive-member-drive-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 690947

media-scan-for-disk-drive-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 560129

media-scan-for-logical-drive-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 691202

media-scan-for-logical-drvie-member-drive-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 691203

media-scan-for-logical-drive-stopped TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 691204

unable-to-start-media-scan-Previous-task-is-still-in-progress TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 691205

media-scan-for-disk-drvie-scan-aborted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 526097

media-scan-for-logical-drive-aborted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 657154

unable-to-start-media-scan-Status-is-invalid-for-media-scan TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 657156

media-scan-for-logical-drive-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 657157

no-spare-drive-for-recovering-the-detected-unrecoverable-error TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 657158

unrecovered-media-error-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 657159

media-scan-for-disk-drive-stopped TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 526088

media-scan-for-logical-drive-member-drive-stopped-Scan-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 657161

drive-media-error-has-been-detected-with-info TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529154

drive-media-error-has-been-detected-with-LBA TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529153

license-key-consistency-check-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 860417

license-key-is-not-supported-by-the-installed-firmware TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 860418

drive-error-has-been-recovered-with-info TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 530690

drive-error-has-been-recovered-with-LBA TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 530689

logical-volume-cache-data-purged TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 722945

logical-drive-cache-data-purged TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 657410

logical-volume-status-back-to-normal TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 738561

logical-volume-degraded TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 738562

logical-volume-failed-fatal-fail TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 738563

logical-volume-failed-invalid-array TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 738564

logical-volume-member-drive-missing-incomplete-array TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 738565

logical-volume-member-drive-missing-missing-drives TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 738566

logical-drive-status-back-to-normal TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 673031

logical-drive-degraded TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 673032

logical-drive-had-fatal-failure TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 673033

logical-drive-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 673034

logical-drive-member-drive-missing-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 673035

logical-drive-member-drive-missing TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 673036

drive-missing TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655617

drive-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655618

drive-removed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655619

a-second-or-third-LD-member-drive-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655620

the-first-LD-member-drive-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655621

logical-drive-member-drive-removed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655622

scsi-parity-or-CRC-error TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 525825

unexpected-select-timeout TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 524545

gross-phase-or-signal-error-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 524802

scsi-channel-failed-detected-with-channel-ID TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 605954

host-channel-failed-with-ID TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 605955

scsi-channel-failed-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 605956

host-channel-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 605957

drive-IO-timeout TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 525569

data-overrun-or-underrun TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 526081

invalid-status-or-sense-data-received-with-info TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 526338

invalid-status-or-sense-data-received TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 526337

drive-not-ready-detected-with-info TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 528642

drive-not-ready-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 528641

unit-attention-received-with-info TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529410

unit-attention-received TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529409

drive-hardware-error-detected-with-info TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 528898

drive-hardware-error-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 528897

drive-command-aborted-with-info TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 530434

drive-command-aborted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 530433

unexpected-sense-data-received-with-info TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529666

unexpected-sense-data-received TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529665

bad-block-reassigned-with-LBA TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 530177

failed-to-reassign-the-bad-block-with-info TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529922

failed-to-reassign-the-bad-block TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529921

power-supply-voltage-3-3V-is-lower-than-lower-threshold TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 205057

power-supply-voltage-5V-is-lower-than-lower-threshold TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 205058

power-supply-voltage-12V-is-lower-than-lower-threshold TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 205059

power-supply-voltage-3-3V-is-higher-than-upper-threshold TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 205060

power-supply-voltage-5V-is-higher-than-upper-threshold TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 205061

power-supply-voltage-12V-is-higher-than-upper-threshold TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 205062

enclosure-power-supply-sensor-detection-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 205063

expansion-enclosure-power-supply-absent TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 205073

expansion-enclosure-power-supply-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 205086

enclosure-fan-sensor-detection-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 336385

expansion-enclosure-fan-absent TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 336403

fan-in-JBOD-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 336406

cpu-low-temperature-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467713

cpu-high-temperature-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467714

controller-ASIC-low-temperature-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467715

controller-ASIC-high-temperature-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467716

controller-drive-channel-IO-chip-low-temperature-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467717

controller-drive-channel-IO-chip-high-temperature-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467718

controller-host-IO-chip-low-temperature-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467719

controller-host-IO-chip-high-temperature-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467720

enclosure-backplane-temperature-sensor-detection-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467721

enclosure-fan-back-to-normal TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 369156

expansion-enclosure-fan-back-to-on-line TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 369169

cpu-temperature-back-to-normal-from-low-temperature TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 500481

cpu-temperature-back-to-normal-from-high-temperature TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 500482

controller-ASIC-temperature-back-to-normal TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 500484

controller-drive-channel-IO-chip-temperature-back-to-normal TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 500486

controller-host-board-IO-chip-temperature-back-to-normal TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 500488

enclosure-backplane-temperature-sensor-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 500483

expansion-enclosure-backplane-temperature-back-to-normal TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 500496

enclosure-backplane-temperature-back-to-normal TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 500497

enclosure-power-supply-sensor-back-to-normal TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 237832

expansion-enclosure-power-supply-back-to-on-line TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 237841

redundant-controller-failure-or-shutdown-was-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 69889

redundant-controller-has-shut-down TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 69890

controller-had-a-hardware-error-and-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 69891

redundant-controller-firmware-updated TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 69892

controller-memory-error-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 66564

logical-volume-rebuild-resumed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 754433

logical-drive-rebuild-resumed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 688898

logical-volume-rebuild-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 754435

logical-drive-rebuild-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 688900

logical-volume-migration-resumed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 755969

logical-drive-RAID-migration-resumed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 690434

logical-volume-add-drive-action-resumed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 755971

logical-drive-add-drive-action-resumed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 690436

logical-volume-migration-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 755973

logical-drive-RAID-migration-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 690438

logical-volume-add-drive-action-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 755975

logical-drive-add-drive-action-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 690440

logical-volume-migration-paused TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 756225

logical-drive-RAID-migration-paused TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 690690

logical-volume-add-drive-action-paused TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 756227

logical-drive-add-drive-action-paused TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 690692

logical-volume-migration-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 756229

logical-drive-RAID-migration-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 690694

logical-volume-add-drive-action-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 756231

logical-drive-add-drive-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 690696

logical-volume-online-expansion-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 755457

logical-drive-online-expansion-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 689922

logical-volume-offline-expansion-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 755459

logical-drive-offline-expansion-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 689924

logical-volume-online-expansion-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 755713

logical-drive-online-expansion-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 690178

logical-drive-expansion-aborted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 656641

logical-drive-expansion-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 656642

logical-volume-offline-expansion-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 755715

logical-drive-offline-expansion-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 690180

logical-volume-rebuild-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 754689

logical-drive-rebuild-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 689154

logical-drive-rebuild-aborted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 656129

logical-drive-rebuild-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 656130

logical-volume-parity-regeneration-resumed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 754945

logical-drive-parity-regeneration-resumed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 689410

logical-volume-parity-regeneration-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 754947

logical-drive-parity-regeneration-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 689412

logical-volume-parity-regeneration-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 755201

logical-drive-parity-regeneration-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 689666

logical-drive-parity-regeneration-aborted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 656385

logical-drive-parity-regeneration-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 656386

logical-volume-online-initialization-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 753921

logical-drive-online-initialization-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 688386

logical-volume-offline-initialization-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 753923

logical-drive-offline-initialization-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 688388

logical-volume-online-initialization-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 754177

logical-drive-online-initialization-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 688642

logical-volume-offline-initialization-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 754179

logical-drive-offline-initialization-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 688644

logical-drive-initialization-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655873

logical-volume-creation-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 753925

logical-drive-creation-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 688390

logical-volume-creation-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 754181

logical-drive-created TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 688646

logical-drive-creation-aborted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655874

logical-drive-creation-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655875

logical-drive-error-detected-Bad-block-count-exceeded TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 672001

logical-drive-error-detected-Bad-block-table-corrupted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 672002

logical-drive-error-detected-Online-init-table-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 672003

logical-drive-bad-data-block-detected-and-marked TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 672257

unprotected-block-on-the-logical-drive-detected-and-marked TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 672258

logical-drive-bad-data-block-recovered TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 672259

logical-drive-bad-data-block-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 672260

drive-media-error-has-been-recovered TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 541441

unrecovered-drive-media-error-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 541442

drive-scanned TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 573697

exiled-drive-detected-with-ch-ID TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 573698

unsupported-drive-detected-drive-type-or-license-is-invalid TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 573699

unsupported-drive-detected-incorrect-bundle-ID TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 573700

unsupported-drive-detected-unsupported-bundle-code TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 573701

drive-detection-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 573703

controller-NVRAM-factory-default-settings-restored TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 100609

controller-NVRAM-restore-from-file-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 100610

controller-NVRAM-restore-from-drive-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 100611

unable-to-start-drive-error-recovery-procedure TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 541186

drive-SMART-error-state-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 541187

drive-error-recovery-procedure-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 541188

drive-error-recovery-procedure-stopped TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 541189

drive-clone-resumed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 696577

drive-clone-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 696578

drive-copy-and-replace-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 696833

drive-clone-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 696834

drive-clone-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 659713

drive-clone-aborted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 659714

controller-initialization-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 98561

controller-slot-B-booted-as-primary-controller TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 98562

redundant-path-error-detected-with-channel-ID TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 605959

redundant-path-error-detected-with-channel-and-target-ID TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 605961

fibre-Channel-loop-connection-restored TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 605962

ch-redundant-path-error-recovered-with-ch TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 605963

ch-redundant-path-error-recovered-with-ch-ID TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 605964

ch-iD-redundant-path-error-recovered-with-ch-ID TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 605965

logical-drive-inconsistent-parity-block-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 672769

logical-drive-inconsistent-parity-block-recovered TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 672770

logical-drive-media-error-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 672771

logical-drive-media-error-recovered TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 672772

controller-battery-backup-unit-BBU-back-to-present TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 304385

controller-battery-backup-unit-BBU-back-to-on-line TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 304386

controller-battery-backup-unit-BBU-fully-charged TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 304387

controller-flash-backup-module-FBM-absent TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 271618

flash-Backup-Module-FBM-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 271619

battery-Backup-Unit-BBU-is-missing TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 271620

battery-Backup-Unit-BBU-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 271621

controller-battery-backup-unit-BBU-charging TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 271622

battery-Backup-Unit-BBU-error-is-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 271623

the-controller-write-policy-was-forced-to-write-through-mode TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 70145

the-controller-write-policy-default-setting-was-restored TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 102913

controller-shutdown-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 102914

controller-shutdown-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 102915

invalid-or-conflicting-enclosure-ID-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 135425

enclosure-drive-configuration-error-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 135681

expansion-enclosure-drive-configuration-error-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 135682

cache-data-present-during-system-power-on TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 100865

logical-volume-status-changed-from-online-to-offline TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 738817

logical-volume-status-changed-from-offline-to-online TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 738818

all-member-drives-of-a-logical-volume-removed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 738819

all-member-drives-of-a-logical-volume-restored TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 738820

logical-volume-undeleted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 738821

logical-drive-status-changed-from-online-to-offline TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 673286

logical-drive-status-changed-from-offline-to-online TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 673287

all-member-drives-of-logical-drive-removed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 673288

all-member-drives-of-logical-drive-restored TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 673289

logical-drive-undeleted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 673290

firmware-synchronization-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 98817

firmware-synchronization-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 98818

host-channel-disconnected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 622849

host-channel-connected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 622850

host-channel-speed-has-backed-to-speed-in-Gb-warning TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 622851

host-channel-speed-has-backed-to-speed-in-Mb-warning TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 622852

host-channel-speed-has-changed-to-speed-in-Gb TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 622853

host-channel-speed-has-changed-to-speed-in-Mb TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 622854

trunking-configuration-error-detected-in-Slot-B TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 595201

trunking-configuration-error-detected-in-Slot-A TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 595202

expansion-enclosure-is-not-supported TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 135937

enclosure-drawer-is-opened TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 131329

expansion-enclosure-drawer-is-opened TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 131330

enclosure-drawer-is-closed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 164097

expansion-enclosure-drawer-is-closed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 164098

psu-voltage-3-3V-is-back-to-normal-and-below-upper-threshold TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 237826

psu-voltage-5V-is-back-to-normal-and-below-upper-threshold TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 237827

psu-voltage-12V-is-back-to-normal-and-below-upper-threshold TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 237828

psu-voltage-3-3V-is-back-to-normal-and-above-lower-threshold TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 237829

psu-voltage-5V-is-back-to-normal-and-above-lower-threshold TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 237830

psu-voltage-12V-is-back-to-normal-and-above-lower-threshold TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 237831

enclosure-drawer-back-is-to-normal TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 164099

expansion-enclosure-drawer-is-back-to-normal TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 164100

enclosure-drawer-is-not-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 131331

expansion-enclosure-drawer-is-not-ready-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 131332

power-supply-in-JBOD-failed-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 205064

power-supply-in-storage-system-failed-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 205065

expansion-enclosure-power-supply-is-absent TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 205068

power-supply-in-storage-system-is-missing TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 205069

expansion-enclosure-PSU-failed-status-recovered-to-on-line TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 237837

enclosure-PSU-failed-status-recovered-to-normal TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 237838

expansion-enclosure-PSU-absent-status-recovered-to-present TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 237839

enclosure-PSU-absent-status-recovered-to-normal TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 237840

expansion-enclosure-fan-failed-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 336394

enclosure-fan-failed-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 336395

expansion-enclosure-fan-absent-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 336396

enclosure-fan-absent-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 336397

expansion-enclosure-fan-low-speed-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 336398

enclosure-fan-low-speed-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 336400

enclosure-drawer-fan-failed-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 336459

enclosure-drawer-fan-absent-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 336461

enclosure-drawer-fan-low-speed-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 336464

expansion-enclosure-fan-backed-to-on-line TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 369163

enclosure-fan-is-back-to-on-line TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 369164

expansion-enclosure-fan-backed-to-present TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 369165

enclosure-fan-is-back-to-present TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 369166

expansion-enclosure-fan-backed-to-normal-speed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 369167

enclosure-fan-is-back-to-normal-speed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 369168

expansion-enclosure-drawer-fan-is-back-to-on-line TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 369227

enclosure-drawer-fan-is-back-to-on-line TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 369228

expansion-enclosure-drawer-fan-is-back-to-present TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 369229

enclosure-drawer-fan-is-back-to-present TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 369230

expansion-enclosure-drawer-fan-is-back-to-normal-RPM TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 369231

enclosure-drawer-fan-speed-is-back-to-normal TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 369232

expansion-enclosure-backplane-low-temperature-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467734

expansion-enclosure-backplane-high-temperature-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467735

enclosure-backplane-high-temperature-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467737

expansion-enclosure-backplane-temperature-is-back-to-normal TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 500490

enclosure-backplane-temperature-is-back-to-normal TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 500491

expansion-enclosure-backplane-temperature-is-back-normal-state TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 500494

enclosure-backplane-temperature-is-back-to-normal-state TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 500495

ipv4-address-conflict-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 595457

ipv6-address-conflict-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 595458

a-non-supported-host-board-has-been-installed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 67841

controller-Super-Capacitor-is-back-to-present TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 304388

controller-Super-Capacitor-is-back-to-on-line TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 304389

controller-Super-Capacitor-has-been-fully-charged TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 304390

super-capacitor-is-missing TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 271624

super-capacitor-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 271625

controller-Super-Capacitor-is-charging TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 271626

controller-Super-Capacitor-error-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 271627

drive-lifetime-estimate-warning-threshold-exceeded TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 541457

host-channel-speed-backed-to-speed-in-Gb-info TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 622851

host-channel-speed-backed-to-speed-in-Mb-info TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 622852

ups-connection-failure-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 402464

ups-AC-power-failure-was-detected-The-device-entered-safe-mode TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 402465

ups-Low-Battery-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 402466

ups-Low-Battery-has-been-detected-Please-shut-down-immediately TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 402467

ups-connection-has-been-restored TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 435212

ups-AC-power-was-restored-The-device-has-exited-safe-mode TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 435213

ups-Battery-Level-Restored-to-Safety TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 435214

controller-host-IO-chip-high-temperature-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467749

controller-host-board-IO-chip-temperature-is-back-to-normal TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 500499

mismatched-SFP-installation-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 597505

inconsistent-board-ID-between-the-controllers-has-been-found TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 67585

inconsistent-board-rev-number-between-the-controllers TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 67586

invalid-hardware-settings-have-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 67587

inconsistent-HW-setting-ID-between-the-controllers TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 67588

inconsistent-host-board-1-HW-setting-ID-between-the-controllers TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 67589

inconsistent-host-board-2-HW-setting-ID-between-the-controllers TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 67590

inconsistent-DRAM-size-between-the-controllers-has-been-found TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 67591

inconsistent-NVRAM-size-between-the-controllers-has-been-found TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 67592

the-temperature-sensor-of-expansion-enclosure-is-not-supported TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467738

the-temperature-sensor-of-expansion-enclosure-is-not-installed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467739

unknown-status-of-the-temperature-sensor-of-expansion-enclosure TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467740

the-temperature-sensor-of-expansion-enclosure-is-not-available TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467741

expansion-enclosure-sensor-detected-low-temperature TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467742

expansion-enclosure-sensor-detected-high-temperature TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467743

cpu-FAN-failure-has-been-detected-with-FAN-number TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 336411

cpu-FAN-is-back-online-with-FAN-number TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 369170

memory-ECC-single-bit-error-has-been-corrected-in-DIMM-module TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 66567

cpu-low-temperature-has-been-detected-with-CPU-number TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467745

cpu-high-temperature-has-been-detected-with-CPU-number TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467746

cpu-temperature-is-back-to-normal-from-low-temperature TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 500513

cpu-temperature-is-back-to-normal-from-high-temperature TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 500514

io-module-low-temperature-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467750

io-module-high-temperature-has-been-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 467751

io-module-temperature-is-back-to-normal-from-low-temperature TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 500500

io-module-temperature-is-back-to-normal-from-high-temperature TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 500501

inconsistent-hostboard-3-HW-setting-ID-between-the-controllers TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 67594

inconsistent-hostboard-4-HW-setting-ID-between-the-controllers TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 67595

inconsistent-hostboard-5-HW-setting-ID-between-the-controllers TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 67596

inconsistent-hostboard-6-HW-setting-ID-between-the-controllers TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 67597

inconsistent-hostboard-7-HW-setting-ID-between-the-controllers TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 67598

inconsistent-hostboard-8-HW-setting-ID-between-the-controllers TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 67599

inconsistent-hostboard-9-HW-setting-ID-between-the-controllers TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 67600

inconsistent-hostboard-10-HW-setting-ID-between-the-controllers TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 67601

the-SSDs-remaining-life-less-than-threshold-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 541458

the-device-password-has-been-reset TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 100609

media-scan-for-disk-drive-in-expansion-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 691009

media-scan-for-disk-drive-enclosure-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 691073

media-scan-for-logical-drive-member-drive-in-expansion-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 691011

media-scan-for-logical-drive-member-drive-in-enclosure-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 691075

media-scan-for-disk-drive-in-expansion-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 560193

media-scan-for-disk-drive-enclosure-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 560257

media-scan-for-logical-drvie-member-in-expansion-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 691267

media-scan-for-logical-drvie-member-in-enclosure-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 691331

media-scan-for-disk-drvie-in-expansion-scan-aborted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 526161

media-scan-for-disk-drvie-in-enclosure-scan-aborted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 526225

unrecovered-media-error-detected-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 657223

unrecovered-media-error-detected-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 657287

media-scan-for-disk-drive-in-expansion-stopped TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 526152

media-scan-for-disk-drive-in-enclosure-stopped TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 526216

media-scan-for-logical-drive-member-drive-in-expansion-stopped TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 657225

media-scan-for-logical-drive-member-drive-in-enclosure-stopped TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 657289

drive-media-error-detected-with-info-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529218

drive-media-error-detected-with-info-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529282

drive-media-error-has-been-detected-with-LBA-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529217

drive-media-error-has-been-detected-with-LBA-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529281

drive-error-has-been-recovered-with-info-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 530754

drive-error-has-been-recovered-with-info-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 530818

drive-error-has-been-recovered-with-LBA-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 530753

drive-error-has-been-recovered-with-LBA-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 530817

expansion-drive-missing TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655681

enclosure-drive-missing TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655745

expansion-drive-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655682

enclosure-drive-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655746

expansion-drive-removed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655683

enclosure-drive-removed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655747

a-second-or-third-LD-member-drive-in-expansion-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655684

a-second-or-third-LD-member-drive-in-enclosure-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655748

the-first-LD-member-drive-in-expansion-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655685

the-first-LD-member-drive-in-enclosure-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655749

logical-drive-member-drive-in-expansion-removed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655686

logical-drive-member-drive-in-enclosure-removed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 655750

scsi-parity-or-CRC-error-detected-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 525889

scsi-parity-or-CRC-error-detected-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 525953

unexpected-select-timeout-detected-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 524609

unexpected-select-timeout-detected-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 524673

gross-phase-or-signal-error-detected-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 524866

gross-phase-or-signal-error-detected-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 524930

drive-IO-timeout-detected-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 525633

drive-IO-timeout-detected-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 525697

data-overrun-or-underrun-detected-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 526145

data-overrun-or-underrun-detected-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 526209

invalid-status-or-sense-data-received-w-info-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 526402

invalid-status-or-sense-data-received-w-info-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 526466

invalid-status-or-sense-data-received-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 526401

invalid-status-or-sense-data-received-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 526465

drive-not-ready-detected-with-info-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 528706

drive-not-ready-detected-with-info-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 528770

drive-not-ready-detected-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 528705

drive-not-ready-detected-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 528769

unit-attention-received-with-info-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529474

unit-attention-received-with-info-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529538

unit-attention-received-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529473

unit-attention-received-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529537

drive-hardware-error-detected-with-info-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 528962

drive-hardware-error-detected-with-info-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529026

drive-hardware-error-detected-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 528961

drive-hardware-error-detected-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529025

drive-command-aborted-with-info-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 530498

drive-command-aborted-with-info-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 530562

drive-command-aborted-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 530497

drive-command-aborted-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 530561

unexpected-sense-data-received-with-info-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529730

unexpected-sense-data-received-with-info-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529794

unexpected-sense-data-received-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529729

unexpected-sense-data-received-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529793

bad-block-reassigned-with-LBA-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 530241

bad-block-reassigned-with-LBA-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 530305

failed-to-reassign-the-bad-block-with-info-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529986

failed-to-reassign-the-bad-block-with-info-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 530050

failed-to-reassign-the-bad-block-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 529985

failed-to-reassign-the-bad-block-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 530049

drive-media-error-has-been-recovered-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 541505

drive-media-error-has-been-recovered-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 541569

unrecovered-drive-media-error-detected-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 541506

unrecovered-drive-media-error-detected-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 541570

drive-scanned-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 573761

drive-scanned-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 573825

exiled-drive-detected-with-ch-ID-in-expansion TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 573762

exiled-drive-detected-with-ch-ID-in-enclosure TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 573826

unsupported-drive-detected-in-expansion TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 573763

unsupported-drive-detected-in-enclosure TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 573827

incorrect-bundle-ID-detected-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 573764

incorrect-bundle-ID-detected-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 573828

unsupported-bundle-code-detected-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 573765

unsupported-bundle-code-detected-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 573829

drive-detection-failed-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 573767

drive-detection-failed-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 573831

drive-SMART-error-state-has-been-detected-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 541251

drive-SMART-error-state-has-been-detected-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 541315

drive-error-recovery-procedure-started-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 541252

drive-error-recovery-procedure-started-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 541316

drive-error-recovery-procedure-stopped-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 541253

drive-error-recovery-procedure-stopped-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 541317

drive-clone-resumed-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 696641

drive-clone-resumed-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 696705

drive-clone-started-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 696642

drive-clone-started-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 696706

drive-copy-and-replace-completed-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 696897

drive-copy-and-replace-completed-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 696961

drive-clone-completed-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 696898

drive-clone-completed-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 696962

drive-clone-failed-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 659777

drive-clone-failed-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 659841

drive-clone-aborted-on-expansion-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 659778

drive-clone-aborted-on-enclosure-drive TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 659842

the-secondary-controller-is-incompatible TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 69633

the-memory-size-of-the-secondary-controller-is-inconsistent TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 69634

the-secondary-controller-with-cache-data-is-not-for-the-device TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 69635

the-firmware-of-the-secondary-controller-is-incompatible TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 69636

cache-memory-range-of-the-secondary-controller-is-incompatible TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 69637

the-fatal-failed-LD-contains-unsaved-write-cache-data TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 69633

the-secondary-controller-is-waiting-for-write-cache-recovery TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 69634

pool-created TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 917505

pool-creation-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 917506

pool-expansion-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 917507

pool-expansion-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 917508

pool-deleted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 917509

pool-status-changed-to-online TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1245192

pool-status-changed-to-offline TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1245193

pool-migration-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 917514

pool-migration-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 917515

pool-migration-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 917516

volume-created TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 983053

volume-creation-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 983054

volume-deleted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 983055

volume-deletion-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 983056

snapshot-image-has-been-activated TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1376273

volume-expansion-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 983058

non-optimal-configuration-may-impact-system-performance TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1507347

snapshot-image-purge-triggered TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1048596

pair-created TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179669

pair-creation-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179670

pair-deleted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179671

synchronous-replication-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179672

synchronous-replication-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179673

synchronous-replication-paused TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179674

synchronous-replication-resumed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179675

synchronous-replication-pair-split TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179676

synchronous-pair-split-because-network-timeout TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179677

asynchronous-replication-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179678

asynchronous-replication-paused TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179679

asynchronous-replication-resumed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179680

asynchronous-replication-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179681

asynchronous-replication-pair-split TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179682

replication-pair-role-switched TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179683

remote-drive-has-connected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1114148

remote-drive-has-disconnected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1114149

logical-volume-created TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 720936

logical-volume-creation-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 720937

logical-volume-expansion-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 720938

logical-volume-expansion-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 720939

logical-volume-deleted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 720940

partition-created TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 786477

partition-creation-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 786478

partition-deleted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 786479

partition-deletion-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 786480

partition-has-been-activated TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 786481

free-space-might-be-insufficient-for-future-volume-usage TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1310770

free-space-might-be-insufficient-for-future-snapshot-usage TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1048627

free-space-might-be-insufficient-for-future-replication-pair TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179700

free-space-recovered-for-future-volume-usage TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1310773

free-space-recovered-for-future-snapshot-usage TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1048630

free-space-recovered-for-future-replication-pair-usage TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179703

insufficient-free-space-for-data-allocation TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1441848

free-space-recovered-for-data-allocation TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1441849

pair-synchronization-aborted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179706

pair-synchronization-failed-and-split TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179707

snapshot-image-created TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1048577

snapshot-image-creation-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1048578

snapshot-image-deleted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1048579

snapshot-image-deletion-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1048580

pool-space-utilization-exceeded-the-threshold-info TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1245190

pool-space-utilization-exceeded-the-threshold-warning TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1245190

pool-space-utilization-exceeded-the-threshold-error TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1245190

pool-space-utilization-exceeded-the-threshold-critical TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1245190

pool-space-utilization-has-dropped-below-threshold-info TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1245191

pool-space-utilization-has-dropped-below-threshold-warning TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1245191

pool-space-utilization-has-dropped-below-threshold-error TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1245191

pool-space-utilization-has-dropped-below-threshold-critical TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1245191

the-pool-allocated-space-has-exceeded-the-threshold-info TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1245222

the-pool-allocated-space-has-exceeded-the-threshold-warning TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1245222

the-pool-allocated-space-has-exceeded-the-threshold-error TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1245222

the-pool-allocated-space-has-exceeded-the-threshold-critical TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1245222

the-pool-allocated-space-has-dropped-below-threshold-info TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1245223

the-pool-allocated-space-has-dropped-below-threshold-warning TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1245223

the-pool-allocated-space-has-dropped-below-threshold-error TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1245223

the-pool-allocated-space-has-dropped-below-threshold-critical TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1245223

the-pool-has-been-foreced-offline-because-error-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1245244

initial-copy-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179709

failed-to-start-the-Initial-copy TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179710

initial-copy-has-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179711

initial-copy-has-continued TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179712

initial-copy-has-been-stopped TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179713

initial-copy-has-been-resumed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179714

failed-to-resume-the-initial-copy TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179715

pair-recovered TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179716

pair-broken TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179717

pair-synchronization-has-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179718

failed-to-start-the-pair-synchronization TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179719

pair-synchronization-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179720

pair-synchronization-has-continued TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179721

pair-synchronization-has-been-stopped TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179722

pair-synchronization-has-been-resumed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179723

failed-to-resume-the-pair-synchronization TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179724

target-volume-full TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179725

pair-synchronization-is-in-progress TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179726

tier-migration-has-been-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1310799

tier-migration-has-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1310800

tier-migration-has-been-aborted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1310801

volume-expansion-has-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1310802

volume-expansion-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1310803

bad-block-found-in-source-volume-pair-synchronization-aborted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1179732

bad-block-found-in-deleted-ME-migration-aborted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 917589

the-snapshot-images-has-been-backed-up-to-cloud TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1048704

the-pool-cannot-connect-to-cloud-because-the-network-error TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 917632

the-pool-cannot-connect-to-cloud-because-authentication-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 917633

the-pool-cannot-connect-to-cloud-because-the-bucket-not-exist TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 917634

the-pool-cannot-connect-to-cloud-because-failed-create-bucket TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 917635

the-pool-cannot-connect-to-cloud-The-bucket-has-been-used TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 917636

the-pool-cannot-connect-to-cloud-Wrong-encryption-key TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 917637

the-pool-has-failed-to-upload-data-to-cloud TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 917638

the-pool-cannot-connect-to-cloud-No-channel-for-iSCSI-device TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 917639

the-pool-has-been-deleted-The-cloud-storage-is-not-deleted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 917640

the-data-of-the-pool-saved-in-the-bucket-is-corrupted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 917641

the-system-has-been-unable-to-satisfy-QoS-policy-for-15-min TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1310858

snapshot-license-expired TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573003

failed-to-take-snapshot-of-pair-target-volume TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573004

the-exception-of-the-snapshot-schedule-prune-rule-occurred TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573005

maximum-snapshot-amount-of-the-volume-reached TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573006

maximum-snapshot-amount-of-the-system-reached TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573007

snapshot-schedule-failed-Some-flush-agents-cannot-be-connected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573008

snapshot-schedule-failed-Host-volume-disk-can-not-be-locked TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573009

snapshot-schedule-failed-Host-cache-flush-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573010

snapshot-schedule-failed-Host-volume-disk-has-been-locked TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573011

snapshot-schedule-failed-Host-cache-data-flush-has-timed-out TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573012

snapshot-schedule-failed-Host-flush-the-database-cache-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573013

snapshot-schedule-failed-File-system-does-not-mount-the-volume TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573014

snapshot-schedule-failed-Volume-has-not-been-mapped-to-host TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573015

snapshot-schedule-failed-Exception-has-occurred TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573016

snapshot-schedule-failed-The-volume-has-not-been-mapped TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573017

the-snapshot-schedule-has-failed-to-unlock-the-host-volume-disk TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573018

the-snapshot-schedule-has-failed-to-resume-the-host-database TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573019

snapshot-schedule-failed-Flush-settings-have-not-configured TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573020

snapshot-schedule-and-backup-to-cloud-failed-Exception-occur TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573021

failed-to-execute-the-snapshot-schedule TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573022

snapshot-schedule-failed-The-device-can-not-be-connected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573023

the-storage-tiering-license-of-the-device-has-expired TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573024

tier-migration-schedule-failed-Previous-process-is-processing TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573025

tier-migration-schedule-failed-Specified-volumes-are-not-found TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573026

tier-migration-schedule-rejected-The-storage-has-one-tier TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573027

tier-migration-schedule-failed-Exception-has-occurred TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573028

volume-replication-schedule-failed-Target-volume-mapped TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573029

the-volume-mirror-license-of-the-device-has-expired TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573030

volume-mirror-schedule-failed-Exceptions-have-occurred TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573031

volume-mirror-schedule-failed-Exception-has-occurred TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573032

volume-mirror-schedule-failed-The-source-volume-is-not-mapped TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573033

volume-mirror-schedule-failed-Flush-agents-not-connected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573034

the-volume-copy-license-of-the-device-has-expired TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573035

volume-copy-schedule-failed-Exception-has-occurred TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1573036

the-SMTP-server-has-not-been-configured-yet TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1507501

user-s-password-has-been-changed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= **********

user-s-password-has-expired TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= **********

the-password-policy-has-been-enabled TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= **********

the-settings-of-the-password-policy-have-been-changed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= **********

the-password-policy-has-been-disabled TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520069

the-service-status-abnormal-has-beendetected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520070

the-service-status-has-returned-to-normal TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520071

abnormal-status-service-will-be-reactivated-in-a-few-minutes TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520072

the-service-has-been-reactivated-successfully TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520073

failed-to-reactivated-the-abnormal-service TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520074

a-user-has-been-created TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= **********

a-user-has-been-assigned-to-the-specific-groups TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= **********

the-superuser-privilege-has-been-assigned-to-a-specific-user TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= **********

the-superuser-privilege-for-a-specific-user-has-been-stopped TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= **********

a-user-account-has-been-deleted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= **********

a-user-group-has-been-created TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= **********

a-user-group-has-been-deleted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= **********

a-user-group-added-users TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= **********

a-user-group-removed-users TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= **********

a-service-has-been-started-successfully TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= **********

a-service-has-been-restarted-successfully TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= **********

a-service-has-been-stopped-successfully TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= **********

the-configuration-of-a-service-has-been-applied-successfully TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520087

failed-to-start-a-data-service TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520088

failed-to-start-an-authentication-service TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520089

failed-to-restart-a-data-service TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520090

failed-to-restart-an-authentication-service TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520091

failed-to-stop-a-data-service TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520092

failed-to-stop-an-authentication-service TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520093

failed-to-set-the-configuration-of-a-data-service TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520094

failed-to-set-the-configuration-of-an-authentication-service TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520095

a-folder-has-been-added-into-share-configuration TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520096

a-folder-has-been-removed-from-share-configuration TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520097

the-share-configuration-of-a-folder-has-been-applied TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520098

failed-to-add-a-folder-into-share-configuration TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520099

failed-to-remove-a-folder-from-share-configuration TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520100

failed-to-apply-the-share-configuration-of-a-folder TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520101

remote-replication-test-failed-The-target-folder-is-invalid TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520102

remote-replication-test-failed-No-response-from-remote-host TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520103

remote-replication-test-failed-Username-or-password-is-invalid TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520104

a-remote-replication-task-has-been-deleted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520105

failed-to-delete-a-remote-replication-task TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520106

the-backup-operation-of-a-remote-repllication-task-has-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520107

failed-to-activate-the-backup-of-a-remote-replication-task TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520108

a-remote-replication-task-has-stopped TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520109

failed-to-stop-a-remote-replication-task TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520110

the-restoration-of-a-remote-replication-task-has-begun TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520111

failed-to-restore-from-source-to-target-of-a-replication-task TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520112

the-restoration-of-a-remote-replication-task-has-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520113

failed-to-restore-from-target-to-source-of-a-replication-task TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520114

a-remote-replication-task-has-been-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520115

failed-to-replicate-from-source-to-target-of-a-replication-task TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520116

the-target-folder-of-a-replication-has-insufficient-capacity TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520117

remote-replication-task-failed-to-start-Netowrk-timeoout TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520118

a-remote-replication-task-has-been-created TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520119

failed-to-create-remote-replication-task TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520120

a-schedule-has-been-created TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520121

failed-to-create-a-schedule TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520122

a-schedule-has-been-enabled TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520123

failed-to-enable-a-schedule TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520124

a-schedule-has-been-disabled TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520125

failed-to-disabled-a-schedule TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520126

a-schedule-has-been-deleted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520127

failed-to-delete-a-schedule TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520128

a-instance-of-a-schedule-task-is-still-running TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520129

a-drive-has-been-inserted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520130

a-drive-has-been-unplugged TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520131

a-file-system-has-been-created TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520132

failed-to-created-a-file-system TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520133

a-file-system-has-been-deleted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520134

failed-to-delete-a-file-system TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520135

a-folder-has-been-created-in-the-file-system TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520136

failed-to-a-folder-in-the-file-system TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520137

a-folder-has-been-deleted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520138

failed-to-delete-a-folder TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520139

the-system-enter-single-controller-mode TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520140

a-controller-has-been-booted-completely TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520141

a-controller-can-t-be-detected-System-will-reboot-to-recovery TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520142

the-configuration-broken-by-power-outage-has-been-recovered TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520143

detected-controller-failure-Ffailover-process-will-be-launched TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520144

the-controller-failover-process-has-been-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520145

failed-controller-recovered-Failback-process-will-be-launched TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520146

the-controller-failback-process-has-been-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520147

a-controller-has-been-unplugged TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520148

a-controller-has-been-inserted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520149

network-connection-of-a-controller-has-been-restored TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520150

netwrok-connection-of-a-controller-is-disconnected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520151

a-interface-of-a-controller-from-aggregation-group-restored TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520152

a-interface-of-a-controller-from-aggregation-group-is-abnormal TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520153

the-address-mode-of-an-interface-has-been-changed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520154

the-MTU-size-of-a-interface-has-been-changed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520155

a-DNS-server-has-been-added-to-the-server-list TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520156

a-DNS-server-has-been-removed-from-the-server-list TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520157

a-DNS-suffix-has-been-added TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520158

a-DNS-suffix-has-been-removed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520159

a-port-aggregation-group-has-been-created TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520160

a-port-aggregation-group-has-been-removed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520161

route-rule-has-been-added-successfully TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520162

route-rule-has-been-removed-successfully TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520163

failed-to-set-IP-configuration-on-an-interface TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520164

failed-to-set-MTU-size-on-an-interface TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520165

failed-to-add-a-DNS-server TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520166

failed-to-remove-a-DNS-server TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520167

failed-to-add-a-DNS-suffix TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520168

failed-to-remove-a-DNS-suffix TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520169

failed-to-create-a-port-aggregation-group TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520170

failed-to-remove-a-prot-aggregation-group TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520171

failed-to-add-an-Route-rule TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520172

failed-to-remove-an-Route-rule TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520173

the-usage-of-the-coredump-folder-is-over-90-percent TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520174

the-space-used-by-a-folder-has-exceeded-monitoring-threshold TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520175

the-ipblock-configuration-has-been-applied-on-a-controller TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520176

failed-to-apply-the-ipblock-configuration-on-a-controller TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520177

a-IP-address-has-been-removed-on-a-controller TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520178

failed-to-remove-a-IP-address-from-ipblock-configuration TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520179

a-IP-address-was-lbocked-The-maximum-login-attempts-exceeded TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520180

failed-to-ban-a-IP-address-on-a-controller TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520181

detect-a-IP-address-has-been-banned-by-system-on-a-controller TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520182

a-schedule-task-has-started TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520183

failed-to-start-a-schedule-task TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520184

failed-to-restore-the-whitelist-or-blacklist TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520185

a-user-failed-to-log-in-from-a-IP-address TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520186

the-LDAP-server-failed-to-add-a-user-from-the-CSV-file TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520187

a-user-has-nearly-reached-the-quota-limit-on-a-volume TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520188

the-LDAP-server-failed-to-add-users-in-batch-mode TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520189

the-AD-or-LDAP-server-has-been-disconnected-from-a-controller TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520190

the-AD-or-LDAP-server-connection-to-controller-has-been-restored TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520191

all-volumes-have-been-deactivated TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520192

all-volumes-have-been-reactivated TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520193

a-service-port-setting-has-conflict-with-another-service TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520194

an-application-server-has-been-started-successfully TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520195

an-application-server-has-been-restarted-successfully TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520196

an-application-server-has-been-stopped-successfully TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520197

an-application-server-has-been-configured-successfully TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520198

failed-to-start-an-application-server TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520199

failed-to-restart-an-application-server TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520200

failed-to-stop-an-application-server TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520201

failed-to-configure-an-application-server TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520202

app-server-stopped-Folder-for-saving-data-can-not-be-accessed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520203

failed-to-backup-LDAP-DB-Folder-for-data-can-not-be-accessed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520204

failed-to-backup-LDAP-DB-Folder-may-assigned-to-sec-controller TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520205

syncCloud-service-started-successfully TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520206

failed-to-start-SyncCloud-service TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520207

syncCloud-service-stopped-successfully TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520208

failed-to-stop-SyncCloud-service TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520209

failed-to-fetch-SyncCloud-database-Service-has-been-disabled TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520210

a-volume-has-duplicated-name-for-file-service-cannot-be-mounted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520211

the-size-of-a-folder-has-exceeded-the-quota-alert-threshold TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520212

ipv4-address-conflict-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520213

ipv6-address-conflict-detected TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520214

failed-to-initialize-the-SyncCloud-database TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520215

the-SyncCloud-task-failed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520216

file-system-usage-exceeds-threshold TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520217

file-system-usage-exceeds-90-percent TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520218

the-file-system-of-the-volume-has-been-repaired TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520219

failed-to-repair-the-file-system-of-the-volume TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520220

failed-to-upgrade-the-LDAP-server-service TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520221

the-LDAP-server-service-has-been-upgraded TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520222

ldap-server-has-been-disabled-Folder-for-data-is-not-found TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520223

ldap-server-has-been-disabled-The-settings-are-incomplete TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520224

ldap-server-has-been-disabled-The-database-was-corrupted TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520225

failed-to-connect-to-AD-server-Incorrect-username-or-password TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520226

failed-to-connect-to-AD-server-KDC-server-is-not-found TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520227

the-AD-server-is-unreachable TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520228

not-enough-privilege-for-the-AD-user-to-join-the-domain TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520229

failed-to-connect-LDAP-server-Incorrect-username-or-password TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520230

the-LDAP-server-is-unreachable TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520232

failed-to-connect-to-the-LDAP-server-The-base-DN-is-incorrect TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520233

cloud-sync-service-started-successfully TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520234

failed-to-start-cloud-sync-service TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520235

cloud-sync-service-stopped-successfully TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520236

failed-to-stop-cloud-sync-service TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520237

failed-to-fetch-cloud-sync-database-service-has-been-disabled TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520238

failed-to-initial-the-cloud-sync-database TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520239

the-cloud-sync-task-failed-due-to-network-problems TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520240

the-antivirus-scan-job-has-been-stopped TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520241

the-antivirus-scheduled-scan-job-has-been-completed TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520242

the-NVR-server-crash-detected-Recovery-process-will-be-start TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520243

the-NVR-server-has-been-recovered TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520244

failed-to-recover-the-NVR-server TRAP-TYPE
	ENTERPRISE	event
	VARIABLES	{eventString}
	DESCRIPTION	" "
	::= 1090520245

END
