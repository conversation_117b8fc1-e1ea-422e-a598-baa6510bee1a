NETGEAR-SMART-SWITCHING-MIB DEFINITIONS ::= BEGIN

-- This SNMP Management Information Specification
-- embodies Broadcom Corporation's confidential and proprietary
-- intellectual property. Broadcom Corporation retains all title
-- and ownership in the Specification including any revisions.

-- This Specification is supplied "AS IS", Broadcom Corporation
-- makes no warranty, either expressed or implied,
-- as to the use, operation, condition, or performance of the
-- Specification.


IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, NOTIFICATION-TYPE, IpAddress,
    Integer32, Unsigned32, Counter32, <PERSON>auge32, TimeTicks
                                        FROM SNMPv2-SMI
    RowStatus,Mac<PERSON>ddress,DateAndTime    FROM SNMPv2-TC
    ng700smartswitch                    FROM NETGEAR-REF-MIB
    DisplayString,PhysAddress           FROM RFC1213-MIB
    <PERSON>,dot1qVlanIndex,dot1qFdbId,
			dot1qTpFdbAddress  FROM Q-BRIDGE-MIB
    IANAifType                          FROM IANAifType-MIB
    ifIndex                             FROM IF-MIB
    In<PERSON>Type, Inet<PERSON><PERSON>ress        FROM INET-ADDRESS-MIB;

    agentSwitching MODULE-IDENTITY
        LAST-UPDATED "200803170000Z" -- 17 Mar 2008 12:00:00 GMT
        ORGANIZATION "Netgear"
        CONTACT-INFO
         ""
        DESCRIPTION
          "The Netgear Private MIB for Switching"

        -- Revision history.
        REVISION
          "200803170000Z" -- 17 Mar 2008 12:00:00 GMT
        DESCRIPTION
          "Updated for release."

    ::= { ng700smartswitch 1 }

PortList ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Each octet within this value specifies a set of eight
        ports, with the first octet specifying ports 1 through
        8, the second octet specifying ports 9 through 16, etc.
        Within each octet, the most significant bit represents
        the lowest numbered port, and the least significant bit
        represents the highest numbered port.  Thus, each port
        of the bridge is represented by a single bit within the
        value of this object.  If that bit has a value of '1'
        then that port is included in the set of ports; the port
        is not included if its bit has a value of '0'."
    SYNTAX      OCTET STRING

VlanList ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Each octet within this value specifies a set of eight
        vlans, with the first octet specifying vlans 1 through
        8, the second octet specifying vlans 9 through 16, etc.
        Within each octet, the most significant bit represents
        the lowest numbered vlan, and the least significant bit
        represents the highest numbered vlan.  Thus, each vlan
        supported is represented by a single bit within the
        value of this object.  If that bit has a value of '1'
        then the associated port is in that vlan; the port is
        not included if the vlans bit has a value of '0'."
    SYNTAX      OCTET STRING


AgentPortMask ::= TEXTUAL-CONVENTION
   STATUS  current
   DESCRIPTION
       "Each octet within this value specifies a set of eight
        ports, with the first octet specifying ports 1 through
        8, the second octet specifying ports 9 through 16, etc.
        Within each octet, the most significant bit represents
        the lowest numbered port, and the least significant bit
        represents the highest numbered port.  Thus, each port
        of the bridge is represented by a single bit within the
        value of this object.  If that bit has a value of '1'
        then that port is included in the set of ports; the port
        is not included if its bit has a value of '0'
             
        When setting this value, the system will ignore 
        configuration for ports not between the first and last 
        valid ports.  Configuration of any port numbers between 
        this range that are not valid ports return a failure 
        message, but will still apply configuration for valid 
        ports."
   SYNTAX  OCTET STRING


   Ipv6Address ::= TEXTUAL-CONVENTION
             DISPLAY-HINT "2x:"
             STATUS       current
             DESCRIPTION
               "This data type is used to model IPv6 addresses.
                This is a binary string of 16 octets in network
                byte-order."
             SYNTAX       OCTET STRING (SIZE (16))

        Ipv6AddressPrefix ::= TEXTUAL-CONVENTION
             DISPLAY-HINT "2x:"
             STATUS       current
             DESCRIPTION
               "This data type is used to model IPv6 address
               prefixes. This is a binary string of up to 16
               octets in network byte-order."
             SYNTAX       OCTET STRING (SIZE (0..16))

        Ipv6AddressIfIdentifier ::= TEXTUAL-CONVENTION
             DISPLAY-HINT "2x:"
             STATUS       current
             DESCRIPTION
               "This data type is used to model IPv6 address
               interface identifiers. This is a binary string
                of up to 8 octets in network byte-order."
             SYNTAX      OCTET STRING (SIZE (0..8))

    Ipv6IfIndex ::= TEXTUAL-CONVENTION
             DISPLAY-HINT "d"
             STATUS       current
             DESCRIPTION
               "A unique value, greater than zero for each
               internetwork-layer interface in the managed
               system. It is recommended that values are assigned
               contiguously starting from 1. The value for each
               internetwork-layer interface must remain constant
               at least from one re-initialization of the entity's
               network management system to the next




               re-initialization."
             SYNTAX       Integer32 (1..2147483647)

        Ipv6IfIndexOrZero ::= TEXTUAL-CONVENTION
             DISPLAY-HINT "d"
             STATUS       current
             DESCRIPTION
                 "This textual convention is an extension of the
                 Ipv6IfIndex convention.  The latter defines
                 a greater than zero value used to identify an IPv6
                 interface in the managed system.  This extension
                 permits the additional value of zero.  The value
                 zero is object-specific and must therefore be
                 defined as part of the description of any object
                 which uses this syntax.  Examples of the usage of
                 zero might include situations where interface was
                 unknown, or when none or all interfaces need to be
                 referenced."
             SYNTAX       Integer32 (0..2147483647)




--**************************************************************************************
--    agentInfoGroup
--**************************************************************************************

agentInfoGroup                             OBJECT IDENTIFIER ::= { agentSwitching 1 }


    --**************************************************************************************
    -- agentInventoryGroup
    --
    --**************************************************************************************


    agentInventoryGroup        OBJECT IDENTIFIER ::= { agentInfoGroup 1 }


    agentInventorySysDescription OBJECT-TYPE
         SYNTAX      DisplayString
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The switch's Inventory system description.  "
         ::= { agentInventoryGroup 1 }

    agentInventoryMachineType OBJECT-TYPE
         SYNTAX      DisplayString
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Type of the Machine used in the Switch "
         ::= { agentInventoryGroup 2 }

    agentInventoryMachineModel OBJECT-TYPE
         SYNTAX      DisplayString
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The switch's Machine Model. "
         ::= { agentInventoryGroup 3 }

    agentInventorySerialNumber OBJECT-TYPE
         SYNTAX      DisplayString
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Serial number of the switch."
         ::= { agentInventoryGroup 4 }

    agentInventoryFRUNumber OBJECT-TYPE
         SYNTAX      DisplayString
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "FRU Number of the switch"
         ::= { agentInventoryGroup 5 }

    agentInventoryMaintenanceLevel OBJECT-TYPE
         SYNTAX      DisplayString
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The switch's Inventory Maintenance Level"
         ::= { agentInventoryGroup 6 }

    agentInventoryPartNumber OBJECT-TYPE
         SYNTAX      DisplayString
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The switch's Inventory Part Number"
         ::= { agentInventoryGroup 7 }

    agentInventoryManufacturer OBJECT-TYPE
         SYNTAX      DisplayString
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Name of the Manufacturer of this unit"
         ::= { agentInventoryGroup 8 }

    agentInventoryBurnedInMacAddress OBJECT-TYPE
         SYNTAX      PhysAddress
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Burned-In MAC Address"

         ::= { agentInventoryGroup 9 }

    agentInventoryOperatingSystem OBJECT-TYPE
         SYNTAX      DisplayString
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Operating System running on this unit"
         ::= { agentInventoryGroup 10 }

    agentInventoryNetworkProcessingDevice OBJECT-TYPE
         SYNTAX      DisplayString
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Network Processing Device for this unit"
         ::= { agentInventoryGroup 11 }

    agentInventoryAdditionalPackages OBJECT-TYPE
         SYNTAX      DisplayString
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Lists additional functional packages available on this unit."
         ::= { agentInventoryGroup 12 }

    agentInventorySoftwareVersion OBJECT-TYPE
         SYNTAX      DisplayString
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Lists the version of software loaded on this unit."
         ::= { agentInventoryGroup 13 }



    --**************************************************************************************
    -- agentTrapLogGroup
    --
    --**************************************************************************************

    agentTrapLogGroup          OBJECT IDENTIFIER ::= { agentInfoGroup 2}

    agentTrapLogTotal OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The total number of traps sent since last reset."
         ::= { agentTrapLogGroup 1 }

    agentTrapLogTotalSinceLastViewed OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The number of traps sent since last viewed."
         ::= { agentTrapLogGroup 3 }

   --**************************************************************************************
   -- agentTrapLogTable

    agentTrapLogTable OBJECT-TYPE
         SYNTAX SEQUENCE OF AgentTrapLogEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Agent Trap Log"
         ::= { agentTrapLogGroup 4 }

    agentTrapLogEntry OBJECT-TYPE
         SYNTAX      AgentTrapLogEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Agent trap log entry"
         INDEX { agentTrapLogIndex }
         ::= { agentTrapLogTable 1 }

    AgentTrapLogEntry ::= SEQUENCE {
          agentTrapLogIndex
              Integer32,
          agentTrapLogSystemTime
              DisplayString,
          agentTrapLogTrap
              DisplayString
          }

    agentTrapLogIndex OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Unique index of trap entry"
         ::= { agentTrapLogEntry 1 }

    agentTrapLogSystemTime OBJECT-TYPE
         SYNTAX      DisplayString
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "System uptime when trap was sent.
                     This entry shows how long the system has been up when the trap occurred."
         ::= { agentTrapLogEntry 2 }

    agentTrapLogTrap OBJECT-TYPE
         SYNTAX      OCTET STRING (SIZE (0..512))
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Description of the trap sent."
         ::= { agentTrapLogEntry 3 }

   --**************************************************************************************
   -- agentSupportedMibTable
   --**************************************************************************************

    agentSupportedMibTable OBJECT-TYPE
         SYNTAX SEQUENCE OF AgentSupportedMibEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Supported MIB table"
         ::= { agentInfoGroup 3 }

    agentSupportedMibEntry OBJECT-TYPE
         SYNTAX      AgentSupportedMibEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Supported MIB entry"
         INDEX { agentSupportedMibIndex }
         ::= { agentSupportedMibTable 1 }

    AgentSupportedMibEntry ::= SEQUENCE {
          agentSupportedMibIndex
              Integer32,
          agentSupportedMibName
              DisplayString,
          agentSupportedMibDescription
              DisplayString
          }

    agentSupportedMibIndex OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Unique index of the Supported MIB entry"
         ::= { agentSupportedMibEntry 1 }

    agentSupportedMibName OBJECT-TYPE
         SYNTAX      DisplayString
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The name of the MIB (RFC or IEEE) that is supported."
         ::= { agentSupportedMibEntry 2 }

    agentSupportedMibDescription OBJECT-TYPE
         SYNTAX      OCTET STRING (SIZE (0..512))
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Description of the MIB (RFC or IEEE) that is supported."
         ::= { agentSupportedMibEntry 3 }


 --**************************************************************************************
 -- agentConfigGroup
 --
 --**************************************************************************************

    agentConfigGroup                           OBJECT IDENTIFIER ::= { agentSwitching 2 }

    agentCLIConfigGroup                        OBJECT IDENTIFIER ::= { agentConfigGroup 1 }

    --**************************************************************************************
    -- agentLoginSessionTable
    --
    --**************************************************************************************

    agentLoginSessionTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentLoginSessionEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "A table of the switch's login session"
         ::= { agentCLIConfigGroup 1 }

    agentLoginSessionEntry OBJECT-TYPE
         SYNTAX      AgentLoginSessionEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Login Session Entry"
         INDEX { agentLoginSessionIndex }
         ::= { agentLoginSessionTable 1 }

    AgentLoginSessionEntry ::= SEQUENCE {
           agentLoginSessionIndex
               Integer32,
           agentLoginSessionUserName
               DisplayString,
           agentLoginSessionIPAddress
               IpAddress,
           agentLoginSessionConnectionType
               INTEGER,
           agentLoginSessionIdleTime
               TimeTicks,
           agentLoginSessionSessionTime
               TimeTicks,
           agentLoginSessionStatus
               RowStatus,
           agentLoginSessionInetAddressType
               InetAddressType,
           agentLoginSessionInetAddress
               InetAddress
       }

    agentLoginSessionIndex OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Agent Login Session Index of the switch"
         ::= { agentLoginSessionEntry 1 }

    agentLoginSessionUserName OBJECT-TYPE
         SYNTAX      DisplayString
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Agent Login Session UserName of the switch"
         ::= { agentLoginSessionEntry 2 }

    agentLoginSessionIPAddress OBJECT-TYPE
         SYNTAX      IpAddress
         MAX-ACCESS  read-only
         STATUS      obsolete
         DESCRIPTION
                     "Agent Login Session IP Address of the switch"
         ::= { agentLoginSessionEntry 3 }

    agentLoginSessionConnectionType OBJECT-TYPE
         SYNTAX      INTEGER {
                      serial(1),
                      telnet(2),
                      ssh(3),
                      http(4),
                      https(5)
                     }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Agent Login Session Connection Type of the switch"
         ::= { agentLoginSessionEntry 4 }

    agentLoginSessionIdleTime OBJECT-TYPE
         SYNTAX      TimeTicks
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Agent Login Session Idle Time of the switch"
         ::= { agentLoginSessionEntry 5 }

    agentLoginSessionSessionTime OBJECT-TYPE
         SYNTAX      TimeTicks
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Agent Login Session Time of the switch"
         ::= { agentLoginSessionEntry 6 }

    agentLoginSessionStatus OBJECT-TYPE
         SYNTAX      RowStatus
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Status of the user.
                     active(1)    - This connection is active.
                     destroy(6)   - Set to this value to disconnect this user."
         ::= { agentLoginSessionEntry 7 }

    agentLoginSessionInetAddressType OBJECT-TYPE
         SYNTAX      InetAddressType
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Type of address agentLoginSessionInetAddress is returning.
                     Can be either unknown(0), ipv4(1), or ipv6 (2).  Will return
                     unknown(0) in the case of a serial login."
         ::= { agentLoginSessionEntry 8 }

    agentLoginSessionInetAddress OBJECT-TYPE
         SYNTAX      InetAddress
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Inet Address of the connecting the user is logging in with.
                     Will return a zero-length string in the case of a serial login."
         ::= { agentLoginSessionEntry 9 }

    --**************************************************************************************
    -- agentTelnetConfigGroup
    --
    --**************************************************************************************

    agentTelnetConfigGroup                  OBJECT IDENTIFIER ::= { agentCLIConfigGroup 2 }

    agentTelnetLoginTimeout OBJECT-TYPE
         SYNTAX      Integer32 (1..160)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Telnet login timeout (minutes)

                     Config telnet timeout  will set the telnet session timeout value.
                     A session is active as long as the session has not remained idle for
                     the value set. Specify a value from 1 to 160.
                     Note: Changing the timeout value for active
                     sessions does not become effective until the session is reaccessed.
                     Any keystroke will also activate the new timeout duration."
         ::= { agentTelnetConfigGroup 1 }

    agentTelnetMaxSessions OBJECT-TYPE
         SYNTAX      Integer32 (0..5)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Maximum number of Telnet Sessions
                     Config telnet maxsessions is an integer value from 0 to 5 that specifies the
                     maximum number of telnet sessions that can be established.
                     If the value is 0, no Telnet session can be established.
                     "
         ::= { agentTelnetConfigGroup 2 }

    agentTelnetAllowNewMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Allow new telnet sessions (enable or disable)
                     Config telnet disable means that no new Telnet sessions are to be
                     established. Any already established session remains active until
                     the session is ended or an abnormal network error ends it.
                     "
          ::= { agentTelnetConfigGroup 3 }

    --**************************************************************************************
    -- agentUserConfigGroup
    --
    --**************************************************************************************

    agentUserConfigGroup                    OBJECT IDENTIFIER ::= { agentCLIConfigGroup 3 }


    agentUserConfigCreate OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(1..8))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Create a new user.
                     When set with a non-empty string, a new user with that name will be created.
                     This object will only return an empty string.
                     This string is limited to alpha-numeric strings (uncluding the '-' and '_' characters)."
         ::= { agentUserConfigGroup 1 }

    --************************************************************************************** 


    agentUserConfigTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentUserConfigEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "User Config Table"
         ::= { agentUserConfigGroup 2 }

    agentUserConfigEntry OBJECT-TYPE
         SYNTAX      AgentUserConfigEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "User Config Entry"
         INDEX { agentUserIndex }
         ::= { agentUserConfigTable 1 }

    AgentUserConfigEntry ::= SEQUENCE {
                 agentUserIndex
                     Integer32,
                 agentUserName
                     DisplayString,
                 agentUserPassword
                     DisplayString,
                 agentUserAccessMode
                     INTEGER,
                 agentUserAuthenticationType
                     INTEGER,
                 agentUserEncryptionType
                     INTEGER,
                 agentUserEncryptionPassword
                     DisplayString,
                 agentUserLockoutStatus
                     INTEGER,
                 agentUserPasswordExpireTime
                     DateAndTime
             }

    agentUserIndex OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Agent User Config Index"
         ::= { agentUserConfigEntry 1 }

    agentUserName OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(1..8))
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Agent User Name.
                     This string is limited to alpha-numeric strings (including the '-' and '_' characters)."
         ::= { agentUserConfigEntry 2 }

    agentUserPassword OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(8..64))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Agent User Password
                     This object will return an empty string even if a password is set."
         ::= { agentUserConfigEntry 3 }

    agentUserAccessMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     read(1),
                     write(2)
                  }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Agent User Access Mode"
         ::= { agentUserConfigEntry 4 }

    agentUserAuthenticationType OBJECT-TYPE
         SYNTAX      INTEGER {
                     none(1),
                     hmacmd5(2),
                     hmacsha(3)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "SNMPv3 User Authentication.  The user passsword must be set
                     to a string greater than or equal to 8 characters for this to be
                     set to anything but none(1).

                     none(1)      - no authentication used
                     hmacmd5(1)   - Use HMAC-MD5 authentication
                     hmacsha(1)   - Use HMAC-SHA authentication"
         ::= { agentUserConfigEntry 6 }

    agentUserEncryptionType OBJECT-TYPE
         SYNTAX      INTEGER {
                     none(1),
                     des(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "SNMPv3 User Encryption
                     Can not be set to des(2) if agentUserAuthenticationType is set to
                     none(1).

                     none(1) - no encryption used
                     des(2)  - DES encryption used"
         ::= { agentUserConfigEntry 7 }

    agentUserEncryptionPassword OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(8..64))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "SNMPv3 User Encryption Password
                     This object will return an empty string even if a password is
                     set. agentUserEncryptionType must be set to des(2) before this
                     object can be configured.
                     This object will return an empty string even if a password is set."
         ::= { agentUserConfigEntry 8 }

    agentUserLockoutStatus OBJECT-TYPE
         SYNTAX      INTEGER {
                     false(0),
                     true(1)
                     }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Displays whether the user account is locked due to excessive failed login attempts."
         ::= { agentUserConfigEntry 9 }

    agentUserPasswordExpireTime OBJECT-TYPE
         SYNTAX      DateAndTime
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Date and time when the user's password expires (past or present).  Only valid when
                      password aging is enabled."
         ::= { agentUserConfigEntry 10 }




    --**************************************************************************************
    -- agentSerialGroup
    --
    --**************************************************************************************

    agentSerialGroup                    OBJECT IDENTIFIER ::= { agentCLIConfigGroup 5 }

    agentSerialTimeout OBJECT-TYPE
         SYNTAX      Integer32 (0..160)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "agentSerialTimeout specifies the maximum connect time(in minutes)
                     without console activity. A value of 0 indicates that a console can
                     be connected indefinitely. The time range is 0 to 160. "
         ::= { agentSerialGroup 1 }

    agentSerialBaudrate OBJECT-TYPE
         SYNTAX      INTEGER {
                      baud-1200(1),
                      baud-2400(2),
                      baud-4800(3),
                      baud-9600(4),
                      baud-19200(5),
                      baud-38400(6),
                      baud-57600(7),
                      baud-115200(8)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     " agentSerialBaudrate specifies the current baud rate at which
                       the serial port will try to connect. The available values
                       are 1200, 2400, 4800, 9600, 19200, 38400,57600, and 115200 baud. "
         ::= { agentSerialGroup 2 }

    agentSerialCharacterSize OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "agentSerialCharacterSize  specifies the number of bits in
                      a character. The number of bits is always 8."
         ::= { agentSerialGroup 3 }

    agentSerialHWFlowControlMode OBJECT-TYPE
         SYNTAX      INTEGER {
                      enable(1),
                      disable(2)
                      }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "agentSerialHardwareFlowControl specifies whether hardware
                      flow-control is enabled or disabled. Hardware Flow Control is
                      always disabled."
         ::= { agentSerialGroup 4 }

    agentSerialStopBits OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     " agentSerialStopBits specifies the number of Stop bits per
                      character. The number of Stop bits is always 1."
         ::= { agentSerialGroup 5 }

    agentSerialParityType OBJECT-TYPE
         SYNTAX      INTEGER {
                      even(1),
                      odd(2),
                      none(3)
                      }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     " agentSerialParityType specifies the Parity Method used on
                     the Serial Port. The Parity Method is always None. "
         ::= { agentSerialGroup 6 }

    --**************************************************************************************
    -- agentPasswordManagementConfigGroup
    --
    --**************************************************************************************

    agentPasswordManagementConfigGroup                  OBJECT IDENTIFIER ::= { agentCLIConfigGroup 6 }

    agentPasswordManagementMinLength OBJECT-TYPE
         SYNTAX      Integer32 (1..20)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Minimum length for user passwords
                     All new local user passwords must be at least this many characters in length.
                     "
         ::= { agentPasswordManagementConfigGroup 1 }

    agentPasswordManagementHistory OBJECT-TYPE
         SYNTAX      Integer32 (0..10)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The number of previous passwords to store for prevention of password reuse.
                     This ensures that each user does not reuse passwords often.  A value of 0
                     indicates that no previous passwords will be stored.
                     "
         ::= { agentPasswordManagementConfigGroup 2 }

    agentPasswordManagementAging OBJECT-TYPE
         SYNTAX      Integer32 (1..365)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The maximum time that user passwords are valid, in days, from the time the
                     password is set.  Once a password expires, the user will be required to enter
                     a new password following the first login after password expiration.
                     A value of 0 indicates that passwords never expire.
                     "
         ::= { agentPasswordManagementConfigGroup 3 }

    agentPasswordManagementLockAttempts OBJECT-TYPE
         SYNTAX      Integer32 (1..5)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The number of allowable failed local authentication attempts before the user's
                     account is locked.  A value of 0 indicates that user accounts will never be locked.
                     "
         ::= { agentPasswordManagementConfigGroup 4 }

    --**************************************************************************************
    -- agentLagConfigGroup
    --
    --**************************************************************************************
    agentLagConfigGroup                 OBJECT IDENTIFIER ::= { agentConfigGroup 2 }

    agentLagConfigCreate OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(0|1..15))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Agent LAG Create.
                     When this object is set with a non-empty string, a new LAG will be created
                     if possible with the entered string as its name."
         ::= { agentLagConfigGroup 1 }

    agentLagSummaryConfigTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentLagSummaryConfigEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "A summary table of the switch's LAG config entries"
         ::= { agentLagConfigGroup 2 }

    agentLagSummaryConfigEntry OBJECT-TYPE
         SYNTAX      AgentLagSummaryConfigEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Switch's LAG config entry"
         INDEX       { agentLagSummaryLagIndex }
         ::= { agentLagSummaryConfigTable 1 }

    AgentLagSummaryConfigEntry ::= SEQUENCE {
             agentLagSummaryLagIndex
                 Integer32,
             agentLagSummaryName
                 DisplayString,
             agentLagSummaryFlushTimer
                 Integer32,
             agentLagSummaryLinkTrap
                 INTEGER,
             agentLagSummaryAdminMode
                 INTEGER,
             agentLagSummaryStpMode
                 INTEGER,
             agentLagSummaryAddPort
                 Integer32,
             agentLagSummaryDeletePort
                 Integer32,
             agentLagSummaryStatus
                 RowStatus,
             agentLagSummaryType
                 INTEGER,
             agentLagSummaryStaticCapability
                 INTEGER
             }
    agentLagSummaryLagIndex OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Agent LAG IfIndex.

                     This value corresponds with the LAG interface in the ifTable."
         ::= { agentLagSummaryConfigEntry 1 }

    agentLagSummaryName OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(1..15))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Agent LAG Name.

                     The associated name of the LAG used during creation."
         ::= { agentLagSummaryConfigEntry 2 }

    agentLagSummaryFlushTimer OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-write
         STATUS      obsolete
         DESCRIPTION
                     "Agent LAG FlushTimer."
         ::= { agentLagSummaryConfigEntry 3 }

    agentLagSummaryLinkTrap OBJECT-TYPE
         SYNTAX      INTEGER {
                      enable(1),
                      disable(2)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Agent LAG LinkTrap.

                     Configures sending Link Up/Down traps when the LAG interface goes Up or Down."
         ::= { agentLagSummaryConfigEntry 4 }

    agentLagSummaryAdminMode OBJECT-TYPE
         SYNTAX      INTEGER {
                      enable(1),
                      disable(2)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Agent LAG AdminMode.

                     Administratively enables or disables this LAG interface."
         ::= { agentLagSummaryConfigEntry 5 }

    agentLagSummaryStpMode OBJECT-TYPE
         SYNTAX      INTEGER {
                      dot1d(1),
                      fast(2),
                      off(3),
                      dot1s(4)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Agent LAG StpMode

                     If Dot1d is enabled, the valid values are:
                     dot1d(1), fast(2), and off(3)

                     If Dot1s is enabled, the valid values are:
                     off(3) and dot1s(4)"

         ::= { agentLagSummaryConfigEntry 6 }

    agentLagSummaryAddPort OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Agent LAG AddPort.

                     Set to a non 0 value to add a port to the LAG.  Values correspond to
                     ifIndex values in the ifTable.

                     Note: agentPortType for the port to be added must be full duplex
                     and the same speed as previously added port(s), if any."
         ::= { agentLagSummaryConfigEntry 7 }

    agentLagSummaryDeletePort OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Agent LAG DeletePort.

                     Set to a non 0 value to remove a port from the LAG.  Values correspond to
                     ifIndex values in the ifTable."
         ::= { agentLagSummaryConfigEntry 8 }

    agentLagSummaryStatus OBJECT-TYPE
         SYNTAX      RowStatus
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Agent LAG Status.

                     active(1)  - This LAG is enabled.
                     destroy(6) - Set to this value to remove the LAG."
         ::= { agentLagSummaryConfigEntry 9 }

    agentLagSummaryType OBJECT-TYPE
         SYNTAX      INTEGER {
                     static(1),
                     dynamic(2)
                     }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Agent LAG Type.

                     static(1)  - This LAG is staticly maintained.
                     dynamic(2) - This LAG is dynamicly maintained."
         ::= { agentLagSummaryConfigEntry 10 }

    agentLagSummaryStaticCapability OBJECT-TYPE
         SYNTAX      INTEGER {
                      enable(1),
                      disable(2)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Agent LAG Static Capability

                     enable(1) - Static capability is enabled for this LAG interface
                     disable(2) - Static capability is disabled for this LAG interface"
         DEFVAL { disable }
         ::= { agentLagSummaryConfigEntry 11 }

    --**************************************************************************************

    agentLagDetailedConfigTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentLagDetailedConfigEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "A detailed table of the switch's LAG config entries"
         ::= { agentLagConfigGroup 3 }

    agentLagDetailedConfigEntry OBJECT-TYPE
         SYNTAX      AgentLagDetailedConfigEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Switch's LAG config entry"
         INDEX       { agentLagDetailedLagIndex, agentLagDetailedIfIndex }
         ::= { agentLagDetailedConfigTable 1 }

    AgentLagDetailedConfigEntry ::= SEQUENCE {
             agentLagDetailedLagIndex
                 Integer32,
             agentLagDetailedIfIndex
                 Integer32,
             agentLagDetailedPortSpeed
                 OBJECT IDENTIFIER,
             agentLagDetailedPortStatus
                 INTEGER
             }

    agentLagDetailedLagIndex OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "LAG index"
         ::= { agentLagDetailedConfigEntry 1 }

    agentLagDetailedIfIndex OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "LAG port index"
         ::= { agentLagDetailedConfigEntry 2 }

    agentLagDetailedPortSpeed OBJECT-TYPE
         SYNTAX      OBJECT IDENTIFIER
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "LAG port speed.  See agentPortType for a description and list
                      of valid values."
         ::= { agentLagDetailedConfigEntry 3 }

    agentLagDetailedPortStatus OBJECT-TYPE
         SYNTAX      INTEGER {
                     active(1),
                     inactive(2)
                     }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "LAG port status.

                     active(1)   - Actively participating in the LAG.
                     inactive(2) - Not participating in the LAG."
         ::= { agentLagDetailedConfigEntry 4 }

    --**************************************************************************************

    agentLagConfigStaticCapability OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                     }
         MAX-ACCESS  read-write
         STATUS      obsolete
         DESCRIPTION
                     "Agent LAG Static Capability.
                     Configures whether Static LAGs are supported on this device."
         ::= { agentLagConfigGroup 4 }

    --**************************************************************************************
    -- agentNetworkConfigGroup
    --
    --**************************************************************************************

    agentNetworkConfigGroup                 OBJECT IDENTIFIER ::= { agentConfigGroup 3 }

    agentNetworkIPAddress OBJECT-TYPE
         SYNTAX      IpAddress
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch's network ip address"
         ::= { agentNetworkConfigGroup 1 }

    agentNetworkSubnetMask OBJECT-TYPE
         SYNTAX      IpAddress
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch's network subnet mask"
         ::= { agentNetworkConfigGroup 2 }

    agentNetworkDefaultGateway OBJECT-TYPE
         SYNTAX      IpAddress
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch's network default gateway"
         ::= { agentNetworkConfigGroup 3 }

    agentNetworkBurnedInMacAddress OBJECT-TYPE
         SYNTAX      PhysAddress
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The switch's network Burned-In MAC address"
         ::= { agentNetworkConfigGroup 4 }

    agentNetworkLocalAdminMacAddress OBJECT-TYPE
         SYNTAX      PhysAddress
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch's network locally administered MAC address"
          ::= { agentNetworkConfigGroup 5 }

    agentNetworkMacAddressType OBJECT-TYPE
         SYNTAX      INTEGER {
                     burned-in(1),
                     local(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch's network Mac Address Type"
         ::= { agentNetworkConfigGroup 6 }

    agentNetworkConfigProtocol OBJECT-TYPE
         SYNTAX      INTEGER {
                     none(1),
                     bootp(2),
                     dhcp(3)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch's network config protocol"
         ::= { agentNetworkConfigGroup 7 }

    agentNetworkWebMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      obsolete
         DESCRIPTION
                     "The switch's network config protocol on next bootup"
          ::= { agentNetworkConfigGroup 8 }

    agentNetworkJavaMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      obsolete
         DESCRIPTION
                     "Configures the use of the Java interface through the Web."
         ::= { agentNetworkConfigGroup 9 }

    agentNetworkMgmtVlan OBJECT-TYPE
         SYNTAX      Integer32 (1..4094)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The VLAN ID through which the switch can be managed using the
                     agentNetworkIPAddress"
         ::= { agentNetworkConfigGroup 10 }




    --**************************************************************************************
    -- ipv6 agentNetworkPortConfigGroup
    --
    --**************************************************************************************

      agentNetworkIpv6AdminMode OBJECT-TYPE
         SYNTAX   INTEGER {
                    enabled(1),
                    disabled(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                "The network port IPv6 administrative state"
          ::= { agentNetworkConfigGroup 11 }

      agentNetworkIpv6Gateway OBJECT-TYPE
             SYNTAX      Ipv6AddressPrefix
             MAX-ACCESS  read-write
             STATUS      current
             DESCRIPTION
                     "The network port IPv6 Gateway Address"
             ::=  { agentNetworkConfigGroup 12 }

      agentNetworkIpv6AddrTable OBJECT-TYPE
             SYNTAX  SEQUENCE OF AgentNetworkIpv6AddrEntry
             MAX-ACCESS  not-accessible
             STATUS      current
             DESCRIPTION
                  "The list of IPv6 address prefixes
                   for the network port."
             ::=  { agentNetworkConfigGroup 13 }

 agentNetworkIpv6AddrEntry OBJECT-TYPE
             SYNTAX  AgentNetworkIpv6AddrEntry
             MAX-ACCESS  not-accessible
             STATUS      current
             DESCRIPTION
                 "An interface entry containing objects of
                 a particular IPv6 address prefix"
             INDEX   {
                       agentNetworkIpv6AddrPrefix
                       }
             ::= { agentNetworkIpv6AddrTable 1 }
      AgentNetworkIpv6AddrEntry ::= SEQUENCE {
        agentNetworkIpv6AddrPrefix
          Ipv6AddressPrefix,
        agentNetworkIpv6AddrPrefixLength
          INTEGER,
        agentNetworkIpv6AddrEuiFlag
          INTEGER,
        agentNetworkIpv6AddrStatus
        RowStatus
    }

      agentNetworkIpv6AddrPrefix OBJECT-TYPE
             SYNTAX      Ipv6AddressPrefix
             MAX-ACCESS  not-accessible --read-write
             STATUS      current
             DESCRIPTION
                  "The service port IPv6 Address Prefix"
             ::=  { agentNetworkIpv6AddrEntry 1 }

     agentNetworkIpv6AddrPrefixLength OBJECT-TYPE
             SYNTAX      INTEGER (0..128)
             MAX-ACCESS  read-create
             STATUS      current
             DESCRIPTION
                  "The service port IPv6 Address Prefix Length"
             ::=  { agentNetworkIpv6AddrEntry 2 }

     agentNetworkIpv6AddrEuiFlag OBJECT-TYPE
             SYNTAX      INTEGER {
                                  enabled(1),
                                  disabled(2)
                                 }
             MAX-ACCESS  read-create
             STATUS      current
             DESCRIPTION
                  "The service port IPv6 Eui Flag"
             ::=  { agentNetworkIpv6AddrEntry 3 }

      agentNetworkIpv6AddrStatus OBJECT-TYPE
             SYNTAX      RowStatus
             MAX-ACCESS  read-create
             STATUS      current
             DESCRIPTION
                          ""
              ::= { agentNetworkIpv6AddrEntry 4}




    --**************************************************************************************
    -- agentServicePortConfigGroup
    --
    --**************************************************************************************

    agentServicePortConfigGroup         OBJECT IDENTIFIER ::= { agentConfigGroup 4 }

    agentServicePortIPAddress OBJECT-TYPE
         SYNTAX      IpAddress
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch's ServicePort ip address"
         ::= { agentServicePortConfigGroup 1 }

    agentServicePortSubnetMask OBJECT-TYPE
         SYNTAX      IpAddress
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch's ServicePort subnet mask"
         ::= { agentServicePortConfigGroup 2 }

    agentServicePortDefaultGateway OBJECT-TYPE
         SYNTAX      IpAddress
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch's ServicePort default gateway"
         ::= { agentServicePortConfigGroup 3 }

    agentServicePortBurnedInMacAddress OBJECT-TYPE
         SYNTAX      PhysAddress
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The switch's ServicePort Burned-In MAC address"
         ::= { agentServicePortConfigGroup 4 }

    agentServicePortConfigProtocol OBJECT-TYPE
         SYNTAX      INTEGER {
                     none(1),
                     bootp(2),
                     dhcp(3)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch's ServicePort config protocol"
          ::= { agentServicePortConfigGroup 5 }

    --**************************************************************************************
    -- ipv6 mgmt on serviceport
    --
    --**************************************************************************************
    agentServicePortIpv6AdminMode OBJECT-TYPE
         SYNTAX   INTEGER {
                     enabled(1),
                     disabled(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                "The service port IPv6 administrative state"
          ::= { agentServicePortConfigGroup 6 }

    agentServicePortIpv6Gateway OBJECT-TYPE
             SYNTAX      Ipv6AddressPrefix
             MAX-ACCESS  read-write
             STATUS      current
             DESCRIPTION
                  "The service port IPv6 Gateway Address"
             ::=  { agentServicePortConfigGroup 7 }

    agentServicePortIpv6AddrTable OBJECT-TYPE
             SYNTAX  SEQUENCE OF AgentServicePortIpv6AddrEntry
             MAX-ACCESS  not-accessible
             STATUS      current
             DESCRIPTION
                  "The list of IPv6 address prefixes for the
                   service port."
             ::=  { agentServicePortConfigGroup 8 }

    agentServicePortIpv6AddrEntry OBJECT-TYPE
             SYNTAX  AgentServicePortIpv6AddrEntry
             MAX-ACCESS  not-accessible
             STATUS      current
             DESCRIPTION
                 "An interface entry containing objects of
                  a particular IPv6 address prefix"
             INDEX { agentServicePortIpv6AddrPrefix }
             ::= { agentServicePortIpv6AddrTable 1 }

    AgentServicePortIpv6AddrEntry ::= SEQUENCE {
        agentServicePortIpv6AddrPrefix
          Ipv6AddressPrefix,
        agentServicePortIpv6AddrPrefixLength
          INTEGER,
        agentServicePortIpv6AddrEuiFlag
          INTEGER,
        agentServicePortIpv6AddrStatus
        RowStatus
    }

      agentServicePortIpv6AddrPrefix OBJECT-TYPE
             SYNTAX      Ipv6AddressPrefix
             MAX-ACCESS  not-accessible --read-write
             STATUS      current
             DESCRIPTION
                  "The service port IPv6 Address Prefix"
             ::=  { agentServicePortIpv6AddrEntry 1 }

     agentServicePortIpv6AddrPrefixLength OBJECT-TYPE
             SYNTAX      INTEGER (0..128)
             MAX-ACCESS  read-create
             STATUS      current
             DESCRIPTION
                  "The service port IPv6 Address Prefix Length"
             ::=  { agentServicePortIpv6AddrEntry 2 }

     agentServicePortIpv6AddrEuiFlag OBJECT-TYPE
             SYNTAX      INTEGER {
                                  enabled(1),
                                  disabled(2)
                                 }
             MAX-ACCESS  read-create
             STATUS      current
             DESCRIPTION
                  "The service port IPv6 Eui Flag"
             ::=  { agentServicePortIpv6AddrEntry 3 }

      agentServicePortIpv6AddrStatus OBJECT-TYPE
             SYNTAX      RowStatus
             MAX-ACCESS  read-create
             STATUS      current
             DESCRIPTION
                          ""
              ::= { agentServicePortIpv6AddrEntry 4}



    --**************************************************************************************
    -- agentSnmpConfigGroup
    --
    --**************************************************************************************

    agentSnmpConfigGroup                OBJECT IDENTIFIER  ::=  {agentConfigGroup 6}

    agentSnmpCommunityCreate OBJECT-TYPE
         SYNTAX      DisplayString (SIZE (1..16))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Creates a new SNMP Community entry.
                     Defaults: IPAddress  0.0.0.0
                               IpMask     0.0.0.0
                               AccessMode read-only
                               Status     config"
         ::= { agentSnmpConfigGroup 1 }

    agentSnmpCommunityConfigTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentSnmpCommunityConfigEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "A table of the switch's SNMP Config entries"
         ::= { agentSnmpConfigGroup 2 }

    agentSnmpCommunityConfigEntry OBJECT-TYPE
         SYNTAX      AgentSnmpCommunityConfigEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Switch's SNMP Config entry"
         INDEX       {agentSnmpCommunityIndex }
         ::= { agentSnmpCommunityConfigTable 1 }

    AgentSnmpCommunityConfigEntry ::= SEQUENCE {
           agentSnmpCommunityIndex
               Integer32,
           agentSnmpCommunityName
               DisplayString (SIZE(1..16)),
           agentSnmpCommunityIPAddress
               IpAddress,
           agentSnmpCommunityIPMask
               IpAddress,
           agentSnmpCommunityAccessMode
               INTEGER,
           agentSnmpCommunityStatus
               INTEGER
       }

    agentSnmpCommunityIndex OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The switch's Snmp Community Index"
         ::= { agentSnmpCommunityConfigEntry 1 }

    agentSnmpCommunityName OBJECT-TYPE
         SYNTAX      DisplayString (SIZE (1..16))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch's Snmp Community Name
                     This name identifies each SNMP community;
                     the name can be up to 16 characters, and it is case-sensitive.
                     Community names in the SNMP community must be unique.
                     If you make multiple entries using the same community name,
                     the first entry is kept and processed and all duplicate entries are ignored.
                     "
         ::= { agentSnmpCommunityConfigEntry 2 }

    agentSnmpCommunityIPAddress OBJECT-TYPE
         SYNTAX      IpAddress
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch's Snmp Community IP Address
                     Client IP Address - This attribute is an IP address (or portion thereof)
                     from which this device will accept SNMP packets with the associated
                     community. The requesting entity's IP address is logical-ANDed with
                     the Client IP Mask and the result must match the Client IP Address.
                     Note: If the Client IP Mask is set
                     to 0.0.0.0, a Client IP Address of 0.0.0.0 matches all IP addresses.
                     "
         ::= { agentSnmpCommunityConfigEntry 3 }

    agentSnmpCommunityIPMask OBJECT-TYPE
         SYNTAX      IpAddress
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch's Snmp Community IP Mask
                     Client IP Mask - This attribute is a mask to be logical-ANDed with the
                     requesting entity's IP address before comparison with the Client IP Address.
                     If the result matches with Client IP Address then the address is an
                     authenticated IP address. For example, if the Client IP Address
                     is ********** and the corresponding Client IP Mask is *************,
                     a range of incoming IP addresses would match, that is, the incoming IP
                     addresses could be a value in the following range: ********** to ************.
                     To have a specific IP address be the only authenticated IP address, set the
                     Client IP Address to the required IP address and set the Client IP Mask
                     to ***************.
                     "
         ::= { agentSnmpCommunityConfigEntry 4 }

    agentSnmpCommunityAccessMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     read-only(1),
                     read-write(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch's Snmp Community Access Mode
                     Access Mode - This value can be read-only or read/write.
                     A community with a read-only access allows for switch information to be
                     displayed. A community with a read/write access allows for configuration
                     changes to be made and for information to be displayed.
                     "
         ::= { agentSnmpCommunityConfigEntry 5 }

    agentSnmpCommunityStatus OBJECT-TYPE
         SYNTAX      INTEGER {
                     active(1),
                     notInService(2),
                     config(3),
                     destroy(4)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch's Snmp Community Status.

                     active(1)        - This community is active, allowing SNMP managers associated
                                        with this community to manage the switch according to its
                                        access right.

                     notInService(2)  - This community is not active; no SNMP requests using this
                                        community will be accepted. In this case the SNMP manager
                                        associated with this community cannot manage the switch until
                                        the Status is changed back to active(1).

                     config(3)        - The community Status must be set to this value in order to
                                        configure it.  When creating a new community entry, initial
                                        Status will be set to this value.

                     destroy(4)       - Set to this value to remove the community from the agent."
         ::= { agentSnmpCommunityConfigEntry 6 }


    --**************************************************************************************
    -- agentSnmpTrapReceiverConfigTable
    --
    --**************************************************************************************

    agentSnmpTrapReceiverCreate OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(1..16))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Creates a new trap receiver entry.
                     Defaults: IPAddress 0.0.0.0
                               status    config"
         ::= { agentSnmpConfigGroup 3 }

    agentSnmpTrapReceiverConfigTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentSnmpTrapReceiverConfigEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Trap messages are sent across a network to an SNMP Network Manager.
                     These messages alert the manager to events occurring within the switch
                     or on the network. Up to six simultaneous trap receivers are supported.
                     "
         ::= { agentSnmpConfigGroup 4 }

    agentSnmpTrapReceiverConfigEntry OBJECT-TYPE
         SYNTAX      AgentSnmpTrapReceiverConfigEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Switch's Snmp Trace Receiver Config entry"
         INDEX       { agentSnmpTrapReceiverIndex }
         ::= { agentSnmpTrapReceiverConfigTable 1 }

    AgentSnmpTrapReceiverConfigEntry ::= SEQUENCE {
           agentSnmpTrapReceiverIndex
               Integer32,
           agentSnmpTrapReceiverCommunityName
               DisplayString (SIZE(1..16)),
           agentSnmpTrapReceiverIPAddress
               IpAddress,
           agentSnmpTrapReceiverStatus
               INTEGER,
           agentSnmpTrapReceiverVersion
               INTEGER,
           agentSnmpTrapReceiverIpv6Address
                Ipv6AddressPrefix
       }

    agentSnmpTrapReceiverIndex OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The switch's Snmp Trap Receiver Index"
         ::= { agentSnmpTrapReceiverConfigEntry 1 }

    agentSnmpTrapReceiverCommunityName OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(1..16))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch's Snmp Trap Receiver Community Name.
                     This is the SNMP community name of the remote network manager;
                     the name can be up to 16 characters, and is case-sensitive.
                     "
         ::= { agentSnmpTrapReceiverConfigEntry 2 }

    agentSnmpTrapReceiverIPAddress OBJECT-TYPE
         SYNTAX      IpAddress
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "IP Address - Each IP address parameter is four integer numbers.
                     The numbers range from 0 to 255.
                     "
         ::= { agentSnmpTrapReceiverConfigEntry 3 }

    agentSnmpTrapReceiverStatus OBJECT-TYPE
         SYNTAX      INTEGER {
                     active(1),
                     notInService(2),
                     config(3),
                     destroy(4)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch's Snmp Trap Reciever Status.
                     active(1)        - This trap receiver is active, allowing SNMP Traps to
                                        be sent to this receiver.

                     notInService(2)  - This trap reciever is not active; no SNMP Traps will
                                        be sent to this reciever until it's set back to active(1).


                     config(3)        - The trap reciever Status must be set to this value in order
                                        to configure it.  When creating a new trap receiver entry,
                                        the Status will initially be set to this value.
                                        Note: agentSnmpTrapReceiverIPAddress must be set to non-zero
                                        before changing to active(1) or notInService(2).

                     destroy(4)       - Set to this value to remove the trap receiver entry from
                                        the agent."
         ::= { agentSnmpTrapReceiverConfigEntry 4 }

    agentSnmpTrapReceiverVersion OBJECT-TYPE
         SYNTAX      INTEGER {
                     snmpv1(1),
                     snmpv2c(2)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The version of trap sent to this Reciever.

                     snmpv1(1)        - This will send a SNMPv1 trap.

                     snmpv2c(2)       - This will send a SNMPv2c trap."
         DEFVAL { snmpv2c }
         ::= { agentSnmpTrapReceiverConfigEntry 5 }

    agentSnmpTrapReceiverIpv6Address OBJECT-TYPE
      SYNTAX      Ipv6AddressPrefix
      MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
                   "IPv6 Address  for SNMP receiver."
       ::= { agentSnmpTrapReceiverConfigEntry 6 }


    --**************************************************************************************
    -- agentSnmpTrapFlagsConfigGroup
    --
    --**************************************************************************************

    agentSnmpTrapFlagsConfigGroup       OBJECT IDENTIFIER ::= { agentSnmpConfigGroup 5  }

    agentSnmpAuthenticationTrapFlag OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Authentication Flag - Enable/Disable authentication Flag."
         ::= { agentSnmpTrapFlagsConfigGroup 1 }

    agentSnmpLinkUpDownTrapFlag OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Link Up/Down Flag - Enable/Disable Link Up/Link Down traps for the
                     entire switch. When set to Enable, the Link Up/Down traps will be
                     sent only if the Link Trap flag setting associated with the port
                     (Port Configuration Menu) is set to Enable.
                     "
         ::= { agentSnmpTrapFlagsConfigGroup 2 }

    agentSnmpMultipleUsersTrapFlag OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Multiple Users Flag - Enable/Disable Multiple User traps. When the value
                     is set to Enable, a Multiple User Trap is sent whenever someone logs in
                     to the terminal interface (EIA 232 or Telnet) and there is already an
                     existing terminal interface session.
                     "
         ::= { agentSnmpTrapFlagsConfigGroup 3 }

    agentSnmpSpanningTreeTrapFlag OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Spanning Tree Flag - This flag enables the sending of new root traps and
                     topology change notification traps."
         ::= { agentSnmpTrapFlagsConfigGroup 4 }

    agentSnmpBroadcastStormTrapFlag OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      obsolete
         DESCRIPTION
                     "Broadcast Storm Flag - This flag enables or disables the broadcast
                     storm trap. You must also enable Broadcast Storm Recovery Mode
                     (see the Switch Configuration Menu). When this
                     value is set to Enable and Broadcast Storm Recovery mode is set to Enable,
                     the Broadcast Storm Start/End traps are sent when the switch enters and
                     leaves Broadcast Storm Recovery.
                     "
         ::= { agentSnmpTrapFlagsConfigGroup 5 }

    --**************************************************************************************
    -- agentSpanningTreePortTable
    --
    --**************************************************************************************
    agentSpanningTreeConfigGroup                 OBJECT IDENTIFIER ::= { agentConfigGroup 7 }


    agentSpanningTreeMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch's Spanning Tree Switch Status"
         ::= { agentSpanningTreeConfigGroup 1 }


    --**************************************************************************************
    -- agentSwitchConfigGroup
    --
    --**************************************************************************************

    agentSwitchConfigGroup                      OBJECT IDENTIFIER ::= { agentConfigGroup 8 }

    agentSwitchAddressAgingTimeoutTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentSwitchAddressAgingTimeoutEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "The switch's address aging timeout table"
         ::= { agentSwitchConfigGroup 4 }

    agentSwitchAddressAgingTimeoutEntry OBJECT-TYPE
         SYNTAX      AgentSwitchAddressAgingTimeoutEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Aging information about a specific Filtering Database."
         INDEX       { dot1qFdbId }
         ::= { agentSwitchAddressAgingTimeoutTable 1 }

    AgentSwitchAddressAgingTimeoutEntry ::=
         SEQUENCE {
             agentSwitchAddressAgingTimeout
                 Integer32
         }

    agentSwitchAddressAgingTimeout OBJECT-TYPE
         SYNTAX   Integer32 (10..1000000)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The FDB entry's address aging timeout (in seconds)"
         DEFVAL { 300 }
         ::= { agentSwitchAddressAgingTimeoutEntry 1 }

    agentSwitchStaticMacFilteringTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentSwitchStaticMacFilteringEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "The switch's static Mac filtering table"
         ::= { agentSwitchConfigGroup 5 }

    agentSwitchStaticMacFilteringEntry OBJECT-TYPE
         SYNTAX      AgentSwitchStaticMacFilteringEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Per-port ingress/egress filter configuration based on VLAN Id and MAC address."
         INDEX       { agentSwitchStaticMacFilteringVlanId, agentSwitchStaticMacFilteringAddress }
         ::= { agentSwitchStaticMacFilteringTable 1 }

    AgentSwitchStaticMacFilteringEntry ::=
         SEQUENCE {
             agentSwitchStaticMacFilteringVlanId
                 Integer32,
             agentSwitchStaticMacFilteringAddress
                 MacAddress,
             agentSwitchStaticMacFilteringSourcePortMask
                 AgentPortMask,
             agentSwitchStaticMacFilteringDestPortMask
                 AgentPortMask,
             agentSwitchStaticMacFilteringStatus
                 RowStatus
         }

    agentSwitchStaticMacFilteringVlanId OBJECT-TYPE
         SYNTAX   Integer32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The Static MAC Filter Vlan Id"
         ::= { agentSwitchStaticMacFilteringEntry 1 }

    agentSwitchStaticMacFilteringAddress OBJECT-TYPE
         SYNTAX   MacAddress
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The Static MAC Filter MAC address"
         ::= { agentSwitchStaticMacFilteringEntry 2 }

    agentSwitchStaticMacFilteringSourcePortMask OBJECT-TYPE
         SYNTAX   AgentPortMask
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The Static MAC Filter source port mask.

                     When setting this value, the system will ignore configuration for ports not
                     between the first and last valid ports.  Configuration of any port numbers
                     between this range that are not valid ports return a failure message, but will
                     still apply configuration for valid ports.

                     To obtain port numbers from interface numbers, use the objects
                     agentPortDot1dBasePort and agentPortIfIndex in the agentPortConfigTable
                     table."

         ::= { agentSwitchStaticMacFilteringEntry 3 }

    agentSwitchStaticMacFilteringDestPortMask OBJECT-TYPE
         SYNTAX   AgentPortMask
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The Static MAC Filter destination port mask.

                     When setting this value, the system will ignore configuration for ports not
                     between the first and last valid ports.  Configuration of any port numbers
                     between this range that are not valid ports return a failure message, but will
                     still apply configuration for valid ports.

                     To obtain port numbers from interface numbers, use the objects
                     agentPortDot1dBasePort and agentPortIfIndex in the agentPortConfigTable
                     table.

                     Configuring destination port mask for a unicast MAC filter is not supported on some platforms."
         ::= { agentSwitchStaticMacFilteringEntry 4 }

    agentSwitchStaticMacFilteringStatus OBJECT-TYPE
         SYNTAX   RowStatus
         MAX-ACCESS  read-create
         STATUS      current
         DESCRIPTION
                     "The Static MAC Filter status.

                     Supported values:
                     active(1)      - valid entry
                     createAndGo(4) - used to create a new entry
                     destroy(6)     - removes the entry"
         ::= { agentSwitchStaticMacFilteringEntry 5 }


    --**************************************************************************************
    -- agentSwitchStormControlGroup
    --
    --**************************************************************************************

    agentSwitchStormControlGroup                      OBJECT IDENTIFIER ::= { agentSwitchConfigGroup 12 }


    agentSwitchDot3FlowControlMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Config switchconfig flowcontrol allows you to enable or disable
                     802.3x flow control for the switch. This value
                     applies to only full-duplex mode ports. "
         DEFVAL { disable }
         ::= { agentSwitchStormControlGroup 1 }

    agentSwitchBroadcastControlMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch config broadcast allows you to enable or disable
                     broadcast storm recovery mode. When you specify Enable for Broadcast
                     Storm Recovery and the broadcast traffic on any Ethernet port exceeds
                     the configured threshold, the switch blocks (discards) the broadcast
                     traffic."
         DEFVAL { disable }
         ::= { agentSwitchStormControlGroup 4 }

    agentSwitchBroadcastControlThreshold OBJECT-TYPE
         SYNTAX      Unsigned32(0..100)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Configures the broadcast storm recovery threshold for this port
                     as a percentage of port speed."
         DEFVAL { 5 }
         ::= { agentSwitchStormControlGroup 5 }

    agentSwitchMulticastControlMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch config multicast allows you to enable or disable
                     multicast storm recovery mode. When you specify Enable for multicast
                     Storm Recovery and the multicast traffic on any Ethernet port exceeds
                     the configured threshold, the switch blocks (discards) the multicast
                     traffic."
         DEFVAL { disable }
         ::= { agentSwitchStormControlGroup 6 }

    agentSwitchMulticastControlThreshold OBJECT-TYPE
         SYNTAX      Unsigned32(0..100)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Configures the multicast storm recovery threshold for this port
                     as a percentage of port speed."
         DEFVAL { 5 }
         ::= { agentSwitchStormControlGroup 7 }

    agentSwitchUnicastControlMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch config unicast allows you to enable or disable
                     unicast storm recovery mode. When you specify Enable for unicast
                     Storm Recovery and the unknown unicast traffic on any Ethernet port exceeds
                     the configured threshold, the switch blocks (discards) the unknown unicast
                     traffic."
         DEFVAL { disable }
         ::= { agentSwitchStormControlGroup 8 }

    agentSwitchUnicastControlThreshold OBJECT-TYPE
         SYNTAX      Unsigned32(0..100)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Configures the unicast storm recovery threshold for this port
                     as a percentage of port speed."
         DEFVAL { 5 }
         ::= { agentSwitchStormControlGroup 9 }

    --**************************************************************************************
    -- agentSwitchSnoopingGroup
    --
    --**************************************************************************************

    agentSwitchSnoopingGroup                      OBJECT IDENTIFIER ::= { agentSwitchConfigGroup 6 }


    agentSwitchSnoopingCfgTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentSwitchSnoopingCfgEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "A table of the IGMP/MLD Snooping Global configuration entries."
         ::= { agentSwitchSnoopingGroup 1 }

    agentSwitchSnoopingCfgEntry OBJECT-TYPE
         SYNTAX      AgentSwitchSnoopingCfgEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Represents entry for Snooping switch Configuration."
         INDEX       { agentSwitchSnoopingProtocol }
         ::= { agentSwitchSnoopingCfgTable 1 }

   AgentSwitchSnoopingCfgEntry ::= SEQUENCE {
           agentSwitchSnoopingProtocol
              InetAddressType,
           agentSwitchSnoopingAdminMode
              INTEGER,
           agentSwitchSnoopingPortMask
	      AgentPortMask,
           agentSwitchSnoopingMulticastControlFramesProcessed
              Counter32
          }

   agentSwitchSnoopingProtocol OBJECT-TYPE
         SYNTAX      InetAddressType
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The protocol type of network protocol in use."
         ::= { agentSwitchSnoopingCfgEntry 1 }

    agentSwitchSnoopingAdminMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This enables or disables Snooping on the system. "
         DEFVAL { disable }
         ::= { agentSwitchSnoopingCfgEntry 2 }

    agentSwitchSnoopingPortMask OBJECT-TYPE
         SYNTAX      AgentPortMask
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "IGMP/MLD Snooping Port Mask.

                     This allows configuration of IGMP/MLD Snooping on selected ports.
                     IGMP Snooping cannot be enabled on an interface that has routing
                     enabled, or is a member of a LAG.  If a port which has IGMP Snooping
                     enabled becomes enabled for routing, or is enlisted as a member of a
                     LAG, IGMP Snooping functionality will be disabled on that port.  IGMP
                     Snooping functionality will be subsequently be reenabled if routing is
                     disabled or LAG membership is removed from an interface that had previously
                     had IGMP Snooping enabled.

                     To obtain port numbers from interface numbers, use the objects
                     agentPortDot1dBasePort and agentPortIfIndex in the agentPortConfigTable
                     table."
         DEFVAL { '************'H }
         ::= { agentSwitchSnoopingCfgEntry 3 }

    agentSwitchSnoopingMulticastControlFramesProcessed OBJECT-TYPE
         SYNTAX      Counter32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Multicast Control Frames Processed by CPU.
                     The number of multicast control frames that have been processed by the CPU."
         ::= { agentSwitchSnoopingCfgEntry 4 }

    --**************************************************************************************
    -- agentSwitchSnoopingIntfGroup
    --
    --**************************************************************************************

    agentSwitchSnoopingIntfGroup                      OBJECT IDENTIFIER ::= { agentSwitchConfigGroup 7 }

    agentSwitchSnoopingIntfTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentSwitchSnoopingIntfEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "A table of the IGMP Snooping Interface configuration entries."
         ::= { agentSwitchSnoopingIntfGroup 1 }

    agentSwitchSnoopingIntfEntry OBJECT-TYPE
         SYNTAX      AgentSwitchSnoopingIntfEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Represents entry for a IGMP Snooping Interface."
         INDEX       { ifIndex, agentSwitchSnoopingProtocol }
         ::= { agentSwitchSnoopingIntfTable 1 }

   AgentSwitchSnoopingIntfEntry ::= SEQUENCE {
          agentSwitchSnoopingIntfIndex
              Unsigned32,
          agentSwitchSnoopingIntfAdminMode
              INTEGER,
          agentSwitchSnoopingIntfGroupMembershipInterval
              Integer32,
          agentSwitchSnoopingIntfMaxResponseTime
              Integer32,
          agentSwitchSnoopingIntfMRPExpirationTime
              Integer32,
          agentSwitchSnoopingIntfFastLeaveAdminMode
              INTEGER,
          agentSwitchSnoopingIntfMulticastRouterMode
              INTEGER,
          agentSwitchSnoopingIntfVlanIDs
              VlanList

          }


   agentSwitchSnoopingIntfIndex OBJECT-TYPE
         SYNTAX      Unsigned32 (1..65535)
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The port number to be selected. Each port maps to an interface."
         ::= { agentSwitchSnoopingIntfEntry 1 }



   agentSwitchSnoopingIntfAdminMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This enables or disables IGMP Snooping on a selected interface."
         DEFVAL { disable }
         ::= { agentSwitchSnoopingIntfEntry 2 }

    agentSwitchSnoopingIntfGroupMembershipInterval OBJECT-TYPE
         SYNTAX      Integer32 (2..3600)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The amount of time in seconds that a switch will wait for a report
                     from a particular group on the selected interface before deleting the
                     interface from the entry. This value must be greater than
                     agentSwitchIGMPSnoopingIntfMaxResponseTime."
         DEFVAL { 260 }
         ::= { agentSwitchSnoopingIntfEntry 3 }

    agentSwitchSnoopingIntfMaxResponseTime OBJECT-TYPE
         SYNTAX      Integer32 (1..3599)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The amount of time in seconds a switch will wait after sending
                     a query on the selected interface because it did not receive a report for
                     a particular group in that interface.  This value must be less
                     than agentSwitchIGMPSnoopingIntfGroupMembershipInterval."
         DEFVAL { 10 }
         ::= { agentSwitchSnoopingIntfEntry 4 }

    agentSwitchSnoopingIntfMRPExpirationTime OBJECT-TYPE
         SYNTAX      Integer32 (0..3600)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The amount of time in seconds that a switch will wait for a query to be
                     received on the selected interface before the interface is removed from
                     the list of interfaces with multicast routers attached."
         DEFVAL { 0 }
         ::= { agentSwitchSnoopingIntfEntry 5 }

    agentSwitchSnoopingIntfFastLeaveAdminMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This enables or disables IGMP Snooping on the selected interface."
         DEFVAL { disable }
         ::= { agentSwitchSnoopingIntfEntry 6 }

    agentSwitchSnoopingIntfMulticastRouterMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This enables or disables Multicast Routing on the selected interface."
         DEFVAL { disable }
         ::= { agentSwitchSnoopingIntfEntry 7 }

    agentSwitchSnoopingIntfVlanIDs OBJECT-TYPE
         SYNTAX      VlanList
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "This field lists all the VlanIDs which include the selected interface."
         ::= { agentSwitchSnoopingIntfEntry 8 }


    --**************************************************************************************
    -- agentSwitchSnoopingVlanGroup
    --
    --**************************************************************************************

    agentSwitchSnoopingVlanGroup                      OBJECT IDENTIFIER ::= { agentSwitchConfigGroup 8 }

    agentSwitchSnoopingVlanTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentSwitchSnoopingVlanEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "A table of the IGMP Snooping Vlan configuration entries."
         ::= { agentSwitchSnoopingVlanGroup 1 }

    agentSwitchSnoopingVlanEntry OBJECT-TYPE
         SYNTAX      AgentSwitchSnoopingVlanEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Represents entry for a IGMP Snooping Vlan."
         INDEX       { dot1qVlanIndex, agentSwitchSnoopingProtocol }
         ::= { agentSwitchSnoopingVlanTable 1 }

   AgentSwitchSnoopingVlanEntry ::= SEQUENCE {
          agentSwitchSnoopingVlanAdminMode
              INTEGER,
          agentSwitchSnoopingVlanGroupMembershipInterval
              Integer32,
          agentSwitchSnoopingVlanMaxResponseTime
              Integer32,
          agentSwitchSnoopingVlanFastLeaveAdminMode
              INTEGER,
          agentSwitchSnoopingVlanMRPExpirationTime
              Integer32
          }


   agentSwitchSnoopingVlanAdminMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     disable(0),
                     enable(1)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This enables or disables IGMP Snooping on a selected Vlan interface."
         DEFVAL { disable }
         ::= { agentSwitchSnoopingVlanEntry 1 }


   agentSwitchSnoopingVlanGroupMembershipInterval OBJECT-TYPE
         SYNTAX      Integer32 (2..3600)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The amount of time in seconds that a switch will wait for a report
                     from a particular group on the selected vlan before deleting the
                     interface participating in the vlan from the entry. This value must
                     be greater than agentSwitchIGMPSnoopingIntfMaxResponseTime."
         DEFVAL { 260 }
         ::= { agentSwitchSnoopingVlanEntry 2 }

   agentSwitchSnoopingVlanMaxResponseTime OBJECT-TYPE
         SYNTAX      Integer32 (1..3599)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The amount of time in seconds a switch will wait after sending
                     a query on the selected vlan because it did not receive a report for
                     a particular group in the interface participating in the vlan.
                     This value must be less than
                     agentSwitchIGMPSnoopingIntfGroupMembershipInterval."
         DEFVAL { 10 }
         ::= { agentSwitchSnoopingVlanEntry 3 }

   agentSwitchSnoopingVlanFastLeaveAdminMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     disable(0),
                     enable(1)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This enables or disables IGMP Snooping on the selected vlan."
         DEFVAL { disable }
         ::= { agentSwitchSnoopingVlanEntry 4 }

   agentSwitchSnoopingVlanMRPExpirationTime OBJECT-TYPE
         SYNTAX      Integer32 (0..3600)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The amount of time in seconds that a switch will wait for a query to be
                     received on the selected interface participating in the vlan before the
                     interface is removed from the list of interfaces with multicast routers
                     attached. This parameter is configurable only for exisiting Vlans."
         DEFVAL { 0 }
         ::= { agentSwitchSnoopingVlanEntry 5 }


    --**************************************************************************************
    -- agentSwitchVlanStaticMrouterGroup
    --
    --**************************************************************************************
    agentSwitchVlanStaticMrouterGroup               OBJECT IDENTIFIER ::= { agentSwitchConfigGroup 9 }

    agentSwitchVlanStaticMrouterTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentSwitchVlanStaticMrouterEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "A table of the IGMP Static Mrouter Configuration parameters."
         ::= { agentSwitchVlanStaticMrouterGroup 1 }

    agentSwitchVlanStaticMrouterEntry OBJECT-TYPE
         SYNTAX      AgentSwitchVlanStaticMrouterEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Represents entry for a IGMP Static Mrouter."
         INDEX       { ifIndex,
                           dot1qVlanIndex, agentSwitchSnoopingProtocol }
         ::= { agentSwitchVlanStaticMrouterTable 1 }

   AgentSwitchVlanStaticMrouterEntry ::= SEQUENCE {
          agentSwitchVlanStaticMrouterAdminMode
                 INTEGER
          }

   agentSwitchVlanStaticMrouterAdminMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     disable(0),
                     enable(1)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This enables or disables IGMP Mrouter on a per-interface per-vlan basis."
         DEFVAL { disable }
         ::= { agentSwitchVlanStaticMrouterEntry 1 }


    --**************************************************************************************
    -- agentSwitchMFDBGroup
    --
    --**************************************************************************************
    agentSwitchMFDBGroup                      OBJECT IDENTIFIER ::= { agentSwitchConfigGroup 10 }

    agentSwitchMFDBTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentSwitchMFDBEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "The Multicast Forwarding Database table"
         ::= { agentSwitchMFDBGroup 1 }

    agentSwitchMFDBEntry OBJECT-TYPE
         SYNTAX      AgentSwitchMFDBEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Contains Forwarding and Filtering information per Vlan Index, MAC Address, and
                     Component User."
         INDEX       { agentSwitchMFDBVlanId, agentSwitchMFDBMacAddress, agentSwitchMFDBProtocolType }
         ::= { agentSwitchMFDBTable 1 }

    AgentSwitchMFDBEntry ::=
         SEQUENCE {
             agentSwitchMFDBVlanId
                 VlanIndex,
             agentSwitchMFDBMacAddress
                 MacAddress,
             agentSwitchMFDBProtocolType
                 INTEGER,
             agentSwitchMFDBType
                 INTEGER,
             agentSwitchMFDBDescription
                 DisplayString,
             agentSwitchMFDBForwardingPortMask
                 AgentPortMask,
             agentSwitchMFDBFilteringPortMask
                 AgentPortMask
         }

    agentSwitchMFDBVlanId OBJECT-TYPE
         SYNTAX   VlanIndex
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Vlan Index for which this entry is associated with."
         ::= { agentSwitchMFDBEntry 1 }

    agentSwitchMFDBMacAddress OBJECT-TYPE
         SYNTAX   MacAddress
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "A multicast MAC address for which the switch has forwarding and or
                     filtering information."
         ::= { agentSwitchMFDBEntry 2 }

    agentSwitchMFDBProtocolType OBJECT-TYPE
         SYNTAX   INTEGER {
                  static(1),
                  gmrp(2),
                  igmp(3),
                  mld(4),
		  mmrp(5),
		  msrp(6)
                  }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The component that is responsible for this entry in the Multicast
                     Forwarding Database."
         ::= { agentSwitchMFDBEntry 3 }

    agentSwitchMFDBType OBJECT-TYPE
         SYNTAX   INTEGER {
                  static(1),
                  dynamic(2)
                  }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "This displays the type of the entry.

                     static(1)  - Entries configured by the end user
                     dynamic(2) - Entries added as a result of a learning process or protocol"
         ::= { agentSwitchMFDBEntry 4 }

    agentSwitchMFDBDescription OBJECT-TYPE
         SYNTAX   DisplayString
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Text description of this multicast table entry."
         ::= { agentSwitchMFDBEntry 5 }

    agentSwitchMFDBForwardingPortMask OBJECT-TYPE
         SYNTAX   AgentPortMask
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "BitMask indicating which ports this entry indicates should be forwarded.

                     To obtain port numbers from interface numbers, use the objects
                     agentPortDot1dBasePort and agentPortIfIndex in the agentPortConfigTable
                     table."
         ::= { agentSwitchMFDBEntry 6 }

    agentSwitchMFDBFilteringPortMask OBJECT-TYPE
         SYNTAX   AgentPortMask
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "BitMask indicating which ports this entry indicates should be filtered.

                     To obtain port numbers from interface numbers, use the objects
                     agentPortDot1dBasePort and agentPortIfIndex in the agentPortConfigTable
                     table."
         ::= { agentSwitchMFDBEntry 7 }

    --**************************************************************************************

    agentSwitchMFDBSummaryTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentSwitchMFDBSummaryEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "The Multicast Forwarding Database Summary table"
         ::= { agentSwitchMFDBGroup 2 }

    agentSwitchMFDBSummaryEntry OBJECT-TYPE
         SYNTAX      AgentSwitchMFDBSummaryEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Summarizes the forwarding ports for all components registered for all
                     MFDB table entries with the same Vlan Index and MAC Address."
         INDEX       { agentSwitchMFDBSummaryVlanId, agentSwitchMFDBSummaryMacAddress }
         ::= { agentSwitchMFDBSummaryTable 1 }

    AgentSwitchMFDBSummaryEntry ::=
         SEQUENCE {
             agentSwitchMFDBSummaryVlanId
                 VlanIndex,
             agentSwitchMFDBSummaryMacAddress
                 MacAddress,
             agentSwitchMFDBSummaryForwardingPortMask
                 AgentPortMask
         }

    agentSwitchMFDBSummaryVlanId OBJECT-TYPE
         SYNTAX   VlanIndex
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Vlan Index for which this entry is associated with."
         ::= { agentSwitchMFDBSummaryEntry 1 }

    agentSwitchMFDBSummaryMacAddress OBJECT-TYPE
         SYNTAX   MacAddress
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "A multicast MAC address for which the switch has forwarding and or
                     filtering information."
         ::= { agentSwitchMFDBSummaryEntry 2 }

    agentSwitchMFDBSummaryForwardingPortMask OBJECT-TYPE
         SYNTAX   AgentPortMask
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Port Mask indicating which ports this entry indicates should be forwarded.

                     To obtain port numbers from interface numbers, use the objects
                     agentPortDot1dBasePort and agentPortIfIndex in the agentPortConfigTable
                     table."
         ::= { agentSwitchMFDBSummaryEntry 3 }

    --**************************************************************************************

    agentSwitchMFDBMaxTableEntries OBJECT-TYPE
         SYNTAX   Gauge32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "This displays the maximum number of entries that can possibly be in
                     the Multicast Forwarding Database table."
         ::= { agentSwitchMFDBGroup 3 }

    agentSwitchMFDBMostEntriesUsed OBJECT-TYPE
         SYNTAX   Gauge32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "This displays the largest number of entries that have been present
                     in the Multicast Forwarding Database table. This value is also known
                     as the MFDB high-water mark."
         ::= { agentSwitchMFDBGroup 4 }

    agentSwitchMFDBCurrentEntries OBJECT-TYPE
         SYNTAX   Gauge32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "This displays the current number of entries in the Multicast
                     Forwarding Database table."
         ::= { agentSwitchMFDBGroup 5 }



    --**************************************************************************************
    -- The  DHCP Filtering Group
    --
    --**************************************************************************************

    agentDhcpFilteringGroup      OBJECT IDENTIFIER ::= { agentSwitchConfigGroup 15 }

    agentDhcpFilteringAdminMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Dhcp Filtering mode:

                     enable - enable dhcp filtering mode
                     disable - disable dhcp filtering mode."
         DEFVAL { disable }
         ::= { agentDhcpFilteringGroup 1 }


    agentDhcpFilteringPortConfigTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentDhcpFilteringPortConfigEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
         "A table that contains the configuration objects for the
         with each port."

         ::= { agentDhcpFilteringGroup 2 }

    agentDhcpFilteringPortConfigEntry OBJECT-TYPE
         SYNTAX      AgentDhcpFilteringPortConfigEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
         "The configuration information for Dhcp Filtering."
         INDEX { ifIndex }
         ::= { agentDhcpFilteringPortConfigTable 1 }

    AgentDhcpFilteringPortConfigEntry ::=
         SEQUENCE {
         agentDhcpFilteringPortTrustedMode
                 INTEGER
         }

    agentDhcpFilteringPortTrustedMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     trusted(1),
                     untrusted(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Port's DHCP mode. It can be either trusted or untrusted
                      default will be untrusted."
         DEFVAL   { untrusted }
         ::= { agentDhcpFilteringPortConfigEntry 1 }




    --**************************************************************************************
    -- agentSwitchProtectedPortGroup
    --
    --**************************************************************************************

    agentSwitchProtectedPortConfigGroup		OBJECT IDENTIFIER ::= { agentSwitchConfigGroup 18 }
    agentSwitchProtectedPortTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentSwitchProtectedPortEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "The switch's protected port mapping table"
         ::= { agentSwitchProtectedPortConfigGroup 1 }

    agentSwitchProtectedPortEntry OBJECT-TYPE
         SYNTAX      AgentSwitchProtectedPortEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Protected ports assigned to groups."
         INDEX       { agentSwitchProtectedPortGroupId }
         ::= { agentSwitchProtectedPortTable 1 }

    AgentSwitchProtectedPortEntry ::=
    SEQUENCE {
        agentSwitchProtectedPortGroupId
            Integer32,
        agentSwitchProtectedPortGroupName
            DisplayString,
        agentSwitchProtectedPortPortList
            PortList
      }

    agentSwitchProtectedPortGroupId  OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "The group that this port belongs to"
         ::= { agentSwitchProtectedPortEntry 1 }

    agentSwitchProtectedPortGroupName  OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(0..31))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The name of the group"
         ::= { agentSwitchProtectedPortEntry 2 }

    agentSwitchProtectedPortPortList  OBJECT-TYPE
         SYNTAX      PortList
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The set of ports that are protected in this group"
         ::= { agentSwitchProtectedPortEntry 3 }



    --**************************************************************************************
    -- agentSwitchSnoopingQuerierGroup
    --
    --**************************************************************************************
    agentSwitchSnoopingQuerierGroup               OBJECT IDENTIFIER ::= { agentSwitchConfigGroup 20 }

    agentSwitchSnoopingQuerierCfgTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentSwitchSnoopingQuerierCfgEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "A table of the IGMP/MLD Snooping Querier Global configuration entries."
         ::= { agentSwitchSnoopingQuerierGroup 1 }

    agentSwitchSnoopingQuerierCfgEntry OBJECT-TYPE
         SYNTAX      AgentSwitchSnoopingQuerierCfgEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Represents entry for Snooping Querier Configuration."
         INDEX       { agentSwitchSnoopingProtocol }
         ::= { agentSwitchSnoopingQuerierCfgTable 1 }

   AgentSwitchSnoopingQuerierCfgEntry ::= SEQUENCE {
           agentSwitchSnoopingQuerierAdminMode
              INTEGER,
           agentSwitchSnoopingQuerierVersion
              Integer32,
           agentSwitchSnoopingQuerierAddress
              InetAddress,
           agentSwitchSnoopingQuerierQueryInterval
              Integer32,
           agentSwitchSnoopingQuerierExpiryInterval
              Integer32
          }

    agentSwitchSnoopingQuerierAdminMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This enables or disables Snooping Querier on the system. "
         DEFVAL { disable }
         ::= { agentSwitchSnoopingQuerierCfgEntry  1 }

    agentSwitchSnoopingQuerierVersion OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This specifies the multicast protocol versions that are
                      supported by the system."
         ::= { agentSwitchSnoopingQuerierCfgEntry  2 }

    agentSwitchSnoopingQuerierAddress OBJECT-TYPE
         SYNTAX      InetAddress
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This specifies the default source ip address to be used
                      while generating general queries."
         ::= { agentSwitchSnoopingQuerierCfgEntry  3 }

    agentSwitchSnoopingQuerierQueryInterval OBJECT-TYPE
         SYNTAX      Integer32 (1..1800)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This specified the timer interval after which the system
                      generates general queries."
         DEFVAL { 60 }
         ::= { agentSwitchSnoopingQuerierCfgEntry  4 }

    agentSwitchSnoopingQuerierExpiryInterval OBJECT-TYPE
         SYNTAX      Integer32 (60..300)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This specified the timer interval after which the detected
                      other querier information is no longer valid."
         DEFVAL { 60 }
         ::= { agentSwitchSnoopingQuerierCfgEntry  5 }

    --**************************************************************************************
    -- agentSwitchSnoopingQuerierVlanCfgTable
    --
    --**************************************************************************************
    agentSwitchSnoopingQuerierVlanTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentSwitchSnoopingQuerierVlanEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "A table of the Snooping Querier Vlan configuration entries."
         ::= { agentSwitchSnoopingQuerierGroup 2 }

    agentSwitchSnoopingQuerierVlanEntry OBJECT-TYPE
         SYNTAX      AgentSwitchSnoopingQuerierVlanEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Represents entry for a Snooping Querier configurable Vlan."
         INDEX       { dot1qVlanIndex, agentSwitchSnoopingProtocol }
         ::= { agentSwitchSnoopingQuerierVlanTable 1 }

   AgentSwitchSnoopingQuerierVlanEntry ::= SEQUENCE {
          agentSwitchSnoopingQuerierVlanAdminMode
              INTEGER,
           agentSwitchSnoopingQuerierVlanOperMode
              INTEGER,
          agentSwitchSnoopingQuerierElectionParticipateMode
              INTEGER,
          agentSwitchSnoopingQuerierVlanAddress
              InetAddress,
          agentSwitchSnoopingQuerierOperVersion
              Integer32,
          agentSwitchSnoopingQuerierOperMaxResponseTime
              Integer32,
          agentSwitchSnoopingQuerierLastQuerierAddress
              InetAddress,
          agentSwitchSnoopingQuerierLastQuerierVersion
              Integer32

          }

   agentSwitchSnoopingQuerierVlanAdminMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     disable(0),
                     enable(1)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This enables or disables Snooping Querier on a selected Vlan interface."
         DEFVAL { disable }
         ::= { agentSwitchSnoopingQuerierVlanEntry 1 }

   agentSwitchSnoopingQuerierVlanOperMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     disabled(0),
                     querier(1),
                     non-querier(2)
                  }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "This specifies the current state of the Snooping Querier on a selected vlan interface"
         DEFVAL { disabled }
         ::= { agentSwitchSnoopingQuerierVlanEntry 2 }

   agentSwitchSnoopingQuerierElectionParticipateMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     disable(0),
                     enable(1)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This enables or disables the Snooping Querier Election Participation mode. When enabled
                      snooping querier switch will participate in querier election up on discovering another
                      querier in the specified vlan. When disabled, up on discovering another querier, snooping
                      querier moves to non-querier state."
         DEFVAL { disable }
         ::= { agentSwitchSnoopingQuerierVlanEntry 3 }

   agentSwitchSnoopingQuerierVlanAddress OBJECT-TYPE
         SYNTAX      InetAddress
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This specifies the default source ip address to be used while generating general queries on
                      the specified vlan."
         ::= { agentSwitchSnoopingQuerierVlanEntry 4 }

   agentSwitchSnoopingQuerierOperVersion OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "This specifies the multicast protocol version that is currently being used by the snooping
                      switch for the specified vlan while generating query messages."
         ::= { agentSwitchSnoopingQuerierVlanEntry  5 }

   agentSwitchSnoopingQuerierOperMaxResponseTime OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The amount of time in seconds a switch will wait after sending
                      a query on the selected vlan because it did not receive a report for
                      a particular group in the interface participating in the vlan. This
                      object is valid only when agentSwitchSnoopingQuerierOperVersion object
                      is valid."
         ::= { agentSwitchSnoopingQuerierVlanEntry  6 }

   agentSwitchSnoopingQuerierLastQuerierAddress OBJECT-TYPE
         SYNTAX      InetAddress
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "This specifies the last querier's ip address for the specified vlan.
                      It represents the detected other multicast querier in the vlan. "
         ::= { agentSwitchSnoopingQuerierVlanEntry  7 }

   agentSwitchSnoopingQuerierLastQuerierVersion OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "This specifies the multicast protocol version that is currently being used
                      by the detected other multicast querier for the specified vlan."
         ::= { agentSwitchSnoopingQuerierVlanEntry  8 }


    --**************************************************************************************
    -- agentTransferConfigGroup
    --
    --**************************************************************************************

    agentTransferConfigGroup                  OBJECT IDENTIFIER ::= { agentConfigGroup 9 }


    --**************************************************************************************
    -- agentTransferUploadGroup
    --
    --**************************************************************************************

    agentTransferUploadGroup                  OBJECT IDENTIFIER ::= { agentTransferConfigGroup 1 }

    agentTransferUploadMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     tftp(1),
                     xmodem(2),
                     ymodem(3),
                     zmodem(4),
                     sftp(5),
                     scp(6)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Transfer upload mode configures the mode to use when uploading from the
                     switch. The mode is either X/Y/ZMODEM, TFTP, SFTP or SCP. X/Y/ZMODEM is
                     valid only when the file transfer is initiated by the serial EIA 232 port.
                     SFTP and SCP are only allowed if the SSH feature is present.
                     "
         ::= { agentTransferUploadGroup 1 }

    agentTransferUploadServerIP OBJECT-TYPE
         SYNTAX      IpAddress
         MAX-ACCESS  read-write
         STATUS      deprecated
         DESCRIPTION
                     "Transfer upload server IP configures the IP address of the server
                     where the file is located. It is valid only when the Transfer Mode is
                     TFTP, SFTP, or SCP. The address is 4 integer bytes ranging from 0 to 255.

                     This object is deprecated in favour of agentTransferUploadServerAddress
                     and agentTransferUploadServerAddressType."
         ::= { agentTransferUploadGroup 2 }

    agentTransferUploadPath OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(0..31))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Transfer upload path configures the directory path where the file
                     is to be uploaded to. The switch remembers the last file path used.
                     "
         ::= { agentTransferUploadGroup 3 }

    agentTransferUploadFilename OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(0..31))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Transfer upload filename configures the file name for the file being
                     uploaded from the switch. It can be up to 32 alphanumeric characters.
                     The switch remembers the last file name used.
                     File path can be appended to the file name if the string is less than 17
                     characters. Otherwise, the File Path field will need to be used and the
                     File Name will be appended to the File Path as is. An example would be
                     File Path set to c:\tftp\code\ and File Name set to e1r1v1.opr.
                     Note: File Name, File Path, and Server IP Address are applicable
                     only if the Transfer Mode is TFTP, SFTP or SCP."
         ::= { agentTransferUploadGroup 4 }

    agentTransferUploadDataType OBJECT-TYPE
         SYNTAX      INTEGER {
                      config(2),
                      errorlog(3),
                      messagelog(4),
                      traplog(5),
                      clibanner(6),
                      code(7)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Transfer upload datatype configures the type of file to upload from the
                     switch.
                     The types for upload are:
                        -       Configuration File
                        -       Error log
                        -       Message log
                        -       Trap log
                        -       Banner File
                     "
         ::= { agentTransferUploadGroup 5 }


    agentTransferUploadStart OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Transfer upload start will start an upload transfer.
                     The agentTransferUploadMode object must not be set to xmodem(2),
                         ymodem(3), or zmodem(4) to initiate a transfer via SNMP."
         ::= { agentTransferUploadGroup 6 }

    agentTransferUploadStatus OBJECT-TYPE
         SYNTAX      INTEGER {
                     notInitiated(1),
                     transferStarting(2),
                     errorStarting(3),
                     wrongFileType(4),
                     updatingConfig(5),
                     invalidConfigFile(6),
                     writingToFlash(7),
                     failureWritingToFlash(8),
                     checkingCRC(9),
                     failedCRC(10),
                     unknownDirection(11),
                     transferSuccessful(12),
                     transferFailed(13)
                  }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Indicates the current status of an upload transfer."
         ::= { agentTransferUploadGroup 7 }

    agentTransferUploadServerAddressType OBJECT-TYPE
         SYNTAX      InetAddressType
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The type of the serverip address, as defined in the InetAddress MIB.

                      The agentTransferUploadServerAddress object is intepreted within the
                      context of agentTransferUploadServerAddressType"
         REFERENCE "RFC 3291"
         ::= { agentTransferUploadGroup 8 }

    agentTransferUploadServerAddress OBJECT-TYPE
         SYNTAX      InetAddress
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Transfer upload serverip configures the IP address of the server
                     where the file is to be uploaded to.It is valid only when the
                     Transfer Mode is TFTP, SFTP or SCP.
                     The type of this address is determined by the value of the
                     agentTransferUploadServerAddressType object.
                     The values for agentTransferUploadServerAddressType and
                     agentTransferUploadServerAddress must be consistent."
         REFERENCE "RFC 3291"
         ::= { agentTransferUploadGroup 9 }

    agentTransferUploadImagename OBJECT-TYPE
         SYNTAX      INTEGER {
                     image1(2),
                     image2(3)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Transfer upload image name. Sets the image to be uploaded as the specified
                      name
                     Note: Imagename, File Name, File Path, and Server IP Address are applicable
                     only if the Transfer Mode is TFTP, SFTP or SCP."

         ::= { agentTransferUploadGroup 10 }

    agentTransferUploadUsername OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(0..32))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Username applicable only to secure upload types, i.e., SFTP or SCP.
                     "

         ::= { agentTransferUploadGroup 11 }

    agentTransferUploadPassword OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(0..64))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Password applicable only to secure upload types, i.e. SFTP or SCP.
                     "

         ::= { agentTransferUploadGroup 12 }

    --**************************************************************************************
    -- agentTransferDownloadGroup
    --
    --**************************************************************************************

    agentTransferDownloadGroup                  OBJECT IDENTIFIER ::= { agentTransferConfigGroup 2 }

    agentTransferDownloadMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     tftp(1),
                     xmodem(2),
                     ymodem(3),
                     zmodem(4),
                     sftp(5),
                     scp(6)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Transfer download mode configures the mode to use when downloading
                     to the switch. The mode is either X/Y/ZMODEM, TFTP, SFTP or SCP. X/Y/ZMODEM is valid only
                     when the file transfer is initiated by the serial EIA 232 port.  SFTP and SCP
                     are only allowed if the SSH feature is present.
                     "
         ::= { agentTransferDownloadGroup 1 }

    agentTransferDownloadServerIP OBJECT-TYPE
         SYNTAX      IpAddress
         MAX-ACCESS  read-write
         STATUS      deprecated
         DESCRIPTION
                     "Transfer download serverip configures the IP address of the server
                     where the file is located. It is valid only when the Transfer Mode is TFTP, SFTP or
                     SCP.
                     The address is 4 integer bytes ranging from 0 to 255.

                     This object is deprecated in favour of agentTransferDownloadServerAddress
                     and agentTransferDownloadServerAddressType."

         ::= { agentTransferDownloadGroup 2 }

    agentTransferDownloadPath OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(0..31))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Transfer download path configures the directory path where the
                     file is located. The switch remembers the last file path used.
                     "

         ::= { agentTransferDownloadGroup 3 }

    agentTransferDownloadFilename OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(0..31))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Transfer download filename configures the file name for the file
                     being downloaded to the switch. It can be up to 32 alphanumeric characters.
                     The switch remembers the last file name used.
                     File path can be appended to the file name if the string is less than 33
                     characters. Otherwise, the File Path field will need to be used and the
                     File Name will be appended to the File Path as is. An example would be
                     File Path set to c:\tftp\code\ and File Name set to e1r1v1.opr.
                     Note: Imagename, File Name, File Path, and Server IP Address are applicable
                     only if the Transfer Mode is TFTP, SFTP or SCP."

         ::= { agentTransferDownloadGroup 4 }

    agentTransferDownloadDataType OBJECT-TYPE
         SYNTAX      INTEGER {
                     code(2),
                     config(3),
                     sshkey-rsa1(4),
                     sshkey-rsa2(5),
                     sshkey-dsa(6),
                     sslpem-root(7),
                     sslpem-server(8),
                     sslpem-dhweak(9),
                     sslpem-dhstrong(10),
                     clibanner(11),
                     kernel(12)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Transfer download datatype configures the type of file to download to
                     the switch.
                     The types for download are:

                     code               - Code File
                     config             - Configuration File
                     sshkey-rsa1        - SSH-1 RSA Key File
                     sshkey-rsa2        - SSH-2 RSA Key PEM File
                     sshkey-dsa         - SSH-2 DSA Key PEM File
                     sslpem-root        - SSL Trusted Root Certificate PEM File
                     sslpem-server      - SSL Server Certificate PEM File
                     sslpem-dhweak      - SSL DH Weak Encryption Parameter PEM File
                     sslpem-dhstrong    - SSL DH Strong Encryption Parameter PEM File
                     cli-banner         - CLI Banner File
                     kernel             - Kernel File

                     Note: SSH Key files can only be downloaded if SSH Server is administratively
                     disabled, and there are no active SSH sessions.
                     Kernel file can be downloaded only on raptor platform and linux operating system.
                     "
         ::= { agentTransferDownloadGroup 5 }


    agentTransferDownloadStart OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Transfer download start will start an download transfer.
                     The agentTransferDownloadMode object must not be set to xmodem(2),
                         ymodem(3), or zmodem(4) to initiate a transfer via SNMP."
         ::= { agentTransferDownloadGroup 6 }

    agentTransferDownloadStatus OBJECT-TYPE
         SYNTAX      INTEGER {
                     notInitiated(1),
                     transferStarting(2),
                     errorStarting(3),
                     wrongFileType(4),
                     updatingConfig(5),
                     invalidConfigFile(6),
                     writingToFlash(7),
                     failureWritingToFlash(8),
                     checkingCRC(9),
                     failedCRC(10),
                     unknownDirection(11),
                     transferSuccessful(12),
                     transferFailed(13)
                  }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Indicates the current status of an download transfer."
         ::= { agentTransferDownloadGroup 7 }

    agentTransferDownloadServerAddressType OBJECT-TYPE
         SYNTAX      InetAddressType
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The type of the serverip address, as defined in the InetAddress MIB.

                      The agentTransferDownloadServerAddress object is intepreted within the
                      context of agentTransferDownloadServerAddressType"
         REFERENCE "RFC 3291"
         ::= { agentTransferDownloadGroup 8 }

    agentTransferDownloadServerAddress OBJECT-TYPE
         SYNTAX      InetAddress
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Transfer download serverip configures the IP address of the server
                     where the file is to be downloaded from.It is valid only when the
                     Transfer Mode is TFTP, SFTP, or SCP.
                     The type of this address is determined by the value of the
                     agentTransferDownloadServerAddressType object.
                     The values for agentTransferDownloadServerAddressType and
                     agentTransferDownloadServerAddress must be consistent."
         REFERENCE "RFC 3291"
         ::= { agentTransferDownloadGroup 9 }

    agentTransferDownloadImagename OBJECT-TYPE
         SYNTAX      INTEGER {
                     image1(2),
                     image2(3)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Transfer download image name. Sets the downloaded image as the specified
                      name
                     Note: Imagename, File Name, File Path, and Server IP Address are applicable
                     only if the Transfer Mode is TFTP, SFTP or SCP."

         ::= { agentTransferDownloadGroup 10 }

    agentTransferDownloadUsername OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(0..32))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Username applicable only to secure download types, i.e., SFTP or SCP.
                     "

         ::= { agentTransferDownloadGroup 11 }

    agentTransferDownloadPassword OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(0..64))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Password applicable only to secure download types, i.e. SFTP or SCP.
                     "

         ::= { agentTransferDownloadGroup 12 }


  --*** switchimage starts ***
    --**************************************************************************************
    -- agentImageConfigGroup
    --
    --**************************************************************************************

    agentImageConfigGroup                  OBJECT IDENTIFIER ::= { agentTransferConfigGroup 3 }

    agentImage1  OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(1..32))
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The image1 softwate version ."
         ::= { agentImageConfigGroup 1 }
    agentImage2   OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(1..32))
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The image2 software version."
         ::= { agentImageConfigGroup 2 }

   agentActiveImage OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(1..32))
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The active image name. image1 or image2.
                      "
         ::= { agentImageConfigGroup 3 }
   agentNextActiveImage OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(1..32))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The next active image name.
                      user assigns either image1 or image2. "
         ::= { agentImageConfigGroup 4 }
    --*** switchimage ends ***

    --**************************************************************************************
    --    agentPortMirroringGroup
    --**************************************************************************************

    agentPortMirroringGroup                     OBJECT IDENTIFIER ::= { agentConfigGroup 10 }

     agentMirroredPortIfIndex OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-write
         STATUS      obsolete
         DESCRIPTION
                     "IfIndex of the mirrored port"
         DEFVAL { 0 }
         ::= { agentPortMirroringGroup 1 }

     agentProbePortIfIndex OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-write
         STATUS      obsolete
         DESCRIPTION
                     "IfIndex of the probe port"
         DEFVAL { 0 }
         ::= { agentPortMirroringGroup 2 }

     agentPortMirroringMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2),
                     delete(3)
                  }
         MAX-ACCESS  read-write
         STATUS      obsolete
         DESCRIPTION
                     "Port mirroring mode:

                     enable - enable mirroring mode
                     disable - disable mirroring mode
                     delete - clear MirroredPort and Probe Port configuration"
         DEFVAL { disable }
         ::= { agentPortMirroringGroup 3 }

    --**************************************************************************************
    --    agentPortMirroringGroup
    --**************************************************************************************

    agentPortMirrorTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentPortMirrorEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "This table includes entries for each Port Mirroring session."
         ::= { agentPortMirroringGroup 4 }

    agentPortMirrorEntry OBJECT-TYPE
         SYNTAX      AgentPortMirrorEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Provides configuration of a Port Mirroring session specifying the
                     destination port, and the source Port Mask, providing a many-to-one
                     mapping."
         INDEX       { agentPortMirrorSessionNum }
         ::= { agentPortMirrorTable 1 }

    AgentPortMirrorEntry ::= SEQUENCE {
         agentPortMirrorSessionNum
              Unsigned32,
         agentPortMirrorDestinationPort
              Unsigned32,
         agentPortMirrorSourcePortMask
              AgentPortMask,
         agentPortMirrorAdminMode
              INTEGER
         }

    agentPortMirrorSessionNum OBJECT-TYPE
         SYNTAX      Unsigned32
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "The Session number of this mirroring entry.  The number of sessions is
                     fixed, and is platform dependant."
         ::= { agentPortMirrorEntry 1 }

    agentPortMirrorDestinationPort OBJECT-TYPE
         SYNTAX      Unsigned32
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The port which traffic from the mirrored ports will be sent to."
         ::= { agentPortMirrorEntry 2 }

    agentPortMirrorSourcePortMask OBJECT-TYPE
         SYNTAX      AgentPortMask
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The ports from which traffic will be sent to the destination port.
                     The destination port can not be included in this list of ports."
         ::= { agentPortMirrorEntry 3 }

    agentPortMirrorAdminMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2),
                     delete(3)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The status of this port mirroring session.

                     enable(1)  - This session is active and all traffic from the source ports
                                  will be mirrored to the destination port.
                     disable(2) - This session is not active.
                     delete(3)  - Remove the configuration for this Session"
         ::= { agentPortMirrorEntry 4 }

agentPortMirrorTypeTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentPortMirrorTypeEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "This table includes entries for each source port's direction of port mirroring."
         ::= { agentPortMirroringGroup 5 }

    agentPortMirrorTypeEntry OBJECT-TYPE
         SYNTAX      AgentPortMirrorTypeEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Provides configuration of a Port Mirroring direction specifying the
                     session of the port mirroring and source port"
         INDEX       { agentPortMirrorSessionNum, agentPortMirrorTypeSourcePort }
         ::= { agentPortMirrorTypeTable 1 }
    AgentPortMirrorTypeEntry ::= SEQUENCE {
         agentPortMirrorTypeSourcePort
              Unsigned32,
         agentPortMirrorTypeType
              INTEGER
         }

    agentPortMirrorTypeSourcePort OBJECT-TYPE
         SYNTAX      Unsigned32
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "The port from which traffic will be sent to the destination port.
                      This port should be a source port in the corresponding session"
         ::= { agentPortMirrorTypeEntry 1 }

    agentPortMirrorTypeType OBJECT-TYPE
         SYNTAX      INTEGER {
                     tx(1),
                     rx(2),
                     txrx(3)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The direction of the data to be mirrored on this source port.

                     tx(1)  - The data that is transmitted from the source port.
                     rx(2) - The data that is received on the source port.
                     txrx(3)  - The data that is transmitted/received from/on the source port"
         DEFVAL { txrx }
         ::= { agentPortMirrorTypeEntry 2 }


    --**************************************************************************************
    -- agentDot3adAggPortTable
    --**************************************************************************************

    agentDot3adAggPortTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentDot3adAggPortEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "This table provides 802.3ad link aggregation information for each
                      physical port that is not available through the standard MIB."
         ::= { agentConfigGroup 12 }

    agentDot3adAggPortEntry OBJECT-TYPE
         SYNTAX      AgentDot3adAggPortEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Information about a table entry.  The agentDot3adAggPort identifies
                      the external interface number of the port."
         INDEX       { agentDot3adAggPort }
         ::= { agentDot3adAggPortTable 1 }

    AgentDot3adAggPortEntry ::= SEQUENCE {
         agentDot3adAggPort
              Integer32,
         agentDot3adAggPortLACPMode
              INTEGER
         }

    agentDot3adAggPort OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "ifIndex of this physical port"
         ::= { agentDot3adAggPortEntry 1 }

    agentDot3adAggPortLACPMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Enable/disable 802.3ad LACP on this port"
         ::= { agentDot3adAggPortEntry 2 }

    --**************************************************************************************
    -- agentPortConfigTable
    --
    --**************************************************************************************

    agentPortConfigTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentPortConfigEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "A table of the switch's physical port config entries"
         ::= { agentConfigGroup 13 }

    agentPortConfigEntry OBJECT-TYPE
         SYNTAX      AgentPortConfigEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Switch's physical port config entry"
         INDEX       { agentPortDot1dBasePort }
         ::= { agentPortConfigTable 1 }

    AgentPortConfigEntry ::= SEQUENCE {
          agentPortDot1dBasePort
              Integer32,
          agentPortIfIndex
              Integer32,
          agentPortIanaType
              IANAifType,
          agentPortSTPMode
              INTEGER,
          agentPortSTPState
              INTEGER,
          agentPortAdminMode
              INTEGER,
          agentPortPhysicalMode
              INTEGER,
          agentPortPhysicalStatus
              INTEGER,
          agentPortLinkTrapMode
              INTEGER,
          agentPortClearStats
              INTEGER,
          agentPortDefaultType
              OBJECT IDENTIFIER,
          agentPortType
              OBJECT IDENTIFIER,
          agentPortAutoNegAdminStatus
              INTEGER,
          agentPortDot3FlowControlMode
              INTEGER,
          agentPortMaxFrameSizeLimit
              Integer32,
          agentPortMaxFrameSize
              Integer32,
          agentPortBroadcastControlMode
              INTEGER,
          agentPortBroadcastControlThreshold
              Integer32,
          agentPortMulticastControlMode
              INTEGER,
          agentPortMulticastControlThreshold
              Integer32,
          agentPortUnicastControlMode
              INTEGER,
          agentPortUnicastControlThreshold
              Integer32

          }

    agentPortDot1dBasePort OBJECT-TYPE
         SYNTAX  Integer32 (1..65535)
         MAX-ACCESS  read-only
         STATUS  current
         DESCRIPTION
                 "The port number of this port."
         ::= { agentPortConfigEntry 1 }

    agentPortIfIndex OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The switch's Port IfIndex"
         ::= { agentPortConfigEntry 2 }

    agentPortIanaType OBJECT-TYPE
         SYNTAX      IANAifType
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The switch's Port Type"
         ::= { agentPortConfigEntry 3 }

    agentPortSTPMode OBJECT-TYPE
         SYNTAX      INTEGER {
                      dot1d(1),
                      fast(2),
                      off(3)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch's Port Spanning Tree Protocol Mode
                      STP mode values are:

                      dot1d (the default)
                      fast, indicates you want to use the fast spanning tree mode
                      off, indicates the STP mode is turned off for a particular port
                      This object is only supported when the Dot1d Protocol is enabled."
         ::= { agentPortConfigEntry 4 }

    agentPortSTPState OBJECT-TYPE
         SYNTAX      INTEGER {
                      blocking(1),
                      listening(2),
                      learning(3),
                      forwarding(4),
                      disabled(5)
                     }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The switch's Port Spanning Tree Protocol State.
                      This object is only supported when the Dot1d Protocol is enabled."
         ::= { agentPortConfigEntry 5 }

    agentPortAdminMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                 }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The switch's Port Admin Mode"
         ::= { agentPortConfigEntry 6 }

    agentPortPhysicalMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     auto-negotiate(1),
                     half-10(2),
                     full-10(3),
                     half-100(4),
                     full-100(5),
                     half-100fx(6),
                     full-100fx(7),
                     full-1000sx(8),
                     full-10gsx(9)
                  }
         MAX-ACCESS  read-write
         STATUS      obsolete
         DESCRIPTION
                     "The switch's Port Speed Mode.  This is the configured physical mode.
                      This object is read-only for gigabit ports"
         ::= { agentPortConfigEntry 7 }

    agentPortPhysicalStatus OBJECT-TYPE
         SYNTAX      INTEGER {
                     auto-negotiate(1),
                     half-10(2),
                     full-10(3),
                     half-100(4),
                     full-100(5),
                     half-100fx(6),
                     full-100fx(7),
                     full-1000sx(8),
                     full-10gsx(9)
                  }
         MAX-ACCESS  read-only
         STATUS      obsolete
         DESCRIPTION
                     "The switch's Port Physical Speed Status.  This is the current actual speed."
         ::= { agentPortConfigEntry 8 }

    agentPortLinkTrapMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "If enabled, link up and link down traps will be sent for this port."
         ::= { agentPortConfigEntry 9 }

     agentPortClearStats OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "clear stats for this port only"
         ::= { agentPortConfigEntry 10 }

    agentPortDefaultType OBJECT-TYPE
         SYNTAX  OBJECT IDENTIFIER
         MAX-ACCESS  read-write
         STATUS  current
         DESCRIPTION
                 "This object identifies the default administrative port type,
                  to be used in conjunction with the operational port type
                  denoted by agentPortType.

                  The set of possible values for this object is
                  the same as the set defined for the agentPortType
                  object.

                  This object represents the administratively-configured type of
                  the MAU.  If auto-negotiation is not enabled or is not
                  implemented for this MAU, the value of this object determines
                  the operational type of the MAU.  In this case, a set to this
                  object will force the MAU into the specified operating mode.

                  If auto-negotiation is implemented and enabled for this MAU,
                  the operational type of the MAU is determined by auto-negotiation,
                  and the value of this object denotes the type to which the MAU
                  will automatically revert if/when auto-negotiation is later disabled.

                  The valid values for this object are:

                       dot3MauType10BaseTHD
                       dot3MauType10BaseTFD
                       dot3MauType100BaseTXHD
                       dot3MauType100BaseTXFD
                       dot3MauType100BaseFXFD
                       dot3MauType10GBaseSX"
         REFERENCE "RFC 2668"
         ::= { agentPortConfigEntry 11 }

    agentPortType OBJECT-TYPE
         SYNTAX  OBJECT IDENTIFIER
         MAX-ACCESS  read-only
         STATUS  current
         DESCRIPTION
                 "This object identifies the port type.  An initial set of MAU types
                  are defined in RFC 2668.  The assignment of OBJECT IDENTIFIERs to
                  new types of MAUs is managed by the IANA.  If the MAU type is
                  unknown, the object identifier

                     unknownMauType OBJECT IDENTIFIER ::= { 0 0 }

                  is returned.  Note that unknownMauType is a syntactically valid
                  object identifier, and any conformant implementation of ASN.1 and
                  the BER must be able to generate and recognize this value.

                  This object represents the operational type of the MAU, as determined
                  by either (1) the result of the auto-negotiation function or (2) if
                  auto-negotiation is not enabled or is not implemented for this MAU,
                  by the value of the object agentPortDefaultType, or (3) for the GigE card
                  a value determined by the GBIC connected to the card.  In case (2), a
                  set to the object agentPortDefaultType will force the MAU into the
                  new operating mode.

                  The valid values for this object are:

                       dot3MauType10BaseTHD
                       dot3MauType10BaseTFD
                       dot3MauType100BaseTXHD
                       dot3MauType100BaseTXFD
                       dot3MauType100BaseFXFD
                       dot3MauType1000BaseSXFD
                       dot3MauType10GBaseSX"
         REFERENCE "RFC 2668"
         ::= { agentPortConfigEntry 12 }

    agentPortAutoNegAdminStatus OBJECT-TYPE
         SYNTAX  INTEGER {
                    enable(1),
                    disable(2)
                 }
         MAX-ACCESS  read-write
         STATUS  current
         DESCRIPTION
                 "This object identifies the administration status of auto negotiation
                  for this port."
         ::= { agentPortConfigEntry 13 }

    agentPortDot3FlowControlMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Config flowcontrol allows you to enable or disable
                     802.3x flow control for this port. This value
                     applies to only full-duplex mode ports. "
         ::= { agentPortConfigEntry 14 }

    agentPortMaxFrameSizeLimit OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "This object identifies the largest value that can be
                     configured for agentPortMaxFrameSize"
         ::= { agentPortConfigEntry 18 }

    agentPortMaxFrameSize OBJECT-TYPE
         SYNTAX      Integer32
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This object identifies the currently configured maximum frame size
                     value for this port. The maximmum value that this object can be set
                     to is the value of agentPortMaxFrameSizeLimit.  For Ethernet ports
                     which support 802.1Q vlan tagging, the minimum value that this object
                     can be set to is 1522"
         ::= { agentPortConfigEntry 19 }


    agentPortBroadcastControlMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "If enabled, broadcast storm recovery will function on this port.
                     When you specify Enable for Broadcast Storm Recovery and the broadcast
                     traffic on this Ethernet port exceeds the configured threshold, the
                     switch blocks (discards) the broadcast traffic."
         ::= { agentPortConfigEntry 20 }

    agentPortBroadcastControlThreshold OBJECT-TYPE
         SYNTAX      Integer32 (0..100)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Configures the broadcast storm recovery threshold for this port
                     as a percentage of port speed."
         ::= { agentPortConfigEntry 21 }

    agentPortMulticastControlMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "If enabled, multicast storm recovery will function on this port.
                     When you specify Enable for Multicast Storm Recovery and the multicast
                     traffic on this Ethernet port exceeds the configured threshold, the
                     switch blocks (discards) the multicast traffic."
         ::= { agentPortConfigEntry 22 }

    agentPortMulticastControlThreshold OBJECT-TYPE
         SYNTAX      Integer32 (0..100)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Configures the multicast storm recovery threshold for this port
                     as a percentage of port speed."
         ::= { agentPortConfigEntry 23 }

    agentPortUnicastControlMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "If enabled, unicast storm recovery will function on this port.
                     When you specify Enable for Unicast Storm Recovery and the unknown unicast
                     traffic on this Ethernet port exceeds the configured threshold, the
                     switch blocks (discards) the unknown unicast traffic."
         ::= { agentPortConfigEntry 24 }

    agentPortUnicastControlThreshold OBJECT-TYPE
         SYNTAX      Integer32 (0..100)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Configures the unicast storm recovery threshold for this port
                     as a percentage of port speed."
         ::= { agentPortConfigEntry 25 }

    --**************************************************************************************
    --    agentStpSwitchConfigGroup
    --**************************************************************************************

    agentStpSwitchConfigGroup                     OBJECT IDENTIFIER ::= { agentConfigGroup 15 }

    agentStpConfigDigestKey OBJECT-TYPE
         SYNTAX      OCTET STRING (SIZE(16))
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MST configuration digest key."
         ::= { agentStpSwitchConfigGroup 1 }

    agentStpConfigFormatSelector OBJECT-TYPE
         SYNTAX      Unsigned32 (0..255)
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MST configuration format selector. A value other than 0 (zero)
                     indicates non-support for the IEEE 802.1s standard."
         ::= { agentStpSwitchConfigGroup 2 }

    agentStpConfigName OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(1..32))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The MST configuration name of at most 32 characters."
         ::= { agentStpSwitchConfigGroup 3 }

    agentStpConfigRevision OBJECT-TYPE
         SYNTAX      Unsigned32 (0..65535)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The MST configuration revision. The default value is 1."
         DEFVAL { 1 }
         ::= { agentStpSwitchConfigGroup 4 }

    agentStpForceVersion OBJECT-TYPE
         SYNTAX      INTEGER {
                     dot1d(1),
                     dot1w(2),
                     dot1s(3)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The MST configuration force protocol version. The default version is dot1s."
         ::= { agentStpSwitchConfigGroup 5 }

    agentStpAdminMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The spanning tree operational status.

                     enable(1)  - enables spanning tree operational status on the switch.
                     disable(2) - disables spanning tree operational status on the switch.

                     The default status is disabled."
         ::= { agentStpSwitchConfigGroup 6 }

    agentStpBpduGuardMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The spanning tree BPDU Guard Mode.

                     enable(1)  - enables BPDU Guard Mode on the switch.
                     disable(2) - disables BPDU Guard Mode on the switch.

                     The default status is disabled."
         ::= { agentStpSwitchConfigGroup 7 }


    agentStpBpduFilterDefault OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The spanning tree BPDU Filter Mode, it enables BPDU Filter on all
                     edge ports.

                     enable(1)  - enables BPDU Filter Mode on the switch.
                     disable(2) - disables BPDU Filter Mode on the switch.

                     The default status is disabled."
         ::= { agentStpSwitchConfigGroup 8 }


    --**************************************************************************************
    -- agentStpPortTable
    --
    --**************************************************************************************

    agentStpPortTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentStpPortEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "STP port table."
         ::= { agentStpSwitchConfigGroup 9 }

    agentStpPortEntry OBJECT-TYPE
         SYNTAX      AgentStpPortEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "STP port entry."
         INDEX       { ifIndex }
         ::= { agentStpPortTable 1 }

    AgentStpPortEntry ::=
         SEQUENCE {
         agentStpPortState
                 INTEGER,
         agentStpPortStatsMstpBpduRx
                 Counter32,
         agentStpPortStatsMstpBpduTx
                 Counter32,
         agentStpPortStatsRstpBpduRx
                 Counter32,
         agentStpPortStatsRstpBpduTx
                 Counter32,
         agentStpPortStatsStpBpduRx
                 Counter32,
         agentStpPortStatsStpBpduTx
                 Counter32,
         agentStpPortUpTime
                 TimeTicks,
         agentStpPortMigrationCheck
                 INTEGER,
         agentStpPortHelloTime
                 Unsigned32
         }

    agentStpPortState OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The administrative STP state for the port.

                     enable(1)  - enables STP on the port.
                     disable(2) - disables STP on the port.

                     The default port STP state is enabled for the first 4095
                     ports and disabled for any ports beyond."
         ::= { agentStpPortEntry 1 }

    agentStpPortStatsMstpBpduRx OBJECT-TYPE
         SYNTAX      Counter32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP BPDUs received on a specific port."
         ::= { agentStpPortEntry 2 }

    agentStpPortStatsMstpBpduTx OBJECT-TYPE
         SYNTAX      Counter32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP BPDUs sent from a specific port."
         ::= { agentStpPortEntry 3 }

    agentStpPortStatsRstpBpduRx OBJECT-TYPE
         SYNTAX      Counter32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The RSTP BPDUs received on a specific port."
         ::= { agentStpPortEntry 4 }

    agentStpPortStatsRstpBpduTx OBJECT-TYPE
         SYNTAX      Counter32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The RSTP BPDUs sent from a specific port."
         ::= { agentStpPortEntry 5 }

    agentStpPortStatsStpBpduRx OBJECT-TYPE
         SYNTAX      Counter32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The STP BPDUs received on a specific port."
         ::= { agentStpPortEntry 6 }

    agentStpPortStatsStpBpduTx OBJECT-TYPE
         SYNTAX      Counter32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The STP BPDUs sent from a specific port."
         ::= { agentStpPortEntry 7 }

    agentStpPortUpTime OBJECT-TYPE
         SYNTAX      TimeTicks
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Time since port was reset.
                     It is displayed in days, hours, minutes, and seconds."
         ::= { agentStpPortEntry 8 }

    agentStpPortMigrationCheck OBJECT-TYPE
         SYNTAX      INTEGER {
                     false(0),
                     true(1)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Force the specified port to transmit RSTP or MSTP BPDU's.

                     Supported values:
                     false(0) - BPDUs are not to be transmitted.
                     true(1)  - BPDUs are to be transmitted

                     A non-zero value indicates that BPDUs are to be sent on the specified port."
         ::= { agentStpPortEntry 9 }

    agentStpPortHelloTime OBJECT-TYPE
         SYNTAX      Unsigned32 (1..10)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Hello time for the STP port. The default value is 2."
         DEFVAL { 2 }
         ::= { agentStpPortEntry 10 }

    --**************************************************************************************
    -- agentStpCstConfigGroup
    --
    --**************************************************************************************

    agentStpCstConfigGroup                      OBJECT IDENTIFIER ::= { agentStpSwitchConfigGroup 10 }

    agentStpCstHelloTime OBJECT-TYPE
         SYNTAX      Unsigned32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP root port hello time for the CIST."
         ::= { agentStpCstConfigGroup 1 }

    agentStpCstMaxAge OBJECT-TYPE
         SYNTAX      Unsigned32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP root port max age for the CIST."
         ::= { agentStpCstConfigGroup 2 }

    agentStpCstRegionalRootId OBJECT-TYPE
         SYNTAX      OCTET STRING (SIZE(8))
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP regional root identifier for the CIST."
         ::= { agentStpCstConfigGroup 3 }

    agentStpCstRegionalRootPathCost OBJECT-TYPE
         SYNTAX      Unsigned32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP regional root path cost for the CIST."
         ::= { agentStpCstConfigGroup 4 }

    agentStpCstRootFwdDelay OBJECT-TYPE
         SYNTAX      Unsigned32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP root port forward delay for the CIST."
         ::= { agentStpCstConfigGroup 5 }

    agentStpCstBridgeFwdDelay OBJECT-TYPE
         SYNTAX      Unsigned32 (4..30)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The MSTP bridge forward delay for the CIST. The default value is 15."
         DEFVAL { 15 }
         ::= { agentStpCstConfigGroup 6 }

    agentStpCstBridgeHelloTime OBJECT-TYPE
         SYNTAX      Unsigned32 (1..10)
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP bridge hello time for the CIST. The default 
                      value is 2. According to IEEE 802.1Q-REV 2005 updating
                      hello time is disallowed"
         DEFVAL { 2 }
         ::= { agentStpCstConfigGroup 7 }

    agentStpCstBridgeHoldTime OBJECT-TYPE
         SYNTAX      Unsigned32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP bridge hold time for the CIST."
         ::= { agentStpCstConfigGroup 8 }

    agentStpCstBridgeMaxAge OBJECT-TYPE
         SYNTAX      Unsigned32 (6..40)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The MSTP bridge max age for the CIST. The default value is 20."
         DEFVAL { 20 }
         ::= { agentStpCstConfigGroup 9 }

    agentStpCstBridgeMaxHops OBJECT-TYPE
         SYNTAX      Unsigned32 (1..127)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The MSTP bridge max hops for the CIST. The default value is 20."
         DEFVAL { 20 }
         ::= { agentStpCstConfigGroup 10 }

    agentStpCstBridgePriority OBJECT-TYPE
         SYNTAX      Unsigned32 (0..61440)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The MSTP bridge priority for the CIST. The default value is 32768."
         DEFVAL { 32768 }
         ::= { agentStpCstConfigGroup 11 }

     agentStpCstBridgeHoldCount OBJECT-TYPE
         SYNTAX      Unsigned32 (1..10)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The MSTP bridge hold count for the CIST. This command sets the value of
                      maximum bpdus that a bridge is allowed to send within a hello time window.
                      The default value is 6."
         DEFVAL { 6 }
         ::= { agentStpCstConfigGroup 12 }

     agentStpCstTimeSinceTopologyChange OBJECT-TYPE
         SYNTAX      TimeTicks
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP time since the last topology change for the CIST."
         ::= { agentStpCstConfigGroup 13 }

     agentStpCstTopologyChangeCount OBJECT-TYPE
         SYNTAX      Counter32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP count of topology changes for the CIST."
         ::= { agentStpCstConfigGroup 14 }

    --**************************************************************************************
    -- agentStpCstPortTable
    --
    --**************************************************************************************

    agentStpCstPortTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentStpCstPortEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "CIST port table."
         ::= { agentStpSwitchConfigGroup 11 }

    agentStpCstPortEntry OBJECT-TYPE
         SYNTAX      AgentStpCstPortEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "CIST port entry."
         INDEX       { ifIndex }
         ::= { agentStpCstPortTable 1 }

    AgentStpCstPortEntry ::=
         SEQUENCE {
         agentStpCstPortOperEdge
                 INTEGER,
         agentStpCstPortOperPointToPoint
                 INTEGER,
         agentStpCstPortTopologyChangeAck
                 INTEGER,
         agentStpCstPortEdge
                 INTEGER,
         agentStpCstPortForwardingState
                 INTEGER,
         agentStpCstPortId
                 OCTET STRING,
         agentStpCstPortPathCost
                 Unsigned32,
         agentStpCstPortPriority
                 Unsigned32,
         agentStpCstDesignatedBridgeId
                 OCTET STRING,
         agentStpCstDesignatedCost
                 Unsigned32,
         agentStpCstDesignatedPortId
                 OCTET STRING,
         agentStpCstExtPortPathCost
                 Unsigned32,
         agentStpCstPortBpduGuardEffect
                 INTEGER,
         agentStpCstPortBpduFilter
                 INTEGER,
         agentStpCstPortBpduFlood
                 INTEGER,
         agentStpCstPortAutoEdge
                 INTEGER,
         agentStpCstPortRootGuard
                 INTEGER,
         agentStpCstPortTCNGuard
                 INTEGER
         }

    agentStpCstPortOperEdge OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                     }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP operational status of a specific port for the CIST."
         ::= { agentStpCstPortEntry 1 }

    agentStpCstPortOperPointToPoint OBJECT-TYPE
         SYNTAX      INTEGER {
                     true(1),
                     false(2)
                     }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP operational point to point mac of a specific port for the CIST."
         ::= { agentStpCstPortEntry 2 }

    agentStpCstPortTopologyChangeAck OBJECT-TYPE
         SYNTAX      INTEGER {
                     true(1),
                     false(2)
                     }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP topology change acknowledge for a specific port in the CIST."
         ::= { agentStpCstPortEntry 3 }

    agentStpCstPortEdge OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The administrative state of a specific port in CIST.

                     enable(1)  - enables the port.
                     disable(2) - disables the port.

                     The default port state is disabled."
         ::= { agentStpCstPortEntry 4 }

    agentStpCstPortForwardingState OBJECT-TYPE
         SYNTAX      INTEGER {
                     discarding(1),
                     learning(2),
                     forwarding(3),
                     disabled(4),
                     manualFwd(5),
                     notParticipate(6)
                     }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP forwarding state of a specific port in CIST."
         ::= { agentStpCstPortEntry 5 }

    agentStpCstPortId OBJECT-TYPE
         SYNTAX      OCTET STRING (SIZE(2))
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP port identifier of a specific port in CIST."
         ::= { agentStpCstPortEntry 6 }

    agentStpCstPortPathCost OBJECT-TYPE
         SYNTAX      Unsigned32 (0..*********)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The MSTP port path cost in CIST. The default value will
                     correspond to the recommendation specified in IEEE 802.1s Table 13-2
                     which varies depending upon link speed."
         ::= { agentStpCstPortEntry 7 }

    agentStpCstPortPriority OBJECT-TYPE
         SYNTAX      Unsigned32 (0..240)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The MSTP port priority in CIST. The priority is
                     in the increments of 16. The default value is 128."
         DEFVAL { 128 }
         ::= { agentStpCstPortEntry 8 }

    agentStpCstDesignatedBridgeId OBJECT-TYPE
         SYNTAX      OCTET STRING (SIZE(8))
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP designated bridge ID of a specific port in CIST."
         ::= { agentStpCstPortEntry 9 }

    agentStpCstDesignatedCost OBJECT-TYPE
         SYNTAX      Unsigned32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP designated cost of a specific port in CIST."
         ::= { agentStpCstPortEntry 10 }

    agentStpCstDesignatedPortId OBJECT-TYPE
         SYNTAX      OCTET STRING (SIZE(2))
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP designated port ID of a specific port in CIST."
         ::= { agentStpCstPortEntry 11 }

    agentStpCstExtPortPathCost OBJECT-TYPE
         SYNTAX      Unsigned32 (0..*********)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The MSTP external port path cost in CIST. The default value varies depending upon the link speed."
         ::= { agentStpCstPortEntry 12 }

    agentStpCstPortBpduGuardEffect OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                     }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "BPDU Guard Mode effect on the port.
                      
                     enable(1)  - BPDU Guard Mode is enabled on the port.
                     disable(2) - BPDU Guard Mode is disabled on the port."
         ::= { agentStpCstPortEntry 13 }
    
    agentStpCstPortBpduFilter OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This command sets BPDU Filter mode on the port. 
                      
                     enable(1)  - BPDU Filter Mode is enabled on the port.
                     disable(2) - BPDU Filter Mode is disabled on the port."
         ::= { agentStpCstPortEntry 14 }

    agentStpCstPortBpduFlood OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This command sets BPDU Flood mode on the port. 
                      
                     enable(1)  - BPDU Flood Mode is enabled on the port.
                     disable(2) - BPDU Flood Mode is disabled on the port."
         ::= { agentStpCstPortEntry 15 }
        
    agentStpCstPortAutoEdge OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This command sets the auto-edge mode of the port which enables it
                      to become an edge port if it does not see BPDUs for some duration.

                     enable(1)  - enables the auto-edge mode for the port.
                     disable(2) - disables the auto-edge mode for the port.

                     The default auto-edge mode is disabled."
         ::= { agentStpCstPortEntry 16 }

    agentStpCstPortRootGuard OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This command sets a port to discard any superior information
                      received by the port and thus protect against root of the device
                      from changing.The port gets put into discarding state and does
                      not forward any packets.

                     enable(1)  - enables the root-guard mode for the port.
                     disable(2) - disables the root-guard mode for the port.

                     The default root-guard mode is disabled."
         ::= { agentStpCstPortEntry 17 }

    agentStpCstPortTCNGuard OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "This command restricts the port from propagating any topology change
                      information received through that port.

                     enable(1)  - enables the tcn-guard mode for the port.
                     disable(2) - disables the tcn-guard mode for the port.

                     The default tcn-guard mode is disabled."
         ::= { agentStpCstPortEntry 18 }

    --**************************************************************************************
    -- agentStpMstTable
    --
    --**************************************************************************************

    agentStpMstTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentStpMstEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "MST table."
         ::= { agentStpSwitchConfigGroup 12 }

    agentStpMstEntry OBJECT-TYPE
         SYNTAX      AgentStpMstEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "MST entry."
         INDEX       { agentStpMstId }
         ::= { agentStpMstTable 1 }

    AgentStpMstEntry ::=
         SEQUENCE {
         agentStpMstId
                 Unsigned32,
         agentStpMstBridgePriority
                 Unsigned32,
         agentStpMstBridgeIdentifier
                 OCTET STRING,
         agentStpMstDesignatedRootId
                 OCTET STRING,
         agentStpMstRootPathCost
                 Unsigned32,
         agentStpMstRootPortId
                 OCTET STRING,
         agentStpMstTimeSinceTopologyChange
                 TimeTicks,
         agentStpMstTopologyChangeCount
                 Counter32,
         agentStpMstTopologyChangeParm
                 INTEGER,
         agentStpMstRowStatus
                 RowStatus
         }

    agentStpMstId OBJECT-TYPE
         SYNTAX      Unsigned32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP instance ID."
         ::= { agentStpMstEntry 1 }

    agentStpMstBridgePriority OBJECT-TYPE
         SYNTAX      Unsigned32 (0..61440)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The MSTP bridge priority in a specific instance. The priority is
                     in the increments of 4096. The recommended default value is 32768."
         ::= { agentStpMstEntry 2 }

    agentStpMstBridgeIdentifier OBJECT-TYPE
         SYNTAX      OCTET STRING (SIZE(8))
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP bridge identifier in a specific instance."
         ::= { agentStpMstEntry 3 }

    agentStpMstDesignatedRootId OBJECT-TYPE
         SYNTAX      OCTET STRING (SIZE(8))
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP designated root bridge identifier in a specific instance."
         ::= { agentStpMstEntry 4 }

    agentStpMstRootPathCost OBJECT-TYPE
         SYNTAX      Unsigned32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP root path cost in a specific instance."
         ::= { agentStpMstEntry 5 }

    agentStpMstRootPortId OBJECT-TYPE
         SYNTAX      OCTET STRING (SIZE(8))
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP root port ID in a specific instance."
         ::= { agentStpMstEntry 6 }

    agentStpMstTimeSinceTopologyChange OBJECT-TYPE
         SYNTAX      TimeTicks
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP time since the last topology change in a specific instance."
         ::= { agentStpMstEntry 7 }

    agentStpMstTopologyChangeCount OBJECT-TYPE
         SYNTAX      Counter32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP count of topology changes in a specific instance."
         ::= { agentStpMstEntry 8 }

    agentStpMstTopologyChangeParm OBJECT-TYPE
         SYNTAX      INTEGER {
                     true(1),
                     false(2)
                     }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP topology change parameter in a specific instance."
         ::= { agentStpMstEntry 9 }

    agentStpMstRowStatus OBJECT-TYPE
         SYNTAX      RowStatus
         MAX-ACCESS  read-create
         STATUS      current
         DESCRIPTION
                     "The MSTP instance status.

                     Supported values:
                     active(1)      - valid instance.
                     createAndGo(4) - used to create a new instance.
                     destroy(6)     - removes an instance."
         ::= { agentStpMstEntry 10 }

    --**************************************************************************************
    -- agentStpMstPortTable
    --
    --**************************************************************************************

    agentStpMstPortTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentStpMstPortEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "MST port table."
         ::= { agentStpSwitchConfigGroup 13 }

    agentStpMstPortEntry OBJECT-TYPE
         SYNTAX      AgentStpMstPortEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "MST port entry."
         INDEX       { agentStpMstId, ifIndex }
         ::= { agentStpMstPortTable 1 }

    AgentStpMstPortEntry ::=
         SEQUENCE {
         agentStpMstPortForwardingState
                 INTEGER,
         agentStpMstPortId
                 OCTET STRING,
         agentStpMstPortPathCost
                 Unsigned32,
         agentStpMstPortPriority
                 Unsigned32,
         agentStpMstDesignatedBridgeId
                 OCTET STRING,
         agentStpMstDesignatedCost
                 Unsigned32,
         agentStpMstDesignatedPortId
                 OCTET STRING
         }

    agentStpMstPortForwardingState OBJECT-TYPE
         SYNTAX      INTEGER {
                     discarding(1),
                     learning(2),
                     forwarding(3),
                     disabled(4),
                     manualFwd(5),
                     notParticipate(6)
                     }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP forwarding state of a specific port in a specific instance."
         ::= { agentStpMstPortEntry 1 }

    agentStpMstPortId OBJECT-TYPE
         SYNTAX      OCTET STRING (SIZE(4))
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP port identifier of a specific port in a specific instance."
         ::= { agentStpMstPortEntry 2 }

    agentStpMstPortPathCost OBJECT-TYPE
         SYNTAX      Unsigned32 (0..*********)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The MSTP port path cost in a specific instance. The default value will
                     correspond to the recommendation specified in IEEE 802.1s Table 13-2
                     which varies depending upon link speed."
         ::= { agentStpMstPortEntry 3 }

    agentStpMstPortPriority OBJECT-TYPE
         SYNTAX      Unsigned32 (0..240)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The MSTP port priority in a specific instance. The priority is
                     in the increments of 16. The default value is 128."
         DEFVAL { 128 }
         ::= { agentStpMstPortEntry 4 }

    agentStpMstDesignatedBridgeId OBJECT-TYPE
         SYNTAX      OCTET STRING (SIZE(8))
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP designated bridge ID of a specific port in a specific instance."
         ::= { agentStpMstPortEntry 5 }

    agentStpMstDesignatedCost OBJECT-TYPE
         SYNTAX      Unsigned32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP designated cost of a specific port in a specific instance."
         ::= { agentStpMstPortEntry 6 }

    agentStpMstDesignatedPortId OBJECT-TYPE
         SYNTAX      OCTET STRING (SIZE(2))
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The MSTP designated port ID of a specific port in a specific instance."
         ::= { agentStpMstPortEntry 7 }

    --**************************************************************************************
    -- agentStpMstVlanTable
    --
    --**************************************************************************************

    agentStpMstVlanTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentStpMstVlanEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "MST VLAN table."
         ::= { agentStpSwitchConfigGroup 14 }

    agentStpMstVlanEntry OBJECT-TYPE
         SYNTAX      AgentStpMstVlanEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "MST VLAN entry."
         INDEX       { agentStpMstId, dot1qVlanIndex }
         ::= { agentStpMstVlanTable 1 }

    AgentStpMstVlanEntry ::=
         SEQUENCE {
         agentStpMstVlanRowStatus
                 RowStatus
         }

    agentStpMstVlanRowStatus OBJECT-TYPE
         SYNTAX      RowStatus
         MAX-ACCESS  read-create
         STATUS      current
         DESCRIPTION
                     "The association status of an MSTP instance and a VLAN.

                     Supported values:
                     active(1)      - valid association between an MSTP instance and a VLAN.
                     createAndGo(4) - used to create a new association between an MSTP instance and a VLAN.
                     destroy(6)     - removes the association between an MSTP instance and a VLAN."
         ::= { agentStpMstVlanEntry 1 }

--**************************************************************************************
--    agentAuthenticationGroup
--**************************************************************************************
     agentAuthenticationGroup                     OBJECT IDENTIFIER ::= { agentConfigGroup 16 }

     agentAuthenticationListCreate OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(0|1..15))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Authentication List Create
                      If set to a non empty string, creates a new Authentication List for
                      configuration."

         ::= { agentAuthenticationGroup 1 }

    --**************************************************************************************

    agentAuthenticationListTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentAuthenticationListEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "MST VLAN table."
         ::= { agentAuthenticationGroup 2 }

    agentAuthenticationListEntry OBJECT-TYPE
         SYNTAX      AgentAuthenticationListEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "MST VLAN entry."
         INDEX       { agentAuthenticationListIndex }
         ::= { agentAuthenticationListTable 1 }

    AgentAuthenticationListEntry ::=
         SEQUENCE {
         agentAuthenticationListIndex
                 Unsigned32,
         agentAuthenticationListName
                 DisplayString,
         agentAuthenticationListMethod1
                 INTEGER,
         agentAuthenticationListMethod2
                 INTEGER,
         agentAuthenticationListMethod3
                 INTEGER,
         agentAuthenticationListStatus
                 RowStatus,
         agentAuthenticationListMethod4
                 INTEGER
         }

    agentAuthenticationListIndex OBJECT-TYPE
         SYNTAX      Unsigned32
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "Authenticaiton List Index
                     Unique number used for indexing into this table."

         ::= { agentAuthenticationListEntry 1 }

    agentAuthenticationListName OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(1..15))
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Authenticaiton List Name
                     Unique name used to identify the Authentication List from other tables."

         ::= { agentAuthenticationListEntry 2 }

    agentAuthenticationListMethod1 OBJECT-TYPE
         SYNTAX      INTEGER {
                     local(1),
                     radius(2),
                     reject(3),
                     tacacs(4)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Authenticion List Method 1
                      Configures the first authentication method to use when this list is
                      specified."

         ::= { agentAuthenticationListEntry 3 }

    agentAuthenticationListMethod2 OBJECT-TYPE
         SYNTAX      INTEGER {
                     undefined(1),
                     local(2),
                     radius(3),
                     reject(4),
                     tacacs(5)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Authenticion List Method 2
                      Configures the second authentication method to use when this list is
                      specified."

         ::= { agentAuthenticationListEntry 4 }

    agentAuthenticationListMethod3 OBJECT-TYPE
         SYNTAX      INTEGER {
                     undefined(1),
                     local(2),
                     radius(3),
                     reject(4),
                     tacacs(5)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Authenticion List Method 3
                      Configures the third authentication method to use when this list is
                      specified."

         ::= { agentAuthenticationListEntry 5 }

    agentAuthenticationListStatus OBJECT-TYPE
         SYNTAX      RowStatus
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "The status of the Authentication List.

                     Supported values:
                     active(1)      - indicates a valid Authenticaiton List
                     destroy(6)     - removes the Authentication List."
         ::= { agentAuthenticationListEntry 6 }

    agentAuthenticationListMethod4 OBJECT-TYPE
         SYNTAX      INTEGER {
                     undefined(1),
                     local(2),
                     radius(3),
                     reject(4),
                     tacacs(5)
                     }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Authenticion List Method 4
                      Configures the fourth authentication method to use when this list is
                      specified."

         ::= { agentAuthenticationListEntry 7 }

    --**************************************************************************************

    agentUserConfigDefaultAuthenticationList OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(1..15))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Default Authentication List
                      This object configures which authentication list to use for users
                      which do not have an Authentication List configured.  The list
                      must be configured before setting."
         ::= { agentAuthenticationGroup 3 }

    --**************************************************************************************

    agentUserAuthenticationConfigTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentUserAuthenticationConfigEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "User Authentication Config Table"
         ::= { agentAuthenticationGroup 4 }

    agentUserAuthenticationConfigEntry OBJECT-TYPE
         SYNTAX      AgentUserAuthenticationConfigEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "User Authentication Config Entry"
         AUGMENTS { agentUserConfigEntry }
         ::= { agentUserAuthenticationConfigTable 1 }

    AgentUserAuthenticationConfigEntry ::= SEQUENCE {
                 agentUserAuthenticationList
                     DisplayString
             }

    agentUserAuthenticationList OBJECT-TYPE
         SYNTAX      DisplayString (SIZE(1..15))
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "User Authentication List
                      This object configures which authentication list to use for this
                      user.  The list must be configured before setting."
         ::= { agentUserAuthenticationConfigEntry 1 }

    --**************************************************************************************

    agentUserPortConfigTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentUserPortConfigEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "User Port Config Table"
         ::= { agentAuthenticationGroup 5 }

    agentUserPortConfigEntry OBJECT-TYPE
         SYNTAX      AgentUserPortConfigEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                     "User Port Config Entry"
         AUGMENTS { agentUserConfigEntry }
         ::= { agentUserPortConfigTable 1 }

    AgentUserPortConfigEntry ::= SEQUENCE {
                 agentUserPortSecurity
                     AgentPortMask
             }

    agentUserPortSecurity OBJECT-TYPE
         SYNTAX      AgentPortMask
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "User Port Port Security
                      This object configures which ports the user has access to.
                      Ports are indexed based on dot1dBasePort entries in
                      dot1dBasePortTable."
         ::= { agentUserPortConfigEntry 1 }


--**************************************************************************************
--    agentClassOfServiceGroup
--**************************************************************************************
     agentClassOfServiceGroup                     OBJECT IDENTIFIER ::= { agentConfigGroup 17 }

    agentClassOfServicePortTable OBJECT-TYPE
         SYNTAX      SEQUENCE OF AgentClassOfServicePortEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                    "A table that contains information about the 802.1p priority
                     mapping to traffic class priority queue for every physical port."
         ::= { agentClassOfServiceGroup 1 }

    agentClassOfServicePortEntry OBJECT-TYPE
         SYNTAX      AgentClassOfServicePortEntry
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                    "A 802.1p priority mapped to a traffic class priority queue."
         INDEX       { ifIndex, agentClassOfServicePortPriority }
         ::= { agentClassOfServicePortTable 1 }

    AgentClassOfServicePortEntry ::= SEQUENCE {
         agentClassOfServicePortPriority
              INTEGER,
         agentClassOfServicePortClass
              INTEGER
         }

    agentClassOfServicePortPriority OBJECT-TYPE
         SYNTAX      INTEGER (0..7)
         MAX-ACCESS  not-accessible
         STATUS      current
         DESCRIPTION
                    "The Priority value determined for the received frame.
                     This value is equivalent to the priority indicated in
                     the tagged frame received, or one of the evaluated
                     priorities, determined according to the media-type.

                     For untagged frames received from Ethernet media, this
                     value is equal to the dot1dPortDefaultUserPriority value
                     for the ingress port."
         ::= { agentClassOfServicePortEntry 1 }

    agentClassOfServicePortClass OBJECT-TYPE
         SYNTAX      INTEGER (0..7)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                    "Traffic class priority queue the received frame is mapped to."
         ::= { agentClassOfServicePortEntry 2 }


    --**************************************************************************************
    -- agentHTTPConfigGroup
    --
    --**************************************************************************************

    agentHTTPConfigGroup                 OBJECT IDENTIFIER ::= { agentConfigGroup 18 }

    agentHTTPWebMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Configures the HTTP server admin mode"
          ::= { agentHTTPConfigGroup 1 }

    agentHTTPJavaMode OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Configures the use of the Java interface through Web connections."
         ::= { agentHTTPConfigGroup 2 }

    agentHTTPMaxSessions OBJECT-TYPE
         SYNTAX      Integer32 (0..16)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Configures the maximum number of allowable HTTP sessions.  The default
                      value is 16."
         ::= { agentHTTPConfigGroup 3 }

    agentHTTPHardTimeout OBJECT-TYPE
         SYNTAX      Integer32 (0..168)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Configures the hard timeout for HTTP sessions in hours.  The default
                      value is 24 hours.  A value of 0 gives an infinite timeout."
         ::= { agentHTTPConfigGroup 4 }

    agentHTTPSoftTimeout OBJECT-TYPE
         SYNTAX      Integer32 (0..60)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Configures the soft (activity) timeout for HTTP sessions in minutes.
                      The default value is 5 minutes.  A value of 0 gives an infinite timeout."
         ::= { agentHTTPConfigGroup 5 }


--**************************************************************************************
--    agentSystemGroup
--**************************************************************************************

agentSystemGroup                             OBJECT IDENTIFIER ::= { agentSwitching 3 }

     agentSaveConfig OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "enable(1) will initiate an configuration save to nvram.

                     Status is returned by the object agentSaveConfigStatus."
         ::= { agentSystemGroup 1 }

     agentClearConfig OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "clear config to factory defaults"
         ::= { agentSystemGroup 2 }

     agentClearLags OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "clear lag configuration"
         ::= { agentSystemGroup 3 }

     agentClearLoginSessions OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "close all telnet sessions"
         ::= { agentSystemGroup 4 }

     agentClearPasswords OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "reset passwords"
         ::= { agentSystemGroup 5 }

     agentClearPortStats OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "clear all port statistics"
         ::= { agentSystemGroup 6 }

     agentClearSwitchStats OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "clear all switch statistics"
         ::= { agentSystemGroup 7 }

     agentClearTrapLog OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "clear trap log"
         ::= { agentSystemGroup 8 }

     agentClearVlan OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "clear vlan entries"
         ::= { agentSystemGroup 9 }

     agentResetSystem OBJECT-TYPE
         SYNTAX      INTEGER {
                     enable(1),
                     disable(2)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Resets the switch.

                      This object is not valid for stacking platforms."
         ::= { agentSystemGroup 10 }

     agentSaveConfigStatus OBJECT-TYPE
         SYNTAX      INTEGER {
                     notInitiated(1),
                     savingInProcess(2),
                     savingComplete(3),
                     savingFailed(4)
                  }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Indicates the current status of an save configuration request."
         ::= { agentSystemGroup 11 }


--**************************************************************************************
--    agentCableTesterGroup
--
--    This group provides configuration and status of the Virtual Cable Tester
--    feature.  This provides cable fault and length estimation on copper cables.
--    Once initiated, the test requires up to 2 seconds before results are obtained.
--    If the link specified to test is active, it will go down for the duration of
--    the test.
--
--**************************************************************************************

agentCableTesterGroup               OBJECT IDENTIFIER ::= { agentSwitching 4 }

     agentCableTesterStatus OBJECT-TYPE
         SYNTAX      INTEGER {
                     active(1),
                     success(2),
                     failure(3),
                     uninitialized(4)
                  }
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Status of the Virtual Cable Tester
                     active(1)        - cable testing is in progress.  Set to this value
                                        to start the test.
                     success(2)       - A successful test was performed.  Cannot be set.
                     failure(3)       - A failure was encountered during the test.
                                        Cannot be set.
                     uninitialized(4) - No test has been performed yet. Cannot be set."
         DEFVAL { uninitialized }
         ::= { agentCableTesterGroup 1 }

     agentCableTesterIfIndex OBJECT-TYPE
         SYNTAX      Unsigned32
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION
                     "Determines on which port to perform the cable test.  Limited to
                     copper based ports."
         DEFVAL { 0 }
         ::= { agentCableTesterGroup 2 }

     agentCableTesterCableStatus OBJECT-TYPE
         SYNTAX      INTEGER {
                     normal(1),
                     open(2),
                     short(3),
                     unknown(4)
                  }
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "Indicates the results of the Cable Test.


                     normal(1)        - The cable is working correctly.
                     open(2)          - The cable is disconnected or there is a faulty
                                        connector.
                     short(3)         - There is an electrical short in the cable.
                     unknown(4)       - No test has been performed yet, or a test is
                                        currently in progress."
         DEFVAL { unknown }
         ::= { agentCableTesterGroup 3 }

     agentCableTesterMinimumCableLength OBJECT-TYPE
         SYNTAX      Unsigned32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The estimated length of the cable in meters.  This value
                     indicates the shortest length estimated.  This object will
                     return 0 if agentCableTesterStatus is not success(2) or the cable
                     length is unknown."
         DEFVAL { 0 }
         ::= { agentCableTesterGroup 4 }

     agentCableTesterMaximumCableLength OBJECT-TYPE
         SYNTAX      Unsigned32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The estimated length of the cable in meters.  This value
                     indicates the longest length estimated.  This object will
                     return 0 if agentCableTesterStatus is not success(2) or the cable
                     length is unknown."
         DEFVAL { 0 }
         ::= { agentCableTesterGroup 5 }

     agentCableTesterCableFailureLocation OBJECT-TYPE
         SYNTAX      Unsigned32
         MAX-ACCESS  read-only
         STATUS      current
         DESCRIPTION
                     "The estimated distance in meters from the end of the cable to
                     the failure location.  This object will return 0 if
                     agentCableTesterStatus is not success(2)."
         DEFVAL { 0 }
         ::= { agentCableTesterGroup 6 }

--**************************************************************************************
--   Traps
--**************************************************************************************

     agentSwitchingTraps OBJECT IDENTIFIER ::= { agentSwitching 0 }


     multipleUsersTrap NOTIFICATION-TYPE
         STATUS             current
         DESCRIPTION
             "This trap is sent when more than one user is logged in with
             administrative access.  Only applies to CLI interface."
         ::= { agentSwitchingTraps 1 }

     broadcastStormStartTrap NOTIFICATION-TYPE
         STATUS             obsolete
         DESCRIPTION
             "This trap is sent when a broadcast storm is detected."
         ::= { agentSwitchingTraps 2 }

     broadcastStormEndTrap NOTIFICATION-TYPE
         STATUS             obsolete
         DESCRIPTION
             "This trap is sent when a broadcast storm is no longer
             detected."
         ::= { agentSwitchingTraps 3 }

     linkFailureTrap NOTIFICATION-TYPE
         STATUS             obsolete
         DESCRIPTION
             ""
         ::= { agentSwitchingTraps 4 }

     vlanRequestFailureTrap NOTIFICATION-TYPE
         OBJECTS {
                      dot1qVlanIndex
                 }
         STATUS             obsolete
         DESCRIPTION
             ""
         ::= { agentSwitchingTraps 5 }

     vlanDeleteLastTrap NOTIFICATION-TYPE
         OBJECTS {
                      dot1qVlanIndex
                 }
         STATUS             current
         DESCRIPTION
             "Trap is sent when attempting to delete the last configured VLAN
             or the Default VLAN."
         ::= { agentSwitchingTraps 6 }

     vlanDefaultCfgFailureTrap NOTIFICATION-TYPE
         OBJECTS {
                      dot1qVlanIndex
                 }
         STATUS             current
         DESCRIPTION
             "Trap is sent if there are failures in resetting VLAN
             configuration to defaults."
         ::= { agentSwitchingTraps 7 }

     vlanRestoreFailureTrap NOTIFICATION-TYPE
         OBJECTS {
                      dot1qVlanIndex
                 }
         STATUS             obsolete
         DESCRIPTION
             ""
         ::= { agentSwitchingTraps 8 }

     fanFailureTrap NOTIFICATION-TYPE
         STATUS             obsolete
         DESCRIPTION
             ""
         ::= { agentSwitchingTraps 9 }

     stpInstanceNewRootTrap NOTIFICATION-TYPE
         OBJECTS {
                      agentStpMstId
                 }
         STATUS             current
         DESCRIPTION
             "Trap is sent when this machine is a new STP Root when there is more
             than one STP instance."
         ::= { agentSwitchingTraps 10 }

     stpInstanceTopologyChangeTrap NOTIFICATION-TYPE
         OBJECTS {
                      agentStpMstId
                 }
         STATUS             current
         DESCRIPTION
             "Trap is sent when there is a STP topology change when there is more
             than one STP instance."
         ::= { agentSwitchingTraps 11 }

     powerSupplyStatusChangeTrap NOTIFICATION-TYPE
         STATUS             obsolete
         DESCRIPTION
             ""
         ::= { agentSwitchingTraps 12 }

     failedUserLoginTrap NOTIFICATION-TYPE
         STATUS             current
         DESCRIPTION
             "Trap is sent when a user fails to authenticate via the CLI or Web
             interfaces."
         ::= { agentSwitchingTraps 13 }

     userLockoutTrap NOTIFICATION-TYPE
         STATUS             current
         DESCRIPTION
             "Trap is sent when a user account is locked due to consecutive failed login attempts via the CLI or Web
             interfaces beyond the allowed limit."
         ::= { agentSwitchingTraps 14 }

     macAddrLearnTrap NOTIFICATION-TYPE
         OBJECTS {
                    ifIndex,
                    dot1qVlanIndex,
                    dot1qTpFdbAddress
                 } 
         STATUS             current
         DESCRIPTION
             "Trap is sent when L2 MAC address is learned i.e when entry is added to FDB."
         ::= { agentSwitchingTraps 15 }

     macAddrAgeoutTrap NOTIFICATION-TYPE
         OBJECTS {
                    ifIndex,
                    dot1qVlanIndex,
                    dot1qTpFdbAddress
                 }
         STATUS             current
         DESCRIPTION
             "Trap is sent when L2 MAC address is agedout i.e when entry is removed from FDB."
         ::= { agentSwitchingTraps 16 }
END
