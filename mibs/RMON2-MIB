RMON2-MIB DEFINITIONS ::= BEGIN
IMPORTS
    MODULE-IDENTITY, OBJECT-TYPE, Counter32, <PERSON>teger32,
    <PERSON><PERSON><PERSON>32, <PERSON><PERSON><PERSON><PERSON><PERSON>, TimeTicks, mib-2         FROM SNMPv2-SMI
    TEXTUAL-CONVENTION, RowStatus, DisplayString, TimeStamp
                                                 FROM SNMPv2-TC
    MODULE-COMPLIANCE, OBJECT-GROUP              FROM SNMPv2-CONF
    ifIndex                                      FROM IF-MIB
    OwnerString, statistics, history, hosts,
    matrix, filter, etherStatsEntry, historyControlEntry,
    hostControlEntry, matrixControlEntry, filterEntry,
    channelEntry                    FROM RMON-MIB
    tokenRing, tokenRingMLStatsEntry, tokenRingPStatsEntry,



    ringStationControlEntry, sourceRoutingStatsEntry
                                    FROM TOKEN-RING-RMON-MIB;
--  Remote Network Monitoring MIB

rmon MODULE-IDENTITY
    LAST-UPDATED "200605020000Z"    -- May 2, 2006
    ORGANIZATION "IETF RMON MIB Working Group"
    CONTACT-INFO
        "Author:
                     <PERSON>
             Phone:  ******-948-6500
             Fax :   ******-745-0671
             Email:  <EMAIL>

         Working Group Chair:
                     Andy Bierman
             E-mail: <EMAIL>

         Working Group Mailing List: <<EMAIL>>
         To subscribe send email to: <<EMAIL>>    "
    DESCRIPTION
        "The MIB module for managing remote monitoring
         device implementations.  This MIB module
         extends the architecture introduced in the original
         RMON MIB as specified in RFC 2819.

         Copyright (C) The Internet Society (2006).  This version of
         this MIB module is part of RFC 4502;  see the RFC itself for
         full legal notices."

    REVISION "200605020000Z"    -- May 2, 2006
    DESCRIPTION
        "This version updates the proposed-standard version of the
        RMON2 MIB (published as RFC 2021) by adding 2 new
        enumerations to the nlMatrixTopNControlRateBase object and
        4 new enumerations to the alMatrixTopNControlRateBase object.
        These new enumerations support the creation of high-capacity
        topN reports in the High Capacity RMON MIB [RFC3273].

        Additionally, the following objects have been deprecated, as
        they have not had enough independent implementations to
        demonstrate interoperability to meet the requirements of a
        Draft Standard:

        probeDownloadFile
        probeDownloadTFTPServer
        probeDownloadAction
        probeDownloadStatus



        serialMode
        serialProtocol
        serialTimeout
        serialModemInitString
        serialModemHangUpString
        serialModemConnectResp
        serialModemNoConnectResp
        serialDialoutTimeout
        serialStatus
        serialConnectDestIpAddress
        serialConnectType
        serialConnectDialString
        serialConnectSwitchConnectSeq
        serialConnectSwitchDisconnectSeq
        serialConnectSwitchResetSeq
        serialConnectOwner
        serialConnectStatus
        netConfigIPAddress
        netConfigSubnetMask
        netConfigStatus
        netDefaultGateway
        tokenRingMLStats2DroppedFrames
        tokenRingMLStats2CreateTime
        tokenRingPStats2DroppedFrames
        tokenRingPStats2CreateTime
        ringStationControl2DroppedFrames
        ringStationControl2CreateTime
        sourceRoutingStats2DroppedFrames
        sourceRoutingStats2CreateTime
        trapDestIndex
        trapDestCommunity
        trapDestProtocol
        trapDestAddress
        trapDestOwner
        trapDestStatus

        In addition, two corrections were made.  The LastCreateTime
        Textual Convention had been defined with a base type of
        another textual convention, which isn't allowed in SMIv2.  The
        definition has been modified to use TimeTicks as the base
        type.

        Further, the SerialConfigEntry SEQUENCE definition included
        sub-typing information that is not allowed in SMIv2.  This
        information has been deleted.  Ranges were added to a number of
        objects and textual-conventions to constrain their maximum
        (and sometimes minimum) sizes.  The addition of these ranges
        documents existing practice for these objects.  These objects



        are:
            ControlString
            protocolDirID
            protocolDirParameters
            addressMapNetworkAddress
            nlHostAddress
            nlMatrixSDSourceAddress
            nlMatrixSDDestAddress
            nlMatrixDSSourceAddress
            nlMatrixDSDestAddress
            nlMatrixTopNSourceAddress
            nlMatrixTopNDestAddress
            alHostEntry
            alMatrixSDEntry
            alMatrixDSEntry
            alMatrixTopNSourceAddress
            alMatrixTopNDestAddress

        Finally, the TimeFilter TC has been updated to encourage agent
        implementations that allow a MIB walk to behave well even when
        performed by an application that is not aware of the special
        TimeFilter semantics."

    REVISION "200207080000Z"        -- 08 July, 2002
    DESCRIPTION
        "Added new enumerations to support the High-Capacity RMON
        MIB as defined in RFC 3273.  Also fixed some typos and
        added clarifications."

    REVISION "199605270000Z"    -- 27 May, 1996
    DESCRIPTION
        "Original version.  Published as RFC 2021."
    ::= { mib-2 16 }

-- { rmon 1 } through { rmon 10 } are defined in RMON and
-- the Token Ring RMON MIB [RFC1513]

    protocolDir     OBJECT IDENTIFIER ::= { rmon 11 }
    protocolDist    OBJECT IDENTIFIER ::= { rmon 12 }
    addressMap      OBJECT IDENTIFIER ::= { rmon 13 }
    nlHost          OBJECT IDENTIFIER ::= { rmon 14 }
    nlMatrix        OBJECT IDENTIFIER ::= { rmon 15 }
    alHost          OBJECT IDENTIFIER ::= { rmon 16 }
    alMatrix        OBJECT IDENTIFIER ::= { rmon 17 }
    usrHistory      OBJECT IDENTIFIER ::= { rmon 18 }
    probeConfig     OBJECT IDENTIFIER ::= { rmon 19 }
    rmonConformance OBJECT IDENTIFIER ::= { rmon 20 }




-- Textual Conventions

ZeroBasedCounter32 ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "This TC describes an object that counts events with the
        following semantics: objects of this type will be set to
        zero(0) on creation and will thereafter count appropriate
        events, wrapping back to zero(0) when the value 2^32 is
        reached.

        Provided that an application discovers the new object within
        the minimum time to wrap, it can use the initial value as a
        delta since it last polled the table of which this object is
        part.  It is important for a management station to be aware of
        this minimum time and the actual time between polls, and to
        discard data if the actual time is too long or there is no
        defined minimum time.

        Typically, this TC is used in tables where the INDEX space is
        constantly changing and/or the TimeFilter mechanism is in use."
    SYNTAX Gauge32

LastCreateTime ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "This TC describes an object that stores the value of the
        sysUpTime object at the last time its entry was created.

        This can be used for polling applications to determine that an
        entry has been deleted and re-created between polls, causing
        an otherwise undetectable discontinuity in the data.

        If sysUpTime is reset to zero as a result of a re-
        initialization of the network management (sub)system, then
        the values of all LastCreateTime objects are also reset.
        However, after approximately 497 days without a re-
        initialization, the sysUpTime object will reach 2^^32-1 and
        then increment to zero; in this case, existing values
        of TimeStamp objects do not change.  This can lead to
        ambiguities in the value of TimeStamp objects."
    SYNTAX TimeTicks

TimeFilter ::= TEXTUAL-CONVENTION
  STATUS        current
  DESCRIPTION
      "To be used for the index to a table.  Allows an application
      to download only those rows changed since a particular time.



      Note that this is not a history mechanism.  Only current values
      of underlying objects are returned; saved instance values
      associated with particular values of sysUpTime are not.

      An entry is considered changed if the value of any object in the
      entry changes, if the row is created, or if any object in the
      entry is created or deleted.  Note that deleted entries cannot
      be detected or downloaded.

      A time-filtered conceptual table is created by inserting a
      single object of SYNTAX TimeFilter as the first INDEX component
      in a copy of an existing basic conceptual table (i.e., any
      SEQUENCE without a TimeFilter INDEX component).  Thus, for
      each conceptual entry 'I' in the basic table, there exists N
      conceptual entries in the time-filtered version, indexed N.I,
      where 'N' is equal to the value of sysUpTime.

      When an application retrieves conceptual instances from a
      time-filtered table, and an INDEX value is provided for the
      TimeFilter INDEX component 'N', the agent will only consider
      returning basic conceptual entries (e.g., 'fooColumn.N.I') if
      any column within the basic conceptual entry has changed since
      sysUpTime 'N'.  If not, the basic conceptual entry will
      be ignored for the particular retrieval operation.

      When sysUpTime is equal to zero, this table shall be empty.

      One conceptual entry exists for each past value of sysUpTime,
      except that the whole table is purged should sysUpTime wrap.

      As an entry in a time-filtered table is updated (i.e., one of
      the columns in the basic conceptual table is changed), new
      conceptual entries are also created in the time-filtered version
      (which still shares the now updated object values with all other
      instances).  The number of unique time-filtered instances that
      are created is determined by the value of sysUpTime at which the
      basic entry was last updated.  One unique instance will exist
      for each value of sysUpTime at the last update time for the row.
      However, a new TimeFilter index instance is created for each new
      sysUpTime value.  The TimeFilter index values not associated
      with entry updates are called duplicate time-filtered instances.

      After some deployment experience, it has been determined that
      a time-filtered table is more efficient if the agent
      stops a MIB walk operation by skipping over rows with a
      TimeFilter index value higher than the value in the received
      GetNext/GetBulk request.  That is, instead of incrementing a
      TimeFilter index value, the agent will continue to the next



      object or table.  As a consequence, GetNext or GetBulk
      operations will provide only one pass through a time-filtered
      table.

      It is suggested that an agent implement a time-filtered table
      in this manner to improve performance and avoid a MIB walk
      getting stuck in time-filtered tables.  It is, however, still
      acceptable for an agent to implement a time-filtered table in
      the traditional manner (i.e., every conceptual time-filtered
      instance is returned in GetNext and GetBulk PDU responses), and
      management applications must be able to deal with such
      traditional implementations.

      See the appendix for further discussion of this textual
      convention.

      The following example is provided to demonstrate TimeFilter
      behavior:

      Consider the following basic conceptual table, basicFooTable.
      (Note that the basic version of a time-filtered table may not
      actually be defined.)

          basicFooTable:

          basicFooTable ...
          INDEX { fooIndex }

          BasicFooEntry {
             fooIndex     Integer32,
             fooCounts    Counter32
          }

      For this example, the basicFooTable contains two static
      conceptual entries (fooIndex equals '1' and '2'), created at
      time zero.  It also contains one dynamic conceptual entry
      (fooIndex equals '3'), which is created at time '3' and deleted
      at time '7'.

      The time-filtered version of the basicFooTable could be defined
      as follows:

          FooTable:

          fooTable ...
          INDEX { fooTimeMark, fooIndex }

          FooEntry {



             fooTimeMark  TimeFilter,
             fooIndex     Integer32,
             fooCounts    Counter32
          }


      Note that entries exist in the time-filtered conceptual table
      only if they actually exist in the underlying (basic) table.

      For this example, the fooTable will have three underlying
      basic entries (fooIndex == 1, 2, and 3), with the following
      activity (for sysUpTime equal 0 to 9):

         - fooEntry.N.1 is created at time '0' and most recently
           updated at time '6' to the value '5'.
         - fooEntry.N.2 is created at time '0' and most recently
           updated at time '8' to the value '9'.
         - fooEntry.N.3 is created at time '3', updated at time '5'
           to the value '17', and deleted at time '7'.

     The following tables show the values that would be returned for
     MIB walk operations with various TimeFilter values, done at
     different times.  An application issues a retrieval request at
     time 'T', with a TimeFilter value, 'N' (typically set to a lower
     value, such as the value of sysUpTime at the last polling cycle).

     The following values would be returned in a MIB walk of
     fooCounts.N if T equals '0' and N equals '0':

           fooCounts.N.I    Value
           ==========================
           fooCounts.0.1    0
           fooCounts.0.2    0

       Note that nothing is returned for fooCounts.0.3, since that
       entry does not exist at sysUpTime equals '0'.

     The following values would be returned in a full (traditional) MIB
     walk of fooCounts.N if T equals '3' and N equals '0':

           fooCounts.N.I    Value
           =======================
           fooCounts.0.1    0
           fooCounts.0.2    0
           fooCounts.0.3    0
           fooCounts.1.3    0
           fooCounts.2.3    0
           fooCounts.3.3    0



       Note that there are no instances for T equals 1 or 2 for the
       first two values of N, as these entries did not change
       since they were created at time '0'.

       Note that the current value for 'fooCounts.N.3' is returned
       here, even for values of N less than '3' (when the entry was
       created).  The agent only considers the current existence of an
       entry in the TimeFilter algorithm, not the time when the entry
       was created.

       Note that the instances 'fooCounts.0.3', 'fooCounts.1.3',
       and 'fooCounts.2.3' are duplicates and can be suppressed by the
       agent in a MIB walk.

     The following values would be returned in a full (traditional)
     MIB walk of fooCounts.N if T equals '6' and N equals '3':

           fooCounts.N.I    Value
           =======================
           fooCounts.3.1    5
           fooCounts.3.3    17
           fooCounts.4.1    5
           fooCounts.4.3    17
           fooCounts.5.1    5
           fooCounts.5.3    17
           fooCounts.6.1    5

        Note that no instances for entry 'fooCounts.N.2' are returned,
        since it has not changed since time '3'.

        Note that all instances except 'fooCounts.5.3' and
        'fooCounts.6.1' are duplicates and can be suppressed by the
        agent in a MIB walk.

     The following values would be returned in a full (traditional)
     MIB walk of fooCounts.N if T equals '9' and N equals '6':

           fooCounts.N.I    Value
           =======================
           fooCounts.6.1    5
           fooCounts.6.2    9
           fooCounts.7.2    9
           fooCounts.8.2    9

        Note that no instances for entry 'fooCounts.N.3' are returned,
        since it was deleted at time '7'.

        Note that instances 'fooCounts.6.2' and 'fooCounts.7.2'



        are duplicates and can be suppressed by the agent in a MIB
        walk."

  SYNTAX    TimeTicks

DataSource ::= TEXTUAL-CONVENTION
    STATUS        current
    DESCRIPTION
        "Identifies the source of the data that the associated
        function is configured to analyze.  This source can be any
        interface on this device.

        In order to identify a particular interface, this
        object shall identify the instance of the ifIndex
        object, defined in [RFC2863], for the desired interface.

        For example, if an entry were to receive data from
        interface #1, this object would be set to ifIndex.1."
    SYNTAX      OBJECT IDENTIFIER

--
-- Protocol Directory Group
--
-- Lists the inventory of protocols the probe has the capability of
-- monitoring and allows the addition, deletion, and configuration of
-- entries in this list.

protocolDirLastChange OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of sysUpTime at the time the protocol directory
        was last modified, either through insertions or deletions,
        or through modifications of the
        protocolDirAddressMapConfig, protocolDirHostConfig, or
        protocolDirMatrixConfig."
    ::= { protocolDir 1 }

protocolDirTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF ProtocolDirEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table lists the protocols that this agent has the
        capability to decode and count.  There is one entry in this
        table for each such protocol.  These protocols represent
        different network-layer, transport-layer, and higher-layer



        protocols.  The agent should boot up with this table
        preconfigured with those protocols that it knows about and
        wishes to monitor.  Implementations are strongly encouraged to
        support protocols higher than the network layer (at least for
        the protocol distribution group), even for implementations
        that don't support the application-layer groups."
    ::= { protocolDir 2 }

protocolDirEntry OBJECT-TYPE
    SYNTAX      ProtocolDirEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A conceptual row in the protocolDirTable.

         An example of the indexing of this entry is
         protocolDirLocalIndex.*******.*******.*******, which is the
         encoding of a length of 8, followed by 8 subids encoding the
         protocolDirID of 1.2048, followed by a length of 2 and the
         2 subids encoding zero-valued parameters.

         Note that some combinations of index values may result in an
         index that exceeds 128 sub-identifiers in length, which exceeds
         the maximum for the SNMP protocol.  Implementations should take
         care to avoid such combinations."
    INDEX { protocolDirID, protocolDirParameters }
    ::= { protocolDirTable  1 }

ProtocolDirEntry ::= SEQUENCE {
    protocolDirID                   OCTET STRING,
    protocolDirParameters           OCTET STRING,
    protocolDirLocalIndex           Integer32,
    protocolDirDescr                DisplayString,
    protocolDirType                 BITS,
    protocolDirAddressMapConfig     INTEGER,
    protocolDirHostConfig           INTEGER,
    protocolDirMatrixConfig         INTEGER,
    protocolDirOwner                OwnerString,
    protocolDirStatus               RowStatus
}

protocolDirID OBJECT-TYPE
    SYNTAX      OCTET STRING  (SIZE (4..128))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A unique identifier for a particular protocol.  Standard
        identifiers will be defined in such a manner that they



        can often be used as specifications for new protocols - i.e.,
        a tree-structured assignment mechanism that matches the
        protocol encapsulation 'tree' and that has algorithmic
        assignment mechanisms for certain subtrees.  See RFC 2074 for
        more details.

        Despite the algorithmic mechanism, the probe will only place
        entries in here for those protocols it chooses to collect.  In
        other words, it need not populate this table with all
        possible ethernet protocol types, nor need it create them on
        the fly when it sees them.  Whether it does these
        things is a matter of product definition (cost/benefit,
        usability) and is up to the designer of the product.

        If an entry is written to this table with a protocolDirID that
        the agent doesn't understand, either directly or
        algorithmically, the SET request will be rejected with an
        inconsistentName or badValue (for SNMPv1) error."
    ::= { protocolDirEntry 1 }

protocolDirParameters OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE (1..32))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A set of parameters for the associated protocolDirID.
        See the associated RMON2 Protocol Identifiers document
        for a description of the possible parameters.  There
        will be one octet in this string for each sub-identifier in
        the protocolDirID, and the parameters will appear here in the
        same order as the associated sub-identifiers appear in the
        protocolDirID.

        Every node in the protocolDirID tree has a different, optional
        set of parameters defined (that is, the definition of
        parameters for a node is optional).  The proper parameter
        value for each node is included in this string.  Note that the
        inclusion of a parameter value in this string for each node is
        not optional.  What is optional is that a node may have no
        parameters defined, in which case the parameter field for that
        node will be zero."
    ::= { protocolDirEntry 2 }

protocolDirLocalIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..**********)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION



        "The locally arbitrary but unique identifier associated
        with this protocolDir entry.

        The value for each supported protocol must remain constant at
        least from one re-initialization of the entity's network
        management system to the next re-initialization, except that
        if a protocol is deleted and re-created, it must be re-created
        with a new value that has not been used since the last
        re-initialization.

        The specific value is meaningful only within a given SNMP
        entity.  A protocolDirLocalIndex must not be re-used until the
        next agent restart in the event that the protocol directory
        entry is deleted."
    ::= { protocolDirEntry 3 }

protocolDirDescr OBJECT-TYPE
    SYNTAX      DisplayString (SIZE (1..64))
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "A textual description of the protocol encapsulation.
        A probe may choose to describe only a subset of the
        entire encapsulation (e.g., only the highest layer).

        This object is intended for human consumption only.

        This object may not be modified if the associated
        protocolDirStatus object is equal to active(1)."
    ::= { protocolDirEntry 4 }

protocolDirType OBJECT-TYPE
    SYNTAX      BITS {
                    extensible(0),
                    addressRecognitionCapable(1)
                }
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object describes 2 attributes of this protocol
         directory entry.

         The presence or absence of the 'extensible' bit describes
         whether this protocol directory entry can be extended
         by the user by creating protocol directory entries that are
         children of this protocol.

         An example of an entry that will often allow extensibility is



         'ip.udp'.  The probe may automatically populate some children
         of this node, such as 'ip.udp.snmp' and 'ip.udp.dns'.
         A probe administrator or user may also populate additional
         children via remote SNMP requests that create entries in this
         table.  When a child node is added for a protocol for which the
         probe has no built-in support extending a parent node (for
         which the probe does have built-in support),
         that child node is not extendable.  This is termed 'limited
         extensibility'.

         When a child node is added through this extensibility
         mechanism, the values of protocolDirLocalIndex and
         protocolDirType shall be assigned by the agent.

         The other objects in the entry will be assigned by the
         manager who is creating the new entry.

         This object also describes whether this agent can
         recognize addresses for this protocol, should it be a
         network-level protocol.  That is, while a probe may be able
         to recognize packets of a particular network-layer protocol
         and count them, it takes additional logic to be able to
         recognize the addresses in this protocol and to populate
         network-layer or application-layer tables with the addresses
         in this protocol.  If this bit is set, the agent will
         recognize network-layer addresses for this protocol and
         populate the network- and application-layer host and matrix
         tables with these protocols.

         Note that when an entry is created, the agent will supply
         values for the bits that match the capabilities of the agent
         with respect to this protocol.  Note that since row creations
         usually exercise the limited extensibility feature, these
         bits will usually be set to zero."
    ::= { protocolDirEntry 5 }

protocolDirAddressMapConfig OBJECT-TYPE
    SYNTAX      INTEGER {
                    notSupported(1),
                    supportedOff(2),
                    supportedOn(3)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "This object describes and configures the probe's support for
        address mapping for this protocol.  When the probe creates
        entries in this table for all protocols that it understands,



        it will set the entry to notSupported(1) if it doesn't have
        the capability to perform address mapping for the protocol or
        if this protocol is not a network-layer protocol.  When
        an entry is created in this table by a management operation as
        part of the limited extensibility feature, the probe must set
        this value to notSupported(1), because limited extensibility
        of the protocolDirTable does not extend to interpreting
        addresses of the extended protocols.

        If the value of this object is notSupported(1), the probe
        will not perform address mapping for this protocol and
        shall not allow this object to be changed to any other value.
        If the value of this object is supportedOn(3), the probe
        supports address mapping for this protocol and is configured
        to perform address mapping for this protocol for all
        addressMappingControlEntries and all interfaces.
        If the value of this object is supportedOff(2), the probe
        supports address mapping for this protocol but is configured
        to not perform address mapping for this protocol for any
        addressMappingControlEntries and all interfaces.
        Whenever this value changes from supportedOn(3) to
        supportedOff(2), the probe shall delete all related entries in
        the addressMappingTable."
    ::= { protocolDirEntry 6 }

protocolDirHostConfig OBJECT-TYPE
    SYNTAX      INTEGER {
                    notSupported(1),
                    supportedOff(2),
                    supportedOn(3)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "This object describes and configures the probe's support for
        the network-layer and application-layer host tables for this
        protocol.  When the probe creates entries in this table for
        all protocols that it understands, it will set the entry to
        notSupported(1) if it doesn't have the capability to track the
        nlHostTable for this protocol or if the alHostTable is
        implemented but doesn't have the capability to track this
        protocol.  Note that if the alHostTable is implemented, the
        probe may only support a protocol if it is supported in both
        the nlHostTable and the alHostTable.

        If the associated protocolDirType object has the
        addressRecognitionCapable bit set, then this is a network-
        layer protocol for which the probe recognizes addresses, and



        thus the probe will populate the nlHostTable and alHostTable
        with addresses it discovers for this protocol.

        If the value of this object is notSupported(1), the probe
        will not track the nlHostTable or alHostTable for this
        protocol and shall not allow this object to be changed to any
        other value.  If the value of this object is supportedOn(3),
        the probe supports tracking of the nlHostTable and alHostTable
        for this protocol and is configured to track both tables
        for this protocol for all control entries and all interfaces.
        If the value of this object is supportedOff(2), the probe
        supports tracking of the nlHostTable and alHostTable for this
        protocol but is configured to not track these tables
        for any control entries or interfaces.
        Whenever this value changes from supportedOn(3) to
        supportedOff(2), the probe shall delete all related entries in
        the nlHostTable and alHostTable.

        Note that since each alHostEntry references 2 protocol
        directory entries, one for the network address and one for the
        type of the highest protocol recognized, an entry will
        only be created in that table if this value is supportedOn(3)
        for both protocols."
    ::= { protocolDirEntry 7 }

protocolDirMatrixConfig OBJECT-TYPE
    SYNTAX      INTEGER {
                    notSupported(1),
                    supportedOff(2),
                    supportedOn(3)
                }
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "This object describes and configures the probe's support for
        the network-layer and application-layer matrix tables for this
        protocol.  When the probe creates entries in this table for
        all protocols that it understands, it will set the entry to
        notSupported(1) if it doesn't have the capability to track the
        nlMatrixTables for this protocol or if the alMatrixTables are
        implemented but don't have the capability to track this
        protocol.  Note that if the alMatrix tables are implemented,
        the probe may only support a protocol if it is supported in
        both of the nlMatrixTables and both of the
        alMatrixTables.

        If the associated protocolDirType object has the
        addressRecognitionCapable bit set, then this is a network-



        layer protocol for which the probe recognizes addresses, and
        thus the probe will populate both of the nlMatrixTables and
        both of the alMatrixTables with addresses it discovers for
        this protocol.

        If the value of this object is notSupported(1), the probe
        will not track either of the nlMatrixTables or the
        alMatrixTables for this protocol and shall not allow this
        object to be changed to any other value.  If the value of this
        object is supportedOn(3), the probe supports tracking of both
        of the nlMatrixTables and (if implemented) both of the
        alMatrixTables for this protocol and is configured to track
        these tables for this protocol for all control entries and all
        interfaces.  If the value of this object is supportedOff(2),
        the probe supports tracking of both of the nlMatrixTables and
        (if implemented) both of the alMatrixTables for this protocol
        but is configured to not track these tables for this
        protocol for any control entries or interfaces.
        Whenever this value changes from supportedOn(3) to
        supportedOff(2), the probe shall delete all related entries in
        the nlMatrixTables and the alMatrixTables.

        Note that since each alMatrixEntry references 2 protocol
        directory entries, one for the network address and one for the
        type of the highest protocol recognized, an entry will
        only be created in that table if this value is supportedOn(3)
        for both protocols."
    ::= { protocolDirEntry 8 }

protocolDirOwner OBJECT-TYPE
    SYNTAX      OwnerString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The entity that configured this entry and is
        therefore using the resources assigned to it."
    ::= { protocolDirEntry 9 }

protocolDirStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The status of this protocol directory entry.

        An entry may not exist in the active state unless all
        objects in the entry have an appropriate value.




        If this object is not equal to active(1), all associated
        entries in the nlHostTable, nlMatrixSDTable, nlMatrixDSTable,
        alHostTable, alMatrixSDTable, and alMatrixDSTable shall be
        deleted."
    ::= { protocolDirEntry 10 }

--
-- Protocol Distribution Group  (protocolDist)
--
-- Collects the relative amounts of octets and packets for the
-- different protocols detected on a network segment.
--    protocolDistControlTable,
--    protocolDistStatsTable

protocolDistControlTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF ProtocolDistControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Controls the setup of protocol type distribution statistics
        tables.

        Implementations are encouraged to add an entry per monitored
        interface upon initialization so that a default collection
        of protocol statistics is available.

        Rationale:
        This table controls collection of very basic statistics
        for any or all of the protocols detected on a given interface.
        An NMS can use this table to quickly determine bandwidth
        allocation utilized by different protocols.

        A media-specific statistics collection could also
        be configured (e.g., etherStats, trPStats) to easily obtain
        total frame, octet, and droppedEvents for the same
        interface."
    ::= { protocolDist 1 }

protocolDistControlEntry OBJECT-TYPE
    SYNTAX      ProtocolDistControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A conceptual row in the protocolDistControlTable.

         An example of the indexing of this entry is
         protocolDistControlDroppedFrames.7"
    INDEX { protocolDistControlIndex }



    ::= { protocolDistControlTable 1 }

ProtocolDistControlEntry ::= SEQUENCE {
    protocolDistControlIndex                Integer32,
    protocolDistControlDataSource           DataSource,
    protocolDistControlDroppedFrames        Counter32,
    protocolDistControlCreateTime           LastCreateTime,
    protocolDistControlOwner                OwnerString,
    protocolDistControlStatus               RowStatus
}

protocolDistControlIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A unique index for this protocolDistControlEntry."
    ::= { protocolDistControlEntry 1 }

protocolDistControlDataSource OBJECT-TYPE
    SYNTAX      DataSource
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The source of data for the this protocol distribution.

        The statistics in this group reflect all packets
        on the local network segment attached to the
        identified interface.

        This object may not be modified if the associated
        protocolDistControlStatus object is equal to active(1)."
    ::= { protocolDistControlEntry 2 }

protocolDistControlDroppedFrames OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
       "The total number of frames that were received by the probe
        and therefore not accounted for in the *StatsDropEvents, but
        that the probe chose not to count for this entry for
        whatever reason.  Most often, this event occurs when the probe
        is out of some resources and decides to shed load from this
        collection.

        This count does not include packets that were not counted
        because they had MAC-layer errors.



        Note that, unlike the dropEvents counter, this number is the
        exact number of frames dropped."
    ::= { protocolDistControlEntry 3 }

protocolDistControlCreateTime OBJECT-TYPE
    SYNTAX     LastCreateTime
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The value of sysUpTime when this control entry was last
        activated.  This can be used by the management station to
        ensure that the table has not been deleted and recreated
        between polls."
    ::= { protocolDistControlEntry 4 }

protocolDistControlOwner OBJECT-TYPE
    SYNTAX      OwnerString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The entity that configured this entry and is
        therefore using the resources assigned to it."
    ::= { protocolDistControlEntry 5 }

protocolDistControlStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The status of this row.

        An entry may not exist in the active state unless all
        objects in the entry have an appropriate value.

        If this object is not equal to active(1), all associated
        entries in the protocolDistStatsTable shall be deleted."
    ::= { protocolDistControlEntry 6 }

-- per interface protocol distribution statistics table
protocolDistStatsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF ProtocolDistStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An entry is made in this table for every protocol in the
        protocolDirTable that has been seen in at least one packet.
        Counters are updated in this table for every protocol type
        that is encountered when parsing a packet, but no counters are



        updated for packets with MAC-layer errors.

        Note that if a protocolDirEntry is deleted, all associated
        entries in this table are removed."
    ::= { protocolDist 2 }

protocolDistStatsEntry OBJECT-TYPE
    SYNTAX      ProtocolDistStatsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A conceptual row in the protocolDistStatsTable.

        The index is composed of the protocolDistControlIndex of the
        associated protocolDistControlEntry, followed by the
        protocolDirLocalIndex of the associated protocol that this
        entry represents.  In other words, the index identifies the
        protocol distribution an entry is a part of and the
        particular protocol that it represents.

        An example of the indexing of this entry is
        protocolDistStatsPkts.1.18"
    INDEX { protocolDistControlIndex, protocolDirLocalIndex }
    ::= { protocolDistStatsTable 1 }

ProtocolDistStatsEntry ::= SEQUENCE {
    protocolDistStatsPkts                    ZeroBasedCounter32,
    protocolDistStatsOctets                  ZeroBasedCounter32
}

protocolDistStatsPkts OBJECT-TYPE
    SYNTAX      ZeroBasedCounter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of packets of this protocol type received
        without errors.  Note that this is the number of
        link-layer packets, so if a single network-layer packet
        is fragmented into several link-layer frames, this counter
        is incremented several times."
    ::= { protocolDistStatsEntry 1 }

protocolDistStatsOctets OBJECT-TYPE
    SYNTAX      ZeroBasedCounter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of octets in packets of this protocol type



        received since it was added to the protocolDistStatsTable
        (excluding framing bits, but including FCS octets), except for
        those octets in packets that contained errors.

        Note that this doesn't count just those octets in the
        particular protocol frames but includes the entire packet
        that contained the protocol."
    ::= { protocolDistStatsEntry 2 }

--
-- Address Map Group   (addressMap)
--
-- Lists MAC address to network address bindings discovered by the
-- probe and what interface they were last seen on.
--    addressMapControlTable
--    addressMapTable

addressMapInserts OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of times an address mapping entry has been
        inserted into the addressMapTable.  If an entry is inserted,
        then deleted, and then inserted, this counter will be
        incremented by 2.

        Note that the table size can be determined by subtracting
        addressMapDeletes from addressMapInserts."
    ::= { addressMap 1 }

addressMapDeletes OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of times an address mapping entry has been
        deleted from the addressMapTable (for any reason).  If
        an entry is deleted, then inserted, and then deleted, this
        counter will be incremented by 2.

        Note that the table size can be determined by subtracting
        addressMapDeletes from addressMapInserts."
    ::= { addressMap 2 }

addressMapMaxDesiredEntries OBJECT-TYPE
    SYNTAX      Integer32 (-1..**********)
    MAX-ACCESS  read-write



    STATUS      current
    DESCRIPTION
        "The maximum number of entries that are desired in the
        addressMapTable.  The probe will not create more than
        this number of entries in the table but may choose to create
        fewer entries in this table for any reason, including the lack
        of resources.

        If this object is set to a value less than the current number
        of entries, enough entries are chosen in an
        implementation-dependent manner and deleted so that the number
        of entries in the table equals the value of this object.

        If this value is set to -1, the probe may create any number
        of entries in this table.

        This object may be used to control how resources are allocated
        on the probe for the various RMON functions."
    ::= { addressMap 3 }

addressMapControlTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AddressMapControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A table to control the collection of mappings from network
         layer address to physical address to interface.

        Note that this is not like the typical RMON
        controlTable and dataTable in which each entry creates
        its own data table.  Each entry in this table enables the
        discovery of addresses on a new interface and the placement
        of address mappings into the central addressMapTable.

        Implementations are encouraged to add an entry per monitored
        interface upon initialization so that a default collection
        of address mappings is available."
    ::= { addressMap 4 }

addressMapControlEntry OBJECT-TYPE
    SYNTAX      AddressMapControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A conceptual row in the addressMapControlTable.

        An example of the indexing of this entry is
        addressMapControlDroppedFrames.1"



    INDEX { addressMapControlIndex }
    ::= { addressMapControlTable 1 }

AddressMapControlEntry ::= SEQUENCE {
    addressMapControlIndex              Integer32,
    addressMapControlDataSource         DataSource,
    addressMapControlDroppedFrames      Counter32,
    addressMapControlOwner              OwnerString,
    addressMapControlStatus             RowStatus
}

addressMapControlIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A unique index for this entry in the addressMapControlTable."
    ::= { addressMapControlEntry 1 }

addressMapControlDataSource OBJECT-TYPE
    SYNTAX      DataSource
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The source of data for this addressMapControlEntry."
    ::= { addressMapControlEntry 2 }

addressMapControlDroppedFrames OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
       "The total number of frames that were received by the probe
        and therefore not accounted for in the *StatsDropEvents, but
        that the probe chose not to count for this entry for
        whatever reason.  Most often, this event occurs when the probe
        is out of some resources and decides to shed load from this
        collection.

        This count does not include packets that were not counted
        because they had MAC-layer errors.

        Note that, unlike the dropEvents counter, this number is the
        exact number of frames dropped."
    ::= { addressMapControlEntry 3 }

addressMapControlOwner OBJECT-TYPE
    SYNTAX      OwnerString



    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The entity that configured this entry and is
        therefore using the resources assigned to it."
    ::= { addressMapControlEntry 4 }

addressMapControlStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The status of this addressMap control entry.

        An entry may not exist in the active state unless all
        objects in the entry have an appropriate value.

        If this object is not equal to active(1), all associated
        entries in the addressMapTable shall be deleted."
    ::= { addressMapControlEntry 5 }

addressMapTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AddressMapEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A table of mappings from network layer address to physical
        address to interface.

        The probe will add entries to this table based on the source
        MAC and network addresses seen in packets without MAC-level
        errors.  The probe will populate this table for all protocols
        in the protocol directory table whose value of
        protocolDirAddressMapConfig is equal to supportedOn(3), and
        will delete any entries whose protocolDirEntry is deleted or
        has a protocolDirAddressMapConfig value of supportedOff(2)."
    ::= { addressMap 5 }

addressMapEntry OBJECT-TYPE
    SYNTAX      AddressMapEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A conceptual row in the addressMapTable.

        The protocolDirLocalIndex in the index identifies the network
        layer protocol of the addressMapNetworkAddress.




        An example of the indexing of this entry is
        addressMapSource.783495.**********.********.*******.*******.1.1.

        Note that some combinations of index values may result in an
        index that exceeds 128 sub-identifiers in length, which exceeds
        the maximum for the SNMP protocol.  Implementations should take
        care to avoid such combinations."
    INDEX { addressMapTimeMark, protocolDirLocalIndex,
            addressMapNetworkAddress, addressMapSource }
    ::= { addressMapTable 1 }

AddressMapEntry ::= SEQUENCE {
    addressMapTimeMark                 TimeFilter,
    addressMapNetworkAddress           OCTET STRING,
    addressMapSource                   OBJECT IDENTIFIER,
    addressMapPhysicalAddress          OCTET STRING,
    addressMapLastChange               TimeStamp
}

addressMapTimeMark OBJECT-TYPE
    SYNTAX      TimeFilter
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A TimeFilter for this entry.  See the TimeFilter textual
        convention to see how this works."
    ::= { addressMapEntry 1 }

addressMapNetworkAddress OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE (1..255))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The network address for this relation.

        This is represented as an octet string with
        specific semantics and length as identified
        by the protocolDirLocalIndex component of the
        index.

        For example, if the protocolDirLocalIndex indicates an
        encapsulation of ip, this object is encoded as a length
        octet of 4, followed by the 4 octets of the IP address,
        in network byte order."
    ::= { addressMapEntry 2 }

addressMapSource OBJECT-TYPE
    SYNTAX      OBJECT IDENTIFIER



    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The interface or port on which the associated network
         address was most recently seen.

        If this address mapping was discovered on an interface, this
        object shall identify the instance of the ifIndex
        object, defined in [RFC2863], for the desired interface.
        For example, if an entry were to receive data from
        interface #1, this object would be set to ifIndex.1.

        If this address mapping was discovered on a port, this
        object shall identify the instance of the rptrGroupPortIndex
        object, defined in [RFC2108], for the desired port.
        For example, if an entry were to receive data from
        group #1, port #1, this object would be set to
        rptrGroupPortIndex.1.1.

        Note that while the dataSource associated with this entry
        may only point to index objects, this object may at times
        point to repeater port objects.  This situation occurs when
        the dataSource points to an interface that is a locally
        attached repeater and the agent has additional information
        about the source port of traffic seen on that repeater."
    ::= { addressMapEntry 3 }

addressMapPhysicalAddress OBJECT-TYPE
    SYNTAX      OCTET STRING
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The last source physical address on which the associated
        network address was seen.  If the protocol of the associated
        network address was encapsulated inside of a network-level or
        higher protocol, this will be the address of the next-lower
        protocol with the addressRecognitionCapable bit enabled and
        will be formatted as specified for that protocol."
    ::= { addressMapEntry 4 }

addressMapLastChange OBJECT-TYPE
    SYNTAX      TimeStamp
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of sysUpTime at the time this entry was last
        created or the values of the physical address changed.




        This can be used to help detect duplicate address problems, in
        which case this object will be updated frequently."
    ::= { addressMapEntry 5 }

--
-- Network Layer Host Group
--
-- Counts the amount of traffic sent from and to each network address
-- discovered by the probe.
-- Note that while the hlHostControlTable also has objects that
-- control an optional alHostTable, implementation of the alHostTable is
-- not required to fully implement this group.

hlHostControlTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF HlHostControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A list of higher-layer (i.e., non-MAC) host table control
        entries.

        These entries will enable the collection of the network- and
        application-level host tables indexed by network addresses.
        Both the network- and application-level host tables are
        controlled by this table so that they will both be created
        and deleted at the same time, further increasing the ease with
        which they can be implemented as a single datastore.  (Note that
        if an implementation stores application-layer host records in
        memory, it can derive network-layer host records from them.)

        Entries in the nlHostTable will be created on behalf of each
        entry in this table.  Additionally, if this probe implements
        the alHostTable, entries in the alHostTable will be created on
        behalf of each entry in this table.

        Implementations are encouraged to add an entry per monitored
        interface upon initialization so that a default collection
        of host statistics is available."
    ::= { nlHost 1 }

hlHostControlEntry OBJECT-TYPE
    SYNTAX      HlHostControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A conceptual row in the hlHostControlTable.

        An example of the indexing of this entry is



        hlHostControlNlDroppedFrames.1"
    INDEX { hlHostControlIndex }
    ::= { hlHostControlTable 1 }

HlHostControlEntry ::= SEQUENCE {
    hlHostControlIndex               Integer32,
    hlHostControlDataSource          DataSource,
    hlHostControlNlDroppedFrames     Counter32,
    hlHostControlNlInserts           Counter32,
    hlHostControlNlDeletes           Counter32,
    hlHostControlNlMaxDesiredEntries Integer32,
    hlHostControlAlDroppedFrames     Counter32,
    hlHostControlAlInserts           Counter32,
    hlHostControlAlDeletes           Counter32,
    hlHostControlAlMaxDesiredEntries Integer32,
    hlHostControlOwner               OwnerString,
    hlHostControlStatus              RowStatus
}

hlHostControlIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        hlHostControlTable.  Each such entry defines
        a function that discovers hosts on a particular
        interface and places statistics about them in the
        nlHostTable, and optionally in the alHostTable, on
        behalf of this hlHostControlEntry."
    ::= { hlHostControlEntry 1 }

hlHostControlDataSource OBJECT-TYPE
    SYNTAX      DataSource
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The source of data for the associated host tables.

        The statistics in this group reflect all packets
        on the local network segment attached to the
        identified interface.

        This object may not be modified if the associated
        hlHostControlStatus object is equal to active(1)."
    ::= { hlHostControlEntry 2 }

hlHostControlNlDroppedFrames OBJECT-TYPE



    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
       "The total number of frames that were received by the probe
        and therefore not accounted for in the *StatsDropEvents, but
        that the probe chose not to count for the associated
        nlHost entries for whatever reason.  Most often, this event
        occurs when the probe is out of some resources and decides to
        shed load from this collection.

        This count does not include packets that were not counted
        because they had MAC-layer errors.

        Note that if the nlHostTable is inactive because no protocols
        are enabled in the protocol directory, this value should be 0.

        Note that, unlike the dropEvents counter, this number is the
        exact number of frames dropped."
    ::= { hlHostControlEntry 3 }

hlHostControlNlInserts OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of times an nlHost entry has been
        inserted into the nlHost table.  If an entry is inserted, then
        deleted, and then inserted, this counter will be incremented
        by 2.

        To allow for efficient implementation strategies, agents may
        delay updating this object for short periods of time.  For
        example, an implementation strategy may allow internal
        data structures to differ from those visible via SNMP for
        short periods of time.  This counter may reflect the internal
        data structures for those short periods of time.

        Note that the table size can be determined by subtracting
        hlHostControlNlDeletes from hlHostControlNlInserts."
    ::= { hlHostControlEntry 4 }

hlHostControlNlDeletes OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of times an nlHost entry has been



        deleted from the nlHost table (for any reason).  If an entry
        is deleted, then inserted, and then deleted, this counter will
        be incremented by 2.

        To allow for efficient implementation strategies, agents may
        delay updating this object for short periods of time.  For
        example, an implementation strategy may allow internal
        data structures to differ from those visible via SNMP for
        short periods of time.  This counter may reflect the internal
        data structures for those short periods of time.

        Note that the table size can be determined by subtracting
        hlHostControlNlDeletes from hlHostControlNlInserts."
    ::= { hlHostControlEntry 5 }

hlHostControlNlMaxDesiredEntries OBJECT-TYPE
    SYNTAX      Integer32 (-1..**********)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum number of entries that are desired in the
        nlHostTable on behalf of this control entry.  The probe will
        not create more than this number of associated entries in the
        table but may choose to create fewer entries in this table
        for any reason, including the lack of resources.

        If this object is set to a value less than the current number
        of entries, enough entries are chosen in an
        implementation-dependent manner and deleted so that the number
        of entries in the table equals the value of this object.

        If this value is set to -1, the probe may create any number
        of entries in this table.  If the associated
        hlHostControlStatus object is equal to 'active', this
        object may not be modified.

        This object may be used to control how resources are allocated
        on the probe for the various RMON functions."
    ::= { hlHostControlEntry 6 }

hlHostControlAlDroppedFrames OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
       "The total number of frames that were received by the probe
        and therefore not accounted for in the *StatsDropEvents, but
        that the probe chose not to count for the associated



        alHost entries for whatever reason.  Most often, this event
        occurs when the probe is out of some resources and decides to
        shed load from this collection.

        This count does not include packets that were not counted
        because they had MAC-layer errors.

        Note that if the alHostTable is not implemented or is inactive
        because no protocols are enabled in the protocol directory,
        this value should be 0.

        Note that, unlike the dropEvents counter, this number is the
        exact number of frames dropped."
    ::= { hlHostControlEntry 7 }

hlHostControlAlInserts OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of times an alHost entry has been
        inserted into the alHost table.  If an entry is inserted, then
        deleted, and then inserted, this counter will be incremented
        by 2.

        To allow for efficient implementation strategies, agents may
        delay updating this object for short periods of time.  For
        example, an implementation strategy may allow internal
        data structures to differ from those visible via SNMP for
        short periods of time.  This counter may reflect the internal
        data structures for those short periods of time.

        Note that the table size can be determined by subtracting
        hlHostControlAlDeletes from hlHostControlAlInserts."
    ::= { hlHostControlEntry 8 }

hlHostControlAlDeletes OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of times an alHost entry has been
        deleted from the alHost table (for any reason).  If an entry
        is deleted, then inserted, and then deleted, this counter will
        be incremented by 2.

        To allow for efficient implementation strategies, agents may
        delay updating this object for short periods of time.  For



        example, an implementation strategy may allow internal
        data structures to differ from those visible via SNMP for
        short periods of time.  This counter may reflect the internal
        data structures for those short periods of time.

        Note that the table size can be determined by subtracting
        hlHostControlAlDeletes from hlHostControlAlInserts."
    ::= { hlHostControlEntry 9 }

hlHostControlAlMaxDesiredEntries OBJECT-TYPE
    SYNTAX      Integer32 (-1..**********)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The maximum number of entries that are desired in the alHost
        table on behalf of this control entry.  The probe will not
        create more than this number of associated entries in the
        table but may choose to create fewer entries in this table
        for any reason, including the lack of resources.

        If this object is set to a value less than the current number
        of entries, enough entries are chosen in an
        implementation-dependent manner and deleted so that the number
        of entries in the table equals the value of this object.

        If this value is set to -1, the probe may create any number
        of entries in this table.  If the associated
        hlHostControlStatus object is equal to 'active', this
        object may not be modified.

        This object may be used to control how resources are allocated
        on the probe for the various RMON functions."
    ::= { hlHostControlEntry 10 }

hlHostControlOwner OBJECT-TYPE
    SYNTAX      OwnerString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The entity that configured this entry and is
        therefore using the resources assigned to it."
    ::= { hlHostControlEntry 11 }

hlHostControlStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION



        "The status of this hlHostControlEntry.

        An entry may not exist in the active state unless all
        objects in the entry have an appropriate value.

        If this object is not equal to active(1), all associated
        entries in the nlHostTable and alHostTable shall be deleted."
    ::= { hlHostControlEntry 12 }

nlHostTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF NlHostEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A collection of statistics for a particular network layer
        address that has been discovered on an interface of this
        device.

        The probe will populate this table for all network layer
        protocols in the protocol directory table whose value of
        protocolDirHostConfig is equal to supportedOn(3), and
        will delete any entries whose protocolDirEntry is deleted or
        has a protocolDirHostConfig value of supportedOff(2).

        The probe will add to this table all addresses seen
        as the source or destination address in all packets with no
        MAC errors, and will increment octet and packet counts in the
        table for all packets with no MAC errors."
::= { nlHost 2 }

nlHostEntry OBJECT-TYPE
    SYNTAX      NlHostEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A conceptual row in the nlHostTable.

        The hlHostControlIndex value in the index identifies the
        hlHostControlEntry on whose behalf this entry was created.
        The protocolDirLocalIndex value in the index identifies the
        network layer protocol of the nlHostAddress.

        An example of the indexing of this entry is
        nlHostOutPkts.1.783495.**********.6.6.

        Note that some combinations of index values may result in an
        index that exceeds 128 sub-identifiers in length, which exceeds
        the maximum for the SNMP protocol.  Implementations should take



        care to avoid such combinations."
    INDEX { hlHostControlIndex, nlHostTimeMark,
            protocolDirLocalIndex, nlHostAddress }
    ::= { nlHostTable 1 }

NlHostEntry ::= SEQUENCE {
    nlHostTimeMark              TimeFilter,
    nlHostAddress               OCTET STRING,
    nlHostInPkts                ZeroBasedCounter32,
    nlHostOutPkts               ZeroBasedCounter32,
    nlHostInOctets              ZeroBasedCounter32,
    nlHostOutOctets             ZeroBasedCounter32,
    nlHostOutMacNonUnicastPkts  ZeroBasedCounter32,
    nlHostCreateTime            LastCreateTime
}

nlHostTimeMark OBJECT-TYPE
    SYNTAX      TimeFilter
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A TimeFilter for this entry.  See the TimeFilter textual
        convention to see how this works."
    ::= { nlHostEntry 1 }

nlHostAddress OBJECT-TYPE
    SYNTAX      OCTET STRING  (SIZE (1..255))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The network address for this nlHostEntry.

        This is represented as an octet string with
        specific semantics and length as identified
        by the protocolDirLocalIndex component of the index.

        For example, if the protocolDirLocalIndex indicates an
        encapsulation of IP, this object is encoded as a length
        octet of 4, followed by the 4 octets of the IP address,
        in network byte order."
    ::= { nlHostEntry 2 }

nlHostInPkts OBJECT-TYPE
    SYNTAX      ZeroBasedCounter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of packets without errors transmitted to



        this address since it was added to the nlHostTable.  Note that
        this is the number of link-layer packets, so if a single
        network-layer packet is fragmented into several link-layer
        frames, this counter is incremented several times."
    ::= { nlHostEntry 3 }

nlHostOutPkts OBJECT-TYPE
    SYNTAX      ZeroBasedCounter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of packets without errors transmitted by
        this address since it was added to the nlHostTable.  Note that
        this is the number of link-layer packets, so if a single
        network-layer packet is fragmented into several link-layer
        frames, this counter is incremented several times."
    ::= { nlHostEntry 4 }

nlHostInOctets OBJECT-TYPE
    SYNTAX      ZeroBasedCounter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of octets transmitted to this address
        since it was added to the nlHostTable (excluding
        framing bits, but including FCS octets), excluding
        octets in packets that contained errors.

        Note that this doesn't count just those octets in the particular
        protocol frames but includes the entire packet that contained
        the protocol."
    ::= { nlHostEntry 5 }

nlHostOutOctets OBJECT-TYPE
    SYNTAX      ZeroBasedCounter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of octets transmitted by this address
        since it was added to the nlHostTable (excluding
        framing bits, but including FCS octets), excluding
        octets in packets that contained errors.

        Note that this doesn't count just those octets in the particular
        protocol frames but includes the entire packet that contained
        the protocol."
    ::= { nlHostEntry 6 }




nlHostOutMacNonUnicastPkts OBJECT-TYPE
    SYNTAX      ZeroBasedCounter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of packets without errors transmitted by this
        address that were directed to any MAC broadcast addresses
        or to any MAC multicast addresses since this host was
        added to the nlHostTable.  Note that this is the number of
        link-layer packets, so if a single network-layer packet is
        fragmented into several link-layer frames, this counter is
        incremented several times."
    ::= { nlHostEntry 7 }

nlHostCreateTime OBJECT-TYPE
    SYNTAX     LastCreateTime
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The value of sysUpTime when this entry was last activated.
        This can be used by the management station to ensure that the
        entry has not been deleted and recreated between polls."
    ::= { nlHostEntry 8 }

--
-- Network Layer Matrix Group
--
-- Counts the amount of traffic sent between each pair of network
-- addresses discovered by the probe.
-- Note that while the hlMatrixControlTable also has objects that
-- control optional alMatrixTables, implementation of the
-- alMatrixTables is not required to fully implement this group.

hlMatrixControlTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF HlMatrixControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A list of higher-layer (i.e., non-MAC) matrix control entries.

        These entries will enable the collection of the network- and
        application-level matrix tables containing conversation
        statistics indexed by pairs of network addresses.
        Both the network- and application-level matrix tables are
        controlled by this table so that they will both be created
        and deleted at the same time, further increasing the ease with
        which they can be implemented as a single datastore.  (Note that
        if an implementation stores application-layer matrix records



        in memory, it can derive network-layer matrix records from
        them.)

        Entries in the nlMatrixSDTable and nlMatrixDSTable will be
        created on behalf of each entry in this table.  Additionally,
        if this probe implements the alMatrix tables, entries in the
        alMatrix tables will be created on behalf of each entry in
        this table."
    ::= { nlMatrix 1 }

hlMatrixControlEntry OBJECT-TYPE
    SYNTAX      HlMatrixControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A conceptual row in the hlMatrixControlTable.

        An example of indexing of this entry is
        hlMatrixControlNlDroppedFrames.1"
    INDEX { hlMatrixControlIndex }
    ::= { hlMatrixControlTable 1 }

HlMatrixControlEntry ::= SEQUENCE {
    hlMatrixControlIndex                  Integer32,
    hlMatrixControlDataSource             DataSource,
    hlMatrixControlNlDroppedFrames        Counter32,
    hlMatrixControlNlInserts              Counter32,
    hlMatrixControlNlDeletes              Counter32,
    hlMatrixControlNlMaxDesiredEntries    Integer32,
    hlMatrixControlAlDroppedFrames        Counter32,
    hlMatrixControlAlInserts              Counter32,
    hlMatrixControlAlDeletes              Counter32,
    hlMatrixControlAlMaxDesiredEntries    Integer32,
    hlMatrixControlOwner                  OwnerString,
    hlMatrixControlStatus                 RowStatus
}

hlMatrixControlIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        hlMatrixControlTable.  Each such entry defines
        a function that discovers conversations on a particular
        interface and places statistics about them in the
        nlMatrixSDTable and the nlMatrixDSTable, and optionally the
        alMatrixSDTable and alMatrixDSTable, on behalf of this



        hlMatrixControlEntry."
    ::= { hlMatrixControlEntry 1 }

hlMatrixControlDataSource OBJECT-TYPE
    SYNTAX      DataSource
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The source of the data for the associated matrix tables.

        The statistics in this group reflect all packets
        on the local network segment attached to the
        identified interface.

        This object may not be modified if the associated
        hlMatrixControlStatus object is equal to active(1)."
    ::= { hlMatrixControlEntry 2 }

hlMatrixControlNlDroppedFrames OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
       "The total number of frames that were received by the probe
        and therefore not accounted for in the *StatsDropEvents, but
        that the probe chose not to count for this entry for
        whatever reason.  Most often, this event occurs when the probe
        is out of some resources and decides to shed load from this
        collection.

        This count does not include packets that were not counted
        because they had MAC-layer errors.

        Note that if the nlMatrixTables are inactive because no
        protocols are enabled in the protocol directory, this value
        should be 0.

        Note that, unlike the dropEvents counter, this number is the
        exact number of frames dropped."
    ::= { hlMatrixControlEntry 3 }

hlMatrixControlNlInserts OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of times an nlMatrix entry has been
        inserted into the nlMatrix tables.  If an entry is inserted,



        then deleted, and then inserted, this counter will be
        incremented by 2.  The addition of a conversation into both
        the nlMatrixSDTable and nlMatrixDSTable shall be counted as
        two insertions (even though every addition into one table must
        be accompanied by an insertion into the other).

        To allow for efficient implementation strategies, agents may
        delay updating this object for short periods of time.  For
        example, an implementation strategy may allow internal
        data structures to differ from those visible via SNMP for
        short periods of time.  This counter may reflect the internal
        data structures for those short periods of time.

        Note that the sum of then nlMatrixSDTable and nlMatrixDSTable
        sizes can be determined by subtracting
        hlMatrixControlNlDeletes from hlMatrixControlNlInserts."
    ::= { hlMatrixControlEntry 4 }

hlMatrixControlNlDeletes OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of times an nlMatrix entry has been
        deleted from the nlMatrix tables (for any reason).  If an
        entry is deleted, then inserted, and then deleted, this
        counter will be incremented by 2.  The deletion of a
        conversation from both the nlMatrixSDTable and nlMatrixDSTable
        shall be counted as two deletions (even though every deletion
        from one table must be accompanied by a deletion from the
        other).

        To allow for efficient implementation strategies, agents may
        delay updating this object for short periods of time.  For
        example, an implementation strategy may allow internal
        data structures to differ from those visible via SNMP for
        short periods of time.  This counter may reflect the internal
        data structures for those short periods of time.

        Note that the table size can be determined by subtracting
        hlMatrixControlNlDeletes from hlMatrixControlNlInserts."
    ::= { hlMatrixControlEntry 5 }

hlMatrixControlNlMaxDesiredEntries OBJECT-TYPE
    SYNTAX      Integer32 (-1..**********)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION



        "The maximum number of entries that are desired in the
        nlMatrix tables on behalf of this control entry.  The probe
        will not create more than this number of associated entries in
        the table but may choose to create fewer entries in this
        table for any reason, including the lack of resources.

        If this object is set to a value less than the current number
        of entries, enough entries are chosen in an
        implementation-dependent manner and deleted so that the number
        of entries in the table equals the value of this object.

        If this value is set to -1, the probe may create any number
        of entries in this table.  If the associated
        hlMatrixControlStatus object is equal to 'active', this
        object may not be modified.

        This object may be used to control how resources are allocated
        on the probe for the various RMON functions."
    ::= { hlMatrixControlEntry 6 }

hlMatrixControlAlDroppedFrames OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
       "The total number of frames that were received by the probe
        and therefore not accounted for in the *StatsDropEvents, but
        that the probe chose not to count for this entry for
        whatever reason.  Most often, this event occurs when the probe
        is out of some resources and decides to shed load from this
        collection.

        This count does not include packets that were not counted
        because they had MAC-layer errors.

        Note that if the alMatrixTables are not implemented or are
        inactive because no protocols are enabled in the protocol
        directory, this value should be 0.

        Note that, unlike the dropEvents counter, this number is the
        exact number of frames dropped."
    ::= { hlMatrixControlEntry 7 }

hlMatrixControlAlInserts OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION



        "The number of times an alMatrix entry has been
        inserted into the alMatrix tables.  If an entry is inserted,
        then deleted, and then inserted, this counter will be
        incremented by 2.  The addition of a conversation into both
        the alMatrixSDTable and alMatrixDSTable shall be counted as
        two insertions (even though every addition into one table must
        be accompanied by an insertion into the other).

        To allow for efficient implementation strategies, agents may
        delay updating this object for short periods of time.  For
        example, an implementation strategy may allow internal
        data structures to differ from those visible via SNMP for
        short periods of time.  This counter may reflect the internal
        data structures for those short periods of time.

        Note that the table size can be determined by subtracting
        hlMatrixControlAlDeletes from hlMatrixControlAlInserts."
    ::= { hlMatrixControlEntry 8 }

hlMatrixControlAlDeletes OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of times an alMatrix entry has been
        deleted from the alMatrix tables.  If an entry is deleted,
        then inserted, and then deleted, this counter will be
        incremented by 2.  The deletion of a conversation from both
        the alMatrixSDTable and alMatrixDSTable shall be counted as
        two deletions (even though every deletion from one table must
        be accompanied by a deletion from the other).

        To allow for efficient implementation strategies, agents may
        delay updating this object for short periods of time.  For
        example, an implementation strategy may allow internal
        data structures to differ from those visible via SNMP for
        short periods of time.  This counter may reflect the internal
        data structures for those short periods of time.

        Note that the table size can be determined by subtracting
        hlMatrixControlAlDeletes from hlMatrixControlAlInserts."
    ::= { hlMatrixControlEntry 9 }

hlMatrixControlAlMaxDesiredEntries OBJECT-TYPE
    SYNTAX      Integer32 (-1..**********)
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION



        "The maximum number of entries that are desired in the
        alMatrix tables on behalf of this control entry.  The probe
        will not create more than this number of associated entries in
        the table but may choose to create fewer entries in this
        table for any reason, including the lack of resources.

        If this object is set to a value less than the current number
        of entries, enough entries are chosen in an
        implementation-dependent manner and deleted so that the number
        of entries in the table equals the value of this object.

        If this value is set to -1, the probe may create any number
        of entries in this table.  If the associated
        hlMatrixControlStatus object is equal to 'active', this
        object may not be modified.

        This object may be used to control how resources are allocated
        on the probe for the various RMON functions."
    ::= { hlMatrixControlEntry 10 }

hlMatrixControlOwner OBJECT-TYPE
    SYNTAX      OwnerString
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The entity that configured this entry and is
        therefore using the resources assigned to it."
    ::= { hlMatrixControlEntry 11 }

hlMatrixControlStatus OBJECT-TYPE
    SYNTAX      RowStatus
    MAX-ACCESS  read-create
    STATUS      current
    DESCRIPTION
        "The status of this hlMatrixControlEntry.

        An entry may not exist in the active state unless all
        objects in the entry have an appropriate value.

        If this object is not equal to active(1), all
        associated entries in the nlMatrixSDTable,
        nlMatrixDSTable, alMatrixSDTable, and alMatrixDSTable
        shall be deleted by the agent."
    ::= { hlMatrixControlEntry 12 }

nlMatrixSDTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF NlMatrixSDEntry
    MAX-ACCESS  not-accessible



    STATUS      current
    DESCRIPTION
        "A list of traffic matrix entries that collect statistics for
        conversations between two network-level addresses.  This table
        is indexed first by the source address and then by the
        destination address to make it convenient to collect all
        conversations from a particular address.

        The probe will populate this table for all network layer
        protocols in the protocol directory table whose value of
        protocolDirMatrixConfig is equal to supportedOn(3), and
        will delete any entries whose protocolDirEntry is deleted or
        has a protocolDirMatrixConfig value of supportedOff(2).

        The probe will add to this table all pairs of addresses
        seen in all packets with no MAC errors and will increment
        octet and packet counts in the table for all packets with no
        MAC errors.

        Further, this table will only contain entries that have a
        corresponding entry in the nlMatrixDSTable with the same
        source address and destination address."
    ::= { nlMatrix 2 }

nlMatrixSDEntry OBJECT-TYPE
    SYNTAX      NlMatrixSDEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A conceptual row in the nlMatrixSDTable.

        The hlMatrixControlIndex value in the index identifies the
        hlMatrixControlEntry on whose behalf this entry was created.
        The protocolDirLocalIndex value in the index identifies the
        network-layer protocol of the nlMatrixSDSourceAddress and
        nlMatrixSDDestAddress.

        An example of the indexing of this table is
        nlMatrixSDPkts.1.783495.**********.*********.2.6.7.

        Note that some combinations of index values may result in an
        index that exceeds 128 sub-identifiers in length, which exceeds
        the maximum for the SNMP protocol.  Implementations should take
        care to avoid such combinations."
    INDEX { hlMatrixControlIndex, nlMatrixSDTimeMark,
            protocolDirLocalIndex,
            nlMatrixSDSourceAddress, nlMatrixSDDestAddress }
    ::= { nlMatrixSDTable 1 }



NlMatrixSDEntry ::= SEQUENCE {
    nlMatrixSDTimeMark              TimeFilter,
    nlMatrixSDSourceAddress         OCTET STRING,
    nlMatrixSDDestAddress           OCTET STRING,
    nlMatrixSDPkts                  ZeroBasedCounter32,
    nlMatrixSDOctets                ZeroBasedCounter32,
    nlMatrixSDCreateTime            LastCreateTime
}

nlMatrixSDTimeMark OBJECT-TYPE
    SYNTAX      TimeFilter
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A TimeFilter for this entry.  See the TimeFilter textual
        convention to see how this works."
    ::= { nlMatrixSDEntry 1 }

nlMatrixSDSourceAddress OBJECT-TYPE
    SYNTAX      OCTET STRING  (SIZE (1..255))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The network source address for this nlMatrixSDEntry.

        This is represented as an octet string with
        specific semantics and length as identified
        by the protocolDirLocalIndex component of the index.

        For example, if the protocolDirLocalIndex indicates an
        encapsulation of IP, this object is encoded as a length
        octet of 4, followed by the 4 octets of the IP address,
        in network byte order."
    ::= { nlMatrixSDEntry 2 }

nlMatrixSDDestAddress OBJECT-TYPE
    SYNTAX      OCTET STRING  (SIZE (1..255))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The network destination address for this
        nlMatrixSDEntry.

        This is represented as an octet string with
        specific semantics and length as identified
        by the protocolDirLocalIndex component of the index.

        For example, if the protocolDirLocalIndex indicates an



        encapsulation of IP, this object is encoded as a length
        octet of 4, followed by the 4 octets of the IP address,
        in network byte order."
    ::= { nlMatrixSDEntry 3 }

nlMatrixSDPkts OBJECT-TYPE
    SYNTAX      ZeroBasedCounter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of packets without errors transmitted from the
        source address to the destination address since this entry was
        added to the nlMatrixSDTable.  Note that this is the number of
        link-layer packets, so if a single network-layer packet is
        fragmented into several link-layer frames, this counter is
        incremented several times."
    ::= { nlMatrixSDEntry 4 }

nlMatrixSDOctets OBJECT-TYPE
    SYNTAX      ZeroBasedCounter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of octets transmitted from the source address to
        the destination address since this entry was added to the
        nlMatrixSDTable (excluding framing bits, but
        including FCS octets), excluding octets in packets that
        contained errors.

        Note that this doesn't count just those octets in the particular
        protocol frames but includes the entire packet that contained
        the protocol."
    ::= { nlMatrixSDEntry 5 }

nlMatrixSDCreateTime OBJECT-TYPE
    SYNTAX     LastCreateTime
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The value of sysUpTime when this entry was last activated.
        This can be used by the management station to ensure that the
        entry has not been deleted and recreated between polls."
    ::= { nlMatrixSDEntry 6 }


-- Traffic matrix tables from destination to source

nlMatrixDSTable OBJECT-TYPE



    SYNTAX      SEQUENCE OF NlMatrixDSEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A list of traffic matrix entries that collect statistics for
        conversations between two network-level addresses.  This table
        is indexed first by the destination address and then by the
        source address to make it convenient to collect all
        conversations to a particular address.

        The probe will populate this table for all network layer
        protocols in the protocol directory table whose value of
        protocolDirMatrixConfig is equal to supportedOn(3), and
        will delete any entries whose protocolDirEntry is deleted or
        has a protocolDirMatrixConfig value of supportedOff(2).

        The probe will add to this table all pairs of addresses
        seen in all packets with no MAC errors and will increment
        octet and packet counts in the table for all packets with no
        MAC errors.

        Further, this table will only contain entries that have a
        corresponding entry in the nlMatrixSDTable with the same
        source address and destination address."
    ::= { nlMatrix 3 }

nlMatrixDSEntry OBJECT-TYPE
    SYNTAX      NlMatrixDSEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A conceptual row in the nlMatrixDSTable.

        The hlMatrixControlIndex value in the index identifies the
        hlMatrixControlEntry on whose behalf this entry was created.
        The protocolDirLocalIndex value in the index identifies the
        network-layer protocol of the nlMatrixDSSourceAddress and
        nlMatrixDSDestAddress.

        An example of the indexing of this table is
        nlMatrixDSPkts.1.783495.**********.*********.2.6.6.

        Note that some combinations of index values may result in an
        index that exceeds 128 sub-identifiers in length, which exceeds
        the maximum for the SNMP protocol.  Implementations should take
        care to avoid such combinations."
    INDEX { hlMatrixControlIndex, nlMatrixDSTimeMark,
            protocolDirLocalIndex,



            nlMatrixDSDestAddress, nlMatrixDSSourceAddress }
    ::= { nlMatrixDSTable 1 }

NlMatrixDSEntry ::= SEQUENCE {
    nlMatrixDSTimeMark                 TimeFilter,
    nlMatrixDSSourceAddress            OCTET STRING,
    nlMatrixDSDestAddress              OCTET STRING,
    nlMatrixDSPkts                     ZeroBasedCounter32,
    nlMatrixDSOctets                   ZeroBasedCounter32,
    nlMatrixDSCreateTime               LastCreateTime
}

nlMatrixDSTimeMark OBJECT-TYPE
    SYNTAX      TimeFilter
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A TimeFilter for this entry.  See the TimeFilter textual
        convention to see how this works."
    ::= { nlMatrixDSEntry 1 }

nlMatrixDSSourceAddress OBJECT-TYPE
    SYNTAX      OCTET STRING  (SIZE (1..255))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The network source address for this nlMatrixDSEntry.

        This is represented as an octet string with
        specific semantics and length as identified
        by the protocolDirLocalIndex component of the index.

        For example, if the protocolDirLocalIndex indicates an
        encapsulation of IP, this object is encoded as a length
        octet of 4, followed by the 4 octets of the IP address,
        in network byte order."
    ::= { nlMatrixDSEntry 2 }

nlMatrixDSDestAddress OBJECT-TYPE
    SYNTAX      OCTET STRING  (SIZE (1..255))
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The network destination address for this
        nlMatrixDSEntry.

        This is represented as an octet string with
        specific semantics and length as identified



        by the protocolDirLocalIndex component of the index.

        For example, if the protocolDirLocalIndex indicates an
        encapsulation of IP, this object is encoded as a length
        octet of 4, followed by the 4 octets of the IP address,
        in network byte order."
    ::= { nlMatrixDSEntry 3 }

nlMatrixDSPkts OBJECT-TYPE
    SYNTAX      ZeroBasedCounter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of packets without errors transmitted from the
        source address to the destination address since this entry was
        added to the nlMatrixDSTable.  Note that this is the number of
        link-layer packets, so if a single network-layer packet is
        fragmented into several link-layer frames, this counter is
        incremented several times."
    ::= { nlMatrixDSEntry 4 }

nlMatrixDSOctets OBJECT-TYPE
    SYNTAX      ZeroBasedCounter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of octets transmitted from the source address
        to the destination address since this entry was added to the
        nlMatrixDSTable (excluding framing bits, but
        including FCS octets), excluding octets in packets that
        contained errors.

        Note that this doesn't count just those octets in the particular
        protocol frames but includes the entire packet that contained
        the protocol."
    ::= { nlMatrixDSEntry 5 }

nlMatrixDSCreateTime OBJECT-TYPE
    SYNTAX     LastCreateTime
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The value of sysUpTime when this entry was last activated.
        This can be used by the management station to ensure that the
        entry has not been deleted and recreated between polls."
    ::= { nlMatrixDSEntry 6 }

nlMatrixTopNControlTable OBJECT-TYPE



    SYNTAX      SEQUENCE OF NlMatrixTopNControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A set of parameters that control the creation of a
        report of the top N matrix entries according to
        a selected metric."
    ::= { nlMatrix 4 }

nlMatrixTopNControlEntry OBJECT-TYPE
    SYNTAX      NlMatrixTopNControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A conceptual row in the nlMatrixTopNControlTable.

        An example of the indexing of this table is
        nlMatrixTopNControlDuration.3"
    INDEX { nlMatrixTopNControlIndex }
    ::= { nlMatrixTopNControlTable 1 }

NlMatrixTopNControlEntry ::= SEQUENCE {
    nlMatrixTopNControlIndex            Integer32,
    nlMatrixTopNControlMatrixIndex      Integer32,
    nlMatrixTopNControlRateBase         INTEGER,
    nlMatrixTopNControlTimeRemaining    Integer32,
    nlMatrixTopNControlGeneratedReports Counter32,
    nlMatrixTopNControlDuration         Integer32,
    nlMatrixTopNControlRequestedSize    Integer32,
    nlMatrixTopNControlGrantedSize      Integer32,
    nlMatrixTopNControlStartTime        TimeStamp,
    nlMatrixTopNControlOwner            OwnerString,
    nlMatrixTopNControlStatus           RowStatus
}

nlMatrixTopNControlIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry
        in the nlMatrixTopNControlTable.  Each such
        entry defines one topN report prepared for
        one interface."
    ::= { nlMatrixTopNControlEntry 1 }

nlMatrixTopNControlMatrixIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)



    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "The nlMatrix[SD/DS] table for which a topN report will be
        prepared on behalf of this entry.  The nlMatrix[SD/DS] table
        is identified by the value of the hlMatrixControlIndex
        for that table - that value is used here to identify the
        particular table.

        This object may not be modified if the associated
        nlMatrixTopNControlStatus object is equal to active(1)."
    ::= { nlMatrixTopNControlEntry 2 }

nlMatrixTopNControlRateBase OBJECT-TYPE
    SYNTAX      INTEGER {
                    nlMatrixTopNPkts(1),
                    nlMatrixTopNOctets(2),
                    nlMatrixTopNHighCapacityPkts(3),
                    nlMatrixTopNHighCapacityOctets(4)
                }
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "The variable for each nlMatrix[SD/DS] entry that the
        nlMatrixTopNEntries are sorted by, as well as a control
        for the table that the results will be reported in.

        This object may not be modified if the associated
        nlMatrixTopNControlStatus object is equal to active(1).

        If this value is less than or equal to 2, when the report
        is prepared, entries are created in the nlMatrixTopNTable
        associated with this object.
        If this value is greater than or equal to 3, when the report
        is prepared, entries are created in the
        nlMatrixTopNHighCapacityTable associated with this object."
    ::= { nlMatrixTopNControlEntry 3 }

nlMatrixTopNControlTimeRemaining OBJECT-TYPE
    SYNTAX     Integer32 (0..**********)
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "The number of seconds left in the report currently
        being collected.  When this object is modified by
        the management station, a new collection is started,
        possibly aborting a currently running report.  The
        new value is used as the requested duration of this



        report and is immediately loaded into the associated
        nlMatrixTopNControlDuration object.

        When the report finishes, the probe will automatically
        start another collection with the same initial value
        of nlMatrixTopNControlTimeRemaining.  Thus, the management
        station may simply read the resulting reports repeatedly,
        checking the startTime and duration each time to ensure that a
        report was not missed or that the report parameters were not
        changed.

        While the value of this object is non-zero, it decrements
        by one per second until it reaches zero.  At the time
        that this object decrements to zero, the report is made
        accessible in the nlMatrixTopNTable, overwriting any report
        that may be there.

        When this object is modified by the management station, any
        associated entries in the nlMatrixTopNTable shall be deleted.

        (Note that this is a different algorithm than the one used
        in the hostTopNTable)."
    DEFVAL { 1800 }
    ::= { nlMatrixTopNControlEntry 4 }

nlMatrixTopNControlGeneratedReports OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of reports that have been generated by this entry."
    ::= { nlMatrixTopNControlEntry 5 }

nlMatrixTopNControlDuration OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of seconds that this report has collected
        during the last sampling interval.

        When the associated nlMatrixTopNControlTimeRemaining object is
        set, this object shall be set by the probe to the
        same value and shall not be modified until the next
        time the nlMatrixTopNControlTimeRemaining is set.

        This value shall be zero if no reports have been
        requested for this nlMatrixTopNControlEntry."



    ::= { nlMatrixTopNControlEntry 6 }

nlMatrixTopNControlRequestedSize OBJECT-TYPE
    SYNTAX     Integer32 (0..**********)
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "The maximum number of matrix entries requested for this report.

        When this object is created or modified, the probe
        should set nlMatrixTopNControlGrantedSize as closely to this
        object as possible for the particular probe
        implementation and available resources."
    DEFVAL { 150 }
    ::= { nlMatrixTopNControlEntry 7 }

nlMatrixTopNControlGrantedSize OBJECT-TYPE
    SYNTAX     Integer32 (0..**********)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The maximum number of matrix entries in this report.

        When the associated nlMatrixTopNControlRequestedSize object is
        created or modified, the probe should set this
        object as closely to the requested value as
        possible for the particular implementation and
        available resources.  The probe must not lower this
        value except as a side-effect of a set to the associated
        nlMatrixTopNControlRequestedSize object.

        If the value of nlMatrixTopNControlRateBase is equal to
        nlMatrixTopNPkts, when the next topN report is generated,
        matrix entries with the highest value of nlMatrixTopNPktRate
        shall be placed in this table in decreasing order of this rate
        until there is no more room or until there are no more
        matrix entries.

        If the value of nlMatrixTopNControlRateBase is equal to
        nlMatrixTopNOctets, when the next topN report is generated,
        matrix entries with the highest value of nlMatrixTopNOctetRate
        shall be placed in this table in decreasing order of this rate
        until there is no more room or until there are no more
        matrix entries.

        It is an implementation-specific matter how entries with the
        same value of nlMatrixTopNPktRate or nlMatrixTopNOctetRate are
        sorted.  It is also an implementation-specific matter as to



        whether zero-valued entries are available."
    ::= { nlMatrixTopNControlEntry 8 }

nlMatrixTopNControlStartTime OBJECT-TYPE
    SYNTAX     TimeStamp
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The value of sysUpTime when this topN report was
        last started.  In other words, this is the time that
        the associated nlMatrixTopNControlTimeRemaining object was
        modified to start the requested report or the time
        the report was last automatically (re)started.

        This object may be used by the management station to
        determine whether a report was missed."
    ::= { nlMatrixTopNControlEntry 9 }

nlMatrixTopNControlOwner OBJECT-TYPE
    SYNTAX     OwnerString
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "The entity that configured this entry and is
        therefore using the resources assigned to it."
    ::= { nlMatrixTopNControlEntry 10 }

nlMatrixTopNControlStatus OBJECT-TYPE
    SYNTAX     RowStatus
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "The status of this nlMatrixTopNControlEntry.

        An entry may not exist in the active state unless all
        objects in the entry have an appropriate value.

        If this object is not equal to active(1), all
        associated entries in the nlMatrixTopNTable shall be deleted
        by the agent."
    ::= { nlMatrixTopNControlEntry 11 }

nlMatrixTopNTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF NlMatrixTopNEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "A set of statistics for those network-layer matrix entries



        that have counted the highest number of octets or packets."
    ::= { nlMatrix 5 }

nlMatrixTopNEntry OBJECT-TYPE
    SYNTAX     NlMatrixTopNEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "A conceptual row in the nlMatrixTopNTable.

        The nlMatrixTopNControlIndex value in the index identifies the
        nlMatrixTopNControlEntry on whose behalf this entry was
        created.

        An example of the indexing of this table is
        nlMatrixTopNPktRate.3.10"
    INDEX { nlMatrixTopNControlIndex, nlMatrixTopNIndex }
    ::= { nlMatrixTopNTable 1 }

NlMatrixTopNEntry ::= SEQUENCE {
    nlMatrixTopNIndex                 Integer32,
    nlMatrixTopNProtocolDirLocalIndex Integer32,
    nlMatrixTopNSourceAddress         OCTET STRING,
    nlMatrixTopNDestAddress           OCTET STRING,
    nlMatrixTopNPktRate               Gauge32,
    nlMatrixTopNReversePktRate        Gauge32,
    nlMatrixTopNOctetRate             Gauge32,
    nlMatrixTopNReverseOctetRate      Gauge32
}

nlMatrixTopNIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in
        the nlMatrixTopNTable among those in the same report.
        This index is between 1 and N, where N is the
        number of entries in this report.

        If the value of nlMatrixTopNControlRateBase is equal to
        nlMatrixTopNPkts, increasing values of nlMatrixTopNIndex shall
        be assigned to entries with decreasing values of
        nlMatrixTopNPktRate until index N is assigned or there are no
        more nlMatrixTopNEntries.

        If the value of nlMatrixTopNControlRateBase is equal to
        nlMatrixTopNOctets, increasing values of nlMatrixTopNIndex



        shall be assigned to entries with decreasing values of
        nlMatrixTopNOctetRate until index N is assigned or there are
        no more nlMatrixTopNEntries."
    ::= { nlMatrixTopNEntry 1 }

nlMatrixTopNProtocolDirLocalIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..**********)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The protocolDirLocalIndex of the network-layer protocol of
        this entry's network address."
    ::= { nlMatrixTopNEntry 2 }

nlMatrixTopNSourceAddress OBJECT-TYPE
    SYNTAX     OCTET STRING  (SIZE (1..255))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The network-layer address of the source host in this
        conversation.

        This is represented as an octet string with
        specific semantics and length as identified
        by the associated nlMatrixTopNProtocolDirLocalIndex.

        For example, if the protocolDirLocalIndex indicates an
        encapsulation of IP, this object is encoded as a length
        octet of 4, followed by the 4 octets of the IP address,
        in network byte order."
    ::= { nlMatrixTopNEntry 3 }

nlMatrixTopNDestAddress OBJECT-TYPE
    SYNTAX     OCTET STRING  (SIZE (1..255))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The network-layer address of the destination host in this
        conversation.

        This is represented as an octet string with
        specific semantics and length as identified
        by the associated nlMatrixTopNProtocolDirLocalIndex.

        For example, if the nlMatrixTopNProtocolDirLocalIndex
        indicates an encapsulation of IP, this object is encoded as a
        length octet of 4, followed by the 4 octets of the IP address,
        in network byte order."



    ::= { nlMatrixTopNEntry 4 }

nlMatrixTopNPktRate OBJECT-TYPE
    SYNTAX     Gauge32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of packets seen from the source host
        to the destination host during this sampling interval, counted
        using the rules for counting the nlMatrixSDPkts object.
        If the value of nlMatrixTopNControlRateBase is
        nlMatrixTopNPkts, this variable will be used to sort this
        report."
    ::= { nlMatrixTopNEntry 5 }

nlMatrixTopNReversePktRate OBJECT-TYPE
    SYNTAX     Gauge32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of packets seen from the destination host to the
        source host during this sampling interval, counted
        using the rules for counting the nlMatrixSDPkts object.  (Note
        that the corresponding nlMatrixSDPkts object selected is the
        one whose source address is equal to nlMatrixTopNDestAddress
        and whose destination address is equal to
        nlMatrixTopNSourceAddress.)

        Note that if the value of nlMatrixTopNControlRateBase is equal
        to nlMatrixTopNPkts, the sort of topN entries is based
        entirely on nlMatrixTopNPktRate, and not on the value of this
        object."
    ::= { nlMatrixTopNEntry 6 }

nlMatrixTopNOctetRate OBJECT-TYPE
    SYNTAX     Gauge32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of octets seen from the source host
        to the destination host during this sampling interval, counted
        using the rules for counting the nlMatrixSDOctets object.  If
        the value of nlMatrixTopNControlRateBase is
        nlMatrixTopNOctets, this variable will be used to sort this
        report."
    ::= { nlMatrixTopNEntry 7 }

nlMatrixTopNReverseOctetRate OBJECT-TYPE



    SYNTAX     Gauge32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of octets seen from the destination host to the
        source host during this sampling interval, counted
        using the rules for counting the nlMatrixDSOctets object.  (Note
        that the corresponding nlMatrixSDOctets object selected is the
        one whose source address is equal to nlMatrixTopNDestAddress
        and whose destination address is equal to
        nlMatrixTopNSourceAddress.)

        Note that if the value of nlMatrixTopNControlRateBase is equal
        to nlMatrixTopNOctets, the sort of topN entries is based
        entirely on nlMatrixTopNOctetRate, and not on the value of
        this object."
    ::= { nlMatrixTopNEntry 8 }

-- Application Layer Functions
--
-- The application layer host, matrix, and matrixTopN functions report
-- on protocol usage at the network layer or higher.  Note that the
-- use of the term application layer does not imply that only
-- application-layer protocols are counted, rather it means that
-- protocols up to and including the application layer are supported.

--
-- Application Layer Host Group
--
-- Counts the amount of traffic, by protocol, sent from and to each
-- network address discovered by the probe.
-- Implementation of this group requires implementation of the Network
-- Layer Host Group.

alHostTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlHostEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A collection of statistics for a particular protocol from a
        particular network address that has been discovered on an
        interface of this device.

        The probe will populate this table for all protocols in the
        protocol directory table whose value of
        protocolDirHostConfig is equal to supportedOn(3), and
        will delete any entries whose protocolDirEntry is deleted or
        has a protocolDirHostConfig value of supportedOff(2).



        The probe will add to this table all addresses
        seen as the source or destination address in all packets with
        no MAC errors and will increment octet and packet counts in
        the table for all packets with no MAC errors.  Further,
        entries will only be added to this table if their address
        exists in the nlHostTable and will be deleted from this table
        if their address is deleted from the nlHostTable."
    ::= { alHost 1 }

alHostEntry OBJECT-TYPE
    SYNTAX      AlHostEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A conceptual row in the alHostTable.

        The hlHostControlIndex value in the index identifies the
        hlHostControlEntry on whose behalf this entry was created.
        The first protocolDirLocalIndex value in the index identifies
        the network-layer protocol of the address.
        The nlHostAddress value in the index identifies the network-
        layer address of this entry.
        The second protocolDirLocalIndex value in the index identifies
        the protocol that is counted by this entry.

        An example of the indexing in this entry is
        alHostOutPkts.1.783495.**********.6.6.34.

        Note that some combinations of index values may result in an
        index that exceeds 128 sub-identifiers in length, which exceeds
        the maximum for the SNMP protocol.  Implementations should take
        care to avoid such combinations."
    INDEX { hlHostControlIndex, alHostTimeMark,
            protocolDirLocalIndex, nlHostAddress,
            protocolDirLocalIndex }
    ::= { alHostTable 1 }

AlHostEntry ::= SEQUENCE {
    alHostTimeMark                 TimeFilter,
    alHostInPkts                   ZeroBasedCounter32,
    alHostOutPkts                  ZeroBasedCounter32,
    alHostInOctets                 ZeroBasedCounter32,
    alHostOutOctets                ZeroBasedCounter32,
    alHostCreateTime               LastCreateTime
}

alHostTimeMark OBJECT-TYPE
    SYNTAX      TimeFilter



    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A TimeFilter for this entry.  See the TimeFilter textual
        convention to see how this works."
    ::= { alHostEntry 1 }

alHostInPkts OBJECT-TYPE
    SYNTAX      ZeroBasedCounter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of packets of this protocol type without errors
        transmitted to this address since it was added to the
        alHostTable.  Note that this is the number of link-layer
        packets, so if a single network-layer packet is fragmented
        into several link-layer frames, this counter is incremented
        several times."
    ::= { alHostEntry 2 }

alHostOutPkts OBJECT-TYPE
    SYNTAX      ZeroBasedCounter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of packets of this protocol type without errors
        transmitted by this address since it was added to the
        alHostTable.  Note that this is the number of link-layer
        packets, so if a single network-layer packet is fragmented
        into several link-layer frames, this counter is incremented
        several times."
     ::= { alHostEntry 3 }

alHostInOctets OBJECT-TYPE
    SYNTAX      ZeroBasedCounter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of octets transmitted to this address
        of this protocol type since it was added to the
        alHostTable (excluding framing bits, but including
        FCS octets), excluding octets in packets that
        contained errors.

        Note that this doesn't count just those octets in the particular
        protocol frames but includes the entire packet that contained
        the protocol."
    ::= { alHostEntry 4 }



alHostOutOctets OBJECT-TYPE
    SYNTAX      ZeroBasedCounter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of octets transmitted by this address
        of this protocol type since it was added to the
        alHostTable (excluding framing bits, but including
        FCS octets), excluding octets in packets that
        contained errors.

        Note that this doesn't count just those octets in the particular
        protocol frames but includes the entire packet that contained
        the protocol."
    ::= { alHostEntry 5 }

alHostCreateTime OBJECT-TYPE
    SYNTAX     LastCreateTime
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The value of sysUpTime when this entry was last activated.
        This can be used by the management station to ensure that the
        entry has not been deleted and recreated between polls."
    ::= { alHostEntry 6 }

--
-- Application Layer Matrix Group
--
-- Counts the amount of traffic, by protocol, sent between each pair
-- of network addresses discovered by the probe.
-- Implementation of this group requires implementation of the Network
-- Layer Matrix Group.

alMatrixSDTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlMatrixSDEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A list of application traffic matrix entries that collect
        statistics for conversations of a particular protocol between
        two network-level addresses.  This table is indexed first by
        the source address and then by the destination address to make
        it convenient to collect all statistics from a particular
        address.

        The probe will populate this table for all protocols in the
        protocol directory table whose value of



        protocolDirMatrixConfig is equal to supportedOn(3), and
        will delete any entries whose protocolDirEntry is deleted or
        has a protocolDirMatrixConfig value of supportedOff(2).

        The probe will add to this table all pairs of addresses for
        all protocols seen in all packets with no MAC errors and will
        increment octet and packet counts in the table for all packets
        with no MAC errors.  Further, entries will only be added to
        this table if their address pair exists in the nlMatrixSDTable
        and will be deleted from this table if the address pair is
        deleted from the nlMatrixSDTable."
    ::= { alMatrix 1 }

alMatrixSDEntry OBJECT-TYPE
    SYNTAX      AlMatrixSDEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A conceptual row in the alMatrixSDTable.

        The hlMatrixControlIndex value in the index identifies the
        hlMatrixControlEntry on whose behalf this entry was created.
        The first protocolDirLocalIndex value in the index identifies
        the network-layer protocol of the nlMatrixSDSourceAddress and
        nlMatrixSDDestAddress.
        The nlMatrixSDSourceAddress value in the index identifies the
        network-layer address of the source host in this conversation.
        The nlMatrixSDDestAddress value in the index identifies the
        network-layer address of the destination host in this
        conversation.
        The second protocolDirLocalIndex value in the index identifies
        the protocol that is counted by this entry.

        An example of the indexing of this entry is
        alMatrixSDPkts.1.783495.**********.*********.********.

        Note that some combinations of index values may result in an
        index that exceeds 128 sub-identifiers in length, which exceeds
        the maximum for the SNMP protocol.  Implementations should take
        care to avoid such combinations."
    INDEX { hlMatrixControlIndex, alMatrixSDTimeMark,
            protocolDirLocalIndex,
            nlMatrixSDSourceAddress, nlMatrixSDDestAddress,
            protocolDirLocalIndex }
    ::= { alMatrixSDTable 1 }

AlMatrixSDEntry ::= SEQUENCE {
    alMatrixSDTimeMark                 TimeFilter,



    alMatrixSDPkts                     ZeroBasedCounter32,
    alMatrixSDOctets                   ZeroBasedCounter32,
    alMatrixSDCreateTime               LastCreateTime
}

alMatrixSDTimeMark OBJECT-TYPE
    SYNTAX      TimeFilter
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A TimeFilter for this entry.  See the TimeFilter textual
        convention to see how this works."
    ::= { alMatrixSDEntry 1 }

alMatrixSDPkts OBJECT-TYPE
    SYNTAX      ZeroBasedCounter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of packets of this protocol type without errors
        transmitted from the source address to the destination address
        since this entry was added to the alMatrixSDTable.  Note that
        this is the number of link-layer packets, so if a single
        network-layer packet is fragmented into several link-layer
        frames, this counter is incremented several times."
    ::= { alMatrixSDEntry 2 }

alMatrixSDOctets OBJECT-TYPE
    SYNTAX      ZeroBasedCounter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of octets in packets of this protocol type
        transmitted from the source address to the destination address
        since this entry was added to the alMatrixSDTable (excluding
        framing bits, but including FCS octets), excluding octets
        in packets that contained errors.

        Note that this doesn't count just those octets in the particular
        protocol frames but includes the entire packet that contained
        the protocol."
    ::= { alMatrixSDEntry 3 }

alMatrixSDCreateTime OBJECT-TYPE
    SYNTAX     LastCreateTime
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION



        "The value of sysUpTime when this entry was last activated.
        This can be used by the management station to ensure that the
        entry has not been deleted and recreated between polls."
    ::= { alMatrixSDEntry 4 }

-- Traffic matrix tables from destination to source

alMatrixDSTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlMatrixDSEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A list of application traffic matrix entries that collect
        statistics for conversations of a particular protocol between
        two network-level addresses.  This table is indexed first by
        the destination address and then by the source address to make
        it convenient to collect all statistics to a particular
        address.

        The probe will populate this table for all protocols in the
        protocol directory table whose value of
        protocolDirMatrixConfig is equal to supportedOn(3), and
        will delete any entries whose protocolDirEntry is deleted or
        has a protocolDirMatrixConfig value of supportedOff(2).

        The probe will add to this table all pairs of addresses for
        all protocols seen in all packets with no MAC errors and will
        increment octet and packet counts in the table for all packets
        with no MAC errors.  Further, entries will only be added to
        this table if their address pair exists in the nlMatrixDSTable
        and will be deleted from this table if the address pair is
        deleted from the nlMatrixDSTable."
    ::= { alMatrix 2 }

alMatrixDSEntry OBJECT-TYPE
    SYNTAX      AlMatrixDSEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A conceptual row in the alMatrixDSTable.

        The hlMatrixControlIndex value in the index identifies the
        hlMatrixControlEntry on whose behalf this entry was created.
        The first protocolDirLocalIndex value in the index identifies
        the network-layer protocol of the alMatrixDSSourceAddress and
        alMatrixDSDestAddress.
        The nlMatrixDSDestAddress value in the index identifies the
        network-layer address of the destination host in this



        conversation.
        The nlMatrixDSSourceAddress value in the index identifies the
        network-layer address of the source host in this conversation.
        The second protocolDirLocalIndex value in the index identifies
        the protocol that is counted by this entry.

        An example of the indexing of this entry is
        alMatrixDSPkts.1.783495.**********.*********.********.

        Note that some combinations of index values may result in an
        index that exceeds 128 sub-identifiers in length, which exceeds
        the maximum for the SNMP protocol.  Implementations should take
        care to avoid such combinations."
    INDEX { hlMatrixControlIndex, alMatrixDSTimeMark,
            protocolDirLocalIndex,
            nlMatrixDSDestAddress, nlMatrixDSSourceAddress,
            protocolDirLocalIndex }
    ::= { alMatrixDSTable 1 }

AlMatrixDSEntry ::= SEQUENCE {
    alMatrixDSTimeMark                 TimeFilter,
    alMatrixDSPkts                     ZeroBasedCounter32,
    alMatrixDSOctets                   ZeroBasedCounter32,
    alMatrixDSCreateTime               LastCreateTime
}

alMatrixDSTimeMark OBJECT-TYPE
    SYNTAX      TimeFilter
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A TimeFilter for this entry.  See the TimeFilter textual
        convention to see how this works."
    ::= { alMatrixDSEntry 1 }

alMatrixDSPkts OBJECT-TYPE
    SYNTAX      ZeroBasedCounter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of packets of this protocol type without errors
        transmitted from the source address to the destination address
        since this entry was added to the alMatrixDSTable.  Note that
        this is the number of link-layer packets, so if a single
        network-layer packet is fragmented into several link-layer
        frames, this counter is incremented several times."
    ::= { alMatrixDSEntry 2 }




alMatrixDSOctets OBJECT-TYPE
    SYNTAX      ZeroBasedCounter32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of octets in packets of this protocol type
        transmitted from the source address to the destination address
        since this entry was added to the alMatrixDSTable (excluding
        framing bits, but including FCS octets), excluding octets
        in packets that contained errors.

        Note that this doesn't count just those octets in the particular
        protocol frames but includes the entire packet that contained
        the protocol."
    ::= { alMatrixDSEntry 3 }

alMatrixDSCreateTime OBJECT-TYPE
    SYNTAX     LastCreateTime
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The value of sysUpTime when this entry was last activated.
        This can be used by the management station to ensure that the
        entry has not been deleted and recreated between polls."
    ::= { alMatrixDSEntry 4 }

alMatrixTopNControlTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF AlMatrixTopNControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A set of parameters that control the creation of a
        report of the top N matrix entries according to
        a selected metric."
    ::= { alMatrix 3 }

alMatrixTopNControlEntry OBJECT-TYPE
    SYNTAX      AlMatrixTopNControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A conceptual row in the alMatrixTopNControlTable.

        An example of the indexing of this table is
        alMatrixTopNControlDuration.3"
    INDEX { alMatrixTopNControlIndex }
    ::= { alMatrixTopNControlTable 1 }




AlMatrixTopNControlEntry ::= SEQUENCE {
    alMatrixTopNControlIndex            Integer32,
    alMatrixTopNControlMatrixIndex      Integer32,
    alMatrixTopNControlRateBase         INTEGER,
    alMatrixTopNControlTimeRemaining    Integer32,
    alMatrixTopNControlGeneratedReports Counter32,
    alMatrixTopNControlDuration         Integer32,
    alMatrixTopNControlRequestedSize    Integer32,
    alMatrixTopNControlGrantedSize      Integer32,
    alMatrixTopNControlStartTime        TimeStamp,
    alMatrixTopNControlOwner            OwnerString,
    alMatrixTopNControlStatus           RowStatus
}

alMatrixTopNControlIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry
        in the alMatrixTopNControlTable.  Each such
        entry defines one topN report prepared for
        one interface."
    ::= { alMatrixTopNControlEntry 1 }

alMatrixTopNControlMatrixIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "The alMatrix[SD/DS] table for which a topN report will be
        prepared on behalf of this entry.  The alMatrix[SD/DS] table
        is identified by the value of the hlMatrixControlIndex
        for that table - that value is used here to identify the
        particular table.

        This object may not be modified if the associated
        alMatrixTopNControlStatus object is equal to active(1)."
    ::= { alMatrixTopNControlEntry 2 }

alMatrixTopNControlRateBase OBJECT-TYPE
    SYNTAX     INTEGER {
                  alMatrixTopNTerminalsPkts(1),
                  alMatrixTopNTerminalsOctets(2),
                  alMatrixTopNAllPkts(3),
                  alMatrixTopNAllOctets(4),
                  alMatrixTopNTerminalsHighCapacityPkts(5),
                  alMatrixTopNTerminalsHighCapacityOctets(6),



                  alMatrixTopNAllHighCapacityPkts(7),
                  alMatrixTopNAllHighCapacityOctets(8)
               }
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "This object controls which alMatrix[SD/DS] entry that the
        alMatrixTopNEntries are sorted by, which view of the matrix
        table that will be used, as well as which table the results
        will be reported in.

        The values alMatrixTopNTerminalsPkts,
        alMatrixTopNTerminalsOctets,
        alMatrixTopNTerminalsHighCapacityPkts, and
        alMatrixTopNTerminalsHighCapacityOctets cause collection
        only from protocols that have no child protocols that are
        counted.  The values alMatrixTopNAllPkts,
        alMatrixTopNAllOctets, alMatrixTopNAllHighCapacityPkts, and
        alMatrixTopNAllHighCapacityOctets cause collection from all
        alMatrix entries.

        This object may not be modified if the associated
        alMatrixTopNControlStatus object is equal to active(1)."
    ::= { alMatrixTopNControlEntry 3 }

alMatrixTopNControlTimeRemaining OBJECT-TYPE
    SYNTAX     Integer32 (0..**********)
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "The number of seconds left in the report currently
        being collected.  When this object is modified by
        the management station, a new collection is started,
        possibly aborting a currently running report.  The
        new value is used as the requested duration of this
        report and is immediately loaded into the associated
        alMatrixTopNControlDuration object.

        When the report finishes, the probe will automatically
        start another collection with the same initial value
        of alMatrixTopNControlTimeRemaining.  Thus, the management
        station may simply read the resulting reports repeatedly,
        checking the startTime and duration each time to ensure that a
        report was not missed or that the report parameters were not
        changed.

        While the value of this object is non-zero, it decrements
        by one per second until it reaches zero.  At the time



        that this object decrements to zero, the report is made
        accessible in the alMatrixTopNTable, overwriting any report
        that may be there.

        When this object is modified by the management station, any
        associated entries in the alMatrixTopNTable shall be deleted.

        (Note that this is a different algorithm than the one used
        in the hostTopNTable)."
    DEFVAL { 1800 }
    ::= { alMatrixTopNControlEntry 4 }

alMatrixTopNControlGeneratedReports OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of reports that have been generated by this entry."
    ::= { alMatrixTopNControlEntry 5 }

alMatrixTopNControlDuration OBJECT-TYPE
    SYNTAX     Integer32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of seconds that this report has collected
        during the last sampling interval.

        When the associated alMatrixTopNControlTimeRemaining object
        is set, this object shall be set by the probe to the
        same value and shall not be modified until the next
        time the alMatrixTopNControlTimeRemaining is set.

        This value shall be zero if no reports have been
        requested for this alMatrixTopNControlEntry."
    ::= { alMatrixTopNControlEntry 6 }

alMatrixTopNControlRequestedSize OBJECT-TYPE
    SYNTAX     Integer32 (0..**********)
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "The maximum number of matrix entries requested for this report.

        When this object is created or modified, the probe
        should set alMatrixTopNControlGrantedSize as closely to this
        object as possible for the particular probe
        implementation and available resources."



    DEFVAL { 150 }
    ::= { alMatrixTopNControlEntry 7 }

alMatrixTopNControlGrantedSize OBJECT-TYPE
    SYNTAX     Integer32 (0..**********)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The maximum number of matrix entries in this report.

        When the associated alMatrixTopNControlRequestedSize object
        is created or modified, the probe should set this
        object as closely to the requested value as
        possible for the particular implementation and
        available resources. The probe must not lower this
        value except as a side-effect of a set to the associated
        alMatrixTopNControlRequestedSize object.

        If the value of alMatrixTopNControlRateBase is equal to
        alMatrixTopNTerminalsPkts or alMatrixTopNAllPkts, when the
        next topN report is generated, matrix entries with the highest
        value of alMatrixTopNPktRate shall be placed in this table in
        decreasing order of this rate until there is no more room or
        until there are no more matrix entries.

        If the value of alMatrixTopNControlRateBase is equal to
        alMatrixTopNTerminalsOctets or alMatrixTopNAllOctets, when the
        next topN report is generated, matrix entries with the highest
        value of alMatrixTopNOctetRate shall be placed in this table
        in decreasing order of this rate until there is no more room
        or until there are no more matrix entries.

        It is an implementation-specific matter how entries with the
        same value of alMatrixTopNPktRate or alMatrixTopNOctetRate are
        sorted.  It is also an implementation-specific matter as to
        whether zero-valued entries are available."
    ::= { alMatrixTopNControlEntry 8 }

alMatrixTopNControlStartTime OBJECT-TYPE
    SYNTAX     TimeStamp
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The value of sysUpTime when this topN report was
        last started.  In other words, this is the time that
        the associated alMatrixTopNControlTimeRemaining object
        was modified to start the requested report or the time
        the report was last automatically (re)started.



        This object may be used by the management station to
        determine whether a report was missed."
    ::= { alMatrixTopNControlEntry 9 }

alMatrixTopNControlOwner OBJECT-TYPE
    SYNTAX     OwnerString
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "The entity that configured this entry and is
        therefore using the resources assigned to it."
    ::= { alMatrixTopNControlEntry 10 }

alMatrixTopNControlStatus OBJECT-TYPE
    SYNTAX     RowStatus
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "The status of this alMatrixTopNControlEntry.

        An entry may not exist in the active state unless all
        objects in the entry have an appropriate value.

        If this object is not equal to active(1), all
        associated entries in the alMatrixTopNTable shall be
        deleted by the agent."
    ::= { alMatrixTopNControlEntry 11 }

alMatrixTopNTable OBJECT-TYPE
    SYNTAX     SEQUENCE OF AlMatrixTopNEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "A set of statistics for those application-layer matrix
        entries that have counted the highest number of octets or
        packets."
    ::= { alMatrix 4 }

alMatrixTopNEntry OBJECT-TYPE
    SYNTAX     AlMatrixTopNEntry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "A conceptual row in the alMatrixTopNTable.

        The alMatrixTopNControlIndex value in the index identifies
        the alMatrixTopNControlEntry on whose behalf this entry was
        created.



        An example of the indexing of this table is
        alMatrixTopNPktRate.3.10"
    INDEX { alMatrixTopNControlIndex, alMatrixTopNIndex }
    ::= { alMatrixTopNTable 1 }

AlMatrixTopNEntry ::= SEQUENCE {
    alMatrixTopNIndex                      Integer32,
    alMatrixTopNProtocolDirLocalIndex      Integer32,
    alMatrixTopNSourceAddress              OCTET STRING,
    alMatrixTopNDestAddress                OCTET STRING,
    alMatrixTopNAppProtocolDirLocalIndex   Integer32,
    alMatrixTopNPktRate                    Gauge32,
    alMatrixTopNReversePktRate             Gauge32,
    alMatrixTopNOctetRate                  Gauge32,
    alMatrixTopNReverseOctetRate           Gauge32
  }

alMatrixTopNIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "An index that uniquely identifies an entry in
        the alMatrixTopNTable among those in the same report.

        This index is between 1 and N, where N is the
        number of entries in this report.

        If the value of alMatrixTopNControlRateBase is equal to
        alMatrixTopNTerminalsPkts or alMatrixTopNAllPkts, increasing
        values of alMatrixTopNIndex shall be assigned to entries with
        decreasing values of alMatrixTopNPktRate until index N is
        assigned or there are no more alMatrixTopNEntries.

        If the value of alMatrixTopNControlRateBase is equal to
        alMatrixTopNTerminalsOctets or alMatrixTopNAllOctets,
        increasing values of alMatrixTopNIndex shall be assigned to
        entries with decreasing values of alMatrixTopNOctetRate until
        index N is assigned or there are no more alMatrixTopNEntries."
    ::= { alMatrixTopNEntry 1 }

alMatrixTopNProtocolDirLocalIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..**********)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The protocolDirLocalIndex of the network-layer protocol of
        this entry's network address."



    ::= { alMatrixTopNEntry 2 }

alMatrixTopNSourceAddress OBJECT-TYPE
    SYNTAX     OCTET STRING  (SIZE (1..255))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The network-layer address of the source host in this
        conversation.

        This is represented as an octet string with
        specific semantics and length as identified
        by the associated alMatrixTopNProtocolDirLocalIndex.

        For example, if the alMatrixTopNProtocolDirLocalIndex
        indicates an encapsulation of IP, this object is encoded as a
        length octet of 4, followed by the 4 octets of the IP address,
        in network byte order."
    ::= { alMatrixTopNEntry 3 }

alMatrixTopNDestAddress OBJECT-TYPE
    SYNTAX     OCTET STRING  (SIZE (1..255))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The network-layer address of the destination host in this
        conversation.

        This is represented as an octet string with
        specific semantics and length as identified
        by the associated alMatrixTopNProtocolDirLocalIndex.

        For example, if the alMatrixTopNProtocolDirLocalIndex
        indicates an encapsulation of IP, this object is encoded as a
        length octet of 4, followed by the 4 octets of the IP address,
        in network byte order."
    ::= { alMatrixTopNEntry 4 }

alMatrixTopNAppProtocolDirLocalIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..**********)
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The type of the protocol counted by this matrix entry."
    ::= { alMatrixTopNEntry 5 }

alMatrixTopNPktRate OBJECT-TYPE
    SYNTAX     Gauge32



    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of packets seen of this protocol from the source
        host to the destination host during this sampling interval,
        counted using the rules for counting the alMatrixSDPkts
        object.

        If the value of alMatrixTopNControlRateBase is
        alMatrixTopNTerminalsPkts or alMatrixTopNAllPkts, this
        variable will be used to sort this report."
    ::= { alMatrixTopNEntry 6 }

alMatrixTopNReversePktRate OBJECT-TYPE
    SYNTAX     Gauge32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of packets seen of this protocol from the
        destination host to the source host during this sampling
        interval, counted using the rules for counting the
        alMatrixDSPkts object.  (Note that the corresponding
        alMatrixSDPkts object selected is the one whose source address
        is equal to alMatrixTopNDestAddress and whose destination
        address is equal to alMatrixTopNSourceAddress.)

        Note that if the value of alMatrixTopNControlRateBase is equal
        to alMatrixTopNTerminalsPkts or alMatrixTopNAllPkts, the sort
        of topN entries is based entirely on alMatrixTopNPktRate, and
        not on the value of this object."
    ::= { alMatrixTopNEntry 7 }

alMatrixTopNOctetRate OBJECT-TYPE
    SYNTAX     Gauge32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of octets seen of this protocol from the source
        host to the destination host during this sampling interval,
        counted using the rules for counting the alMatrixSDOctets
        object.

        If the value of alMatrixTopNControlRateBase is
        alMatrixTopNTerminalsOctets or alMatrixTopNAllOctets, this
        variable will be used to sort this report."
    ::= { alMatrixTopNEntry 8 }

alMatrixTopNReverseOctetRate OBJECT-TYPE



    SYNTAX     Gauge32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The number of octets seen of this protocol from the
        destination host to the source host during this sampling
        interval, counted using the rules for counting the
        alMatrixDSOctets object.  (Note that the corresponding
        alMatrixSDOctets object selected is the one whose source
        address is equal to alMatrixTopNDestAddress and whose
        destination address is equal to alMatrixTopNSourceAddress.)

        Note that if the value of alMatrixTopNControlRateBase is equal
        to alMatrixTopNTerminalsOctets or alMatrixTopNAllOctets, the
        sort of topN entries is based entirely on
        alMatrixTopNOctetRate, and not on the value of this object."
    ::= { alMatrixTopNEntry 9 }

--
-- User History Collection Group (usrHistory)
--
-- The usrHistory group combines mechanisms seen in the alarm and
-- history groups to provide user-specified history collection,
-- utilizing two additional control tables and one additional data
-- table.  This function has traditionally been done by NMS
-- applications, via periodic polling.  The usrHistory group allows
-- this task to be offloaded to an RMON probe.
--
-- Data (an ASN.1 INTEGER based object) is collected in the same
-- manner as any history data table (e.g., etherHistoryTable) except
-- that the user specifies the MIB instances to be collected.  Objects
-- are collected in bucket-groups, with the intent that all MIB
-- instances in the same bucket-group are collected as atomically as
-- possible by the RMON probe.
--
-- The usrHistoryControlTable is a one-dimensional read-create table.
-- Each row configures a collection of user history buckets, much
-- the same as a historyControlEntry, except that the creation of a
-- row in this table will cause one or more associated instances in
-- the usrHistoryObjectTable to be created.  The user specifies the
-- number of bucket elements (rows in the usrHistoryObjectTable)
-- requested, as well as the number of buckets requested.
--
-- The usrHistoryObjectTable is a 2-d read-write table.
-- Each row configures a single MIB instance to be collected.
-- All rows with the same major index constitute a bucket-group.
--
-- The usrHistoryTable is a 3-d read-only table containing



-- the data of associated usrHistoryControlEntries.  Each
-- entry represents the value of a single MIB instance
-- during a specific sampling interval (or the rate of
-- change during the interval).
--
-- A sample value is stored in two objects - an absolute value and
-- a status object.  This allows numbers from -(2G-1) to +4G to be
-- stored.  The status object also indicates whether a sample is
-- valid.  This allows data collection to continue if periodic
-- retrieval of a particular instance fails for any reason.
--
-- Row Creation Order Relationships
--
-- The static nature of the usrHistoryObjectTable creates
-- some row creation/modification issues.  The rows in this
-- table need to be set before the associated
-- usrHistoryControlEntry can be activated.
--
-- Note that the usrHistoryObject entries associated with a
-- particular usrHistoryControlEntry are not required to
-- be active before the control entry is activated.  However,
-- the usrHistory data entries associated with an inactive
-- usrHistoryObject entry will be inactive (i.e.,
-- usrHistoryValStatus == valueNotAvailable).
--

usrHistoryControlTable OBJECT-TYPE
    SYNTAX SEQUENCE OF UsrHistoryControlEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A list of data-collection configuration entries."
    ::= { usrHistory 1 }

usrHistoryControlEntry OBJECT-TYPE
    SYNTAX UsrHistoryControlEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A list of parameters that set up a group of user-defined
        MIB objects to be sampled periodically (called a
        bucket-group).

        For example, an instance of usrHistoryControlInterval
        might be named usrHistoryControlInterval.1"
    INDEX { usrHistoryControlIndex }
    ::= { usrHistoryControlTable 1 }




UsrHistoryControlEntry ::= SEQUENCE {
    usrHistoryControlIndex             Integer32,
    usrHistoryControlObjects           Integer32,
    usrHistoryControlBucketsRequested  Integer32,
    usrHistoryControlBucketsGranted    Integer32,
    usrHistoryControlInterval          Integer32,
    usrHistoryControlOwner             OwnerString,
    usrHistoryControlStatus            RowStatus
}

usrHistoryControlIndex OBJECT-TYPE
    SYNTAX Integer32 (1..65535)
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An index that uniquely identifies an entry in the
        usrHistoryControlTable.  Each such entry defines a
        set of samples at a particular interval for a specified
        set of MIB instances available from the managed system."
    ::= { usrHistoryControlEntry 1 }

usrHistoryControlObjects OBJECT-TYPE
    SYNTAX Integer32 (1..65535)
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "The number of MIB objects to be collected
        in the portion of usrHistoryTable associated with this
        usrHistoryControlEntry.

        This object may not be modified if the associated instance
        of usrHistoryControlStatus is equal to active(1)."
    ::= { usrHistoryControlEntry 2 }

usrHistoryControlBucketsRequested OBJECT-TYPE
    SYNTAX Integer32 (1..65535)
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "The requested number of discrete time intervals
        over which data is to be saved in the part of the
        usrHistoryTable associated with this usrHistoryControlEntry.

        When this object is created or modified, the probe
        should set usrHistoryControlBucketsGranted as closely to
        this object as possible for the particular probe
        implementation and available resources."
    DEFVAL { 50 }



    ::= { usrHistoryControlEntry 3 }

usrHistoryControlBucketsGranted OBJECT-TYPE
    SYNTAX Integer32 (1..65535)
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The number of discrete sampling intervals
        over which data shall be saved in the part of
        the usrHistoryTable associated with this
        usrHistoryControlEntry.

        When the associated usrHistoryControlBucketsRequested
        object is created or modified, the probe should set
        this object as closely to the requested value as
        possible for the particular probe implementation and
        available resources.  The probe must not lower this
        value except as a result of a modification to the associated
        usrHistoryControlBucketsRequested object.

        The associated usrHistoryControlBucketsRequested object
        should be set before or at the same time as this object
        to allow the probe to accurately estimate the resources
        required for this usrHistoryControlEntry.

        There will be times when the actual number of buckets
        associated with this entry is less than the value of
        this object.  In this case, at the end of each sampling
        interval, a new bucket will be added to the usrHistoryTable.

        When the number of buckets reaches the value of this object
        and a new bucket is to be added to the usrHistoryTable,
        the oldest bucket associated with this usrHistoryControlEntry
        shall be deleted by the agent so that the new bucket can be
        added.

        When the value of this object changes to a value less than
        the current value, entries are deleted from the
        usrHistoryTable associated with this usrHistoryControlEntry.
        Enough of the oldest of these entries shall be deleted by the
        agent so that their number remains less than or equal to the
        new value of this object.

        When the value of this object changes to a value greater
        than the current value, the number of associated usrHistory
        entries may be allowed to grow."
    ::= { usrHistoryControlEntry 4 }




usrHistoryControlInterval OBJECT-TYPE
    SYNTAX Integer32 (1..**********)
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "The interval in seconds over which the data is
        sampled for each bucket in the part of the usrHistory
        table associated with this usrHistoryControlEntry.

        Because the counters in a bucket may overflow at their
        maximum value with no indication, a prudent manager will
        take into account the possibility of overflow in any of
        the associated counters.  It is important to consider the
        minimum time in which any counter could overflow on a
        particular media type and to set the usrHistoryControlInterval
        object to a value less than this interval.

        This object may not be modified if the associated
        usrHistoryControlStatus object is equal to active(1)."
    DEFVAL { 1800 }
    ::= { usrHistoryControlEntry 5 }

usrHistoryControlOwner OBJECT-TYPE
    SYNTAX OwnerString
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "The entity that configured this entry and is
        therefore using the resources assigned to it."
    ::= { usrHistoryControlEntry 6 }

usrHistoryControlStatus OBJECT-TYPE
    SYNTAX RowStatus
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "The status of this variable history control entry.

        An entry may not exist in the active state unless all
        objects in the entry have an appropriate value.

        If this object is not equal to active(1), all associated
        entries in the usrHistoryTable shall be deleted."
    ::= { usrHistoryControlEntry 7 }

-- Object table

usrHistoryObjectTable OBJECT-TYPE



    SYNTAX SEQUENCE OF UsrHistoryObjectEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A list of data-collection configuration entries."
    ::= { usrHistory 2 }

usrHistoryObjectEntry OBJECT-TYPE
    SYNTAX UsrHistoryObjectEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A list of MIB instances to be sampled periodically.

        Entries in this table are created when an associated
        usrHistoryControlObjects object is created.

        The usrHistoryControlIndex value in the index is
        that of the associated usrHistoryControlEntry.

        For example, an instance of usrHistoryObjectVariable might be
        usrHistoryObjectVariable.1.3"
    INDEX { usrHistoryControlIndex, usrHistoryObjectIndex }
    ::= { usrHistoryObjectTable 1 }

UsrHistoryObjectEntry ::= SEQUENCE {
    usrHistoryObjectIndex             Integer32,
    usrHistoryObjectVariable          OBJECT IDENTIFIER,
    usrHistoryObjectSampleType        INTEGER
}

usrHistoryObjectIndex OBJECT-TYPE
    SYNTAX Integer32 (1..65535)
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "An index used to uniquely identify an entry in the
        usrHistoryObject table.  Each such entry defines a
        MIB instance to be collected periodically."
    ::= { usrHistoryObjectEntry 1 }


usrHistoryObjectVariable OBJECT-TYPE
    SYNTAX OBJECT IDENTIFIER
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "The object identifier of the particular variable to be



        sampled.

        Only variables that resolve to an ASN.1 primitive type of
        Integer32 (Integer32, Counter, Gauge, or TimeTicks) may be
        sampled.

        Because SNMP access control is articulated entirely in terms
        of the contents of MIB views, no access control mechanism
        exists that can restrict the value of this object to identify
        only those objects that exist in a particular MIB view.
        Because there is thus no acceptable means of restricting the
        read access that could be obtained through the user history
        mechanism, the probe must only grant write access to this
        object in those views that have read access to all objects on
        the probe.  See USM [RFC3414] and VACM [RFC3415] for more
        information.

        During a set operation, if the supplied variable name is not
        available in the selected MIB view, a badValue error must be
        returned.

        This object may not be modified if the associated
        usrHistoryControlStatus object is equal to active(1)."
    ::= { usrHistoryObjectEntry 2 }

usrHistoryObjectSampleType OBJECT-TYPE
    SYNTAX INTEGER {
               absoluteValue(1),
               deltaValue(2)
           }
    MAX-ACCESS read-create
    STATUS current
    DESCRIPTION
        "The method of sampling the selected variable for storage in
        the usrHistoryTable.

        If the value of this object is absoluteValue(1), the value of
        the selected variable will be copied directly into the history
        bucket.

        If the value of this object is deltaValue(2), the value of the
        selected variable at the last sample will be subtracted from
        the current value, and the difference will be stored in the
        history bucket.  If the associated usrHistoryObjectVariable
        instance could not be obtained at the previous sample
        interval, then a delta sample is not possible, and the value
        of the associated usrHistoryValStatus object for this interval
        will be valueNotAvailable(1).



        This object may not be modified if the associated
        usrHistoryControlStatus object is equal to active(1)."
    ::= { usrHistoryObjectEntry 3 }

-- data table

usrHistoryTable OBJECT-TYPE
    SYNTAX SEQUENCE OF UsrHistoryEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A list of user-defined history entries."
    ::= { usrHistory 3 }

usrHistoryEntry OBJECT-TYPE
    SYNTAX UsrHistoryEntry
    MAX-ACCESS not-accessible
    STATUS current
    DESCRIPTION
        "A historical sample of user-defined variables.  This sample
        is associated with the usrHistoryControlEntry that set up the
        parameters for a regular collection of these samples.

        The usrHistoryControlIndex value in the index identifies the
        usrHistoryControlEntry on whose behalf this entry was created.
        The usrHistoryObjectIndex value in the index identifies the
        usrHistoryObjectEntry on whose behalf this entry was created.

        For example, an instance of usrHistoryAbsValue, which represents
        the 14th sample of a variable collected as specified by
        usrHistoryControlEntry.1 and usrHistoryObjectEntry.1.5,
        would be named usrHistoryAbsValue.1.14.5"
    INDEX { usrHistoryControlIndex, usrHistorySampleIndex,
            usrHistoryObjectIndex }
    ::= { usrHistoryTable 1 }

UsrHistoryEntry ::= SEQUENCE {
    usrHistorySampleIndex   Integer32,
    usrHistoryIntervalStart TimeStamp,
    usrHistoryIntervalEnd   TimeStamp,
    usrHistoryAbsValue      Gauge32,
    usrHistoryValStatus     INTEGER
}

usrHistorySampleIndex OBJECT-TYPE
    SYNTAX     Integer32 (1..**********)
    MAX-ACCESS not-accessible
    STATUS     current



    DESCRIPTION
        "An index that uniquely identifies the particular sample this
        entry represents among all samples associated with the same
        usrHistoryControlEntry.  This index starts at 1 and increases
        by one as each new sample is taken."
    ::= { usrHistoryEntry 1 }

usrHistoryIntervalStart OBJECT-TYPE
    SYNTAX TimeStamp
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The value of sysUpTime at the start of the interval over
        which this sample was measured.  If the probe keeps track of
        the time of day, it should start the first sample of the
        history at a time such that when the next hour of the day
        begins, a sample is started at that instant.

        Note that following this rule may require that the probe delay
        collecting the first sample of the history, as each sample
        must be of the same interval.  Also note that the sample that
        is currently being collected is not accessible in this table
        until the end of its interval."
    ::= { usrHistoryEntry 2 }

usrHistoryIntervalEnd OBJECT-TYPE
    SYNTAX TimeStamp
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The value of sysUpTime at the end of the interval over which
        this sample was measured."
    ::= { usrHistoryEntry 3 }

usrHistoryAbsValue OBJECT-TYPE
    SYNTAX Gauge32
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "The absolute value (i.e., unsigned value) of the
        user-specified statistic during the last sampling period.  The
        value during the current sampling period is not made available
        until the period is completed.

        To obtain the true value for this sampling interval, the
        associated instance of usrHistoryValStatus must be checked,
        and usrHistoryAbsValue adjusted as necessary.




        If the MIB instance could not be accessed during the sampling
        interval, then this object will have a value of zero, and the
        associated instance of usrHistoryValStatus will be set to
        'valueNotAvailable(1)'.

        The access control check prescribed in the definition of
        usrHistoryObjectVariable SHOULD be checked for each sampling
        interval.  If this check determines that access should not be
        allowed, then this object will have a value of zero, and the
        associated instance of usrHistoryValStatus will be set to
        'valueNotAvailable(1)'."
    ::= { usrHistoryEntry 4 }


usrHistoryValStatus OBJECT-TYPE
    SYNTAX INTEGER {
        valueNotAvailable(1),
        valuePositive(2),
        valueNegative(3)
    }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "This object indicates the validity and sign of the data in
        the associated instance of usrHistoryAbsValue.

        If the MIB instance could not be accessed during the sampling
        interval, then 'valueNotAvailable(1)' will be returned.

        If the sample is valid and the actual value of the sample is
        greater than or equal to zero, then 'valuePositive(2)' is
        returned.

        If the sample is valid and the actual value of the sample is
        less than zero, 'valueNegative(3)' will be returned.  The
        associated instance of usrHistoryAbsValue should be multiplied
        by -1 to obtain the true sample value."
    ::= { usrHistoryEntry 5 }

-- The Probe Configuration Group
--
-- This group controls the configuration of various operating
-- parameters of the probe.

ControlString ::= TEXTUAL-CONVENTION
    STATUS current
    DESCRIPTION
        "This data type is used to communicate with a modem or a



        serial data switch.  A ControlString contains embedded
        commands to control how the device will interact with the
        remote device through the serial interface.  Commands are
        represented as two-character sequences beginning with
        the '^' character.

        The following commands are recognized by the device (note
        that command characters are case sensitive):

           ^s  Send string that follows, which is terminated by the
               next command or the end of string.
           ^c  Delay for the number of seconds that follows.  Toss
               out any data received rather than store it in a
               buffer for parsing.
           ^t  Set timeout to the value represented by the decimal
               digits that follow.  The default timeout is 20
               seconds.  Note that this timeout may be overridden
               by a smaller serialTimeout configured for the
               associated serial interface (see serialConfigTable).
           ^w  Wait for the reply string that follows, which is
               terminated by the next command or the end of string.
               Partial and case-insensitive matching is applied, i.e.,
               if the reply string (any case combination) is found
               anywhere in the received string, then the a match is
               found.  If the current timeout elapses without a match,
               then the remaining control string is ignored.
           ^!  The ^ character.
           ^d  Delay the number of seconds specified by the decimal
               digits that follow.
           ^b  Send break for the number of milliseconds specified by
               the decimal digits that follow.  If no digits follow,
               break will be enforced for 250 milliseconds by default.

        The following ASCII control characters may be inserted into
        the '^s' send string or the '^w' reply string:

           ^@    0x00
           ^A    0x01
            ..
           ^M    0x0D
            ..
           ^Z    0x1A
           ^[    0x1B
           ^    0x1C
           ^]    0x1D
           ^^    0x1E
           ^_    0x1F




        Binary data may also be inserted into the data stream.  The
        control sequence for each byte of binary data is ^0x##, where
        ## is the hexadecimal representation of the data byte.  Two
        ASCII characters (0-9, a-f, A-F) must follow the '^0x'
        control prefix.  For example, '^0x0D^0x0A' is interpreted as a
        carriage return followed by a line feed."
    SYNTAX OCTET STRING (SIZE (0..255))

probeCapabilities OBJECT-TYPE
    SYNTAX BITS {
        etherStats(0),
        historyControl(1),
        etherHistory(2),
        alarm(3),
        hosts(4),
        hostTopN(5),
        matrix(6),
        filter(7),
        capture(8),
        event(9),
        tokenRingMLStats(10),
        tokenRingPStats(11),
        tokenRingMLHistory(12),
        tokenRingPHistory(13),
        ringStation(14),
        ringStationOrder(15),
        ringStationConfig(16),
        sourceRouting(17),
        protocolDirectory(18),
        protocolDistribution(19),
        addressMapping(20),
        nlHost(21),
        nlMatrix(22),
        alHost(23),
        alMatrix(24),
        usrHistory(25),
        probeConfig(26)
    }
    MAX-ACCESS read-only
    STATUS current
    DESCRIPTION
        "An indication of the RMON MIB groups supported
        on at least one interface by this probe."
    ::= { probeConfig 1 }

probeSoftwareRev  OBJECT-TYPE
    SYNTAX     DisplayString (SIZE(0..15))
    MAX-ACCESS read-only



    STATUS     current
    DESCRIPTION
        "The software revision of this device.  This string will have
        a zero length if the revision is unknown."
    ::= { probeConfig 2 }

probeHardwareRev  OBJECT-TYPE
    SYNTAX     DisplayString (SIZE(0..31))
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The hardware revision of this device.  This string will have
        a zero length if the revision is unknown."
    ::= { probeConfig 3 }

probeDateTime  OBJECT-TYPE
    SYNTAX     OCTET STRING (SIZE (0 | 8 | 11))
    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Probe's current date and time.

         field  octets  contents                  range
         -----  ------  --------                  -----
           1      1-2   year                      0..65536
           2       3    month                     1..12
           3       4    day                       1..31
           4       5    hour                      0..23
           5       6    minutes                   0..59
           6       7    seconds                   0..60
                         (use 60 for leap-second)
           7       8    deci-seconds              0..9
           8       9    direction from UTC        '+' / '-'
           9      10    hours from UTC            0..11
          10      11    minutes from UTC          0..59

         For example, Tuesday May 26, 1992 at 1:30:15 PM
         EDT would be displayed as:

                     1992-5-26,13:30:15.0,-4:0

         Note that if only local time is known, then
         time zone information (fields 8-10) is not
         present, and that if no time information is known, the
         null string is returned."
    ::= { probeConfig 4 }

probeResetControl  OBJECT-TYPE



    SYNTAX     INTEGER {
                    running(1),
                    warmBoot(2),
                    coldBoot(3)
              }

    MAX-ACCESS read-write
    STATUS     current
    DESCRIPTION
        "Setting this object to warmBoot(2) causes the device to
        restart the application software with current configuration
        parameters saved in non-volatile memory.  Setting this
        object to coldBoot(3) causes the device to reinitialize
        configuration parameters in non-volatile memory to default
        values and to restart the application software.  When the device
        is running normally, this variable has a value of
        running(1)."
    ::= { probeConfig 5 }

-- The following download objects do not restrict an implementation
-- from implementing additional download mechanisms (controlled in an
-- implementation-specific manner).  Further, in the case where the RMON
-- agent shares a processor with other types of systems, the
-- implementation is not required to download those non-RMON functions
-- with this mechanism.

probeDownloadFile  OBJECT-TYPE
    SYNTAX     DisplayString (SIZE(0..127))
    MAX-ACCESS read-write
    STATUS     deprecated
    DESCRIPTION
        "The file name to be downloaded from the TFTP server when a
        download is next requested via this MIB.  This value is set to
        the zero-length string when no file name has been specified.

        This object has been deprecated, as it has not had enough
        independent implementations to demonstrate interoperability to
        meet the requirements of a Draft Standard."
    ::= { probeConfig 6 }

probeDownloadTFTPServer  OBJECT-TYPE
    SYNTAX     IpAddress
    MAX-ACCESS read-write
    STATUS     deprecated
    DESCRIPTION
        "The IP address of the TFTP server that contains the boot
        image to load when a download is next requested via this MIB.
        This value is set to '0.0.0.0' when no IP address has been



        specified.

        This object has been deprecated, as it has not had enough
        independent implementations to demonstrate interoperability to
        meet the requirements of a Draft Standard."
    ::= { probeConfig 7 }

probeDownloadAction  OBJECT-TYPE
    SYNTAX     INTEGER {
                  notDownloading(1),
                  downloadToPROM(2),
                  downloadToRAM(3)
               }
    MAX-ACCESS read-write
    STATUS     deprecated
    DESCRIPTION
        "When this object is set to downloadToRAM(3) or
        downloadToPROM(2), the device will discontinue its
        normal operation and begin download of the image specified
        by probeDownloadFile from the server specified by
        probeDownloadTFTPServer using the TFTP protocol.  If
        downloadToRAM(3) is specified, the new image is copied
        to RAM only (the old image remains unaltered in the flash
        EPROM).  If downloadToPROM(2) is specified,
        the new image is written to the flash EPROM
        memory after its checksum has been verified to be correct.
        When the download process is completed, the device will
        warm boot to restart the newly loaded application.
        When the device is not downloading, this object will have
        a value of notDownloading(1).

        This object has been deprecated, as it has not had enough
        independent implementations to demonstrate interoperability to
        meet the requirements of a Draft Standard."
    ::= { probeConfig 8 }

probeDownloadStatus  OBJECT-TYPE
    SYNTAX     INTEGER {
                    downloadSuccess(1),
                    downloadStatusUnknown(2),
                    downloadGeneralError(3),
                    downloadNoResponseFromServer(4),
                    downloadChecksumError(5),
                    downloadIncompatibleImage(6),
                    downloadTftpFileNotFound(7),
                    downloadTftpAccessViolation(8)
               }
    MAX-ACCESS read-only



    STATUS     deprecated
    DESCRIPTION
        "The status of the last download procedure, if any.  This
        object will have a value of downloadStatusUnknown(2) if no
        download process has been performed.

        This object has been deprecated, as it has not had enough
        independent implementations to demonstrate interoperability to
        meet the requirements of a Draft Standard."
    ::= { probeConfig 9 }

serialConfigTable  OBJECT-TYPE
    SYNTAX     SEQUENCE OF SerialConfigEntry
    MAX-ACCESS not-accessible
    STATUS     deprecated
    DESCRIPTION
        "A table of serial interface configuration entries.  This data
        will be stored in non-volatile memory and preserved across
        probe resets or power loss.

        This table has been deprecated, as it has not had enough
        independent implementations to demonstrate interoperability to
        meet the requirements of a Draft Standard."
    ::= { probeConfig 10 }

serialConfigEntry  OBJECT-TYPE
    SYNTAX     SerialConfigEntry
    MAX-ACCESS not-accessible
    STATUS     deprecated
    DESCRIPTION
        "A set of configuration parameters for a particular
        serial interface on this device.  If the device has no serial
        interfaces, this table is empty.

        The index is composed of the ifIndex assigned to this serial
        line interface."
    INDEX  { ifIndex }
    ::= { serialConfigTable 1 }

SerialConfigEntry ::= SEQUENCE {
    serialMode                   INTEGER,
    serialProtocol               INTEGER,
    serialTimeout                Integer32,
    serialModemInitString        ControlString,
    serialModemHangUpString      ControlString,
    serialModemConnectResp       DisplayString,
    serialModemNoConnectResp     DisplayString,
    serialDialoutTimeout         Integer32,



    serialStatus                 RowStatus
}

serialMode  OBJECT-TYPE
    SYNTAX     INTEGER {
                   direct(1),
                   modem(2)
               }
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "The type of incoming connection to be expected on this
         serial interface."
    DEFVAL { direct }
    ::= { serialConfigEntry 1 }

serialProtocol  OBJECT-TYPE
    SYNTAX     INTEGER {
                   other(1),
                   slip(2),
                   ppp(3)
               }
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "The type of data link encapsulation to be used on this
        serial interface."
    DEFVAL { slip }
    ::= { serialConfigEntry 2 }

serialTimeout  OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "This timeout value is used when the Management Station has
        initiated the conversation over the serial link.  This variable
        represents the number of seconds of inactivity allowed before
        terminating the connection on this serial interface.  Use the
        serialDialoutTimeout in the case where the probe has initiated
        the connection for the purpose of sending a trap."
    DEFVAL { 300 }
    ::= { serialConfigEntry 3 }

serialModemInitString  OBJECT-TYPE
    SYNTAX     ControlString (SIZE (0..255))
    MAX-ACCESS read-create
    STATUS     deprecated



    DESCRIPTION
        "A control string that controls how a modem attached to this
        serial interface should be initialized.  The initialization
        is performed once during startup and again after each
        connection is terminated if the associated serialMode has the
        value of modem(2).

        A control string that is appropriate for a wide variety of
        modems is: '^s^MATE0Q0V1X4 S0=1 S2=43^M'."
    ::= { serialConfigEntry 4 }

serialModemHangUpString  OBJECT-TYPE
    SYNTAX     ControlString (SIZE (0..255))
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "A control string that specifies how to disconnect a modem
         connection on this serial interface.  This object is only
         meaningful if the associated serialMode has the value
         of modem(2).

         A control string that is appropriate for a wide variety of
         modems is: '^d2^s+++^d2^sATH0^M^d2'."
    ::= { serialConfigEntry 5 }

serialModemConnectResp  OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..255))
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "An ASCII string containing substrings that describe the
        expected modem connection response code and associated bps
        rate.  The substrings are delimited by the first character
        in the string, for example:
           /CONNECT/300/CONNECT 1200/1200/CONNECT 2400/2400/
           CONNECT 4800/4800/CONNECT 9600/9600
        will be interpreted as:
            response code    bps rate
            CONNECT            300
            CONNECT 1200      1200
            CONNECT 2400      2400
            CONNECT 4800      4800
            CONNECT 9600      9600
        The agent will use the information in this string to adjust
        the bps rate of this serial interface once a modem connection
        is established.

        A value that is appropriate for a wide variety of modems is:



        '/CONNECT/300/CONNECT 1200/1200/CONNECT 2400/2400/
         CONNECT 4800/4800/CONNECT 9600/9600/CONNECT 14400/14400/
        CONNECT 19200/19200/CONNECT 38400/38400/'."
    ::= { serialConfigEntry 6 }

serialModemNoConnectResp  OBJECT-TYPE
    SYNTAX     DisplayString (SIZE (0..255))
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "An ASCII string containing response codes that may be
        generated by a modem to report the reason why a connection
        attempt has failed.  The response codes are delimited by
        the first character in the string, for example:
           /NO CARRIER/BUSY/NO DIALTONE/NO ANSWER/ERROR/

        If one of these response codes is received via this serial
        interface while attempting to make a modem connection,
        the agent will issue the hang up command as specified by
        serialModemHangUpString.

        A value that is appropriate for a wide variety of modems is:
        '/NO CARRIER/BUSY/NO DIALTONE/NO ANSWER/ERROR/'."
    ::= { serialConfigEntry 7 }

serialDialoutTimeout  OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "This timeout value is used when the probe initiates the
        serial connection with the intention of contacting a
        management station.  This variable represents the number
        of seconds of inactivity allowed before terminating the
        connection on this serial interface."
    DEFVAL { 20 }
    ::= { serialConfigEntry 8 }

serialStatus  OBJECT-TYPE
    SYNTAX     RowStatus
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "The status of this serialConfigEntry.

        An entry may not exist in the active state unless all
        objects in the entry have an appropriate value."
    ::= { serialConfigEntry 9 }



netConfigTable  OBJECT-TYPE
    SYNTAX     SEQUENCE OF NetConfigEntry
    MAX-ACCESS not-accessible
    STATUS     deprecated
    DESCRIPTION
        "A table of netConfigEntries.

        This table has been deprecated, as it has not had enough
        independent implementations to demonstrate interoperability to
        meet the requirements of a Draft Standard."
    ::= { probeConfig 11 }

netConfigEntry  OBJECT-TYPE
    SYNTAX     NetConfigEntry
    MAX-ACCESS not-accessible
    STATUS     deprecated
    DESCRIPTION
        "A set of configuration parameters for a particular
        network interface on this device.  If the device has no network
        interface, this table is empty.

        The index is composed of the ifIndex assigned to the
        corresponding interface."
    INDEX  { ifIndex }
    ::= { netConfigTable 1 }

NetConfigEntry ::= SEQUENCE {
    netConfigIPAddress         IpAddress,
    netConfigSubnetMask        IpAddress,
    netConfigStatus            RowStatus
}

netConfigIPAddress  OBJECT-TYPE
    SYNTAX     IpAddress
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "The IP address of this Net interface.  The default value
        for this object is 0.0.0.0.  If either the netConfigIPAddress
        or netConfigSubnetMask is 0.0.0.0, then when the device
        boots, it may use BOOTP to try to figure out what these
        values should be.  If BOOTP fails before the device
        can talk on the network, this value must be configured
        (e.g., through a terminal attached to the device).  If BOOTP is
        used, care should be taken to not send BOOTP broadcasts too
        frequently and to eventually send them very infrequently if no
        replies are received."
    ::= { netConfigEntry 1 }



netConfigSubnetMask  OBJECT-TYPE
    SYNTAX     IpAddress
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "The subnet mask of this Net interface.  The default value
        for this object is 0.0.0.0.  If either the netConfigIPAddress
        or netConfigSubnetMask is 0.0.0.0, then when the device
        boots, it may use BOOTP to try to figure out what these
        values should be.  If BOOTP fails before the device
        can talk on the network, this value must be configured
        (e.g., through a terminal attached to the device).  If BOOTP is
        used, care should be taken to not send BOOTP broadcasts too
        frequently and to eventually send them very infrequently if no
        replies are received."
    ::= { netConfigEntry 2 }

netConfigStatus  OBJECT-TYPE
    SYNTAX     RowStatus
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "The status of this netConfigEntry.

        An entry may not exist in the active state unless all
        objects in the entry have an appropriate value."
    ::= { netConfigEntry 3 }

netDefaultGateway  OBJECT-TYPE
    SYNTAX     IpAddress
    MAX-ACCESS read-write
    STATUS     deprecated
    DESCRIPTION
        "The IP Address of the default gateway.  If this value is
        undefined or unknown, it shall have the value 0.0.0.0."
    ::= { probeConfig 12 }

-- Trap Destination Table
--
-- This table defines the destination addresses for traps generated
-- from the device.  This table maps a community to one or more trap
-- destination entries.
--
-- The same trap will be sent to all destinations specified in the
-- entries that have the same trapDestCommunity as the eventCommunity
-- (as defined by RMON MIB), as long as no access control mechanism
-- (e.g., VACM) prohibits sending to one or more of the destinations.
-- Information in this table will be stored in non-volatile memory.



-- If the device has gone through a hard restart, this information
-- will be reset to its default state.

trapDestTable  OBJECT-TYPE
    SYNTAX     SEQUENCE OF TrapDestEntry
    MAX-ACCESS not-accessible
    STATUS     deprecated
    DESCRIPTION
        "A list of trap destination entries."
    ::= { probeConfig 13 }

trapDestEntry  OBJECT-TYPE
    SYNTAX     TrapDestEntry
    MAX-ACCESS not-accessible
    STATUS     deprecated
    DESCRIPTION
        "This entry includes a destination IP address to which
        traps are sent for this community."
    INDEX { trapDestIndex }
    ::= { trapDestTable 1 }

TrapDestEntry ::= SEQUENCE {
    trapDestIndex               Integer32,
    trapDestCommunity           OCTET STRING,
    trapDestProtocol            INTEGER,
    trapDestAddress             OCTET STRING,
    trapDestOwner               OwnerString,
    trapDestStatus              RowStatus
}

trapDestIndex  OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS not-accessible
    STATUS     deprecated
    DESCRIPTION
        "A value that uniquely identifies this trapDestEntry."
    ::= { trapDestEntry 1 }

trapDestCommunity  OBJECT-TYPE
    SYNTAX     OCTET STRING (SIZE(0..127))
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "A community to which this destination address belongs.
        This entry is associated with any eventEntries in the RMON
        MIB whose value of eventCommunity is equal to the value of
        this object.  Every time an associated event entry sends a
        trap due to an event, that trap will be sent to each



        address in the trapDestTable with a trapDestCommunity equal
        to eventCommunity, as long as no access control mechanism
        precludes it (e.g., VACM).

        This object may not be modified if the associated
        trapDestStatus object is equal to active(1)."
    ::= { trapDestEntry 2 }

trapDestProtocol OBJECT-TYPE
    SYNTAX     INTEGER {
                    ip(1),
                    ipx(2)
                }
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "The protocol with which this trap is to be sent."
    ::= { trapDestEntry 3 }

trapDestAddress  OBJECT-TYPE
    SYNTAX     OCTET STRING
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "The destination address for traps on behalf of this entry.

        If the associated trapDestProtocol object is equal to ip(1),
        the encoding of this object is the same as the snmpUDPAddress
        textual convention in RFC 3417, 'Transport Mappings for the
         Simple Network Management Protocol (SNMP)' [RFC3417]:
          -- for a SnmpUDPAddress of length 6:
          --
          -- octets   contents        encoding
          --  1-4     IP-address      network-byte order
          --  5-6     UDP-port        network-byte order

        If the associated trapDestProtocol object is equal to ipx(2),
        the encoding of this object is the same as the snmpIPXAddress
        textual convention in RFC 3417, 'Transport Mappings for the
         Simple Network Management Protocol (SNMP)' [RFC3417]:
          -- for a SnmpIPXAddress of length 12:
          --
          -- octets   contents            encoding
          --  1-4     network-number      network-byte order
          --  5-10    physical-address    network-byte order
          -- 11-12    socket-number       network-byte order

        This object may not be modified if the associated



        trapDestStatus object is equal to active(1)."
    ::= { trapDestEntry 4 }

trapDestOwner  OBJECT-TYPE
    SYNTAX     OwnerString
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "The entity that configured this entry and is
        therefore using the resources assigned to it."
    ::= { trapDestEntry 5 }

trapDestStatus  OBJECT-TYPE
    SYNTAX     RowStatus
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "The status of this trap destination entry.

        An entry may not exist in the active state unless all
        objects in the entry have an appropriate value."
    ::= { trapDestEntry 6 }

-- Serial Connection Table
--
-- The device may communicate with a management station using
-- SLIP.  In order for the device to send traps via SLIP, it must
-- be able to initiate a connection over the serial interface.  The
-- serialConnectionTable stores the parameters for such connection
-- initiation.

serialConnectionTable  OBJECT-TYPE
    SYNTAX     SEQUENCE OF SerialConnectionEntry
    MAX-ACCESS not-accessible
    STATUS     deprecated
    DESCRIPTION
        "A list of serialConnectionEntries.

        This table has been deprecated, as it has not had enough
        independent implementations to demonstrate interoperability
        to meet the requirements of a Draft Standard."
    ::= { probeConfig 14 }

serialConnectionEntry  OBJECT-TYPE
    SYNTAX     SerialConnectionEntry
    MAX-ACCESS not-accessible
    STATUS     deprecated
    DESCRIPTION



        "Configuration for a SLIP link over a serial line."
    INDEX { serialConnectIndex }
    ::= { serialConnectionTable 1 }

SerialConnectionEntry ::= SEQUENCE {
    serialConnectIndex                   Integer32,
    serialConnectDestIpAddress           IpAddress,
    serialConnectType                    INTEGER,
    serialConnectDialString              ControlString,
    serialConnectSwitchConnectSeq        ControlString,
    serialConnectSwitchDisconnectSeq     ControlString,
    serialConnectSwitchResetSeq          ControlString,
    serialConnectOwner                   OwnerString,
    serialConnectStatus                  RowStatus
}

serialConnectIndex  OBJECT-TYPE
    SYNTAX     Integer32 (1..65535)
    MAX-ACCESS not-accessible
    STATUS     deprecated
    DESCRIPTION
        "A value that uniquely identifies this serialConnection
        entry."
    ::= { serialConnectionEntry 1 }

serialConnectDestIpAddress  OBJECT-TYPE
    SYNTAX     IpAddress
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "The IP Address that can be reached at the other end of this
        serial connection.

        This object may not be modified if the associated
        serialConnectStatus object is equal to active(1)."
    ::= { serialConnectionEntry 2 }


serialConnectType  OBJECT-TYPE
    SYNTAX     INTEGER {
                    direct(1),
                    modem(2),
                    switch(3),
                    modemSwitch(4)
               }
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION



        "The type of outgoing connection to be made.  If this object
        has the value direct(1), then a direct serial connection
        is assumed.  If this object has the value modem(2),
        then serialConnectDialString will be used to make a modem
        connection.  If this object has the value switch(3),
        then serialConnectSwitchConnectSeq will be used to establish
        the connection over a serial data switch, and
        serialConnectSwitchDisconnectSeq will be used to terminate
        the connection.  If this object has the value
        modem-switch(4), then a modem connection will be made first,
        followed by the switch connection.

        This object may not be modified if the associated
        serialConnectStatus object is equal to active(1)."
    DEFVAL { direct }
    ::= { serialConnectionEntry 3 }

serialConnectDialString  OBJECT-TYPE
    SYNTAX     ControlString (SIZE(0..255))
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "A control string that specifies how to dial the phone
        number in order to establish a modem connection.  The
        string should include the dialing prefix and suffix.  For
        example: '^s^MATD9,888-1234^M' will instruct the Probe
        to send a carriage return, followed by the dialing prefix
        'ATD', the phone number '9,888-1234', and a carriage
        return as the dialing suffix.

        This object may not be modified if the associated
        serialConnectStatus object is equal to active(1)."
    ::= { serialConnectionEntry 4 }

serialConnectSwitchConnectSeq  OBJECT-TYPE
    SYNTAX     ControlString (SIZE(0..255))
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "A control string that specifies how to establish a
        data switch connection.

        This object may not be modified if the associated
        serialConnectStatus object is equal to active(1)."
     ::= { serialConnectionEntry 5 }

serialConnectSwitchDisconnectSeq  OBJECT-TYPE
    SYNTAX     ControlString (SIZE(0..255))



    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "A control string that specifies how to terminate a
        data switch connection.

        This object may not be modified if the associated
        serialConnectStatus object is equal to active(1)."
    ::= { serialConnectionEntry 6 }

serialConnectSwitchResetSeq  OBJECT-TYPE
    SYNTAX     ControlString (SIZE(0..255))
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "A control string that specifies how to reset a data
        switch in the event of a timeout.

        This object may not be modified if the associated
        serialConnectStatus object is equal to active(1)."
    ::= { serialConnectionEntry 7 }

serialConnectOwner  OBJECT-TYPE
    SYNTAX     OwnerString
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "The entity that configured this entry and is
        therefore using the resources assigned to it."
    ::= { serialConnectionEntry 8 }

serialConnectStatus  OBJECT-TYPE
    SYNTAX     RowStatus
    MAX-ACCESS read-create
    STATUS     deprecated
    DESCRIPTION
        "The status of this serialConnectionEntry.

        If the manager attempts to set this object to active(1) when
        the serialConnectType is set to modem(2) or modem-switch(4)
        and the serialConnectDialString is a zero-length string or
        cannot be correctly parsed as a ConnectString, the set
        request will be rejected with badValue(3).

        If the manager attempts to set this object to active(1) when
        the serialConnectType is set to switch(3) or modem-switch(4)
        and the serialConnectSwitchConnectSeq,
        the serialConnectSwitchDisconnectSeq, or



        the serialConnectSwitchResetSeq is a zero-length string
        or cannot be correctly parsed as a ConnectString, the set
        request will be rejected with badValue(3).

        An entry may not exist in the active state unless all
        objects in the entry have an appropriate value."
    ::= { serialConnectionEntry 9 }

--
-- Extensions to the RMON 1 MIB for RMON 2 devices
--
-- These extensions include the standard LastCreateTime Textual
-- Convention for all control tables, as well as an augmentation of
-- the filter entry that provides variable-length offsets into
-- packets.


-- Each of the following, except for filterDroppedFrames, is a
-- read-only object which, if implemented, automatically appears when
-- the RMON1 row it is associated with is created.

etherStats2Table  OBJECT-TYPE
    SYNTAX     SEQUENCE OF EtherStats2Entry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "Contains the RMON-2 augmentations to RMON-1."
    ::= { statistics 4 }

etherStats2Entry  OBJECT-TYPE
    SYNTAX     EtherStats2Entry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "Contains the RMON-2 augmentations to RMON-1."
    AUGMENTS { etherStatsEntry }
    ::= { etherStats2Table 1 }

EtherStats2Entry ::= SEQUENCE {
    etherStatsDroppedFrames     Counter32,
    etherStatsCreateTime        LastCreateTime
}

etherStatsDroppedFrames OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION



       "The total number of frames that were received by the probe
        and therefore not accounted for in the *StatsDropEvents, but
        that the probe chose not to count for this entry for
        whatever reason.  Most often, this event occurs when the
        probe is out of some resources and decides to shed load from
        this collection.

        This count does not include packets that were not counted
        because they had MAC-layer errors.

        Note that, unlike the dropEvents counter, this number is the
        exact number of frames dropped."
    ::= { etherStats2Entry 1 }

etherStatsCreateTime OBJECT-TYPE
    SYNTAX     LastCreateTime
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The value of sysUpTime when this control entry was last
        activated.  This can be used by the management station to
        ensure that the table has not been deleted and recreated
        between polls."
    ::= { etherStats2Entry 2 }

historyControl2Table  OBJECT-TYPE
    SYNTAX     SEQUENCE OF HistoryControl2Entry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "Contains the RMON-2 augmentations to RMON-1."
    ::= { history 5 }

historyControl2Entry  OBJECT-TYPE
    SYNTAX     HistoryControl2Entry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "Contains the RMON-2 augmentations to RMON-1."
    AUGMENTS { historyControlEntry }
    ::= { historyControl2Table 1 }

HistoryControl2Entry ::= SEQUENCE {
    historyControlDroppedFrames Counter32
}

historyControlDroppedFrames OBJECT-TYPE
    SYNTAX     Counter32



    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
       "The total number of frames that were received by the probe
        and therefore not accounted for in the *StatsDropEvents, but
        that the probe chose not to count for this entry for
        whatever reason.  Most often, this event occurs when the
        probe is out of some resources and decides to shed load from
        this collection.

        This count does not include packets that were not counted
        because they had MAC-layer errors.

        Note that, unlike the dropEvents counter, this number is the
        exact number of frames dropped."
    ::= { historyControl2Entry 1 }

hostControl2Table  OBJECT-TYPE
    SYNTAX     SEQUENCE OF HostControl2Entry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "Contains the RMON-2 augmentations to RMON-1."
    ::= { hosts 4 }

hostControl2Entry  OBJECT-TYPE
    SYNTAX     HostControl2Entry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "Contains the RMON-2 augmentations to RMON-1."
    AUGMENTS { hostControlEntry }
    ::= { hostControl2Table 1 }

HostControl2Entry ::= SEQUENCE {
    hostControlDroppedFrames    Counter32,
    hostControlCreateTime       LastCreateTime
}

hostControlDroppedFrames OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
       "The total number of frames that were received by the probe
        and therefore not accounted for in the *StatsDropEvents, but
        that the probe chose not to count for this entry for
        whatever reason.  Most often, this event occurs when the



        probe is out of some resources and decides to shed load from
        this collection.

        This count does not include packets that were not counted
        because they had MAC-layer errors.

        Note that, unlike the dropEvents counter, this number is the
        exact number of frames dropped."
    ::= { hostControl2Entry 1 }

hostControlCreateTime OBJECT-TYPE
    SYNTAX     LastCreateTime
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The value of sysUpTime when this control entry was last
        activated.  This can be used by the management station to
        ensure that the table has not been deleted and recreated
        between polls."
    ::= { hostControl2Entry 2 }

matrixControl2Table  OBJECT-TYPE
    SYNTAX     SEQUENCE OF MatrixControl2Entry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "Contains the RMON-2 augmentations to RMON-1."
    ::= { matrix 4 }

matrixControl2Entry  OBJECT-TYPE
    SYNTAX     MatrixControl2Entry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "Contains the RMON-2 augmentations to RMON-1."
    AUGMENTS { matrixControlEntry }
    ::= { matrixControl2Table 1 }

MatrixControl2Entry ::= SEQUENCE {
    matrixControlDroppedFrames  Counter32,
    matrixControlCreateTime     LastCreateTime
}

matrixControlDroppedFrames OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION



       "The total number of frames that were received by the probe
        and therefore not accounted for in the *StatsDropEvents, but
        that the probe chose not to count for this entry for
        whatever reason.  Most often, this event occurs when the
        probe is out of some resources and decides to shed load from
        this collection.

        This count does not include packets that were not counted
        because they had MAC-layer errors.

        Note that, unlike the dropEvents counter, this number is the
        exact number of frames dropped."
    ::= { matrixControl2Entry 1 }

matrixControlCreateTime OBJECT-TYPE
    SYNTAX     LastCreateTime
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The value of sysUpTime when this control entry was last
        activated.  This can be used by the management station to
        ensure that the table has not been deleted and recreated
        between polls."
    ::= { matrixControl2Entry 2 }

channel2Table  OBJECT-TYPE
    SYNTAX     SEQUENCE OF Channel2Entry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "Contains the RMON-2 augmentations to RMON-1."
    ::= { filter 3 }

channel2Entry  OBJECT-TYPE
    SYNTAX     Channel2Entry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "Contains the RMON-2 augmentations to RMON-1."
    AUGMENTS { channelEntry }
    ::= { channel2Table 1 }

Channel2Entry ::= SEQUENCE {
    channelDroppedFrames    Counter32,
    channelCreateTime       LastCreateTime
}

channelDroppedFrames OBJECT-TYPE



    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
       "The total number of frames that were received by the probe
        and therefore not accounted for in the *StatsDropEvents, but
        that the probe chose not to count for this entry for
        whatever reason.  Most often, this event occurs when the
        probe is out of some resources and decides to shed load from
        this collection.

        This count does not include packets that were not counted
        because they had MAC-layer errors.

        Note that, unlike the dropEvents counter, this number is the
        exact number of frames dropped."
    ::= { channel2Entry 1 }

channelCreateTime OBJECT-TYPE
    SYNTAX     LastCreateTime
    MAX-ACCESS read-only
    STATUS     current
    DESCRIPTION
        "The value of sysUpTime when this control entry was last
        activated.  This can be used by the management station to
        ensure that the table has not been deleted and recreated
        between polls."
    ::= { channel2Entry 2 }

tokenRingMLStats2Table  OBJECT-TYPE
    SYNTAX     SEQUENCE OF TokenRingMLStats2Entry
    MAX-ACCESS not-accessible
    STATUS     deprecated
    DESCRIPTION
        "Contains the RMON-2 augmentations to RMON-1.

        This table has been deprecated, as it has not had enough
        independent implementations to demonstrate interoperability
        to meet the requirements of a Draft Standard."
    ::= { statistics 5 }

tokenRingMLStats2Entry  OBJECT-TYPE
    SYNTAX     TokenRingMLStats2Entry
    MAX-ACCESS not-accessible
    STATUS     deprecated
    DESCRIPTION
        "Contains the RMON-2 augmentations to RMON-1."
    AUGMENTS { tokenRingMLStatsEntry }



    ::= { tokenRingMLStats2Table 1 }

TokenRingMLStats2Entry ::= SEQUENCE {
    tokenRingMLStatsDroppedFrames       Counter32,
    tokenRingMLStatsCreateTime          LastCreateTime
}

tokenRingMLStatsDroppedFrames OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     deprecated
    DESCRIPTION
       "The total number of frames that were received by the probe
        and therefore not accounted for in the *StatsDropEvents, but
        that the probe chose not to count for this entry for
        whatever reason.  Most often, this event occurs when the
        probe is out of some resources and decides to shed load from
        this collection.

        This count does not include packets that were not counted
        because they had MAC-layer errors.

        Note that, unlike the dropEvents counter, this number is the
        exact number of frames dropped."
    ::= { tokenRingMLStats2Entry 1 }

tokenRingMLStatsCreateTime OBJECT-TYPE
    SYNTAX     LastCreateTime
    MAX-ACCESS read-only
    STATUS     deprecated
    DESCRIPTION
        "The value of sysUpTime when this control entry was last
        activated.  This can be used by the management station to
        ensure that the table has not been deleted and recreated
        between polls."
    ::= { tokenRingMLStats2Entry 2 }

tokenRingPStats2Table  OBJECT-TYPE
    SYNTAX     SEQUENCE OF TokenRingPStats2Entry
    MAX-ACCESS not-accessible
    STATUS     deprecated
    DESCRIPTION
        "Contains the RMON-2 augmentations to RMON-1.

        This table has been deprecated, as it has not had enough
        independent implementations to demonstrate interoperability
        to meet the requirements of a Draft Standard."
    ::= { statistics 6 }



tokenRingPStats2Entry  OBJECT-TYPE
    SYNTAX     TokenRingPStats2Entry
    MAX-ACCESS not-accessible
    STATUS     deprecated
    DESCRIPTION
        "Contains the RMON-2 augmentations to RMON-1."
    AUGMENTS {  tokenRingPStatsEntry }
    ::= { tokenRingPStats2Table 1 }

TokenRingPStats2Entry ::= SEQUENCE {
    tokenRingPStatsDroppedFrames    Counter32,
    tokenRingPStatsCreateTime       LastCreateTime
}

tokenRingPStatsDroppedFrames OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     deprecated
    DESCRIPTION
       "The total number of frames that were received by the probe
        and therefore not accounted for in the *StatsDropEvents, but
        that the probe chose not to count for this entry for
        whatever reason.  Most often, this event occurs when the
        probe is out of some resources and decides to shed load from
        this collection.

        This count does not include packets that were not counted
        because they had MAC-layer errors.

        Note that, unlike the dropEvents counter, this number is the
        exact number of frames dropped."
    ::= { tokenRingPStats2Entry 1 }

tokenRingPStatsCreateTime OBJECT-TYPE
    SYNTAX     LastCreateTime
    MAX-ACCESS read-only
    STATUS     deprecated
    DESCRIPTION
        "The value of sysUpTime when this control entry was last
        activated.  This can be used by the management station to
        ensure that the table has not been deleted and recreated
        between polls."
    ::= { tokenRingPStats2Entry 2 }

ringStationControl2Table  OBJECT-TYPE
    SYNTAX     SEQUENCE OF RingStationControl2Entry
    MAX-ACCESS not-accessible
    STATUS     deprecated



    DESCRIPTION
        "Contains the RMON-2 augmentations to RMON-1.

        This table has been deprecated, as it has not had enough
        independent implementations to demonstrate interoperability
        to meet the requirements of a Draft Standard."
    ::= { tokenRing 7 }

ringStationControl2Entry  OBJECT-TYPE
    SYNTAX     RingStationControl2Entry
    MAX-ACCESS not-accessible
    STATUS     deprecated
    DESCRIPTION
        "Contains the RMON-2 augmentations to RMON-1."
    AUGMENTS { ringStationControlEntry }
    ::= { ringStationControl2Table 1 }

RingStationControl2Entry ::= SEQUENCE {
    ringStationControlDroppedFrames Counter32,
    ringStationControlCreateTime    LastCreateTime
}

ringStationControlDroppedFrames OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     deprecated
    DESCRIPTION
       "The total number of frames that were received by the probe
        and therefore not accounted for in the *StatsDropEvents, but
        that the probe chose not to count for this entry for
        whatever reason.  Most often, this event occurs when the
        probe is out of some resources and decides to shed load from
        this collection.

        This count does not include packets that were not counted
        because they had MAC-layer errors.

        Note that, unlike the dropEvents counter, this number is the
        exact number of frames dropped."
    ::= { ringStationControl2Entry 1 }

ringStationControlCreateTime OBJECT-TYPE
    SYNTAX     LastCreateTime
    MAX-ACCESS read-only
    STATUS     deprecated
    DESCRIPTION
        "The value of sysUpTime when this control entry was last
        activated.  This can be used by the management station to



        ensure that the table has not been deleted and recreated
        between polls."
    ::= { ringStationControl2Entry 2 }

sourceRoutingStats2Table  OBJECT-TYPE
    SYNTAX     SEQUENCE OF SourceRoutingStats2Entry
    MAX-ACCESS not-accessible
    STATUS     deprecated
    DESCRIPTION
        "Contains the RMON-2 augmentations to RMON-1.

        This table has been deprecated, as it has not had enough
        independent implementations to demonstrate interoperability
        to meet the requirements of a Draft Standard."
    ::= { tokenRing 8 }

sourceRoutingStats2Entry  OBJECT-TYPE
    SYNTAX     SourceRoutingStats2Entry
    MAX-ACCESS not-accessible
    STATUS     deprecated
    DESCRIPTION
        "Contains the RMON-2 augmentations to RMON-1."
    AUGMENTS { sourceRoutingStatsEntry }
    ::= { sourceRoutingStats2Table 1 }

SourceRoutingStats2Entry ::= SEQUENCE {
    sourceRoutingStatsDroppedFrames Counter32,
    sourceRoutingStatsCreateTime    LastCreateTime
}

sourceRoutingStatsDroppedFrames OBJECT-TYPE
    SYNTAX     Counter32
    MAX-ACCESS read-only
    STATUS     deprecated
    DESCRIPTION
       "The total number of frames that were received by the probe
        and therefore not accounted for in the *StatsDropEvents, but
        that the probe chose not to count for this entry for
        whatever reason.  Most often, this event occurs when the
        probe is out of some resources and decides to shed load from
        this collection.

        This count does not include packets that were not counted
        because they had MAC-layer errors.

        Note that, unlike the dropEvents counter, this number is the
        exact number of frames dropped."
    ::= { sourceRoutingStats2Entry 1 }



sourceRoutingStatsCreateTime OBJECT-TYPE
    SYNTAX     LastCreateTime
    MAX-ACCESS read-only
    STATUS     deprecated
    DESCRIPTION
        "The value of sysUpTime when this control entry was last
        activated.  This can be used by the management station to
        ensure that the table has not been deleted and recreated
        between polls."
    ::= { sourceRoutingStats2Entry 2 }

filter2Table OBJECT-TYPE
    SYNTAX     SEQUENCE OF Filter2Entry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "Provides a variable-length packet filter feature to the
        RMON-1 filter table."
    ::= { filter 4 }

filter2Entry OBJECT-TYPE
    SYNTAX     Filter2Entry
    MAX-ACCESS not-accessible
    STATUS     current
    DESCRIPTION
        "Provides a variable-length packet filter feature to the
        RMON-1 filter table."
    AUGMENTS { filterEntry }
    ::= { filter2Table 1 }

Filter2Entry ::= SEQUENCE {
    filterProtocolDirDataLocalIndex     Integer32,
    filterProtocolDirLocalIndex         Integer32
}

filterProtocolDirDataLocalIndex OBJECT-TYPE
    SYNTAX     Integer32 (0..**********)
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "When this object is set to a non-zero value, the filter that
        it is associated with performs the following operations on
        every packet:

        1) If the packet doesn't match the protocol directory entry
           identified by this object, discard the packet and exit
           (i.e., discard the packet if it is not of the identified
           protocol).



        2) If the associated filterProtocolDirLocalIndex is non-zero
           and the packet doesn't match the protocol directory
           entry identified by that object, discard the packet and
           exit.
        3) If the packet matches, perform the regular filter
           algorithm as if the beginning of this named protocol is
           the beginning of the packet, potentially applying the
           filterOffset value to move further into the packet."
    DEFVAL { 0 }
    ::= { filter2Entry 1 }

filterProtocolDirLocalIndex OBJECT-TYPE
    SYNTAX     Integer32 (0..**********)
    MAX-ACCESS read-create
    STATUS     current
    DESCRIPTION
        "When this object is set to a non-zero value, the filter that
        it is associated with will discard the packet if the packet
        doesn't match this protocol directory entry."
    DEFVAL { 0 }
    ::= { filter2Entry 2 }

-- Conformance Macros

rmon2MIBCompliances OBJECT IDENTIFIER ::= { rmonConformance 1 }
rmon2MIBGroups      OBJECT IDENTIFIER ::= { rmonConformance 2 }


rmon2MIBCompliance MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
        "Describes the requirements for conformance to
        the RMON2 MIB"
    MODULE  -- this module
        MANDATORY-GROUPS { protocolDirectoryGroup,
                           protocolDistributionGroup,
                           addressMapGroup,
                           nlHostGroup,
                           nlMatrixGroup,
                           usrHistoryGroup,
                           probeInformationGroup }

        OBJECT nlMatrixTopNControlRateBase
            SYNTAX      INTEGER {
                          nlMatrixTopNPkts(1),
                          nlMatrixTopNOctets(2)
                        }
            DESCRIPTION



                "Conformance to RMON2 requires only support for these
                values of nlMatrixTopNControlRateBase."

        GROUP   rmon1EnhancementGroup
            DESCRIPTION
                "The rmon1EnhancementGroup is mandatory for systems
                that implement RMON [RFC2819]."
        GROUP  rmon1EthernetEnhancementGroup
            DESCRIPTION
                "The rmon1EthernetEnhancementGroup is optional and is
                appropriate for systems that implement the Ethernet
                group of RMON [RFC2819]."
    ::= { rmon2MIBCompliances 1 }

rmon2MIBApplicationLayerCompliance MODULE-COMPLIANCE
    STATUS  current
    DESCRIPTION
        "Describes the requirements for conformance to
        the RMON2 MIB with Application-Layer Enhancements."
    MODULE  -- this module
        MANDATORY-GROUPS { protocolDirectoryGroup,
                           protocolDistributionGroup,
                           addressMapGroup,
                           nlHostGroup,
                           nlMatrixGroup,
                           alHostGroup,
                           alMatrixGroup,
                           usrHistoryGroup,
                           probeInformationGroup }

        OBJECT nlMatrixTopNControlRateBase
            SYNTAX      INTEGER {
                          nlMatrixTopNPkts(1),
                          nlMatrixTopNOctets(2)
                        }
            DESCRIPTION
                "Conformance to RMON2 requires only support for these
                values of nlMatrixTopNControlRateBase."

        OBJECT alMatrixTopNControlRateBase
            SYNTAX     INTEGER {
                           alMatrixTopNTerminalsPkts(1),
                           alMatrixTopNTerminalsOctets(2),
                           alMatrixTopNAllPkts(3),
                           alMatrixTopNAllOctets(4)
                       }
            DESCRIPTION
                "Conformance to RMON2 requires only support for these



                values of alMatrixTopNControlRateBase."

        GROUP   rmon1EnhancementGroup
            DESCRIPTION
                "The rmon1EnhancementGroup is mandatory for systems
                that implement RMON [RFC2819]."
        GROUP  rmon1EthernetEnhancementGroup
            DESCRIPTION
                "The rmon1EthernetEnhancementGroup is optional and is
                appropriate for systems that implement the Ethernet
                group of RMON [RFC2819]."
    ::= { rmon2MIBCompliances 2 }


protocolDirectoryGroup OBJECT-GROUP
    OBJECTS { protocolDirLastChange,
              protocolDirLocalIndex, protocolDirDescr,
              protocolDirType, protocolDirAddressMapConfig,
              protocolDirHostConfig, protocolDirMatrixConfig,
              protocolDirOwner, protocolDirStatus }
    STATUS  current
    DESCRIPTION
        "Lists the inventory of protocols the probe has the
        capability of monitoring and allows the addition, deletion,
        and configuration of entries in this list."
    ::= { rmon2MIBGroups 1 }

protocolDistributionGroup OBJECT-GROUP
    OBJECTS { protocolDistControlDataSource,
              protocolDistControlDroppedFrames,
              protocolDistControlCreateTime,
              protocolDistControlOwner, protocolDistControlStatus,
              protocolDistStatsPkts, protocolDistStatsOctets }
    STATUS  current
    DESCRIPTION
        "Collects the relative amounts of octets and packets for the
        different protocols detected on a network segment."
    ::= { rmon2MIBGroups 2 }

addressMapGroup OBJECT-GROUP
    OBJECTS { addressMapInserts, addressMapDeletes,
              addressMapMaxDesiredEntries,
              addressMapControlDataSource,
              addressMapControlDroppedFrames,
              addressMapControlOwner, addressMapControlStatus,
              addressMapPhysicalAddress,
              addressMapLastChange }
    STATUS  current



    DESCRIPTION
        "Lists MAC address to network address bindings discovered by
        the probe and what interface they were last seen on."
    ::= { rmon2MIBGroups 3 }

nlHostGroup OBJECT-GROUP
    OBJECTS { hlHostControlDataSource,
              hlHostControlNlDroppedFrames, hlHostControlNlInserts,
              hlHostControlNlDeletes,
              hlHostControlNlMaxDesiredEntries,
              hlHostControlAlDroppedFrames, hlHostControlAlInserts,
              hlHostControlAlDeletes,
              hlHostControlAlMaxDesiredEntries, hlHostControlOwner,
              hlHostControlStatus, nlHostInPkts, nlHostOutPkts,
              nlHostInOctets, nlHostOutOctets,
              nlHostOutMacNonUnicastPkts, nlHostCreateTime }
    STATUS  current
    DESCRIPTION
        "Counts the amount of traffic sent from and to each network
        address discovered by the probe.  Note that while the
        hlHostControlTable also has objects that control an optional
        alHostTable, implementation of the alHostTable is not
        required to fully implement this group."
    ::= { rmon2MIBGroups 4 }

nlMatrixGroup OBJECT-GROUP
    OBJECTS { hlMatrixControlDataSource,
              hlMatrixControlNlDroppedFrames,
              hlMatrixControlNlInserts, hlMatrixControlNlDeletes,
              hlMatrixControlNlMaxDesiredEntries,
              hlMatrixControlAlDroppedFrames,
              hlMatrixControlAlInserts, hlMatrixControlAlDeletes,
              hlMatrixControlAlMaxDesiredEntries,
              hlMatrixControlOwner, hlMatrixControlStatus,
              nlMatrixSDPkts, nlMatrixSDOctets, nlMatrixSDCreateTime,
              nlMatrixDSPkts, nlMatrixDSOctets, nlMatrixDSCreateTime,
              nlMatrixTopNControlMatrixIndex,
              nlMatrixTopNControlRateBase,
              nlMatrixTopNControlTimeRemaining,
              nlMatrixTopNControlGeneratedReports,
              nlMatrixTopNControlDuration,
              nlMatrixTopNControlRequestedSize,
              nlMatrixTopNControlGrantedSize,
              nlMatrixTopNControlStartTime,
              nlMatrixTopNControlOwner, nlMatrixTopNControlStatus,
              nlMatrixTopNProtocolDirLocalIndex,
              nlMatrixTopNSourceAddress, nlMatrixTopNDestAddress,
              nlMatrixTopNPktRate, nlMatrixTopNReversePktRate,



              nlMatrixTopNOctetRate, nlMatrixTopNReverseOctetRate }
    STATUS  current
    DESCRIPTION
        "Counts the amount of traffic sent between each pair of
        network addresses discovered by the probe.  Note that while
        the hlMatrixControlTable also has objects that control
        optional alMatrixTables, implementation of the
        alMatrixTables is not required to fully implement this
        group."
     ::= { rmon2MIBGroups 5 }

alHostGroup OBJECT-GROUP
    OBJECTS { alHostInPkts, alHostOutPkts,
              alHostInOctets, alHostOutOctets, alHostCreateTime }
    STATUS  current
    DESCRIPTION
        "Counts the amount of traffic, by protocol, sent from and to
        each network address discovered by the probe.  Implementation
        of this group requires implementation of the Network-Layer
        Host Group."
    ::= { rmon2MIBGroups 6 }

alMatrixGroup OBJECT-GROUP
    OBJECTS { alMatrixSDPkts, alMatrixSDOctets, alMatrixSDCreateTime,
              alMatrixDSPkts, alMatrixDSOctets, alMatrixDSCreateTime,
              alMatrixTopNControlMatrixIndex,
              alMatrixTopNControlRateBase,
              alMatrixTopNControlTimeRemaining,
              alMatrixTopNControlGeneratedReports,
              alMatrixTopNControlDuration,
              alMatrixTopNControlRequestedSize,
              alMatrixTopNControlGrantedSize,
              alMatrixTopNControlStartTime,
              alMatrixTopNControlOwner, alMatrixTopNControlStatus,
              alMatrixTopNProtocolDirLocalIndex,
              alMatrixTopNSourceAddress, alMatrixTopNDestAddress,
              alMatrixTopNAppProtocolDirLocalIndex,
              alMatrixTopNPktRate, alMatrixTopNReversePktRate,
              alMatrixTopNOctetRate, alMatrixTopNReverseOctetRate }
    STATUS  current
    DESCRIPTION
        "Counts the amount of traffic, by protocol, sent between each
        pair of network addresses discovered by the
        probe.  Implementation of this group requires implementation
        of the Network-Layer Matrix Group."
    ::= { rmon2MIBGroups 7 }

usrHistoryGroup OBJECT-GROUP



    OBJECTS { usrHistoryControlObjects,
              usrHistoryControlBucketsRequested,
              usrHistoryControlBucketsGranted,
              usrHistoryControlInterval,
              usrHistoryControlOwner, usrHistoryControlStatus,
              usrHistoryObjectVariable, usrHistoryObjectSampleType,
              usrHistoryIntervalStart, usrHistoryIntervalEnd,
              usrHistoryAbsValue, usrHistoryValStatus }
    STATUS  current
    DESCRIPTION
        "The usrHistoryGroup provides user-defined collection of
        historical information from MIB objects on the probe."
    ::= { rmon2MIBGroups 8 }

probeInformationGroup OBJECT-GROUP
    OBJECTS { probeCapabilities,
              probeSoftwareRev, probeHardwareRev, probeDateTime }
    STATUS  current
    DESCRIPTION
        "This group describes various operating parameters of the
        probe and controls the local time of the probe."
    ::= { rmon2MIBGroups 9 }

probeConfigurationGroup OBJECT-GROUP
    OBJECTS { probeResetControl, probeDownloadFile,
              probeDownloadTFTPServer, probeDownloadAction,
              probeDownloadStatus,
              serialMode, serialProtocol, serialTimeout,
              serialModemInitString, serialModemHangUpString,
              serialModemConnectResp, serialModemNoConnectResp,
              serialDialoutTimeout, serialStatus,
              netConfigIPAddress, netConfigSubnetMask,
              netConfigStatus, netDefaultGateway,
              trapDestCommunity, trapDestProtocol, trapDestAddress,
              trapDestOwner, trapDestStatus,
              serialConnectDestIpAddress, serialConnectType,
              serialConnectDialString, serialConnectSwitchConnectSeq,
              serialConnectSwitchDisconnectSeq,
              serialConnectSwitchResetSeq,
              serialConnectOwner, serialConnectStatus }
    STATUS  deprecated
    DESCRIPTION
        "This group controls the configuration of various operating
        parameters of the probe.  This group is not referenced by any
        MODULE-COMPLIANCE macro because it is 'grandfathered' from
        more recent MIB review rules that would require it."
    ::= { rmon2MIBGroups 10 }




rmon1EnhancementGroup OBJECT-GROUP
    OBJECTS { historyControlDroppedFrames, hostControlDroppedFrames,
              hostControlCreateTime, matrixControlDroppedFrames,
              matrixControlCreateTime, channelDroppedFrames,
              channelCreateTime, filterProtocolDirDataLocalIndex,
              filterProtocolDirLocalIndex }
    STATUS  current
    DESCRIPTION
        "This group adds some enhancements to RMON-1 that help
        management stations."
    ::= { rmon2MIBGroups 11 }

rmon1EthernetEnhancementGroup OBJECT-GROUP
    OBJECTS { etherStatsDroppedFrames, etherStatsCreateTime }
    STATUS  current
    DESCRIPTION
        "This group adds some enhancements to RMON-1 that help
        management stations."
    ::= { rmon2MIBGroups 12 }

rmon1TokenRingEnhancementGroup OBJECT-GROUP
    OBJECTS { tokenRingMLStatsDroppedFrames,
              tokenRingMLStatsCreateTime,
              tokenRingPStatsDroppedFrames, tokenRingPStatsCreateTime,
              ringStationControlDroppedFrames,
              ringStationControlCreateTime,
              sourceRoutingStatsDroppedFrames,
              sourceRoutingStatsCreateTime }
    STATUS  deprecated
    DESCRIPTION
        "This group adds some enhancements to RMON-1 that help
        management stations.  This group is not referenced by any
        MODULE-COMPLIANCE macro because it is 'grandfathered' from
        more recent MIB review rules that would require it."
    ::= { rmon2MIBGroups 13 }
END
