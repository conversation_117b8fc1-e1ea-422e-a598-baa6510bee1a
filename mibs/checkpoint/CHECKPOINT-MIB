
CHECKPOINT-MIB DEFINITIONS ::= BEGIN

	  -- SUBTREE: 1.3.6.1.4.1.2620.1
	  -- iso.org.dod.internet.private.enterprises.checkpoint.products

          IMPORTS
                  MODULE-IDENTITY, OBJECT-TYP<PERSON>, <PERSON><PERSON>ger<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, NOTI<PERSON>CATION-T<PERSON><PERSON>, enterprises
                      	FROM SNMPv2-SMI
                  OBJECT-GROUP
                      	FROM SNMPv2-CONF    
                  DisplayString 
                  		FROM SNMPv2-TC;

    checkpoint MODULE-IDENTITY
		LAST-UPDATED "201312261309Z"
		ORGANIZATION "Check Point"
		CONTACT-INFO "Check Point"
		DESCRIPTION "Check Point MIB
					 See the most common OIDs, with detailed descriptions, in the SNMP Best Practices Guide - sk98552
					(https://supportcenter.checkpoint.com/supportcenter/portal?eventSubmit_doGoviewsolutiondetails=&solutionid=sk98552)"
		REVISION "201312261309Z"
		DESCRIPTION "Update the SMIv1 MIB to SMIv2"
		::= { enterprises 2620 }              		


--	checkpoint				OBJECT IDENTIFIER ::= { enterprises 2620 }
	products				OBJECT IDENTIFIER ::= { checkpoint 1 }
	tables					OBJECT IDENTIFIER ::= { checkpoint 500 }
	fw						OBJECT IDENTIFIER ::= { products 1 }
	vpn						OBJECT IDENTIFIER ::= { products 2 }
	fg						OBJECT IDENTIFIER ::= { products 3 }
	ha						OBJECT IDENTIFIER ::= { products 5 }
	svn						OBJECT IDENTIFIER ::= { products 6 }
	mngmt					OBJECT IDENTIFIER ::= { products 7 }
	wam						OBJECT IDENTIFIER ::= { products 8 }
	dtps					OBJECT IDENTIFIER ::= { products 9 }
	ls						OBJECT IDENTIFIER ::= { products 11 }
	vsx						OBJECT IDENTIFIER ::= { products 16 }
	smartDefense			OBJECT IDENTIFIER ::= { products 17 }
	gx						OBJECT IDENTIFIER ::= { products 20 }
	avi 					OBJECT IDENTIFIER ::= { products 24 }
	eventiaAnalyzer			OBJECT IDENTIFIER ::= { products 25 }
	uf						OBJECT IDENTIFIER ::= { products 29 }
	ms						OBJECT IDENTIFIER ::= { products 30 }
	voip					OBJECT IDENTIFIER ::= { products 31 }
	identityAwareness		OBJECT IDENTIFIER ::= { products 38 }
	applicationControl		OBJECT IDENTIFIER ::= { products 39 }
	thresholds				OBJECT IDENTIFIER ::= { products 42 }
	advancedUrlFiltering	OBJECT IDENTIFIER ::= { products 43 }
	dlp						OBJECT IDENTIFIER ::= { products 44 }
	amw						OBJECT IDENTIFIER ::= { products 46 }
	te						OBJECT IDENTIFIER ::= { products 49 }
	treatExtarction			OBJECT IDENTIFIER ::= { products 50 }
	sxl						OBJECT IDENTIFIER ::= { products 36 }

	vsxVsSupported OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "The maximum number of supported Virtual Systems"
              ::= {vsx 11 }

	vsxVsConfigured OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "The number of configured Virtual Systems"
              ::= {vsx 12 }

	vsxVsInstalled OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "The number of installed Virtual Systems"
              ::= {vsx 13 }
			  
	vsxVrfConfigured OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "The number of configured VRFs"
              ::= {vsx 14 }

	vsxStatus OBJECT IDENTIFIER ::= { vsx 22 } 

	 vsxStatusTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  VsxStatusEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current       
			  DESCRIPTION
                      ""
			   ::= { vsxStatus 1 }        
			   
	vsxStatusEntry OBJECT-TYPE
	          SYNTAX  VsxStatusEntry
	          MAX-ACCESS  not-accessible
	          STATUS  current
	          DESCRIPTION
                      ""
		      INDEX { vsxStatusVSId }
	          ::= { vsxStatusTable 1 } 	
		
     VsxStatusEntry ::=
              SEQUENCE {
                 vsxStatusVSId
                      Unsigned32,
                 vsxStatusVRId
                      Unsigned32,
                 vsxStatusVsName 
                      DisplayString,
                 vsxStatusVsType
                      DisplayString,
                 vsxStatusMainIP
                      DisplayString,
                 vsxStatusPolicyName
                      DisplayString,
                 vsxStatusVsPolicyType
                      DisplayString,
                 vsxStatusSicTrustState
                      DisplayString,
                 vsxStatusHAState
                      DisplayString,
                 vsxStatusVSWeight
                      Unsigned32
              }
              
      vsxStatusVSId OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                    "Virtual System ID"
            ::= { vsxStatusEntry 1 }

      vsxStatusVRId OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                    "Operating System ID (VRF ID)"
            ::= { vsxStatusEntry 2 }
            
      vsxStatusVsName OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                     "Virtual System's name"
            ::= { vsxStatusEntry 3 }

      vsxStatusVsType OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                     "Virtual System's type"
            ::= { vsxStatusEntry 4 }

      vsxStatusMainIP OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                    "Virtual System's Main IP"
            ::= { vsxStatusEntry 5 }

      vsxStatusPolicyName OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                    "Security Policy name"
            ::= { vsxStatusEntry 6 }

      vsxStatusVsPolicyType OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                    "Security Policy type"
            ::= { vsxStatusEntry 7 }

      vsxStatusSicTrustState OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                    "SIC status"
            ::= { vsxStatusEntry 8 }

      vsxStatusHAState OBJECT-TYPE
            SYNTAX  DisplayString (SIZE (0..255))
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                "High Availability Status"
            ::= { vsxStatusEntry 9 }

      vsxStatusVSWeight OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                "Virtual System Resource Control Weight"
            ::= { vsxStatusEntry 10 }



	  vsxStatusCPUUsageTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  VsxStatusCPUUsageEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current
			  DESCRIPTION
                      ""  
			   ::= { vsxStatus 2 }        
			   
	vsxStatusCPUUsageEntry OBJECT-TYPE
	          SYNTAX  VsxStatusCPUUsageEntry
	          MAX-ACCESS  not-accessible
	          STATUS  current
	          DESCRIPTION
                      ""
		      INDEX { vsxStatusVSId }
	          ::= { vsxStatusCPUUsageTable 1 } 
		
     VsxStatusCPUUsageEntry ::=
              SEQUENCE {
				vsxStatusCPUUsage1sec
                      INTEGER,
				vsxStatusCPUUsage10sec
                      INTEGER,
				vsxStatusCPUUsage1min
                      INTEGER,
				vsxStatusCPUUsage1hr
                      INTEGER,
				vsxStatusCPUUsage24hr
                      INTEGER,
				vsxStatusCPUUsageVSId
                      Unsigned32
                      }

      vsxStatusCPUUsage1sec OBJECT-TYPE
              SYNTAX  INTEGER (0..100)
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "CPU usage (%) in the last 1 second, averaged for all cores"
              ::= { vsxStatusCPUUsageEntry 1 } 

      vsxStatusCPUUsage10sec OBJECT-TYPE
              SYNTAX  INTEGER (0..100)
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "CPU usage (%) in the last 10 seconds, averaged for all cores"
              ::= { vsxStatusCPUUsageEntry 2 } 

      vsxStatusCPUUsage1min OBJECT-TYPE
              SYNTAX  INTEGER (0..100)
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "CPU usage (%) in the last 1 minute, averaged for all cores"
              ::= { vsxStatusCPUUsageEntry 3 } 

      vsxStatusCPUUsage1hr OBJECT-TYPE
              SYNTAX  INTEGER (0..100)
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "CPU usage (%) in the last 1 hour, averaged for all cores"
              ::= { vsxStatusCPUUsageEntry 4 } 

      vsxStatusCPUUsage24hr OBJECT-TYPE
              SYNTAX  INTEGER (0..100)
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "CPU usage (%) in the last 24 hours, averaged for all cores"
              ::= { vsxStatusCPUUsageEntry 5 } 

      vsxStatusCPUUsageVSId OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "Virtual System ID"
              ::= { vsxStatusCPUUsageEntry 6 }

	  vsxStatusMemoryUsageTable OBJECT-TYPE
		  SYNTAX  SEQUENCE OF  VsxStatusMemoryUsageEntry
		  MAX-ACCESS  not-accessible
		  STATUS  current
		  DESCRIPTION "VS memory usage table per-VS."  
		   ::= { vsxStatus 3 }   

		vsxStatusMemoryUsageEntry OBJECT-TYPE
		          SYNTAX  VsxStatusMemoryUsageEntry
		          MAX-ACCESS  not-accessible
		          STATUS  current
		          DESCRIPTION ""
			      INDEX { vsxStatusMemoryUsageVSId }
		          ::= { vsxStatusMemoryUsageTable 1 } 

	    VsxStatusMemoryUsageEntry ::=
	              SEQUENCE {
					vsxStatusMemoryUsageVSId 	INTEGER,
					vsxStatusMemoryUsageVSName 	DisplayString,
					vsxStatusMemoryUsage 		Unsigned32
	                      }

      vsxStatusMemoryUsageVSId OBJECT-TYPE
              SYNTAX  INTEGER(0..250)
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION "Virtual System ID"
              ::= { vsxStatusMemoryUsageEntry 1 }

      vsxStatusMemoryUsageVSName OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION "Virtual-System name"
              ::= { vsxStatusMemoryUsageEntry 2 }

      vsxStatusMemoryUsage OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION "Current VS memory usage (KB)"
              ::= { vsxStatusMemoryUsageEntry 3 } 

	  vsxStatusCPUUsagePerCPUTable OBJECT-TYPE
		  SYNTAX  SEQUENCE OF  VsxStatusCPUUsagePerCPUEntry
		  MAX-ACCESS  not-accessible
		  STATUS  current
		  DESCRIPTION "CPU usage table per Core and per VS."  
		   ::= { vsxStatus 4 }   

		vsxStatusCPUUsagePerCPUEntry OBJECT-TYPE
		          SYNTAX  VsxStatusCPUUsagePerCPUEntry
		          MAX-ACCESS  not-accessible
		          STATUS  current
		          DESCRIPTION ""
			      INDEX { vsxStatusCPUUsagePerCPUVSId }
		          ::= { vsxStatusCPUUsagePerCPUTable 1 } 

	     VsxStatusCPUUsagePerCPUEntry ::=
	              SEQUENCE {
					vsxStatusCPUUsagePerCPUVSId 		INTEGER,
					vsxStatusCPUUsagePerCPUVSName 		DisplayString,
					vsxStatusCPUUsagePerCPUCoreNumber 	INTEGER,
					vsxStatusCPUUsagePerCPU1sec 		INTEGER,
					vsxStatusCPUUsagePerCPU10sec 		INTEGER,
					vsxStatusCPUUsagePerCPU1min 		INTEGER,
					vsxStatusCPUUsagePerCPU1hour 		INTEGER,
					vsxStatusCPUUsagePerCPU24hours 		INTEGER
	                      }

      vsxStatusCPUUsagePerCPUVSId OBJECT-TYPE
              SYNTAX  INTEGER(0..250)
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION "Virtual-System ID"
              ::= { vsxStatusCPUUsagePerCPUEntry 1 }

      vsxStatusCPUUsagePerCPUVSName OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION "Virtual-System name"
              ::= { vsxStatusCPUUsagePerCPUEntry 2 }

      vsxStatusCPUUsagePerCPUCoreNumber OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION "Core number"
              ::= { vsxStatusCPUUsagePerCPUEntry 3 }

      vsxStatusCPUUsagePerCPU1sec OBJECT-TYPE
              SYNTAX  INTEGER (0..100)
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION "CPU usage % in the last 1 second"
              ::= { vsxStatusCPUUsagePerCPUEntry 4 } 

      vsxStatusCPUUsagePerCPU10sec OBJECT-TYPE
              SYNTAX  INTEGER (0..100)
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION "CPU usage % in the last 10 seconds"
              ::= { vsxStatusCPUUsagePerCPUEntry 5 } 

      vsxStatusCPUUsagePerCPU1min OBJECT-TYPE
              SYNTAX  INTEGER (0..100)
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION "CPU usage % in the last 1 minute"
              ::= { vsxStatusCPUUsagePerCPUEntry 6 } 
 
      vsxStatusCPUUsagePerCPU1hour OBJECT-TYPE
              SYNTAX  INTEGER (0..100)
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION "CPU usage % in the last 1 hour"
              ::= { vsxStatusCPUUsagePerCPUEntry 7 } 

      vsxStatusCPUUsagePerCPU24hours OBJECT-TYPE
              SYNTAX  INTEGER (0..100)
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION "CPU usage % in the last 24 hours"
              ::= { vsxStatusCPUUsagePerCPUEntry 8 } 

      vsxCounters OBJECT IDENTIFIER ::= { vsx 23 } 

	vsxCountersTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  VsxCountersEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current
			  DESCRIPTION
                      ""  
			   ::= { vsxCounters 1 }        
			   
	vsxCountersEntry OBJECT-TYPE
	          SYNTAX  VsxCountersEntry
	          MAX-ACCESS  not-accessible
	          STATUS  current
	          DESCRIPTION
                      ""
		      INDEX { vsxStatusVSId }
	          ::= { vsxCountersTable 1 }
		
     VsxCountersEntry ::=
              SEQUENCE {
                 vsxCountersVSId
                      Unsigned32,
                 vsxCountersConnNum
                      Unsigned32,
                 vsxCountersConnPeakNum
                      Unsigned32,
                 vsxCountersConnTableLimit 
                      Unsigned32,
                 vsxCountersPackets
                      DisplayString,
                 vsxCountersDroppedTotal
                      DisplayString,
                 vsxCountersAcceptedTotal
		      		  DisplayString,
                 vsxCountersRejectedTotal
     		      	  DisplayString,
                 vsxCountersBytesAcceptedTotal
                      DisplayString,
                 vsxCountersBytesDroppedTotal
                      DisplayString,
                 vsxCountersBytesRejectedTotal
                      DisplayString,
                 vsxCountersLoggedTotal
                      DisplayString,
                 vsxCountersIsDataValid
                      INTEGER
              }
              
      vsxCountersVSId OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                    "Virtual System ID"
            ::= { vsxCountersEntry 1 }   

      vsxCountersConnNum OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                    "Number of active connections"
            ::= { vsxCountersEntry 2 }   
            
      vsxCountersConnPeakNum OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                    "Peak number of active connections"
            ::= { vsxCountersEntry 3 } 

      vsxCountersConnTableLimit OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                    "Connection table limit"
            ::= { vsxCountersEntry 4 } 

      vsxCountersPackets OBJECT-TYPE
            SYNTAX  DisplayString (SIZE (0..255))
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                    "Total number of packets processed"
            ::= { vsxCountersEntry 5 } 

      vsxCountersDroppedTotal OBJECT-TYPE
            SYNTAX  DisplayString (SIZE (0..255))
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                    "Total number of dropped packets"
            ::= { vsxCountersEntry 6 } 

      vsxCountersAcceptedTotal OBJECT-TYPE
            SYNTAX  DisplayString (SIZE (0..255))
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                    "Total number of accepted packets"
            ::= { vsxCountersEntry 7 } 

      vsxCountersRejectedTotal OBJECT-TYPE
            SYNTAX  DisplayString (SIZE (0..255))
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                    "Total number of rejected packets"
            ::= { vsxCountersEntry 8 } 

      vsxCountersBytesAcceptedTotal OBJECT-TYPE
            SYNTAX  DisplayString (SIZE (0..255))
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                    "Total number of accepted bytes"
            ::= { vsxCountersEntry 9 } 

      vsxCountersBytesDroppedTotal OBJECT-TYPE
            SYNTAX  DisplayString (SIZE (0..255))
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                    "Total number of dropped bytes"
            ::= { vsxCountersEntry 10 } 

      vsxCountersBytesRejectedTotal OBJECT-TYPE
            SYNTAX  DisplayString (SIZE (0..255))
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                    "Total number of rejected bytes"
            ::= { vsxCountersEntry 11 } 

      vsxCountersLoggedTotal OBJECT-TYPE
            SYNTAX  DisplayString (SIZE (0..255))
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                    "Total number of logs sent"
            ::= { vsxCountersEntry 12 } 

      vsxCountersIsDataValid OBJECT-TYPE
            SYNTAX  INTEGER {
                       invalid(0),
                       valid(1)
                    }
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                    "Data validity indicator 
					 invalid(0)
					 valid(1)"
            ::= { vsxCountersEntry 13 } 



	  raUsersTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  RaUsersEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current  
              DESCRIPTION
                      "A table containing Remote Access users tunnel information."
			   ::= { tables 9000 }        
			   
			   
	  raUsersEntry OBJECT-TYPE
	          SYNTAX  RaUsersEntry
	          MAX-ACCESS  not-accessible
	          STATUS  current
	          DESCRIPTION ""
	          INDEX   { raInternalIpAddr }
	          ::= { raUsersTable 1 } 	
		
      RaUsersEntry ::=
              SEQUENCE {
                 raInternalIpAddr
                      IpAddress,
                 raExternalIpAddr
                      IpAddress,
                 raUserState
                      INTEGER,
                 raOfficeMode 
                      INTEGER,
                 raIkeOverTCP
                      INTEGER,
                 raUseUDPEncap
                      INTEGER,
                 raVisitorMode
				      INTEGER,
     		     raRouteTraffic
     		          INTEGER,
     		     raCommunity
     		          DisplayString,
     		     raTunnelEncAlgorithm
     		          INTEGER,
     		     raTunnelAuthMethod
     		          INTEGER,
     		     raLogonTime
     		          INTEGER
              }
              
      raInternalIpAddr OBJECT-TYPE
            SYNTAX  IpAddress
            MAX-ACCESS read-only
            STATUS  current 
            DESCRIPTION ""
            ::= { raUsersEntry 1 }   
            
      raExternalIpAddr OBJECT-TYPE
            SYNTAX  IpAddress
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { raUsersEntry 19 } 
            
      raUserState OBJECT-TYPE
            SYNTAX  INTEGER {
                          active(3),
                          destroy(4),
                          idle(129),
                          phase1(130),
                          down(131),
                          init(132)
             		}
            MAX-ACCESS  read-write
            STATUS  current
            DESCRIPTION ""
            ::= { raUsersEntry 20 }   
            
      raOfficeMode OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { raUsersEntry 21 } 

      raIkeOverTCP OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { raUsersEntry 22 } 
            
      raUseUDPEncap OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { raUsersEntry 23 } 
            
      raVisitorMode OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { raUsersEntry 24 } 

      raRouteTraffic OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { raUsersEntry 25 } 

      raCommunity OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { raUsersEntry 26 } 

      raTunnelEncAlgorithm OBJECT-TYPE
            SYNTAX  INTEGER {
                          espDES(1),
                          esp3DES(2),
                          espCAST(5),
                          esp3IDEA(7),
                          espNULL(9),
                          espAES128(129),
                          espAES256(130)
             		}
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { raUsersEntry 27 } 

      raTunnelAuthMethod OBJECT-TYPE
            SYNTAX  INTEGER {
                          preshared-key(1),
                          dss-signature(2),
                          rsa-signature(3),
                          rsa-encryption(4),
                          rev-rsa-encryption(5),
                          xauth(129),
                          crack(130)
             		}
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { raUsersEntry 28 } 

      raLogonTime OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { raUsersEntry 29 } 

	  tunnelTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  TunnelEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current  
              DESCRIPTION
                      "A table containing VPN tunnel information."
			   ::= { tables 9002 }        
			   
			   
	  tunnelEntry OBJECT-TYPE
	          SYNTAX  TunnelEntry
	          MAX-ACCESS  not-accessible
	          STATUS  current
	          DESCRIPTION ""
	          INDEX   { tunnelPeerIpAddr }
	          ::= { tunnelTable 1 } 	
		
      TunnelEntry ::=
              SEQUENCE {
                 tunnelPeerIpAddr
                      IpAddress,
                 tunnelPeerObjName
                      DisplayString,
                 tunnelState
                      INTEGER,
                 tunnelCommunity 
                      DisplayString,
                 tunnelNextHop
                      IpAddress,
                 tunnelInterface
                      DisplayString,
                 tunnelSourceIpAddr
                      IpAddress,
                 tunnelLinkPriority
                      INTEGER,
                 tunnelProbState
                      INTEGER,
                 tunnelPeerType
                      INTEGER,
                 tunnelType
                      INTEGER
              }
              
      tunnelPeerIpAddr OBJECT-TYPE
            SYNTAX  IpAddress
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { tunnelEntry 1 }   

      tunnelPeerObjName OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { tunnelEntry 2 }   

      tunnelState OBJECT-TYPE
            SYNTAX  INTEGER {
                          active(3),
                          destroy(4),
                          idle(129),
                          phase1(130),
                          down(131),
                          init(132)
             		}
            MAX-ACCESS  read-write
            STATUS  current
            DESCRIPTION ""
            ::= { tunnelEntry 3 }   

      tunnelCommunity OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { tunnelEntry 4 }   

      tunnelNextHop OBJECT-TYPE
            SYNTAX  IpAddress
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { tunnelEntry 5 }   
            
      tunnelInterface OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { tunnelEntry 6 }   

      tunnelSourceIpAddr OBJECT-TYPE
            SYNTAX  IpAddress
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { tunnelEntry 7 }

      tunnelLinkPriority OBJECT-TYPE
            SYNTAX  INTEGER {
                          primary(0),
                          backup(1),
                          on-demand(2)
             		}
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { tunnelEntry 8 }

      tunnelProbState OBJECT-TYPE
            SYNTAX  INTEGER {
                          unknown(0),
                          alive(1),
                          dead(2)
             		}
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { tunnelEntry 9 }

      tunnelPeerType OBJECT-TYPE
            SYNTAX  INTEGER {
                          regular(1),
                          daip(2),
                          robo(3),
			  lsv(4)
             		}
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { tunnelEntry 10 }
            
      tunnelType OBJECT-TYPE
            SYNTAX  INTEGER {
                          regular(1),
                          permanent(2)
             		}
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { tunnelEntry 11 }
            
	  permanentTunnelTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  PermanentTunnelEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current  
              DESCRIPTION
                      "A table containing VPN permanent tunnel information."
			   ::= { tables 9003 }        
			   
			   
	  permanentTunnelEntry OBJECT-TYPE
	          SYNTAX  PermanentTunnelEntry
	          MAX-ACCESS  not-accessible
	          STATUS  current
	          DESCRIPTION ""
	          INDEX   { permanentTunnelPeerIpAddr }
	          ::= { permanentTunnelTable 1 } 	
		
      PermanentTunnelEntry ::=
              SEQUENCE {
                 permanentTunnelPeerIpAddr
                      IpAddress,
                 permanentTunnelPeerObjName
                      DisplayString,
                 permanentTunnelState
                      INTEGER,
                 permanentTunnelCommunity 
                      DisplayString,
                 permanentTunnelNextHop
                      IpAddress,
                 permanentTunnelInterface
                      IpAddress,
                 permanentTunnelSourceIpAddr
                      IpAddress,
                 permanentTunnelLinkPriority
                      INTEGER,
                 permanentTunnelProbState
                      INTEGER,
                 permanentTunnelPeerType
                      INTEGER
              }
              
      permanentTunnelPeerIpAddr OBJECT-TYPE
            SYNTAX  IpAddress
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { permanentTunnelEntry 1 }   

      permanentTunnelPeerObjName OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { permanentTunnelEntry 2 }   

      permanentTunnelState OBJECT-TYPE
            SYNTAX  INTEGER {
                          active(3),
                          destroy(4),
                          idle(129),
                          phase1(130),
                          down(131),
                          init(132)
             		}
            MAX-ACCESS  read-write
            STATUS  current
            DESCRIPTION ""
            ::= { permanentTunnelEntry 3 }   

      permanentTunnelCommunity OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { permanentTunnelEntry 4 }   

      permanentTunnelNextHop OBJECT-TYPE
            SYNTAX  IpAddress
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { permanentTunnelEntry 5 }   
            
      permanentTunnelInterface OBJECT-TYPE
            SYNTAX  IpAddress
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { permanentTunnelEntry 6 }   

      permanentTunnelSourceIpAddr OBJECT-TYPE
            SYNTAX  IpAddress
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { permanentTunnelEntry 7 }

      permanentTunnelLinkPriority OBJECT-TYPE
            SYNTAX  INTEGER {
                          primary(0),
                          backup(1),
                          on-demand(2)
             		}
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { permanentTunnelEntry 8 }

      permanentTunnelProbState OBJECT-TYPE
            SYNTAX  INTEGER {
                          unknown(0),
                          alive(1),
                          dead(2)
             		}
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { permanentTunnelEntry 9 }

      permanentTunnelPeerType OBJECT-TYPE
            SYNTAX  INTEGER {
                          regular(1),
                          daip(2),
                          robo(3)
             		}
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { permanentTunnelEntry 10 }
            
	  -- the FW group
	  -- Overall statistics and state
	

	  fwPolicyStat  OBJECT IDENTIFIER ::= { fw 25 }  
      fwPerfStat    OBJECT IDENTIFIER ::= { fw 26 }
      
	  fwHmem      OBJECT IDENTIFIER ::= { fwPerfStat 1 }
	  fwKmem      OBJECT IDENTIFIER ::= { fwPerfStat 2 }
	  fwInspect   OBJECT IDENTIFIER ::= { fwPerfStat 3 }
	  fwCookies   OBJECT IDENTIFIER ::= { fwPerfStat 4 }
	  fwChains    OBJECT IDENTIFIER ::= { fwPerfStat 5 }
	  fwFragments OBJECT IDENTIFIER ::= { fwPerfStat 6 }
	  fwUfp       OBJECT IDENTIFIER ::= { fwPerfStat 8 }
	  fwSS        OBJECT IDENTIFIER ::= { fwPerfStat 9 }
	  fwConnectionsStat OBJECT IDENTIFIER ::= { fwPerfStat 11 }
	  fwHmem64 OBJECT IDENTIFIER ::= { fwPerfStat 12 }
	  fwSS-http   OBJECT IDENTIFIER ::= { fwSS 1 }
	  fwSS-ftp    OBJECT IDENTIFIER ::= { fwSS 2 }
	  fwSS-telnet OBJECT IDENTIFIER ::= { fwSS 3 }
	  fwSS-rlogin OBJECT IDENTIFIER ::= { fwSS 4 }
	  fwSS-ufp    OBJECT IDENTIFIER ::= { fwSS 5 }
	  fwSS-smtp   OBJECT IDENTIFIER ::= { fwSS 6 }    
	  fwSS-POP3	  OBJECT IDENTIFIER ::= { fwSS 7 } 	  
	  fwSS-av-total	  OBJECT IDENTIFIER ::= { fwSS 10 } 
	  
	  
	  fwModuleState OBJECT-TYPE
              SYNTAX  DisplayString (SIZE (0..255))
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "The state of the fw module"
              ::= { fw 1 }

	  fwFilterName OBJECT-TYPE
              SYNTAX  DisplayString (SIZE (0..255))
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "The name of the loaded filter"
              ::= { fw 2 }

	  fwFilterDate OBJECT-TYPE
              SYNTAX  DisplayString (SIZE (0..255))
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "Install time"
              ::= { fw 3 }

	  fwAccepted OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "The number of accepted packets."
              ::= { fw 4 }

	  fwRejected OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "The number of rejected  packets."
              ::= { fw 5 }

	  fwDropped OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "The number of dropped packets."
              ::= { fw 6 }

	  fwLogged OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "The number of logged packets."
              ::= { fw 7 }

	  fwMajor OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "Major version"
              ::= { fw 8 }

	  fwMinor OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "Minor version"
              ::= { fw 9 }

	  fwProduct OBJECT-TYPE
              SYNTAX  DisplayString (SIZE (0..255))
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "Product name"
              ::= { fw 10 }

	  fwEvent OBJECT-TYPE
              SYNTAX  DisplayString (SIZE (0..255))
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "A string containing the last snmp trap sent via fw"
              ::= { fw 11 }

	  fwSICTrustState OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "SIC Trust State : Possible values include:
                                            Not initialized 0 
                                            Initialized but not established 1
                                            Established 2
                                            Error 3
                                            Unknown 4"

              ::= { fw 12 }

      fwTrapPrefix		OBJECT IDENTIFIER ::= { fw 0 }

	  fwTrap NOTIFICATION-TYPE
			  OBJECTS	{ fwEvent }
			  STATUS  current
			  DESCRIPTION
			  "FireWall-1 SNMP trap"
			  ::= { fwTrapPrefix 1 }

      fwProdName OBJECT-TYPE
              SYNTAX  DisplayString (SIZE (0..255))
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "Product name"
             ::= { fw 21 }

	  fwVerMajor OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "Major version"
              ::= { fw 22 }

	  fwVerMinor OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "Minor version"
              ::= { fw 23 }

	  fwKernelBuild OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION
                      "Kernel build num"
              ::= { fw 24 }

	  fwPolicyName OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
	  		  MAX-ACCESS read-only
	  		  STATUS  current
	  		  DESCRIPTION
	  		          "Policy Name"
	  		  ::= { fwPolicyStat 1 }
	  		  
	  fwInstallTime OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
	  		  MAX-ACCESS read-only
	  		  STATUS  current
	  		  DESCRIPTION
	  		          "Policy install time"
	  		  ::= { fwPolicyStat 2 }
      fwNumConn OBJECT-TYPE
	  		  SYNTAX  Unsigned32
	  		  MAX-ACCESS read-only
	  		  STATUS  current
	  		  DESCRIPTION
	  		          "Number of connections"
	  		  ::= { fwPolicyStat 3 }
	  		  
	  fwPeakNumConn OBJECT-TYPE
	  		  SYNTAX  Unsigned32
	  		  MAX-ACCESS read-only
	  		  STATUS  current
	  		  DESCRIPTION
	  		          "Peak number of connections"
	  		  ::= { fwPolicyStat 4 }	
	  		  
	  fwIfTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  FwIfEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current
			  DESCRIPTION ""  
			   ::= { fwPolicyStat 5 }        

      fwConnTableLimit OBJECT-TYPE
	  		  SYNTAX  Unsigned32
	  		  MAX-ACCESS read-only
	  		  STATUS  current
	  		  DESCRIPTION
	  		          "Connection table limit"
	  		  ::= { fwPolicyStat 10 }
			   
			   
	  fwIfEntry OBJECT-TYPE
	          SYNTAX  FwIfEntry
	          MAX-ACCESS  not-accessible
	          STATUS  current
	          DESCRIPTION ""
	          INDEX   { fwIfIndex }
	          ::= { fwIfTable 1 } 	
		
      FwIfEntry ::=
              SEQUENCE {
                 fwIfIndex
                      Unsigned32,
                 fwIfName
                      DisplayString,
                 fwAcceptPcktsIn
                      Unsigned32,
                 fwAcceptPcktsOut 
                      Unsigned32,
                 fwAcceptBytesIn
                      Unsigned32,
                 fwAcceptBytesOut
                      Unsigned32,
                 fwDropPcktsIn
                      Unsigned32,
                 fwDropPcktsOut
                      Unsigned32,
                 fwRejectPcktsIn
                      Unsigned32,
                 fwRejectPcktsOut
                      Unsigned32,
                 fwLogIn
                      Unsigned32,
                 fwLogOut
                      Unsigned32
              }
              
      fwIfIndex OBJECT-TYPE
            SYNTAX Unsigned32 
            MAX-ACCESS read-only
            STATUS  current      
            DESCRIPTION ""
            ::= { fwIfEntry 1 }   
            
      fwIfName OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { fwIfEntry 2 } 
            
      fwAcceptPcktsIn OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { fwIfEntry 5 }   
            
      fwAcceptPcktsOut OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { fwIfEntry 6 } 

	  fwAcceptBytesIn OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { fwIfEntry 7 } 

	  fwAcceptBytesOut OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { fwIfEntry 8 } 


      fwDropPcktsIn OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { fwIfEntry 9 } 
          
      fwDropPcktsOut OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { fwIfEntry 10 } 


      fwRejectPcktsIn OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { fwIfEntry 11 }   
            
      fwRejectPcktsOut OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { fwIfEntry 12 } 
            
      fwLogIn OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { fwIfEntry 13 } 
            
      fwLogOut OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { fwIfEntry 14 } 

	  fwAcceptedTotal OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
	  		  MAX-ACCESS read-only
	  		  STATUS  current
	  		  DESCRIPTION
	  		          "Total number of accepted packets"
	  		  ::= { fwPolicyStat 6 }	

	  fwDroppedTotal OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
	  		  MAX-ACCESS read-only
	  		  STATUS  current
	  		  DESCRIPTION
	  		          "Total number of dropped packets"
	  		  ::= { fwPolicyStat 16 }	

          fwRejectedTotal OBJECT-TYPE
                          SYNTAX DisplayString (SIZE (0..255))
	  		  MAX-ACCESS read-only
                          STATUS  current
                          DESCRIPTION
                                  "Total number of rejected packets"
                          ::= { fwPolicyStat 14 }

          fwLoggedTotal OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
	  		  MAX-ACCESS read-only
	  		  STATUS  current
	  		  DESCRIPTION
                                  "Total number of logs sent"
                          ::= { fwPolicyStat 13 }

	  fwAcceptedBytesTotal OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
	  		  MAX-ACCESS read-only
	  		  STATUS  current
	  		  DESCRIPTION
	  		          "Total number of accepted bytes"
	  		  ::= { fwPolicyStat 8 }	

	  fwDroppedBytesTotal OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
	  		  MAX-ACCESS read-only
	  		  STATUS  current
	  		  DESCRIPTION
	  		          "Total number of dropped bytes"
	  		  ::= { fwPolicyStat 9 }	

	  fwRejectedBytesTotal OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
	  		  MAX-ACCESS read-only
	  		  STATUS  current
	  		  DESCRIPTION
	  		          "Total number of rejected bytes"
	  		  ::= { fwPolicyStat 15 }

	  fwAcceptedBytesRates OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
	  		  MAX-ACCESS read-only
	  		  STATUS  current
	  		  DESCRIPTION
	  		          "Accepted bytes rates"
	  		  ::= { fwPolicyStat 20 }

	  fwAcceptedPcktsRates OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
	  		  MAX-ACCESS read-only
	  		  STATUS  current
	  		  DESCRIPTION
	  		          "Accepted packets rates"
	  		  ::= { fwPolicyStat 21 }

	  fwConnsRate OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
	  		  MAX-ACCESS read-only
	  		  STATUS  current
	  		  DESCRIPTION
	  		          "Connection Rates"
	  		  ::= { fwPolicyStat 22 }

	fwIfTable64 OBJECT-TYPE
		SYNTAX  SEQUENCE OF  FwIfEntry64
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION ""  
		::= { fwPolicyStat 25 }        

	fwIfEntry64 OBJECT-TYPE
		SYNTAX  FwIfEntry64
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION ""
		INDEX   { fwIfIndex64 }
		::= { fwIfTable64 1 } 	

	FwIfEntry64 ::=
		SEQUENCE {
			fwIfIndex64
				Unsigned32,
			fwIfName64
				DisplayString,
			fwAcceptPcktsIn64
				DisplayString,
			fwAcceptPcktsOut64
				DisplayString,
			fwAcceptBytesIn64
				DisplayString,
			fwAcceptBytesOut64
				DisplayString,
			fwDropPcktsIn64
				DisplayString,
			fwDropPcktsOut64
				DisplayString,
			fwRejectPcktsIn64
				DisplayString,
			fwRejectPcktsOut64
				DisplayString,
			fwLogIn64
				DisplayString,
			fwLogOut64
				DisplayString
		}

	fwIfIndex64 OBJECT-TYPE
		SYNTAX Unsigned32
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION ""
		::= { fwIfEntry64 1 }

	fwIfName64 OBJECT-TYPE
		SYNTAX  DisplayString
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION ""
		::= { fwIfEntry64 2 }

	fwAcceptPcktsIn64 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION ""
		::= { fwIfEntry64 5 }

	fwAcceptPcktsOut64 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION ""
		::= { fwIfEntry64 6 }

	fwAcceptBytesIn64 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION ""
		::= { fwIfEntry64 7 }

	fwAcceptBytesOut64 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION ""
		::= { fwIfEntry64 8 }

	fwDropPcktsIn64 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION ""
		::= { fwIfEntry64 9 }

	fwDropPcktsOut64 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION ""
		::= { fwIfEntry64 10 }

	fwRejectPcktsIn64 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION ""
		::= { fwIfEntry64 11 }

	fwRejectPcktsOut64 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION ""
		::= { fwIfEntry64 12 }

	fwLogIn64 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION ""
		::= { fwIfEntry64 13 }

	fwLogOut64 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION ""
		::= { fwIfEntry64 14 }


	  fwHmem-block-size	 OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - block size"
            ::= { fwHmem 1 } 

	  fwHmem-requested-bytes  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - requested bytes"
            ::= { fwHmem 2 } 

	  fwHmem-initial-allocated-bytes  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - initial allocated bytes"
            ::= { fwHmem 3 } 

	  fwHmem-initial-allocated-blocks OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - initial allocated blocks"
            ::= { fwHmem 4 } 

	  fwHmem-initial-allocated-pools  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - initial allocated pools"
            ::= { fwHmem 5 } 

	  fwHmem-current-allocated-bytes OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - current allocated bytes"
            ::= { fwHmem 6 } 

	  fwHmem-current-allocated-blocks OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - current allocated blocks"
            ::= { fwHmem 7 } 

	  fwHmem-current-allocated-pools  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - current allocated pools"
            ::= { fwHmem 8 } 

	  fwHmem-maximum-bytes  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - maximum bytes"
            ::= { fwHmem 9 } 
		
	  fwHmem-maximum-pools  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - maximum pools"
            ::= { fwHmem 10 } 
		
	  fwHmem-bytes-used  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - bytes used"
            ::= { fwHmem 11 } 
		
	  fwHmem-blocks-used  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - blocks used"
            ::= { fwHmem 12 } 
		
	  fwHmem-bytes-unused  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - bytes unused"
            ::= { fwHmem 13 } 
		
	  fwHmem-blocks-unused  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - blocks unused"
            ::= { fwHmem 14 } 
		
	  fwHmem-bytes-peak  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - bytes peak"
            ::= { fwHmem 15 } 
		
	  fwHmem-blocks-peak  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - blocks peak"
            ::= { fwHmem 16 } 
		
	  fwHmem-bytes-internal-use  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - bytes internal use"
            ::= { fwHmem 17 } 

	  fwHmem-number-of-items  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - number of items"
            ::= { fwHmem 18 } 
	
	  fwHmem-alloc-operations OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - alloc operations"
            ::= { fwHmem 19 } 
	
	  fwHmem-free-operations OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - free operations"
            ::= { fwHmem 20 } 
	
	  fwHmem-failed-alloc  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - failed alloc"
            ::= { fwHmem 21 } 
		
	  fwHmem-failed-free  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - failed free"
            ::= { fwHmem 22 } 
		
		
	  fwKmem-system-physical-mem  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "kmem - system physical mem"
            ::= { fwKmem 1 } 

	  fwKmem-available-physical-mem  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "kmem - available physical mem"
            ::= { fwKmem 2 } 

	  fwKmem-aix-heap-size  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "kmem - aix heap size"
            ::= { fwKmem 3 } 

	  fwKmem-bytes-used  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "kmem - bytes used"
            ::= { fwKmem 4 } 
	
	  fwKmem-blocking-bytes-used  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "kmem - blocking bytes used"
            ::= { fwKmem 5 } 

	  fwKmem-non-blocking-bytes-used  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "kmem - non blocking bytes used"
            ::= { fwKmem 6 } 

	  fwKmem-bytes-unused  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "kmem - bytes unused"
            ::= { fwKmem 7 } 

	  fwKmem-bytes-peak  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "kmem - bytes peak"
            ::= { fwKmem 8 } 

	  fwKmem-blocking-bytes-peak  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "kmem - blocking bytes peak"
            ::= { fwKmem 9 } 

	  fwKmem-non-blocking-bytes-peak  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "kmem - non blocking bytes peak"
            ::= { fwKmem 10 } 

	  fwKmem-bytes-internal-use  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "kmem - bytes internal use"
            ::= { fwKmem 11 } 

	  fwKmem-number-of-items  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "kmem - number of items"
            ::= { fwKmem 12 } 

	  fwKmem-alloc-operations  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "kmem - alloc operations"
            ::= { fwKmem 13 } 

	  fwKmem-free-operations  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "kmem - free operations"
            ::= { fwKmem 14 } 

	  fwKmem-failed-alloc  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "kmem - failed alloc"
            ::= { fwKmem 15 } 

	  fwKmem-failed-free  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "kmem - failed free"
            ::= { fwKmem 16 } 

	  fwInspect-packets  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "inspect - packets"
            ::= { fwInspect 1 } 

	  fwInspect-operations  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "inspect - operations"
            ::= { fwInspect 2 } 

	  fwInspect-lookups  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "inspect - lookups"
            ::= { fwInspect 3 } 

	  fwInspect-record  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "inspect - record"
            ::= { fwInspect 4 } 

	  fwInspect-extract  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "inspect - extract"
            ::= { fwInspect 5 } 
            
      fwCookies-total  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "cookies - total"
            ::= { fwCookies 1 } 
            
	  fwCookies-allocfwCookies-total  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "cookies - alloc"
            ::= { fwCookies 2 } 

	  fwCookies-freefwCookies-total  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "cookies - free"
            ::= { fwCookies 3 } 

	  fwCookies-dupfwCookies-total  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "cookies - dup"
            ::= { fwCookies 4 } 

	  fwCookies-getfwCookies-total  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "cookies - get"
            ::= { fwCookies 5 } 

	  fwCookies-putfwCookies-total  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "cookies - put"
            ::= { fwCookies 6 } 

	  fwCookies-lenfwCookies-total  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "cookies - len"
            ::= { fwCookies 7 } 

	  fwChains-alloc  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "chains - alloc"
            ::= { fwChains 1 }
            
      fwChains-free  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "chains - free"
            ::= { fwChains 2 } 
            
      fwFrag-fragments  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "fragments - fragments"
            ::= { fwFragments 1 }   
            
      fwFrag-expired  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "fragments - expired"
            ::= { fwFragments 2 }   
            
      fwFrag-packets  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "fragments - packets"
            ::= { fwFragments 3 }  

      fwUfpHitRatio  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ufp - % hits ratio"
            ::= { fwUfp 1 }   

      fwUfpInspected  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ufp - total connections"
            ::= { fwUfp 2 }   

      fwUfpHits  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ufp - hits connections"
            ::= { fwUfp 3 }   

      fwSS-http-pid  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - pid"
            ::= { fwSS-http 1 }   

      fwSS-http-proto  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - proto"
            ::= { fwSS-http 2 }   

      fwSS-http-port  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - port"
            ::= { fwSS-http 3 }   

      fwSS-http-logical-port  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - logical port"
            ::= { fwSS-http 4 }   

      fwSS-http-max-avail-socket  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - max avail socket"
            ::= { fwSS-http 5 }   

      fwSS-http-socket-in-use-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - socket in use max"
            ::= { fwSS-http 6 }   

      fwSS-http-socket-in-use-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - socket in use current"
            ::= { fwSS-http 7 }   

      fwSS-http-socket-in-use-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - socket in use count"
            ::= { fwSS-http 8 }   

      fwSS-http-sess-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - session max"
            ::= { fwSS-http 9 }   

      fwSS-http-sess-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - session current"
            ::= { fwSS-http 10 }   

      fwSS-http-sess-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - session count"
            ::= { fwSS-http 11 }   

      fwSS-http-auth-sess-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - auth session max"
            ::= { fwSS-http 12 }   

      fwSS-http-auth-sess-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - auth session current"
            ::= { fwSS-http 13 }   

      fwSS-http-auth-sess-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - auth session count"
            ::= { fwSS-http 14 }   

      fwSS-http-accepted-sess  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - accepted session"
            ::= { fwSS-http 15 }   

      fwSS-http-rejected-sess  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - rejected session"
            ::= { fwSS-http 16 }   

      fwSS-http-auth-failures  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - auth failures"
            ::= { fwSS-http 17 }   

      fwSS-http-ops-cvp-sess-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - opsec cvp session max"
            ::= { fwSS-http 18 }   

      fwSS-http-ops-cvp-sess-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - opsec cvp session current"
            ::= { fwSS-http 19 }   

      fwSS-http-ops-cvp-sess-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - opsec cvp session count"
            ::= { fwSS-http 20 }   

      fwSS-http-ops-cvp-rej-sess  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - opsec cvp rej session"
            ::= { fwSS-http 21 }   

      fwSS-http-ssl-encryp-sess-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - ssl encryp session max"
            ::= { fwSS-http 22 }   

      fwSS-http-ssl-encryp-sess-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - ssl encryp session current"
            ::= { fwSS-http 23 }   

      fwSS-http-ssl-encryp-sess-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - ssl encryp session count"
            ::= { fwSS-http 24 }   

      fwSS-http-transp-sess-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - transparent session max"
            ::= { fwSS-http 25 }   

      fwSS-http-transp-sess-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - transparent session current"
            ::= { fwSS-http 26 }   

      fwSS-http-transp-sess-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - transparent session count"
            ::= { fwSS-http 27 }   

      fwSS-http-proxied-sess-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - proxied session max"
            ::= { fwSS-http 28 }   

      fwSS-http-proxied-sess-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - proxied session current"
            ::= { fwSS-http 29 }   

      fwSS-http-proxied-sess-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - proxied session count"
            ::= { fwSS-http 30 }   

      fwSS-http-tunneled-sess-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - tunneled session max"
            ::= { fwSS-http 31 }   

      fwSS-http-tunneled-sess-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - tunneled session current"
            ::= { fwSS-http 32 }   

      fwSS-http-tunneled-sess-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - tunneled session count"
            ::= { fwSS-http 33 }   

      fwSS-http-ftp-sess-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - ftp session max"
            ::= { fwSS-http 34 }   

      fwSS-http-ftp-sess-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - ftp session current"
            ::= { fwSS-http 35 }   

      fwSS-http-ftp-sess-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - ftp session count"
            ::= { fwSS-http 36 }   

      fwSS-http-time-stamp  OBJECT-TYPE
            SYNTAX  DisplayString (SIZE (0..255))
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - time stamp"
            ::= { fwSS-http 37 }   

      fwSS-http-is-alive  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "http - is alive"
            ::= { fwSS-http 38 }   
                                 
      fwSS-http-blocked-cnt  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of HTTP items blocked, being classified as 'infected' by Anti Virus"
            ::= { fwSS-http 39 }  	 

      fwSS-http-blocked-total  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "sum of all the counts of the previous OID (fwss-http-blocked-cnt)"
            ::= { fwSS-http 40 }  	 

      fwSS-http-scanned-total  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "sum of all the counts of 'blocked' (infected) and 'passed' items"
            ::= { fwSS-http 41 }  	 

      fwSS-http-blocked-by-file-type  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of HTTP items blocked by 'file type' policy in Content Inspection tab in SmartDashboard"
            ::= { fwSS-http 42 }  	 

      fwSS-http-blocked-by-size-limit  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of HTTP items blocked by 'size limit' policy in Content Inspection tab in SmartDashboard"
            ::= { fwSS-http 43 }  	 

      fwSS-http-blocked-by-archive-limit  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of HTTP items blocked by 'archive limit' policy in Content Inspection tab in SmartDashboard"
            ::= { fwSS-http 44 }  	 
            
      fwSS-http-blocked-by-internal-error  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of HTTP items blocked because of 'internal error' in Content Inspection tab in SmartDashboard"            
	  ::= { fwSS-http 45 }  	 
	  		        

      fwSS-http-passed-cnt  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of HTTP items passed, being classified as 'safe' by Anti Virus"
            ::= { fwSS-http 46 }  	 
            
      fwSS-http-passed-by-file-type  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of HTTP items allowed (without AV scan) by 'File Type' policy in Content Inspection tab in 
	  		         SmartDashboard"
            ::= { fwSS-http 47 }  	 
          
      fwSS-http-passed-by-size-limit  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of HTTP items allowed (without AV scan) by 'Size Limit' policy in Content Inspection tab in 
	  		         SmartDashboard"
            ::= { fwSS-http 48 }  	 

      fwSS-http-passed-by-archive-limit  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of HTTP items allowed (without AV scan) by 'Archive Limit' policy in Content Inspection tab in 
	  		         SmartDashboard"
            ::= { fwSS-http 49 }  	 

      fwSS-http-passed-by-internal-error  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of HTTP items allowed (without AV scan) by 'internal error' in Content Inspection tab in 
	  		         SmartDashboard"
            ::= { fwSS-http 50 }  	 

      fwSS-http-passed-total  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "sum of all the above 'passed' counts - OIDs .46 to .50"
            ::= { fwSS-http 51 }  	 

      fwSS-http-blocked-by-AV-settings  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of HTTP items rejected by Anti Virus as either: 
	  		        exceeding size or archive limits, or failed scan due to 
	  		        internal error (configured in the 'settings' pane in 
	  		        'Content Inspection' tab in SmartDashboard)"
            ::= { fwSS-http 52 }  
            	 
      fwSS-http-passed-by-AV-settings  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of HTTP items allowed by Anti Virus as either: 
	  		        exceeding size or archive limits, or failed scan due to 
	  		        internal error (configured in the 'settings' pane in 
	  		        'Content Inspection' tab in SmartDashboard)"
            ::= { fwSS-http 53 }  	 

      fwSS-http-blocked-by-URL-filter-category  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of URLs blocked by 'Web Filtering Policy' in 'Content Inspection' tab in SmartDashboard"
            ::= { fwSS-http 54 }  	 

      fwSS-http-blocked-by-URL-block-list  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of URLs blocked by 'Blocked URLs/IPs' in 'Content Inspection' tab in SmartDashboard 
	  		        (overriding 'Web Filtering Policy')."
            ::= { fwSS-http 55 }  	 

      fwSS-http-passed-by-URL-allow-list  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of Allowed by 'allowed URLs/IPs' in 'Content Inspection' tab in SmartDashboard 
	  		        (overriding 'Web Filtering Policy')"
            ::= { fwSS-http 56 }  	 
                                         
      fwSS-http-passed-by-URL-filter-category  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of URLs allowed by 'Web Filtering Policy' in 'Content Inspection' tab in SmartDashboard."
            ::= { fwSS-http 57 }                                             
                                 
      fwSS-ftp-pid  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - pid"
            ::= { fwSS-ftp 1 }   

      fwSS-ftp-proto  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - proto"
            ::= { fwSS-ftp 2 }   

      fwSS-ftp-port  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - port"
            ::= { fwSS-ftp 3 }   

      fwSS-ftp-logical-port  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - logical port"
            ::= { fwSS-ftp 4 }   

      fwSS-ftp-max-avail-socket  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - max avail socket"
            ::= { fwSS-ftp 5 }   

      fwSS-ftp-socket-in-use-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - socket in use max"
            ::= { fwSS-ftp 6 }   

      fwSS-ftp-socket-in-use-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - socket in use current"
            ::= { fwSS-ftp 7 }   

      fwSS-ftp-socket-in-use-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - socket in use count"
            ::= { fwSS-ftp 8 }   

      fwSS-ftp-sess-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - session max"
            ::= { fwSS-ftp 9 }   

      fwSS-ftp-sess-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - session current"
            ::= { fwSS-ftp 10 }   

      fwSS-ftp-sess-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - session count"
            ::= { fwSS-ftp 11 }   

      fwSS-ftp-auth-sess-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - auth session max"
            ::= { fwSS-ftp 12 }   

      fwSS-ftp-auth-sess-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - auth session current"
            ::= { fwSS-ftp 13 }   

      fwSS-ftp-auth-sess-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - auth session count"
            ::= { fwSS-ftp 14 }   

      fwSS-ftp-accepted-sess  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - accepted session"
            ::= { fwSS-ftp 15 }   

      fwSS-ftp-rejected-sess  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - rejected session"
            ::= { fwSS-ftp 16 }   

      fwSS-ftp-auth-failures  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - auth failures"
            ::= { fwSS-ftp 17 }   

      fwSS-ftp-ops-cvp-sess-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - opsec cvp session max"
            ::= { fwSS-ftp 18 }   

      fwSS-ftp-ops-cvp-sess-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - opsec cvp session current"
            ::= { fwSS-ftp 19 }   

      fwSS-ftp-ops-cvp-sess-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - opsec cvp session count"
            ::= { fwSS-ftp 20 }   

      fwSS-ftp-ops-cvp-rej-sess  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - opsec cvp rej session"
            ::= { fwSS-ftp 21 }   

      fwSS-ftp-time-stamp  OBJECT-TYPE
            SYNTAX  DisplayString (SIZE (0..255))
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - time stamp"
            ::= { fwSS-ftp 22 }   

      fwSS-ftp-is-alive  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ftp - is alive"
            ::= { fwSS-ftp 23 }   

      fwSS-ftp-blocked-cnt  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of ftp items blocked, being classified as 'infected' by Anti Virus"
            ::= { fwSS-ftp 24 }  	 

      fwSS-ftp-blocked-total  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "sum of all the counts of the previous OID (fwss-ftp-blocked-cnt)"
            ::= { fwSS-ftp 25 }  	 

      fwSS-ftp-scanned-total  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "sum of all the counts of 'blocked' (infected) and 'passed' items"
            ::= { fwSS-ftp 26 }  	 

      fwSS-ftp-blocked-by-file-type  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of ftp items blocked by 'file type' policy in Content Inspection tab in SmartDashboard"
            ::= { fwSS-ftp 27 }  	 

      fwSS-ftp-blocked-by-size-limit  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of ftp items blocked by 'size limit' policy in Content Inspection tab in SmartDashboard"
            ::= { fwSS-ftp 28 }  	 

      fwSS-ftp-blocked-by-archive-limit  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of ftp items blocked by 'archive limit' policy in Content Inspection tab in SmartDashboard"
            ::= { fwSS-ftp 29 }  	 
            
      fwSS-ftp-blocked-by-internal-error  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of ftp items blocked because of 'internal error' in Content Inspection tab in SmartDashboard"            
	  		::= { fwSS-ftp 30 }  	 
	  		        

      fwSS-ftp-passed-cnt  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of ftp items passed, being classified as 'safe' by Anti Virus"
            ::= { fwSS-ftp 31 }  	 
            
      fwSS-ftp-passed-by-file-type  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of ftp items allowed (without AV scan) by 'File Type' policy in Content Inspection tab in 
	  		         SmartDashboard"
            ::= { fwSS-ftp 32 }  	 
          
      fwSS-ftp-passed-by-size-limit  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of ftp items allowed (without AV scan) by 'Size Limit' policy in Content Inspection tab in 
	  		         SmartDashboard"
            ::= { fwSS-ftp 33 }  	 

      fwSS-ftp-passed-by-archive-limit  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of ftp items allowed (without AV scan) by 'Archive Limit' policy in Content Inspection tab in 
	  		         SmartDashboard"
            ::= { fwSS-ftp 34 }  	 

      fwSS-ftp-passed-by-internal-error  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of ftp items allowed (without AV scan) by 'internal error' in Content Inspection tab in 
	  		         SmartDashboard"
            ::= { fwSS-ftp 35 }  	 

      fwSS-ftp-passed-total  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "sum of all the above 'passed' counts - OIDs .31 to .35"
            ::= { fwSS-ftp 36 }  	 

      fwSS-ftp-blocked-by-AV-settings  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of ftp items rejected by Anti Virus as either: 
	  		        exceeding size or archive limits, or failed scan due to 
	  		        internal error (configured in the 'settings' pane in 
	  		        'Content Inspection' tab in SmartDashboard)"
            ::= { fwSS-ftp 37 }  
            	 
      fwSS-ftp-passed-by-AV-settings  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of ftp items allowed by Anti Virus as either: 
	  		        exceeding size or archive limits, or failed scan due to 
	  		        internal error (configured in the 'settings' pane in 
	  		        'Content Inspection' tab in SmartDashboard)"
            ::= { fwSS-ftp 38 }  	                                         
                                        
      fwSS-telnet-pid  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "telnet - pid"
            ::= { fwSS-telnet 1 }   

      fwSS-telnet-proto  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "telnet - proto"
            ::= { fwSS-telnet 2 }   

      fwSS-telnet-port  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "telnet - port"
            ::= { fwSS-telnet 3 }   

      fwSS-telnet-logical-port  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "telnet - logical port"
            ::= { fwSS-telnet 4 }   

      fwSS-telnet-max-avail-socket  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "telnet - max avail socket"
            ::= { fwSS-telnet 5 }   

      fwSS-telnet-socket-in-use-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "telnet - socket in use max"
            ::= { fwSS-telnet 6 }   

      fwSS-telnet-socket-in-use-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "telnet - socket in use current"
            ::= { fwSS-telnet 7 }   

      fwSS-telnet-socket-in-use-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "telnet - socket in use count"
            ::= { fwSS-telnet 8 }   

      fwSS-telnet-sess-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "telnet - session max"
            ::= { fwSS-telnet 9 }   

      fwSS-telnet-sess-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "telnet - session current"
            ::= { fwSS-telnet 10 }   

      fwSS-telnet-sess-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "telnet - session count"
            ::= { fwSS-telnet 11 }   

      fwSS-telnet-auth-sess-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "telnet - auth session max"
            ::= { fwSS-telnet 12 }   

      fwSS-telnet-auth-sess-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "telnet - auth session current"
            ::= { fwSS-telnet 13 }   

      fwSS-telnet-auth-sess-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "telnet - auth session count"
            ::= { fwSS-telnet 14 }   

      fwSS-telnet-accepted-sess  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "telnet - accepted session"
            ::= { fwSS-telnet 15 }   

      fwSS-telnet-rejected-sess  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "telnet - rejected session"
            ::= { fwSS-telnet 16 }   

      fwSS-telnet-auth-failures  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "telnet - auth failures"
            ::= { fwSS-telnet 17 }   

      fwSS-telnet-time-stamp  OBJECT-TYPE
            SYNTAX  DisplayString (SIZE (0..255))
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "telnet - time stamp"
            ::= { fwSS-telnet 18 }   

      fwSS-telnet-is-alive  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "telnet - is alive"
            ::= { fwSS-telnet 19 }   

      fwSS-rlogin-pid  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "rlogin - pid"
            ::= { fwSS-rlogin 1 }   

      fwSS-rlogin-proto  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "rlogin - proto"
            ::= { fwSS-rlogin 2 }   

      fwSS-rlogin-port  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "rlogin - port"
            ::= { fwSS-rlogin 3 }   

      fwSS-rlogin-logical-port  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "rlogin - logical port"
            ::= { fwSS-rlogin 4 }   

      fwSS-rlogin-max-avail-socket  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "rlogin - max avail socket"
            ::= { fwSS-rlogin 5 }   

      fwSS-rlogin-socket-in-use-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "rlogin - socket in use max"
            ::= { fwSS-rlogin 6 }   

      fwSS-rlogin-socket-in-use-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "rlogin - socket in use current"
            ::= { fwSS-rlogin 7 }   

      fwSS-rlogin-socket-in-use-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "rlogin - socket in use count"
            ::= { fwSS-rlogin 8 }   

      fwSS-rlogin-sess-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "rlogin - session max"
            ::= { fwSS-rlogin 9 }   

      fwSS-rlogin-sess-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "rlogin - session current"
            ::= { fwSS-rlogin 10 }   

      fwSS-rlogin-sess-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "rlogin - session count"
            ::= { fwSS-rlogin 11 }   

      fwSS-rlogin-auth-sess-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "rlogin - auth session max"
            ::= { fwSS-rlogin 12 }   

      fwSS-rlogin-auth-sess-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "rlogin - auth session current"
            ::= { fwSS-rlogin 13 }   

      fwSS-rlogin-auth-sess-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "rlogin - auth session count"
            ::= { fwSS-rlogin 14 }   

      fwSS-rlogin-accepted-sess  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "rlogin - accepted session"
            ::= { fwSS-rlogin 15 }   

      fwSS-rlogin-rejected-sess  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "rlogin - rejected session"
            ::= { fwSS-rlogin 16 }   

      fwSS-rlogin-auth-failures  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "rlogin - auth failures"
            ::= { fwSS-rlogin 17 }   

      fwSS-rlogin-time-stamp  OBJECT-TYPE
            SYNTAX  DisplayString (SIZE (0..255))
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "rlogin - time stamp"
            ::= { fwSS-rlogin 18 }   

      fwSS-rlogin-is-alive  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "rlogin - is alive"
            ::= { fwSS-rlogin 19 }   

      fwSS-ufp-ops-ufp-sess-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ufp - session max"
            ::= { fwSS-ufp 1 }   

      fwSS-ufp-ops-ufp-sess-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ufp - session current"
            ::= { fwSS-ufp 2 }   

      fwSS-ufp-ops-ufp-sess-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ufp - session count"
            ::= { fwSS-ufp 3 }  
            
      fwSS-ufp-ops-ufp-rej-sess  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ufp - rej session"
            ::= { fwSS-ufp 4 }   

      fwSS-ufp-time-stamp  OBJECT-TYPE
            SYNTAX  DisplayString (SIZE (0..255))
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ufp - time stamp"
            ::= { fwSS-ufp 5 }   

      fwSS-ufp-is-alive  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "ufp - is alive"
            ::= { fwSS-ufp 6 }   

      fwSS-smtp-pid  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - pid"
            ::= { fwSS-smtp 1 }   

      fwSS-smtp-proto  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - proto"
            ::= { fwSS-smtp 2 }   

      fwSS-smtp-port  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - port"
            ::= { fwSS-smtp 3 }   

      fwSS-smtp-logical-port  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - logical port"
            ::= { fwSS-smtp 4 }   

      fwSS-smtp-max-avail-socket  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - max avail socket"
            ::= { fwSS-smtp 5 }   

      fwSS-smtp-socket-in-use-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - socket in use max"
            ::= { fwSS-smtp 6 }   

      fwSS-smtp-socket-in-use-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - socket in use current"
            ::= { fwSS-smtp 7 }   

      fwSS-smtp-socket-in-use-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - socket in use count"
            ::= { fwSS-smtp 8 }   

      fwSS-smtp-sess-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - session max"
            ::= { fwSS-smtp 9 }   

      fwSS-smtp-sess-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - session current"
            ::= { fwSS-smtp 10 }   

      fwSS-smtp-sess-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - session count"
            ::= { fwSS-smtp 11 }   

      fwSS-smtp-auth-sess-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - auth session max"
            ::= { fwSS-smtp 12 }   

      fwSS-smtp-auth-sess-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - auth session current"
            ::= { fwSS-smtp 13 }   

      fwSS-smtp-auth-sess-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - auth session count"
            ::= { fwSS-smtp 14 }   

      fwSS-smtp-accepted-sess  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - accepted session"
            ::= { fwSS-smtp 15 }   

      fwSS-smtp-rejected-sess  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - rejected session"
            ::= { fwSS-smtp 16 }   

      fwSS-smtp-auth-failures  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - auth failures"
            ::= { fwSS-smtp 17 }   

      fwSS-smtp-mail-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - mail max"
            ::= { fwSS-smtp 18 }   

      fwSS-smtp-mail-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - mail curr"
            ::= { fwSS-smtp 19 }   

      fwSS-smtp-mail-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - mail count"
            ::= { fwSS-smtp 20 }   

      fwSS-smtp-outgoing-mail-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - outgoing mail max"
            ::= { fwSS-smtp 21 }   

      fwSS-smtp-outgoing-mail-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - outgoing mail curr"
            ::= { fwSS-smtp 22 }   

      fwSS-smtp-outgoing-mail-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - outgoing mail count"
            ::= { fwSS-smtp 23 }   

      fwSS-smtp-max-mail-on-conn  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - max mail on conn"
            ::= { fwSS-smtp 24 }   

      fwSS-smtp-total-mails  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - total mails"
            ::= { fwSS-smtp 25 }   

      fwSS-smtp-time-stamp  OBJECT-TYPE
            SYNTAX  DisplayString (SIZE (0..255))
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - time stamp"
            ::= { fwSS-smtp 26 }   

      fwSS-smtp-is-alive  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "smtp - is alive"
            ::= { fwSS-smtp 27 }   

      fwSS-smtp-blocked-cnt  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of smtp items blocked, being classified as 'infected' by Anti Virus"
            ::= { fwSS-smtp 28 }  	 

      fwSS-smtp-blocked-total  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "sum of all the counts of the previous OID (fwss-smtp-blocked-cnt)"
            ::= { fwSS-smtp 29 }  	 

      fwSS-smtp-scanned-total  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "sum of all the counts of 'blocked' (infected) and 'passed' items"
            ::= { fwSS-smtp 30 }  	 

      fwSS-smtp-blocked-by-file-type  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of smtp items blocked by 'file type' policy in Content Inspection tab in SmartDashboard"
            ::= { fwSS-smtp 31 }  	 

      fwSS-smtp-blocked-by-size-limit  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of smtp items blocked by 'size limit' policy in Content Inspection tab in SmartDashboard"
            ::= { fwSS-smtp 32 }  	 

      fwSS-smtp-blocked-by-archive-limit  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of smtp items blocked by 'archive limit' policy in Content Inspection tab in SmartDashboard"
            ::= { fwSS-smtp 33 }  	 
            
      fwSS-smtp-blocked-by-internal-error  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of smtp items blocked because of 'internal error' in Content Inspection tab in SmartDashboard"            
	  		::= { fwSS-smtp 34 }  	 
	  		        

      fwSS-smtp-passed-cnt  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of smtp items passed, being classified as 'safe' by Anti Virus"
            ::= { fwSS-smtp 35 }  	 
            
      fwSS-smtp-passed-by-file-type  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of smtp items allowed (without AV scan) by 'File Type' policy in Content Inspection tab in 
	  		         SmartDashboard"
            ::= { fwSS-smtp 36 }  	 
          
      fwSS-smtp-passed-by-size-limit  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of smtp items allowed (without AV scan) by 'Size Limit' policy in Content Inspection tab in 
	  		         SmartDashboard"
            ::= { fwSS-smtp 37 }  	 

      fwSS-smtp-passed-by-archive-limit  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of smtp items allowed (without AV scan) by 'Archive Limit' policy in Content Inspection tab in 
	  		         SmartDashboard"
            ::= { fwSS-smtp 38 }  	 

      fwSS-smtp-passed-by-internal-error  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of smtp items allowed (without AV scan) by 'internal error' in Content Inspection tab in 
	  		         SmartDashboard"
            ::= { fwSS-smtp 39 }  	 

      fwSS-smtp-passed-total  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "sum of all the above 'passed' counts - OIDs .35 to .39"
            ::= { fwSS-smtp 40 }  	 

      fwSS-smtp-blocked-by-AV-settings  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of smtp items rejected by Anti Virus as either: 
	  		        exceeding size or archive limits, or failed scan due to 
	  		        internal error (configured in the 'settings' pane in 
	  		        'Content Inspection' tab in SmartDashboard)"
            ::= { fwSS-smtp 41 }  
            	 
      fwSS-smtp-passed-by-AV-settings  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of smtp items allowed by Anti Virus as either: 
	  		        exceeding size or archive limits, or failed scan due to 
	  		        internal error (configured in the 'settings' pane in 
	  		        'Content Inspection' tab in SmartDashboard)"
            ::= { fwSS-smtp 42 }  	                                                       


      fwSS-POP3-pid  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - pid"
            ::= { fwSS-POP3 1 }   

      fwSS-POP3-proto  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - proto"
            ::= { fwSS-POP3 2 }   

      fwSS-POP3-port  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - port"
            ::= { fwSS-POP3 3 }   

      fwSS-POP3-logical-port  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - logical port"
            ::= { fwSS-POP3 4 }   

      fwSS-POP3-max-avail-socket  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - max avail socket"
            ::= { fwSS-POP3 5 }   

      fwSS-POP3-socket-in-use-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - socket in use max"
            ::= { fwSS-POP3 6 }   

      fwSS-POP3-socket-in-use-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - socket in use current"
            ::= { fwSS-POP3 7 }   

      fwSS-POP3-socket-in-use-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - socket in use count"
            ::= { fwSS-POP3 8 }   

      fwSS-POP3-sess-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - session max"
            ::= { fwSS-POP3 9 }   

      fwSS-POP3-sess-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - session current"
            ::= { fwSS-POP3 10 }   

      fwSS-POP3-sess-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - session count"
            ::= { fwSS-POP3 11 }   

      fwSS-POP3-auth-sess-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - auth session max"
            ::= { fwSS-POP3 12 }   

      fwSS-POP3-auth-sess-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - auth session current"
            ::= { fwSS-POP3 13 }   

      fwSS-POP3-auth-sess-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - auth session count"
            ::= { fwSS-POP3 14 }   

      fwSS-POP3-accepted-sess  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - accepted session"
            ::= { fwSS-POP3 15 }   

      fwSS-POP3-rejected-sess  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - rejected session"
            ::= { fwSS-POP3 16 }   

      fwSS-POP3-auth-failures  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - auth failures"
            ::= { fwSS-POP3 17 }   

      fwSS-POP3-mail-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - mail max"
            ::= { fwSS-POP3 18 }   

      fwSS-POP3-mail-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - mail curr"
            ::= { fwSS-POP3 19 }   

      fwSS-POP3-mail-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - mail count"
            ::= { fwSS-POP3 20 }   

      fwSS-POP3-outgoing-mail-max  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - outgoing mail max"
            ::= { fwSS-POP3 21 }   

      fwSS-POP3-outgoing-mail-curr  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - outgoing mail curr"
            ::= { fwSS-POP3 22 }   

      fwSS-POP3-outgoing-mail-count  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - outgoing mail count"
            ::= { fwSS-POP3 23 }   

      fwSS-POP3-max-mail-on-conn  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - max mail on conn"
            ::= { fwSS-POP3 24 }   

      fwSS-POP3-total-mails  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - total mails"
            ::= { fwSS-POP3 25 }   

      fwSS-POP3-time-stamp  OBJECT-TYPE
            SYNTAX  DisplayString (SIZE (0..255))
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - time stamp"
            ::= { fwSS-POP3 26 }   

      fwSS-POP3-is-alive  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "POP3 - is alive"
            ::= { fwSS-POP3 27 }                                 

      fwSS-POP3-blocked-cnt  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of POP3 items blocked, being classified as 'infected' by Anti Virus"
            ::= { fwSS-POP3 28 }  	 

      fwSS-POP3-blocked-total  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "sum of all the counts of the previous OID (fwss-POP3-blocked-cnt)"
            ::= { fwSS-POP3 29 }  	 

      fwSS-POP3-scanned-total  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "sum of all the counts of 'blocked' (infected) and 'passed' items"
            ::= { fwSS-POP3 30 }  	 

      fwSS-POP3-blocked-by-file-type  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of POP3 items blocked by 'file type' policy in Content Inspection tab in SmartDashboard"
            ::= { fwSS-POP3 31 }  	 

      fwSS-POP3-blocked-by-size-limit  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of POP3 items blocked by 'size limit' policy in Content Inspection tab in SmartDashboard"
            ::= { fwSS-POP3 32 }  	 

      fwSS-POP3-blocked-by-archive-limit  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of POP3 items blocked by 'archive limit' policy in Content Inspection tab in SmartDashboard"
            ::= { fwSS-POP3 33 }  	 
            
      fwSS-POP3-blocked-by-internal-error  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of POP3 items blocked because of 'internal error' in Content Inspection tab in SmartDashboard"            
	  		::= { fwSS-POP3 34 }  	 
	  		        

      fwSS-POP3-passed-cnt  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of POP3 items passed, being classified as 'safe' by Anti Virus"
            ::= { fwSS-POP3 35 }  	 
            
      fwSS-POP3-passed-by-file-type  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of POP3 items allowed (without AV scan) by 'File Type' policy in Content Inspection tab in 
	  		         SmartDashboard"
            ::= { fwSS-POP3 36 }  	 
          
      fwSS-POP3-passed-by-size-limit  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of POP3 items allowed (without AV scan) by 'Size Limit' policy in Content Inspection tab in 
	  		         SmartDashboard"
            ::= { fwSS-POP3 37 }  	 

      fwSS-POP3-passed-by-archive-limit  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of POP3 items allowed (without AV scan) by 'Archive Limit' policy in Content Inspection tab in 
	  		         SmartDashboard"
            ::= { fwSS-POP3 38 }  	 

      fwSS-POP3-passed-by-internal-error  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of POP3 items allowed (without AV scan) by 'internal error' in Content Inspection tab in 
	  		         SmartDashboard"
            ::= { fwSS-POP3 39 }  	 

      fwSS-POP3-passed-total  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "sum of all the above 'passed' counts - OIDs .35 to .39"
            ::= { fwSS-POP3 40 }  	 

      fwSS-POP3-blocked-by-AV-settings  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of POP3 items rejected by Anti Virus as either: 
	  		        exceeding size or archive limits, or failed scan due to 
	  		        internal error (configured in the 'settings' pane in 
	  		        'Content Inspection' tab in SmartDashboard)"
            ::= { fwSS-POP3 41 }  
            	 
      fwSS-POP3-passed-by-AV-settings  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "count of POP3 items allowed by Anti Virus as either: 
	  		        exceeding size or archive limits, or failed scan due to 
	  		        internal error (configured in the 'settings' pane in 
	  		        'Content Inspection' tab in SmartDashboard)"
            ::= { fwSS-POP3 42 }  	                         
                        
      fwSS-total-blocked-by-av  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "total blocked items for all services (HTTP,FTP,POP3,SMTP)"
            ::= { fwSS-av-total 1 }  	                         
      
      fwSS-total-blocked  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "total blocked items for all policies (filetype,size limit, archive limit,internal error)"
            ::= { fwSS-av-total 2 }  	                         
      
      fwSS-total-scanned  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "total scanned items for all services (HTTP,FTP,POP3,SMTP)"
            ::= { fwSS-av-total 3 }  	                         
      
      fwSS-total-blocked-by-file-type  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "total blocked items by 'File Type' policy for all services (HTTP,FTP,POP3,SMTP)"
            ::= { fwSS-av-total 4 }  	   
            
      fwSS-total-blocked-by-size-limit  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "total blocked items by 'Size Limit' policy for all services (HTTP,FTP,POP3,SMTP)"
            ::= { fwSS-av-total 5 }  	

      fwSS-total-blocked-by-archive-limit  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "total blocked items by 'Archive Limit' policy for all services (HTTP,FTP,POP3,SMTP)"
            ::= { fwSS-av-total 6 }  	
            
      fwSS-total-blocked-by-interal-error  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "total blocked items by 'Internal Error' for all services (HTTP,FTP,POP3,SMTP)"
            ::= { fwSS-av-total 7 }  	
            
       fwSS-total-passed-by-av  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "total passed items for all services (HTTP,FTP,POP3,SMTP)"
            ::= { fwSS-av-total 8 }  	
            
      fwSS-total-passed-by-file-type  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "total passed items by 'File Type' policy for all services (HTTP,FTP,POP3,SMTP)"
            ::= { fwSS-av-total 9 }  	   
            
      fwSS-total-passed-by-size-limit  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "total passed items by 'Size Limit' policy for all services (HTTP,FTP,POP3,SMTP)"
            ::= { fwSS-av-total 10 }  	

      fwSS-total-passed-by-archive-limit  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "total blocked items by 'Archive Limit' policy for all services (HTTP,FTP,POP3,SMTP)"
            ::= { fwSS-av-total 11 }  	
            
      fwSS-total-passed-by-interal-error  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "total blocked items by 'Internal Error' for all services (HTTP,FTP,POP3,SMTP)"
            ::= { fwSS-av-total 12 }
              	                                                                                   
      fwSS-total-passed  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "total passed items for all policies (filetype,size limit, archive limit,internal error)"
            ::= { fwSS-av-total 13 }
            
	  fwSS-total-blocked-by-av-settings  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "total blocked items by AV settings"
            ::= { fwSS-av-total 14 }  
            
      fwSS-total-passed-by-av-settings  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "total passed items by AV settings"
            ::= { fwSS-av-total 15 }                
                
      fwConnectionsStatConnectionsTcp  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                                "tcp connections passing through the FireWall-1 Module"
            ::= { fwConnectionsStat 1 }

      fwConnectionsStatConnectionsUdp  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                                "udp connections passing through the FireWall-1 Module"
            ::= { fwConnectionsStat 2 }
                
      fwConnectionsStatConnectionsIcmp  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                                "icmp connections passing through the FireWall-1 Module"
            ::= { fwConnectionsStat 3 }

      fwConnectionsStatConnectionsOther  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                                "other connections passing through the FireWall-1 Module"
            ::= { fwConnectionsStat 4 }      

      fwConnectionsStatConnections  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "total connections passing through the FireWall-1 Module"
            ::= { fwConnectionsStat 5 }   

      fwConnectionsStatConnectionRate  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "connection rate (per second) passing through the FireWall-1 Module"
            ::= { fwConnectionsStat 6 }   

                                                        	  
      fwHmem64-block-size	 OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - block size"
            ::= { fwHmem64 1 } 

	  fwHmem64-requested-bytes  OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - requested bytes"
            ::= { fwHmem64 2 } 

	  fwHmem64-initial-allocated-bytes  OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - initial allocated bytes"
            ::= { fwHmem64 3 } 

	  fwHmem64-initial-allocated-blocks OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - initial allocated blocks"
            ::= { fwHmem64 4 } 

	  fwHmem64-initial-allocated-pools  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - initial allocated pools"
            ::= { fwHmem64 5 } 

	  fwHmem64-current-allocated-bytes OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - current allocated bytes"
            ::= { fwHmem64 6 } 

	  fwHmem64-current-allocated-blocks OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - current allocated blocks"
            ::= { fwHmem64 7 } 

	  fwHmem64-current-allocated-pools  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - current allocated pools"
            ::= { fwHmem64 8 } 

	  fwHmem64-maximum-bytes  OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - maximum bytes"
            ::= { fwHmem64 9 } 
		
	  fwHmem64-maximum-pools  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - maximum pools"
            ::= { fwHmem64 10 } 
		
	  fwHmem64-bytes-used  OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - bytes used"
            ::= { fwHmem64 11 } 
		
	  fwHmem64-blocks-used  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - blocks used"
            ::= { fwHmem64 12 } 
		
	  fwHmem64-bytes-unused  OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - bytes unused"
            ::= { fwHmem64 13 } 
		
	  fwHmem64-blocks-unused  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - blocks unused"
            ::= { fwHmem64 14 } 
		
	  fwHmem64-bytes-peak  OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - bytes peak"
            ::= { fwHmem64 15 } 
		
	  fwHmem64-blocks-peak  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - blocks peak"
            ::= { fwHmem64 16 } 
		
	  fwHmem64-bytes-internal-use  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - bytes internal use"
            ::= { fwHmem64 17 } 

	  fwHmem64-number-of-items  OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - number of items"
            ::= { fwHmem64 18 } 
	
	  fwHmem64-alloc-operations OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - alloc operations"
            ::= { fwHmem64 19 } 
	
	  fwHmem64-free-operations OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - free operations"
            ::= { fwHmem64 20 } 
	
	  fwHmem64-failed-alloc  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - failed alloc"
            ::= { fwHmem64 21 } 
		
	  fwHmem64-failed-free  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "hmem - failed free"
            ::= { fwHmem64 22 } 
			
	  
	  fwNetIfTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  FwNetIfEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION ""  
			   ::= { fw 27 }        
			   
			   
	  fwNetIfEntry OBJECT-TYPE
	          SYNTAX  FwNetIfEntry
	          MAX-ACCESS  not-accessible
	          STATUS  current
	          DESCRIPTION ""
	          INDEX   { fwNetIfIndex }
	          ::= { fwNetIfTable 1 } 	
		
      FwNetIfEntry ::=
              SEQUENCE {                               
                 fwNetIfIndex
                      Unsigned32, 
                 fwNetIfName
                      DisplayString,
                 fwNetIfIPAddr
					  IpAddress,
				 fwNetIfNetmask
					  IpAddress,
	             fwNetIfFlags
					  Unsigned32,					  
                 fwNetIfPeerName 
                      DisplayString,
                 fwNetIfRemoteIp
                     IpAddress, 
				 fwNetIfTopology
                      Unsigned32,     
				 fwNetIfProxyName 
                      DisplayString,
				 fwNetIfSlaves 
                      DisplayString,
                 fwNetIfPorts
                 	  DisplayString,
		fwNetIfIPV6Addr 
                      OCTET STRING,
                 fwNetIfIPV6AddrLen
                 	  Unsigned32					  
              }
              
      fwNetIfIndex OBJECT-TYPE
            SYNTAX Unsigned32 
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { fwNetIfEntry 1 }   
            
      fwNetIfName OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
			DESCRIPTION
	  		        "Interface Name"            
            ::= { fwNetIfEntry 2 } 
            
      fwNetIfIPAddr OBJECT-TYPE
            SYNTAX  IpAddress 
            MAX-ACCESS read-only
            STATUS  current 
            DESCRIPTION
	  		        "IPv4 Address (an empty address will be denoted by 0.0.0.0)"
            ::= { fwNetIfEntry 3 }   
            
      fwNetIfNetmask OBJECT-TYPE
            SYNTAX  IpAddress 
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Network Mask of the GW behind the Bridge (an empty mask will be denoted by 0.0.0.0)"
            ::= { fwNetIfEntry 4 } 

	  fwNetIfFlags OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current 
            DESCRIPTION
	  		        "Flags indicating the type(s) of the device: 
	  		         Possible values include:
	  		         VPN tunnel device	1
					 Unnumbered device	2
					 Proxy device		4
					 Bridge			8
					 Bond			16
					 Slave			32 (e.g. belongs to a bond or bridge device)"
            ::= { fwNetIfEntry 5 } 

	  fwNetIfPeerName OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current    
            DESCRIPTION
	  		        "VPN Tunnel Peer Name"
            ::= { fwNetIfEntry 6 } 


      fwNetIfRemoteIp OBJECT-TYPE
            SYNTAX  IpAddress
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "VPN Tunnel Remote IP"
            ::= { fwNetIfEntry 7 } 

      fwNetIfTopology OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Planned for future use only - currently should have the constant value of 4"
            ::= { fwNetIfEntry 8 } 

      fwNetIfProxyName OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "VPN Tunnel Proxy Name"
            ::= { fwNetIfEntry 9 }           

      fwNetIfSlaves OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "If this device is acting as a master, this string contains space separated names of all its slave devices."
            ::= { fwNetIfEntry 10 }  
            
      fwNetIfPorts OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION      
            		"Given an interface which is a port in a bridge, list all its siblings interfaces. The interfaces are separated by a single space."
            ::= { fwNetIfEntry 11 }     
            
      fwNetIfIPV6Addr OBJECT-TYPE
            SYNTAX OCTET STRING
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                        " IPv6 Address given in an hexa format ."
            ::= { fwNetIfEntry 12 }

      fwNetIfIPV6AddrLen OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
                        "IPv6 Address length"
            ::= { fwNetIfEntry 13 }
            
      fwLSConn    OBJECT IDENTIFIER ::= { fw 30 }
            
	  fwLSConnOverall  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Connectivity with log servers, Ok(0), Warnning(1) and Error(2)"
            ::= { fwLSConn 1 }                
                
      fwLSConnOverallDesc  OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Description of connectivity status with log servers"
            ::= { fwLSConn 2 }       

	  fwLSConnTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  FwLSConnEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION ""  
			   ::= { fwLSConn 3 }        
	
	  fwLocalLoggingDesc  OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Description of local logging status"
            ::= { fwLSConn 4 }
	  
	  fwLocalLoggingStat  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Writing logs localy, To log servers(0), Local configured (1) Local due to connectivity(2) Local due to high rate(3)"
            ::= { fwLSConn 5 } 	

	  fwLocalLoggingWriteRate  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Local Logging Writing Rate"
            ::= { fwLSConn 6 } 

	  fwLoggingHandlingRate  OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Total Logging Handling Rate (Local writing and Sending to Log Server)"
            ::= { fwLSConn 7 } 			
			   
	  fwLSConnEntry OBJECT-TYPE
	          SYNTAX  FwLSConnEntry
	          MAX-ACCESS  not-accessible
	          STATUS  current
	          DESCRIPTION ""
	          INDEX   { fwLSConnIndex }
	          ::= { fwLSConnTable 1 } 	
		
      FwLSConnEntry ::=
              SEQUENCE {                               
                 fwLSConnIndex
                      Unsigned32,
                 fwLSConnName
                      DisplayString,
                 fwLSConnState
					  Unsigned32,
				 fwLSConnStateDesc
					  DisplayString,
				fwLSConnSendRate
                      Unsigned32
              }
              
      fwLSConnIndex OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { fwLSConnEntry 1 }   
            
      fwLSConnName OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
			DESCRIPTION
	  		        "Log Server Name\IP"            
            ::= { fwLSConnEntry 2 } 
            
      fwLSConnState OBJECT-TYPE
            SYNTAX  Unsigned32 
            MAX-ACCESS read-only
            STATUS  current 
            DESCRIPTION
	  		        "Connectivity state with the log server Ok(0), Error(1) and Not Active(2)"
            ::= { fwLSConnEntry 3 }   
            
      fwLSConnStateDesc OBJECT-TYPE
            SYNTAX  DisplayString 
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Description of connectivity state with the log server"
            ::= { fwLSConnEntry 4 }
			
      fwLSConnSendRate OBJECT-TYPE
            SYNTAX  Unsigned32 
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Logs Sending Rate to the Log Server"
            ::= { fwLSConnEntry 5 }

	fwSXLGroup OBJECT IDENTIFIER ::= { sxl 1 }

	fwSXLStatus OBJECT-TYPE
	SYNTAX INTEGER {
		disabled(0),
		enabled(1)
		}
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
		"The current status of SecureXL. "
	::= { fwSXLGroup 1 }

	fwSXLConnsExisting OBJECT-TYPE
	SYNTAX INTEGER
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
                "The number of existing connections handled by SecureXL. "
        ::= { fwSXLGroup 2 }

	fwSXLConnsAdded OBJECT-TYPE
	SYNTAX INTEGER
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
                "The number of connections added by SecureXL. "
        ::= { fwSXLGroup 3 }

	fwSXLConnsDeleted OBJECT-TYPE
	SYNTAX INTEGER
	MAX-ACCESS read-only
	STATUS current
	DESCRIPTION
                "The number of connections deleted by SecureXL. "
        ::= { fwSXLGroup 4 }


      -- the VPN status
	  -- Overall statistics and state

     cpvGeneral		    	OBJECT IDENTIFIER ::= { vpn 4 }
     cpvIpsec               OBJECT IDENTIFIER ::= { vpn 5 }
     cpvFwz                 OBJECT IDENTIFIER ::= { vpn 6 }
     cpvAccelerator         OBJECT IDENTIFIER ::= { vpn 8 }
     cpvIKE                 OBJECT IDENTIFIER ::= { vpn 9 }
     cpvIPsec               OBJECT IDENTIFIER ::= { vpn 10 }
     cpvStatistics          OBJECT IDENTIFIER ::= { cpvGeneral 1 }
     cpvErrors              OBJECT IDENTIFIER ::= { cpvGeneral 2 }
     cpvSaStatistics        OBJECT IDENTIFIER ::= { cpvIpsec 2 } 
     cpvSaErrors            OBJECT IDENTIFIER ::= { cpvIpsec 3 }
     cpvIpsecStatistics     OBJECT IDENTIFIER ::= { cpvIpsec 4 }
     cpvFwzStatistics       OBJECT IDENTIFIER ::= { cpvFwz 1 }
     cpvFwzErrors           OBJECT IDENTIFIER ::= { cpvFwz 2 } 
     cpvHwAccelGeneral      OBJECT IDENTIFIER ::= { cpvAccelerator 1 }     
     cpvHwAccelStatistics   OBJECT IDENTIFIER ::= { cpvAccelerator 2 } 
     cpvIKEglobals          OBJECT IDENTIFIER ::= { cpvIKE 1 } 
     cpvIKEerrors           OBJECT IDENTIFIER ::= { cpvIKE 2 } 
     cpvIPsecNIC            OBJECT IDENTIFIER ::= { cpvIPsec 1 } 
      
     cpvProdName OBJECT-TYPE
	        SYNTAX  DisplayString (SIZE (0..255))
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Product name"
	        ::= { vpn 1 } 

     cpvVerMajor OBJECT-TYPE
	        SYNTAX  INTEGER
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Major version"
	        ::= { vpn 2 }   
	        
	 cpvVerMinor OBJECT-TYPE
	        SYNTAX  INTEGER
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Minor version"
	        ::= { vpn 3 }

     cpvEncPackets OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Encrypted packets"
	        ::= { cpvStatistics 1 }

     cpvDecPackets OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Decrypted packets"
	        ::= { cpvStatistics 2 }

     cpvErrOut OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Encryption errors"
	        ::= { cpvErrors 1 }

     cpvErrIn OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Decryption errors"
	        ::= { cpvErrors 2 }
	        
	 cpvErrIke OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IKE errors"
	        ::= { cpvErrors 3 }        

     cpvErrPolicy OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Policy errors"
	        ::= { cpvErrors 4 }  
	        
	                 
	 cpvCurrEspSAsIn OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec current Inbound ESP SAs"
	        ::= { cpvSaStatistics 1 }                 
	
	 cpvTotalEspSAsIn OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec total Inbound ESP SAs"
	        ::= { cpvSaStatistics 2 }                  
	
	 cpvCurrEspSAsOut OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec current Outbound ESP SAs"
	        ::= { cpvSaStatistics 3 }
	        
	 cpvTotalEspSAsOut OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec total Outbound ESP SAs"
	        ::= { cpvSaStatistics 4 }

	 cpvCurrAhSAsIn OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec current Inbound AH SAs"
	        ::= { cpvSaStatistics 5 }
		
	 cpvTotalAhSAsIn OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec total Inbound AH SAs"
	        ::= { cpvSaStatistics 6 }
	        
	 cpvCurrAhSAsOut OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec current Outbound AH SAs"
	        ::= { cpvSaStatistics 7 }
		
	 cpvTotalAhSAsOut OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec total Outbound AH SAs"
	        ::= { cpvSaStatistics 8 }		
	
	 cpvMaxConncurEspSAsIn OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec max concurrent Inbound ESP SAs"
	        ::= { cpvSaStatistics 9 }		
	        
	 cpvMaxConncurEspSAsOut OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec max concurrent Outbound ESP SAs"
	        ::= { cpvSaStatistics 10 }		
	        
	 cpvMaxConncurAhSAsIn OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec max concurrent Inbound AH SAs"
	        ::= { cpvSaStatistics 11 }		
	        
	 cpvMaxConncurAhSAsOut OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec max concurrent Outbound AH SAs"
	        ::= { cpvSaStatistics 12 }	
	        
	 cpvSaDecrErr OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec decryption errors"
	        ::= { cpvSaErrors 1 }

	 cpvSaAuthErr OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec authentication errors"
	        ::= { cpvSaErrors 2 }		
			
	 cpvSaReplayErr OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec replay errors"
	        ::= { cpvSaErrors 3 }      
	 
	 cpvSaPolicyErr OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec policy errors"
	        ::= { cpvSaErrors 4 }       
	        
	 cpvSaOtherErrIn OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec other inbound errors"
	        ::= { cpvSaErrors 5 }		
		
	 cpvSaOtherErrOut OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec other outbound errors"
	        ::= { cpvSaErrors 6 }    
	        
	 cpvSaUnknownSpiErr OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec unknown SPI errors"
	        ::= { cpvSaErrors 7 }		
	
			
	 cpvIpsecUdpEspEncPkts OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec UDP ESP encrypted packets"
	        ::= { cpvIpsecStatistics 1 }
	
	 cpvIpsecUdpEspDecPkts OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec UDP ESP decrypted packets"
	        ::= { cpvIpsecStatistics 2 }

	 cpvIpsecAhEncPkts OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec AH encrypted packets"
	        ::= { cpvIpsecStatistics 3 }		
			
	 cpvIpsecAhDecPkts OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec AH decrypted packets"
	        ::= { cpvIpsecStatistics 4 }
	        		
	 cpvIpsecEspEncPkts OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec ESP encrypted packets"
	        ::= { cpvIpsecStatistics 5 }
	        		
	 cpvIpsecEspDecPkts OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec ESP decrypted packets"
	        ::= { cpvIpsecStatistics 6 }
	        		
     cpvIpsecDecomprBytesBefore OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec bytes before decompression"
	        ::= { cpvIpsecStatistics 7 }

	
	 cpvIpsecDecomprBytesAfter OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec bytes after decompression"
	        ::= { cpvIpsecStatistics 8 }		

	 cpvIpsecDecomprOverhead OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec bytes decompression overhead"
	        ::= { cpvIpsecStatistics 9 }		

	 cpvIpsecDecomprPkts OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec packets decompressed"
	        ::= { cpvIpsecStatistics 10 }
	
	 cpvIpsecDecomprErr OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec decompression errors"
	        ::= { cpvIpsecStatistics 11 }        		
		
	 cpvIpsecComprBytesBefore OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec bytes before compression"
	        ::= { cpvIpsecStatistics 12 }
	        		
     cpvIpsecComprBytesAfter OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec bytes after compression"
	        ::= { cpvIpsecStatistics 13 }
  
 	 cpvIpsecComprOverhead OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec bytes compression overhead"
	        ::= { cpvIpsecStatistics 14 }

	 cpvIpsecNonCompressibleBytes OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec bytes non compressible"
	        ::= { cpvIpsecStatistics 15 }
	
     cpvIpsecCompressiblePkts OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec packets compressed"
	        ::= { cpvIpsecStatistics 16 }

     cpvIpsecNonCompressiblePkts OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec packets non compressible"
	        ::= { cpvIpsecStatistics 17 }

	 cpvIpsecComprErrors OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec compression errors"
	        ::= { cpvIpsecStatistics 18 }		

	 cpvIpsecEspEncBytes OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec ESP encrypted bytes"
	        ::= { cpvIpsecStatistics 19 }	

	 cpvIpsecEspDecBytes OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec ESP decrypted bytes"
	        ::= { cpvIpsecStatistics 20 }	
	        
     cpvFwzEncapsEncPkts OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Fwz encapsulated encrypted packets"
	        ::= { cpvFwzStatistics 1 }
      
     cpvFwzEncapsDecPkts OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Fwz encapsulated decrypted packets"
	        ::= { cpvFwzStatistics 2 }  
	        
     cpvFwzEncPkts OBJECT-TYPE
	        SYNTAX  INTEGER
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Fwz encrypted packets"
	        ::= { cpvFwzStatistics 3 }

	 cpvFwzDecPkts OBJECT-TYPE
	        SYNTAX  INTEGER
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Fwz decrypted packets"
	        ::= { cpvFwzStatistics 4 }
	
		
	 cpvFwzEncapsEncErrs OBJECT-TYPE
	        SYNTAX DisplayString 
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Fwz encapsulated encryption errors"
	        ::= { cpvFwzErrors 1 }

     cpvFwzEncapsDecErrs OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Fwz encapsulated decryption errors"
	        ::= { cpvFwzErrors 2 }  
    
     cpvFwzEncErrs OBJECT-TYPE
	        SYNTAX  INTEGER
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Fwz encryption errors"
	        ::= { cpvFwzErrors 3 }

	 cpvFwzDecErrs OBJECT-TYPE
	        SYNTAX  INTEGER
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Fwz decryption errors"
	        ::= { cpvFwzErrors 4 }		
		    
     cpvHwAccelVendor OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "HW accel. vendor"
	        ::= { cpvHwAccelGeneral 1 }  
       
     cpvHwAccelStatus OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "HW accel. status"
	        ::= { cpvHwAccelGeneral 2 }  
       
     cpvHwAccelDriverMajorVer OBJECT-TYPE
	        SYNTAX  Unsigned32
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "HW accel. driver major version"
	        ::= { cpvHwAccelGeneral 3 }  

     cpvHwAccelDriverMinorVer OBJECT-TYPE
	        SYNTAX  Unsigned32
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "HW accel. driver minor version"
	        ::= { cpvHwAccelGeneral 4 }  
	        
     cpvHwAccelEspEncPkts OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "HW accel. encrypted IPsec ESP packets"
	        ::= { cpvHwAccelStatistics 1 }
       
     
     cpvHwAccelEspDecPkts OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "HW accel. decrypted IPsec ESP packets"
	        ::= { cpvHwAccelStatistics 2 } 
	        
	cpvHwAccelEspEncBytes OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "HW accel. encrypted IPsec ESP bytes"
	        ::= { cpvHwAccelStatistics 3 }	         
                                          
	cpvHwAccelEspDecBytes OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "HW accel. decrypted IPsec ESP bytes"
	        ::= { cpvHwAccelStatistics 4 }                                          
    
	cpvHwAccelAhEncPkts OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "HW accel. encrypted IPsec AH packets"
	        ::= { cpvHwAccelStatistics 5 }  			
		                                  
	cpvHwAccelAhDecPkts OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "HW accel. decrypted IPsec AH packets"
	        ::= { cpvHwAccelStatistics 6 } 		                                  
		
	cpvHwAccelAhEncBytes OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "HW accel. encrypted IPsec AH bytes"
	        ::= { cpvHwAccelStatistics 7 } 		
     
     
     cpvHwAccelAhDecBytes OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "HW accel. decrypted IPsec AH bytes"
	        ::= { cpvHwAccelStatistics 8 } 


     cpvIKECurrSAs OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IKE current SAs"
	        ::= { cpvIKEglobals 1 } 
	 
     cpvIKECurrInitSAs OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IKE current initiated SAs"
	        ::= { cpvIKEglobals 2 } 

     cpvIKECurrRespSAs OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IKE current responded SAs"
	        ::= { cpvIKEglobals 3 } 

     cpvIKETotalSAs OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IKE total SAs"
	        ::= { cpvIKEglobals 4 } 

     cpvIKETotalInitSAs OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IKE total initiated SAs"
	        ::= { cpvIKEglobals 5 } 

     cpvIKETotalRespSAs OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IKE total responded SAs"
	        ::= { cpvIKEglobals 6 } 

     cpvIKETotalSAsAttempts OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IKE total SAs attempts"
	        ::= { cpvIKEglobals 7 } 

     cpvIKETotalSAsInitAttempts OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IKE total SAs initiated attempts"
	        ::= { cpvIKEglobals 8 } 

     cpvIKETotalSAsRespAttempts OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IKE total SAs responded attempts"
	        ::= { cpvIKEglobals 9 } 

     cpvIKEMaxConncurSAs OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IKE max concurrent SAs"
	        ::= { cpvIKEglobals 10 } 

     cpvIKEMaxConncurInitSAs OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IKE max concurrent initiated SAs"
	        ::= { cpvIKEglobals 11 } 

     cpvIKEMaxConncurRespSAs OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IKE max concurrent responded SAs"
	        ::= { cpvIKEglobals 12 } 

     cpvIKETotalFailuresInit OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IKE total failures (initiator errors)"
	        ::= { cpvIKEerrors 1 } 

     cpvIKENoResp OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IKE no response from peer (initiator errors)"
	        ::= { cpvIKEerrors 2 } 

     cpvIKETotalFailuresResp OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IKE total failures (responder errors)"
	        ::= { cpvIKEerrors 3 } 

     cpvIPsecNICsNum OBJECT-TYPE
	        SYNTAX  Unsigned32
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec NIC: Number of IPsec NIC's"
	        ::= { cpvIPsecNIC 1 } 

     cpvIPsecNICTotalDownLoadedSAs OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec NIC: Total downloaded SA's"
	        ::= { cpvIPsecNIC 2 } 

     cpvIPsecNICCurrDownLoadedSAs OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec NIC: Current downloaded SA's"
	        ::= { cpvIPsecNIC 3 } 

     cpvIPsecNICDecrBytes OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec NIC: Decrypted bytes by NIC"
	        ::= { cpvIPsecNIC 4 } 

     cpvIPsecNICEncrBytes OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec NIC: Encrypted bytes by NIC"
	        ::= { cpvIPsecNIC 5 } 

     cpvIPsecNICDecrPackets OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec NIC: Decrypted packets by NIC"
	        ::= { cpvIPsecNIC 6 } 

     cpvIPsecNICEncrPackets OBJECT-TYPE
	        SYNTAX  DisplayString
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "IPsec NIC: Encrypted packets by NIC"
	        ::= { cpvIPsecNIC 7 } 
	        
--     cpvTnlMon OBJECT-TYPE
--	        SYNTAX  SEQUENCE OF cpvTnlMonEntry
--	        MAX-ACCESS read-only
--	        STATUS  current
--	        DESCRIPTION
--	        	   "The table of monitored tunnels"
--	        ::= { vpn 11 } 
--
--    cpvTnlMonEntry OBJECT-TYPE
--	        SYNTAX  CpvTnlMonEntry
--	        MAX-ACCESS read-only
--	        STATUS  current
--			INDEX	{ cpvTnlMonAddr }
--	        ::= { cpvTnlMon 1 } 
--
--      CpvTnlMonEntry ::=
--           SEQUENCE {
--              cpvTnlMonAddr
--                    IpAddress,
--               cpvTnlMonStatus
--                    INTEGER,
--               cpvTNlMonCurrAddr
--                    IpAddress
--            }
--
--    cpvTnlMonAddr OBJECT-TYPE
--	        SYNTAX  IpAddress
--	        MAX-ACCESS read-only
--	        STATUS  current
--	        DESCRIPTION
--	        	   "The main IP address of the peer (bogus IP for DAIP)"
--	        ::= { cpvTnlMonEntry 1 } 
--
--     cpvTnlMonStatus OBJECT-TYPE
--	        SYNTAX  INTEGER {
--				INITIAL	(0),
--				UP		(1)
--			}
--	        MAX-ACCESS read-only
--	        STATUS  current
--	        DESCRIPTION
--	        	   "The status of the peer"
--	        ::= { cpvTnlMonEntry 2 } 
--
--     cpvTnlMonCurrAddr OBJECT-TYPE
--	        SYNTAX  IpAddress
--	        MAX-ACCESS read-only
--	        STATUS  current
--	        DESCRIPTION
--	        	   "The current IP address of the peer (for DAIPs)"
--	        ::= { cpvTnlMonEntry 3 } 

	  -- the FG status
	  -- Overall statistics and state
	
	 fgProdName OBJECT-TYPE
	        SYNTAX  DisplayString (SIZE (0..255))
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Product name"
	        ::= { fg 1 }  
	        
	 fgVerMajor OBJECT-TYPE
	        SYNTAX  INTEGER
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Major version"
	        ::= { fg 2 }
	        
	 fgVerMinor OBJECT-TYPE
	        SYNTAX  INTEGER
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Minor version"
	        ::= { fg 3 }
	        
	 fgVersionString OBJECT-TYPE
	        SYNTAX  DisplayString (SIZE (0..255))
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Version string"
	        ::= { fg 4 }
	        
	 fgModuleKernelBuild OBJECT-TYPE
	        SYNTAX  INTEGER
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Kernel build"
	        ::= { fg 5 }

	 fgStrPolicyName OBJECT-TYPE
	        SYNTAX  DisplayString (SIZE (0..255))
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Policy name"
	        ::= { fg 6 }
	        
	 fgInstallTime OBJECT-TYPE
	        SYNTAX  DisplayString (SIZE (0..255))
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Install time"
	        ::= { fg 7 }
	        
	 fgNumInterfaces OBJECT-TYPE
	        SYNTAX  DisplayString (SIZE (0..255))
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Interfaces num"
	        ::= { fg 8 }
	        
	  fgIfTable OBJECT-TYPE
	        SYNTAX  SEQUENCE OF FgIfEntry
	        MAX-ACCESS  not-accessible
	        STATUS  current
	        DESCRIPTION ""
	         ::= { fg 9 }
	        
	  fgIfEntry OBJECT-TYPE
	  		SYNTAX  FgIfEntry
	  		MAX-ACCESS  not-accessible
	  		STATUS  current
	  		DESCRIPTION ""
	  		INDEX   { fgIfIndex }
	  		::= { fgIfTable 1 }     
		
      FgIfEntry ::=
      		SEQUENCE {
      			fgIfIndex		
		          	Unsigned32,
		        fgIfName
		        	DisplayString,
		        fgPolicyName
		        	DisplayString,
		        fgRateLimitIn
		          	INTEGER,
		        fgRateLimitOut
		          	INTEGER,
		        fgAvrRateIn
		          	INTEGER,
                fgAvrRateOut
                  	INTEGER,
                fgRetransPcktsIn
         		  	INTEGER, 
	            fgRetransPcktsOut
	              	INTEGER,
	            fgPendPcktsIn
	              	INTEGER,
	            fgPendPcktsOut
	              	INTEGER,
	            fgPendBytesIn
	              	INTEGER,
	            fgPendBytesOut
	              	INTEGER,
	            fgNumConnIn
	              	INTEGER,
	            fgNumConnOut
	              	INTEGER
	         } 
	         
	 fgIfIndex OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { fgIfEntry 1 } 
              
     fgIfName OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { fgIfEntry 2 }
              
     fgPolicyName OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { fgIfEntry 3 }
              
              
     fgRateLimitIn OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { fgIfEntry 4 }
              
     fgRateLimitOut OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { fgIfEntry 5 }         
     
     fgAvrRateIn OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { fgIfEntry 6 }         
                                 
     fgAvrRateOut OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { fgIfEntry 7 }
              
     fgRetransPcktsIn OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { fgIfEntry 8 }
              
     fgRetransPcktsOut OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { fgIfEntry 9 }
              
     fgPendPcktsIn OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { fgIfEntry 10 }   
              
     fgPendPcktsOut OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { fgIfEntry 11 } 
              
     fgPendBytesIn OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { fgIfEntry 12 } 
              
     fgPendBytesOut OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { fgIfEntry 13 }
              
     fgNumConnIn OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { fgIfEntry 14 }
              
     fgNumConnOut OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { fgIfEntry 15 }
                                                                  
	  -- the HA status
	  -- Overall statistics and state
	 	 
	 haProdName OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Product name"
			  ::= { ha 1 }
			  
	 haInstalled OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "HA installed"
			  ::= { ha 2 }
    
     haVerMajor OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Major version"
			  ::= { ha 3 }
			  
	 haVerMinor OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Minor version"
			  ::= { ha 4 }
			  
	 haStarted OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "HA started"
			  ::= { ha 5 } 
			  
	 haState OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "HA state"
			  ::= { ha 6 }
			  
	 haBlockState OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "HA blocking state"
			  ::= { ha 7 }
			  
	 haIdentifier OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "HA identifier"
			  ::= { ha 8 }
			  
	 haProtoVersion OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "HA protocol version"
			  ::= { ha 10 }
			  
	 haWorkMode OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Working mode"
			  ::= { ha 11 }
	  
	 haVersionSting OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Version string"
			  ::= { ha 14 }

	 haStatCode OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Status code"
			  ::= { ha 101 }
			  
	 haStatShort OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Status short"
			  ::= { ha 102 }
			  
	 haStatLong OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Status long"
			  ::= { ha 103 }
			  
	 haServicePack OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Service pack"
			  ::= { ha 999 }

	  haIfTable OBJECT-TYPE
	        SYNTAX  SEQUENCE OF HaIfEntry
	        MAX-ACCESS  not-accessible
	        STATUS  current
	        DESCRIPTION ""
	         ::= { ha 12 }
	        
	  haIfEntry OBJECT-TYPE
	  		SYNTAX  HaIfEntry
	  		MAX-ACCESS  not-accessible
	  		STATUS  current
	  		DESCRIPTION ""
	  		INDEX   { haIfIndex }
	  		::= { haIfTable 1 }     
		
      HaIfEntry ::=
      		SEQUENCE {
      			haIfIndex		
		          	Unsigned32,
		        haIfName
		        	DisplayString,
		        haIP
		          	IpAddress,
		        haStatus
		          	DisplayString,
		        haVerified
		          	Unsigned32,
                haTrusted
                  	INTEGER,
                haShared
         		  	INTEGER
	         } 
	         
	 haIfIndex OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { haIfEntry 1 } 
              
     haIfName OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { haIfEntry 2 }
              
     haIP OBJECT-TYPE
              SYNTAX  IpAddress
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { haIfEntry 3 }
              
     haStatus OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { haIfEntry 4 }         
     
     haVerified OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { haIfEntry 5 }         
                                 
     haTrusted OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { haIfEntry 6 }
              
     haShared OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { haIfEntry 7 }
              
                                                                  
	  haProblemTable OBJECT-TYPE
	        SYNTAX  SEQUENCE OF HaProblemEntry
	        MAX-ACCESS  not-accessible
	        STATUS  current
	        DESCRIPTION ""
	         ::= { ha 13 }
	        
	  haProblemEntry OBJECT-TYPE
	  		SYNTAX  HaProblemEntry
	  		MAX-ACCESS  not-accessible
	  		STATUS  current
	  		DESCRIPTION ""
	  		INDEX   { haIfIndex }
	  		::= { haProblemTable 1 }     
		
      HaProblemEntry ::=
      		SEQUENCE {
      			haProblemIndex		
		          	Unsigned32,
		        haProblemName
		        	DisplayString,
		        haProblemStatus
		          	DisplayString,
		        haProblemPriority
		          	INTEGER,
                haProblemVerified
                  	Unsigned32,
                haProblemDescr
         		  	DisplayString
	         } 
	         
	 haProblemIndex OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { haProblemEntry 1 } 
              
     haProblemName OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { haProblemEntry 2 }
                            
     haProblemStatus OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { haProblemEntry 3 }         
     
     haProblemPriority OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { haProblemEntry 4 }         
                                 
     haProblemVerified OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { haProblemEntry 5 }
              
     haProblemDescr OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { haProblemEntry 6 }
              
     haClusterIpTable OBJECT-TYPE
	      SYNTAX  SEQUENCE OF HaClusterIpEntry
	      MAX-ACCESS  not-accessible
	      STATUS  current
	      DESCRIPTION ""
	      ::= { ha 15 }
     
     haClusterIpEntry OBJECT-TYPE
	      SYNTAX  HaClusterIpEntry
	      MAX-ACCESS  not-accessible
	      STATUS  current
	      DESCRIPTION ""
	      INDEX   { haClusterIpIndex }
	      ::= { haClusterIpTable 1 }     

     HaClusterIpEntry ::=
      	      SEQUENCE {
      	            haClusterIpIndex Unsigned32,
      	            haClusterIpIfName DisplayString,
      	            haClusterIpAddr IpAddress,
      	            haClusterIpNetMask IpAddress,
                    haClusterIpMemberNet IpAddress,
                    haClusterIpMemberNetMask IpAddress
              } 

     haClusterIpIndex OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
	      DESCRIPTION
	      	"Cluster IP index"	
              ::= { haClusterIpEntry 1 } 
     
     haClusterIpIfName OBJECT-TYPE
              SYNTAX  DisplayString
	      MAX-ACCESS read-only
	      STATUS  current
	      DESCRIPTION
	        "Cluster IP interface name"
	      ::= { haClusterIpEntry 2 }

     
     haClusterIpAddr OBJECT-TYPE
              SYNTAX  IpAddress
              MAX-ACCESS read-only
              STATUS  current
	      DESCRIPTION
	      	"Cluster IP address"	
              ::= { haClusterIpEntry 3 } 
     
     haClusterIpNetMask OBJECT-TYPE
              SYNTAX  IpAddress
              MAX-ACCESS read-only
              STATUS  current
	      DESCRIPTION
	      	"Cluster network mask"	
              ::= { haClusterIpEntry 4 } 
     
     haClusterIpMemberNet OBJECT-TYPE
              SYNTAX  IpAddress
              MAX-ACCESS read-only
              STATUS  current
	      DESCRIPTION
	      	"Cluster member network"	
              ::= { haClusterIpEntry 5 } 
     
     haClusterIpMemberNetMask OBJECT-TYPE
              SYNTAX  IpAddress
              MAX-ACCESS read-only
              STATUS  current
	      DESCRIPTION
	      	"Cluster member network mask"	
              ::= { haClusterIpEntry 6 } 
     

     haClusterSyncTable OBJECT-TYPE
	      SYNTAX  SEQUENCE OF HaClusterSyncEntry
	      MAX-ACCESS  not-accessible
	      STATUS  current
	      DESCRIPTION ""
	      ::= { ha 16 }
     
     haClusterSyncEntry OBJECT-TYPE
	      SYNTAX  HaClusterSyncEntry
	      MAX-ACCESS  not-accessible
	      STATUS  current
	      DESCRIPTION ""
	      INDEX   { haClusterSyncIndex }
	      ::= { haClusterSyncTable 1 }     

     HaClusterSyncEntry ::=
      	      SEQUENCE {
      	            haClusterSyncIndex Unsigned32,
		    haClusterSyncName DisplayString,
		    haClusterSyncAddr INTEGER,
		    		haClusterSyncNetMask INTEGER
              } 

     haClusterSyncIndex OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
	      DESCRIPTION
	      	"Cluster sync index"	
              ::= { haClusterSyncEntry 1 } 
     
     haClusterSyncName OBJECT-TYPE
              SYNTAX  DisplayString
	      MAX-ACCESS read-only
	      STATUS  current
	      DESCRIPTION
	        "Cluster sync name"
	      ::= { haClusterSyncEntry 2 }

     
     haClusterSyncAddr OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
	      DESCRIPTION
	      	"Cluster sync address"	
              ::= { haClusterSyncEntry 3 } 
     
     haClusterSyncNetMask OBJECT-TYPE
              SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
	      DESCRIPTION
	      	"Cluster sync network mask"	
              ::= { haClusterSyncEntry 4 } 

	  -- the SVN status
	  -- Overall statistics and state
	        
      svnInfo        OBJECT IDENTIFIER ::= { svn 4 }
	  svnOSInfo      OBJECT IDENTIFIER ::= { svn 5 }
	  svnPerf        OBJECT IDENTIFIER ::= { svn 7 }
	  svnApplianceInfo	        OBJECT IDENTIFIER ::= { svn 16 }
	  svnMem         OBJECT IDENTIFIER ::= { svnPerf 1 }
	  svnProc        OBJECT IDENTIFIER ::= { svnPerf 2 }
	  svnDisk        OBJECT IDENTIFIER ::= { svnPerf 3 }
	  svnMem64       OBJECT IDENTIFIER ::= { svnPerf 4 }
	  svnRoutingModify OBJECT IDENTIFIER ::= { svn 9 }
          svnLogDaemon	 OBJECT IDENTIFIER ::= { svn 11 }
		  
	  svnUpdatesInfo OBJECT IDENTIFIER ::= { svn 20 }
	  svnVsxInfo OBJECT IDENTIFIER ::= { svn 21 }

     svnProdName OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
	        	   "Product name"
	        ::= { svn 1 } 

     svnProdVerMajor OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
	        	   "Major version"
	        ::= { svn 2 }   
	        
	 svnProdVerMinor OBJECT-TYPE
	        SYNTAX  INTEGER
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Minor version"
	        ::= { svn 3 }
	        
	 svnUpdatesInfoBuild OBJECT-TYPE
	  		  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Deployment agent build number"
			  ::= { svnUpdatesInfo 1 }
			  
	 svnUpdatesInfoStatus OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Deployment agent status"
			  ::= { svnUpdatesInfo 2 }
			
	 svnUpdatesInfoConnection OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Cloud connection"
			  ::= { svnUpdatesInfo 3 }
			  
	 svnUpdatesInfoAvailablePackages OBJECT-TYPE
	  		  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Available packages count"
			  ::= { svnUpdatesInfo 4 }
			  
	 svnUpdatesInfoAvailableRecommended OBJECT-TYPE
	  		  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Available recommended packages count"
			  ::= { svnUpdatesInfo 5 }
			
	 svnUpdatesInfoAvailableHotfixes OBJECT-TYPE
	  		  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Available hotfixes count"
			  ::= { svnUpdatesInfo 6 }
			
	
	   updatesInstalledTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  UpdatesInstalledEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current
  			  DESCRIPTION "Installed updates table. This feature is supported only on gaia"
			  ::= { svnUpdatesInfo 7 } 
			  
	  updatesInstalledEntry OBJECT-TYPE 
	          SYNTAX UpdatesInstalledEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current 
			  DESCRIPTION ""
			  INDEX   { updatesInstalledIndex }
			  ::= { updatesInstalledTable 1 } 
			  
	  UpdatesInstalledEntry ::= 
	          SEQUENCE {
				updatesInstalledIndex 
				      Unsigned32,
				updatesInstalledName
				      DisplayString,
				updatesInstalledType      
					  DisplayString
			  } 
	       
	  updatesInstalledIndex OBJECT-TYPE
	  		 SYNTAX  Unsigned32
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION ""
    	     ::= { updatesInstalledEntry 1 }     
    	     
	  updatesInstalledName OBJECT-TYPE
	  		 SYNTAX  DisplayString (SIZE (0..255))
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Installed package name"
	       ::= { updatesInstalledEntry 2 }   
	       
	  updatesInstalledType OBJECT-TYPE
	  		 SYNTAX  DisplayString (SIZE (0..255))
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Installed package type"
	       ::= { updatesInstalledEntry 3 } 
	
	updatesRecommendedTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  UpdatesRecommendedEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current
  			  DESCRIPTION "Recommended updates table. This feature is supported only on gaia"
			  ::= { svnUpdatesInfo 8 } 
			  
	  updatesRecommendedEntry OBJECT-TYPE 
	          SYNTAX UpdatesRecommendedEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current 
			  DESCRIPTION ""
			  INDEX   { updatesRecommendedIndex }
			  ::= { updatesRecommendedTable 1 } 
			  
	  UpdatesRecommendedEntry ::= 
	          SEQUENCE {
				updatesRecommendedIndex 
				      Unsigned32,
				updatesRecommendedName
				      DisplayString,
				updatesRecommendedType      
					  DisplayString,
				updatesRecommendedStatus
					  DisplayString
			  } 
	       
	  updatesRecommendedIndex OBJECT-TYPE
	  		 SYNTAX  Unsigned32
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION ""
    	     ::= { updatesRecommendedEntry 1 }     
    	     
	  updatesRecommendedName OBJECT-TYPE
	  		 SYNTAX  DisplayString (SIZE (0..255))
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Recommended package name"
	       ::= { updatesRecommendedEntry 2 }   
	       
	  updatesRecommendedType OBJECT-TYPE
	  		 SYNTAX  DisplayString (SIZE (0..255))
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Recommended package type"
	       ::= { updatesRecommendedEntry 3 }

	  updatesRecommendedStatus OBJECT-TYPE
	  		 SYNTAX  DisplayString (SIZE (0..255))
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Recommended package status"
	       ::= { updatesRecommendedEntry 4 }		   
			
	
	  svnVersion OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "SVN version"
			  ::= { svnInfo 1 }

	  svnBuild OBJECT-TYPE
	  		  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "SVN build number"
			  ::= { svnInfo 2 }

	  osName OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "OS name"
			  ::= { svnOSInfo 1 }

	  osMajorVer OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "OS Major version"
			  ::= { svnOSInfo 2 }

	  osMinorVer OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "OS minor version"
			  ::= { svnOSInfo 3 }

	  osBuildNum OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "OS build number"
			  ::= { svnOSInfo 4 }

	  osSPmajor OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "OS SP major"
			  ::= { svnOSInfo 5 }

	  osSPminor OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "OS SP minor"
			  ::= { svnOSInfo 6 }

	  osVersionLevel OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "OS version level"
			  ::= { svnOSInfo 7 }

	  svnApplianceSerialNumber OBJECT-TYPE
	  		  SYNTAX DisplayString (SIZE (0..255)) 
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Appliance Serial Number"
			  ::= { svnApplianceInfo 3 }

	  svnApplianceManufacturer OBJECT-TYPE
	  		  SYNTAX DisplayString (SIZE (0..255)) 
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Appliance Manufacturer"
			  ::= { svnApplianceInfo 9 }

	  svnApplianceProductName OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Appliance Product Name"
			  ::= { svnApplianceInfo 7 }

	  svnApplianceSeriesString OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Appliance Series String"
			  ::= { svnApplianceInfo 10 }
			  
	  memTotalVirtual OBJECT-TYPE
			  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Total virtual memory (32 bit)
					  NOTE: This 32 bit object has been superseded by a 64 bit object -
					  memTotalVirtual64, which is supported by Check Point SVN FP3 and above. The
					  32 bit object is still supported, but it is recommended to work with the new
					  64 bit object (avoid receiving erroneous values from 64 bit machines)."
			  ::= { svnMem 1 }

	  memActiveVirtual OBJECT-TYPE
			  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Active virtual memory (32 bit)
					  NOTE: This 32 bit object has been superseded by a 64 bit object -
					  memActiveVirtual64, which is supported by Check Point SVN FP3 and above. The
					  32 bit object is still supported, but it is recommended to work with the new
					  64 bit object (avoid receiving erroneous values from 64 bit machines)."
			  ::= { svnMem 2 }

	  memTotalReal OBJECT-TYPE
			  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Total real memory (32 bit)
					  NOTE: This 32 bit object has been superseded by a 64 bit object -
					  memTotalReal64, which is supported by Check Point SVN FP3 and above. The
					  32 bit object is still supported, but it is recommended to work with the new
					  64 bit object (avoid receiving erroneous values from 64 bit machines)."
			  ::= { svnMem 3 }

	  memActiveReal OBJECT-TYPE
			  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Active real memory (32 bit)
					  NOTE: This 32 bit object has been superseded by a 64 bit object -
					  memActiveReal64, which is supported by Check Point SVN FP3 and above. The
					  32 bit object is still supported, but it is recommended to work with the new
					  64 bit object (avoid receiving erroneous values from 64 bit machines)."
			  ::= { svnMem 4 }

	  memFreeReal OBJECT-TYPE
			  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Free real memory (32 bit)
					  NOTE: This 32 bit object has been superseded by a 64 bit object -
					  memFreeReal64, which is supported by Check Point SVN FP3 and above. The
					  32 bit object is still supported, but it is recommended to work with the new
					  64 bit object (avoid receiving erroneous values from 64 bit machines)."
			  ::= { svnMem 5 }

	  memSwapsSec OBJECT-TYPE
			  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Memory swaped pages/sec (32 bit)
					  NOTE: This 32 bit object has been superseded by a 64 bit object -
					  memSwapsSec64, which is supported by Check Point SVN FP3 and above. The
					  32 bit object is still supported, but it is recommended to work with the new
					  64 bit object (avoid receiving erroneous values from 64 bit machines)."
			  ::= { svnMem 6 }

	  memDiskTransfers OBJECT-TYPE
			  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Memory to Disk transfers/sec (32 bit)
					  NOTE: This 32 bit object has been superseded by a 64 bit object -
					  memDiskTransfers64, which is supported by Check Point SVN FP3 and above. The
					  32 bit object is still supported, but it is recommended to work with the new
					  64 bit object (avoid receiving erroneous values from 64 bit machines)."
			  ::= { svnMem 7 }

	  procUsrTime OBJECT-TYPE
			  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Processor user time"
			  ::= { svnProc 1 }

	  procSysTime OBJECT-TYPE
			  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Processor system time"
			  ::= { svnProc 2 }

	  procIdleTime OBJECT-TYPE
			  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Processor idle time"
			  ::= { svnProc 3 }

	  procUsage OBJECT-TYPE
			  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Processor usage"
			  ::= { svnProc 4 }

	  procQueue OBJECT-TYPE
			  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Processor queue length"
			  ::= { svnProc 5 }

	  procInterrupts OBJECT-TYPE
			  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Processor interrupts/sec"
			  ::= { svnProc 6 }

	  procNum OBJECT-TYPE
			  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Number of processors"
			  ::= { svnProc 7 }

	  diskTime OBJECT-TYPE
              SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Time the disk is performing io requests"
			  ::= { svnDisk 1 }

	  diskQueue OBJECT-TYPE
              SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Disk requests queue length"
			  ::= { svnDisk 2 }

	  diskPercent OBJECT-TYPE
              SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Percent of free space"
			  ::= { svnDisk 3 }

	  diskFreeTotal OBJECT-TYPE
              SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Disk total free space"
			  ::= { svnDisk 4 }

	  diskFreeAvail OBJECT-TYPE
              SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Disk available free space"
			  ::= { svnDisk 5 }

	  diskTotal OBJECT-TYPE
              SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Disk total space"
			  ::= { svnDisk 6 }

	  memTotalVirtual64 OBJECT-TYPE
			  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Total virtual memory"
			  ::= { svnMem64 1 }

	  memActiveVirtual64 OBJECT-TYPE
			  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Active virtual memory"
			  ::= { svnMem64 2 }

	  memTotalReal64 OBJECT-TYPE
			  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Total real memory"
			  ::= { svnMem64 3 }

	  memActiveReal64 OBJECT-TYPE
			  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Active real memory"
			  ::= { svnMem64 4 }

	  memFreeReal64 OBJECT-TYPE
			  SYNTAX  DisplayString (SIZE (0..255))
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Free real memory"
			  ::= { svnMem64 5 }

	  memSwapsSec64 OBJECT-TYPE
			  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Memory swaped pages/sec"
			  ::= { svnMem64 6 }

	  memDiskTransfers64 OBJECT-TYPE
			  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
					 "Memory to Disk transfers/sec"
			  ::= { svnMem64 7 }

	  multiProcTable OBJECT-TYPE
			  SYNTAX  SEQUENCE OF  MultiProcEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current
			  DESCRIPTION ""
			  ::= { svnPerf 5 }

	  vdName OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
			  ACCESS read-only
			  STATUS  optional
			  DESCRIPTION
					 "Virtual-Device name"
			  ::= { svnVsxInfo 1 }

	  vdType OBJECT-TYPE
	  		  SYNTAX  DisplayString (SIZE (0..255))
			  ACCESS read-only
			  STATUS  optional
			  DESCRIPTION
					 "Virtual-Device type"
			  ::= { svnVsxInfo 2 }

	  ctxId OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  ACCESS read-only
			  STATUS  optional
			  DESCRIPTION
					 "Virtual-Device Context ID"
			  ::= { svnVsxInfo 3 }

	  multiProcEntry OBJECT-TYPE
			  SYNTAX MultiProcEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current 
			  DESCRIPTION ""
			  INDEX   { multiProcIndex }
			  ::= { multiProcTable 1 }

	  MultiProcEntry ::=
			  SEQUENCE {
				multiProcIndex		Unsigned32,
				multiProcUserTime	Unsigned32,
				multiProcSystemTime	Unsigned32,
				multiProcIdleTime	Unsigned32,
				multiProcUsage		Unsigned32,
				multiProcRunQueue	INTEGER,
				multiProcInterrupts	Unsigned32
			  }

	  multiProcIndex OBJECT-TYPE
			  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { multiProcEntry 1 }  

	  multiProcUserTime OBJECT-TYPE
			  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { multiProcEntry 2 }  

	  multiProcSystemTime OBJECT-TYPE
			  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { multiProcEntry 3 }  

	  multiProcIdleTime OBJECT-TYPE
			  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { multiProcEntry 4 }  

	  multiProcUsage OBJECT-TYPE
			  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { multiProcEntry 5 }  

	  multiProcRunQueue OBJECT-TYPE
			  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { multiProcEntry 6 }  

	  multiProcInterrupts OBJECT-TYPE
			  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { multiProcEntry 7 }  

	  multiDiskTable OBJECT-TYPE
			  SYNTAX  SEQUENCE OF  MultiDiskEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current
			  DESCRIPTION ""
			  ::= { svnPerf 6 }

	  multiDiskEntry OBJECT-TYPE
			  SYNTAX MultiDiskEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current 
			  DESCRIPTION ""
			  INDEX   { multiDiskIndex }
			  ::= { multiDiskTable 1 }

	  MultiDiskEntry ::=
			  SEQUENCE {
				multiDiskIndex		Unsigned32,
				multiDiskName		DisplayString,
				multiDiskSize		DisplayString,
				multiDiskUsed		DisplayString,
				multiDiskFreeTotalBytes			DisplayString,
				multiDiskFreeTotalPercent		INTEGER,
				multiDiskFreeAvailableBytes		DisplayString,
				multiDiskFreeAvailablePercent	INTEGER
			  }

	  multiDiskIndex OBJECT-TYPE
			  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { multiDiskEntry 1 }  

	  multiDiskName OBJECT-TYPE
			  SYNTAX  DisplayString
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { multiDiskEntry 2 }  

	  multiDiskSize OBJECT-TYPE
			  SYNTAX  DisplayString
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { multiDiskEntry 3 }  

	  multiDiskUsed OBJECT-TYPE
			  SYNTAX  DisplayString
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { multiDiskEntry 4 }  

	  multiDiskFreeTotalBytes OBJECT-TYPE
			  SYNTAX  DisplayString
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { multiDiskEntry 5 }  

	  multiDiskFreeTotalPercent OBJECT-TYPE
			  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { multiDiskEntry 6 }  

	  multiDiskFreeAvailableBytes OBJECT-TYPE
			  SYNTAX  DisplayString
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { multiDiskEntry 7 }  

	  multiDiskFreeAvailablePercent OBJECT-TYPE
			  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { multiDiskEntry 8 }  

	  raidInfo       OBJECT IDENTIFIER ::= { svnPerf 7 }
	  sensorInfo       OBJECT IDENTIFIER ::= { svnPerf 8 }
	  powerSupplyInfo       OBJECT IDENTIFIER ::= { svnPerf 9 }
	  
	  raidVolumeTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  RaidVolumeEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION ""
			  ::= { raidInfo 1 } 
			  
	  raidVolumeEntry OBJECT-TYPE 
	          SYNTAX RaidVolumeEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current 
			  DESCRIPTION ""
			  INDEX   { raidVolumeIndex }
			  ::= { raidVolumeTable 1 } 
			  
	  RaidVolumeEntry ::= 
	          SEQUENCE {
	            raidVolumeIndex
	                  INTEGER,  
	          	raidVolumeID
	          		  INTEGER,
	          	raidVolumeType
	          		  INTEGER,
				numOfDisksOnRaid 
				      INTEGER,
				raidVolumeMaxLBA
				      INTEGER,
				raidVolumeState      
					  INTEGER,
				raidVolumeFlags  
				      INTEGER,
				raidVolumeSize  
				      Unsigned32
			  }
			  
	 raidVolumeIndex OBJECT-TYPE
	  		  SYNTAX  INTEGER (0..100)
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
	        ::= { raidVolumeEntry 1 }    
    
	  
	  raidVolumeID OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
	        	   "Raid volume identification number"
	        ::= { raidVolumeEntry 2 }  
	  
	  raidVolumeType OBJECT-TYPE
	  		 SYNTAX  INTEGER
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Raid volume type, RAID-0, RAID-1E, RAID-1, RAID_10, RAID-4, RAID-5, RAID-6, RAID-60, RAID-50 (value 0-8 respectively)"
	       ::= { raidVolumeEntry 3 }
	       
	 numOfDisksOnRaid OBJECT-TYPE
	  		 SYNTAX  INTEGER
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Number of disks on the raid"
	       ::= { raidVolumeEntry 4 }

     raidVolumeMaxLBA OBJECT-TYPE
	  		 SYNTAX  INTEGER
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Raid maximum number of Logical Block Addressing"
	       ::= { raidVolumeEntry 5 }
	        
	  raidVolumeState OBJECT-TYPE
	  		 SYNTAX  INTEGER
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Raid state: OPTIMAL(0), DEGRADED (1), FAILED(2) or UNKNOWN(other))"
	       ::= { raidVolumeEntry 6 }
	       
	  raidVolumeFlags OBJECT-TYPE
	  		 SYNTAX  INTEGER
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Raid status flags (can get more then one): ENABLED(0x01), QUIESCED(0x02), RESYNC_IN_PROGRESS(0x04),
                    VOLUME_INACTIVE(0x08), NOT_CONFIGURED(0x10), USING_INTERIM_RECOVERY_MODE(0x20),
		    READY_FOR_RECOVERY_OPERATION(0x40), WRONG_PHYSICAL_DRIVE_WAS_REPLACED(0x80),
		    A_PHYSICAL_DRIVE_IS_NOT_PROPERLY_CONNECTED(0x100), HARDWARE_IS_OVER_HEATING(0x200),
		    HARDWARE_WAS_OVERHEATED(0x400), CURRENTLY_EXPENDING(0x800), NOT_YET_AVAILABLE(0x1000),
		    QUEUED_FOR_EXPENSION(0x2000), MIGRATING(0x4000), IMPACTED(0x8000),OFFLINE(0x10000) and CLEARING(0x20000) or NONE(0x00)"
	       ::= { raidVolumeEntry 7 } 

	  raidVolumeSize OBJECT-TYPE
	  		 SYNTAX  Unsigned32
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Raid Volume Size (GB)"
	       ::= { raidVolumeEntry 8 }
	       
	  raidDiskTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  RaidDiskEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION ""
			  ::= { raidInfo 2 } 
			  
	  raidDiskEntry OBJECT-TYPE 
	          SYNTAX RaidDiskEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current 
			  DESCRIPTION ""
			  INDEX   { raidDiskIndex }
			  ::= { raidDiskTable 1 } 
			  
	  RaidDiskEntry ::= 
	          SEQUENCE {  
	          	raidDiskIndex
	          		  INTEGER,
	          	raidDiskVolumeID
	          		  INTEGER,
				raidDiskID 
				      INTEGER,
				raidDiskNumber
				      INTEGER,
				raidDiskVendor      
					  DisplayString,
				raidDiskProductID  
				      DisplayString,
				raidDiskRevision
				      DisplayString, 
				raidDiskMaxLBA
				      INTEGER,
			    raidDiskState
				      INTEGER,
				raidDiskFlags
				      INTEGER,
				raidDiskSyncState
				      INTEGER,
				raidDiskSize
				      Unsigned32
			  }   
			  
	  raidDiskIndex OBJECT-TYPE
	  		 SYNTAX  INTEGER (0..100)
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION ""
	       ::= { raidDiskEntry 1 }   
	       
	  raidDiskVolumeID OBJECT-TYPE
	  		 SYNTAX  INTEGER
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Raid volume identification"
	       ::= { raidDiskEntry 2 }

	  raidDiskID OBJECT-TYPE
	  		 SYNTAX  INTEGER
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Raid disk SCSI identification number"
	       ::= { raidDiskEntry 3 }
	
	  raidDiskNumber OBJECT-TYPE
	  		 SYNTAX  INTEGER
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Raid disk number: on Appliance 0 - upper disc, 1 - lower disc"
	       ::= { raidDiskEntry 4 }
	       
	  raidDiskVendor OBJECT-TYPE
	  		 SYNTAX  DisplayString (SIZE (0..255))
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Raid disk vendor"
	       ::= { raidDiskEntry 5 }  
	       
	  raidDiskProductID OBJECT-TYPE
	  		 SYNTAX  DisplayString (SIZE (0..255))
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Raid disk product identification"
	       ::= { raidDiskEntry 6 }

      raidDiskRevision OBJECT-TYPE
	  		 SYNTAX  DisplayString (SIZE (0..255))
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Raid disk revision level"
	       ::= { raidDiskEntry 7 }

	  raidDiskMaxLBA OBJECT-TYPE
	  		 SYNTAX  INTEGER
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Raid disk maximum number of Logical Block Addressing"
	       ::= { raidDiskEntry 8 }      
	       
	  raidDiskState OBJECT-TYPE
	  		 SYNTAX  INTEGER
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Raid disk state (can get more then one): ONLINE(0x00), MISSING(0x01), NOT_COMPATIBLE(0x02), DISC_FAILED(0x03), 
				INITIALIZING(0x04), OFFLINE_REQUESTED(0x05), FAILED_REQUESTED(0x06), UNCONFIGURED_GOOD_SPUN_UP(0x07), UNCONFIGURED_GOOD_SPUN_DOWN (0x08),
				UNCONFIGURED_BAD(0x09), HOTSPARE(0x0A), DRIVE_OFFLINE(0x0B), REBUILD(0x0C), FAILED(0x0D), COPYBACK(0x0F), 
                OTHER_OFFLINE(0xFF) or UNKNOWN (other)"
	       ::= { raidDiskEntry 9 }
	       
	  raidDiskFlags OBJECT-TYPE
	  		 SYNTAX  INTEGER
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Raid disk flags: OUT_OF_SYNC(0x01),QUIESCED(0x02),VERIFYING(0x04),READY(0x08) or NONE(other)"
	       ::= { raidDiskEntry 10 }
	       
	 raidDiskSyncState OBJECT-TYPE
	  		 SYNTAX  INTEGER
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Raid disk synchronized percent completed during the synchronization process, relevant only when
                RESYNC_IN_PROGRESS flag is on"
	       ::= { raidDiskEntry 11 }    
	       
	  raidDiskSize OBJECT-TYPE
	  		 SYNTAX  Unsigned32
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Raid disk size (GB)"
	       ::= { raidDiskEntry 12 }      

	       tempertureSensorTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  TempertureSensorEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current
  			  DESCRIPTION ""
			  ::= { sensorInfo 1 } 
			  
	  tempertureSensorEntry OBJECT-TYPE 
	          SYNTAX TempertureSensorEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current 
			  DESCRIPTION ""
			  INDEX   { tempertureSensorIndex }
			  ::= { tempertureSensorTable 1 } 
			  
	  TempertureSensorEntry ::= 
	          SEQUENCE {
				tempertureSensorIndex 
				      INTEGER,
				tempertureSensorName
				      DisplayString,
				tempertureSensorValue      
					  DisplayString,
				tempertureSensorUnit  
				      DisplayString,
				tempertureSensorType
				      DisplayString, 
				tempertureSensorStatus
				      INTEGER
			  } 
	       
	  tempertureSensorIndex OBJECT-TYPE
	  		 SYNTAX  INTEGER (0..100)
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION ""
    	     ::= { tempertureSensorEntry 1 }     
    	     
	  tempertureSensorName OBJECT-TYPE
	  		 SYNTAX  DisplayString (SIZE (0..255))
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Sensor name"
	       ::= { tempertureSensorEntry 2 }   
	       
	  tempertureSensorValue OBJECT-TYPE
	  		 SYNTAX  DisplayString (SIZE (0..255))
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Sensor value"
	       ::= { tempertureSensorEntry 3 } 
	       
	  tempertureSensorUnit OBJECT-TYPE
	  		 SYNTAX  DisplayString (SIZE (0..255))
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Sensor unit"
	       ::= { tempertureSensorEntry 4 }  
	       
	  tempertureSensorType OBJECT-TYPE
	  		 SYNTAX  DisplayString (SIZE (0..255))
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Sensor type"
	       ::= { tempertureSensorEntry 5 }
	       
	  tempertureSensorStatus OBJECT-TYPE
	  		 SYNTAX  INTEGER			 
	  		 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Sensor is out of range TRUE(1), FALSE(0), READING ERROR(2)"
	       ::= { tempertureSensorEntry 6 } 
	       
	  fanSpeedSensorTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  FanSpeedSensorEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION ""
			  ::= { sensorInfo 2 } 
			  
	  fanSpeedSensorEntry OBJECT-TYPE 
	          SYNTAX FanSpeedSensorEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current 
			  DESCRIPTION ""
			  INDEX   { fanSpeedSensorIndex }
			  ::= { fanSpeedSensorTable 1 }
			  
	  FanSpeedSensorEntry ::= 
	          SEQUENCE {
				fanSpeedSensorIndex 
				      INTEGER,
				fanSpeedSensorName
				      DisplayString,
				fanSpeedSensorValue      
					  DisplayString,
				fanSpeedSensorUnit  
				      DisplayString,
				fanSpeedSensorType
				      DisplayString, 
				fanSpeedSensorStatus
				      INTEGER
			  }  
		       
	  fanSpeedSensorIndex OBJECT-TYPE
	  		 SYNTAX  INTEGER (0..100)
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION ""
    	     ::= { fanSpeedSensorEntry 1 }     
    	     
	  fanSpeedSensorName OBJECT-TYPE
	  		 SYNTAX  DisplayString (SIZE (0..255))
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Sensor name"
	       ::= { fanSpeedSensorEntry 2 }   
	       
	  fanSpeedSensorValue OBJECT-TYPE
	  		 SYNTAX  DisplayString (SIZE (0..255))
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Sensor value"
	       ::= { fanSpeedSensorEntry 3 } 
	       
	  fanSpeedSensorUnit OBJECT-TYPE
	  		 SYNTAX  DisplayString (SIZE (0..255))
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Sensor unit"
	       ::= { fanSpeedSensorEntry 4 }  
	       
	  fanSpeedSensorType OBJECT-TYPE
	  		 SYNTAX  DisplayString (SIZE (0..255))
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Sensor type"
	       ::= { fanSpeedSensorEntry 5 }
	       
	  fanSpeedSensorStatus OBJECT-TYPE
	  		 SYNTAX  INTEGER			 
	  		 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Sensor is out of range TRUE(1), FALSE(0), READING ERROR(2)"
	       ::= { fanSpeedSensorEntry 6 }  
	       
	  voltageSensorTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  VoltageSensorEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current
			  DESCRIPTION ""  
			  ::= { sensorInfo 3 } 
			  
	  voltageSensorEntry OBJECT-TYPE 
	          SYNTAX VoltageSensorEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current
			  DESCRIPTION "" 
			  INDEX   { voltageSensorIndex }
			  ::= { voltageSensorTable 1 }       
			
	  VoltageSensorEntry ::= 
	          SEQUENCE {
				voltageSensorIndex 
				      INTEGER,
				voltageSensorName
				      DisplayString,
				voltageSensorValue      
					  DisplayString,
				voltageSensorUnit  
				      DisplayString,
				voltageSensorType
				      DisplayString, 
				voltageSensorStatus
				      INTEGER
			  }  
		       
	  voltageSensorIndex OBJECT-TYPE
	  		 SYNTAX  INTEGER (0..100)
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION ""
    	     ::= { voltageSensorEntry 1 }     
    	     
	  voltageSensorName OBJECT-TYPE
	  		 SYNTAX  DisplayString (SIZE (0..255))
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Sensor name"
	       ::= { voltageSensorEntry 2 }   
	       
	  voltageSensorValue OBJECT-TYPE
	  		 SYNTAX  DisplayString (SIZE (0..255))
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Sensor value"
	       ::= { voltageSensorEntry 3 } 
	       
	  voltageSensorUnit OBJECT-TYPE
	  		 SYNTAX  DisplayString (SIZE (0..255))
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Sensor unit"
	       ::= { voltageSensorEntry 4 }  
	       
	  voltageSensorType OBJECT-TYPE
	  		 SYNTAX  DisplayString (SIZE (0..255))
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Sensor type"
	       ::= { voltageSensorEntry 5 }
	       
	  voltageSensorStatus OBJECT-TYPE
	  		 SYNTAX  INTEGER			 
	  		 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Sensor is out of range TRUE(1), FALSE(0), READING ERROR(2)"
	       ::= { voltageSensorEntry 6 }

	  powerSupplyTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  PowerSupplyEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION ""
			  ::= { powerSupplyInfo 1 } 
			  
	  powerSupplyEntry OBJECT-TYPE 
	          SYNTAX PowerSupplyEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current 
			  DESCRIPTION ""
			  INDEX   { powerSupplyIndex }
			  ::= { powerSupplyTable 1 }       
			
	  PowerSupplyEntry ::= 
	          SEQUENCE {
				powerSupplyIndex 
				      INTEGER, 
				powerSupplyStatus
				      DisplayString
			  }  
		       
	  powerSupplyIndex OBJECT-TYPE
	  		 SYNTAX  INTEGER (0..100)
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION ""
    	     ::= { powerSupplyEntry 1 }     
    	     
	  powerSupplyStatus OBJECT-TYPE
	  		 SYNTAX  DisplayString
			 MAX-ACCESS read-only
			 STATUS  current
			 DESCRIPTION
	       	   "Power supply status"
	       ::= { powerSupplyEntry 2 }

	  routingTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  RoutingEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION ""
			  ::= { svn 6 }

	  routingEntry OBJECT-TYPE 
	          SYNTAX RoutingEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current 
			  DESCRIPTION ""
			  INDEX   { routingIndex }
			  ::= { routingTable 1 }
	                    
	  RoutingEntry ::= 
	          SEQUENCE {
				routingIndex 
				     Unsigned32, 
				routingDest
				      IpAddress,
				routingMask      
					  IpAddress,
				routingGatweway  
				      IpAddress,
				routingIntrfName
				      DisplayString 
			  }

      routingIndex OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { routingEntry 1 }  
              
      routingDest OBJECT-TYPE
      		  SYNTAX  IpAddress
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { routingEntry 2 }      
              
      routingMask OBJECT-TYPE
      		  SYNTAX  IpAddress
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { routingEntry 3 }   
              
      routingGatweway OBJECT-TYPE
      		  SYNTAX  IpAddress
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { routingEntry 4 }   
              
      routingIntrfName OBJECT-TYPE
      		  SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { routingEntry 5 }
              
      svnSysTime OBJECT-TYPE
      		  SYNTAX  Unsigned32
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "System time"
      		  ::= { svn 8 }

      svnRouteModDest OBJECT-TYPE
      		  SYNTAX  IpAddress
		  MAX-ACCESS read-only
		  STATUS  current
		  DESCRIPTION
		  	   "Destination routing modification"
		  ::= { svnRoutingModify 2 }

      svnRouteModMask OBJECT-TYPE
      		  SYNTAX  IpAddress
		  MAX-ACCESS read-only
		  STATUS  current
		  DESCRIPTION
		  	   "Subnet mask routing modification"
		  ::= { svnRoutingModify 3 }

      svnRouteModGateway OBJECT-TYPE
      		  SYNTAX  IpAddress
		  MAX-ACCESS read-only
		  STATUS  current
		  DESCRIPTION
		  	   "Gateway routing modification"
		  ::= { svnRoutingModify 4 }

      svnRouteModIfIndex OBJECT-TYPE
      		  SYNTAX  INTEGER
		  MAX-ACCESS read-only
		  STATUS  current
		  DESCRIPTION
		  	   "Interface index routing modification"
		  ::= { svnRoutingModify 5 }

      svnRouteModIfName OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
		  MAX-ACCESS read-only
		  STATUS  current
		  DESCRIPTION
		  	   "Interface name routing modification"
		  ::= { svnRoutingModify 6 }

      svnRouteModAction OBJECT-TYPE
      		  SYNTAX  INTEGER
		  MAX-ACCESS read-only
		  STATUS  current
		  DESCRIPTION
		  	   "Routing modification action"
		  ::= { svnRoutingModify 10 }

      svnUTCTimeOffset OBJECT-TYPE
      		  SYNTAX  INTEGER
		  MAX-ACCESS read-only
		  STATUS  current
		  DESCRIPTION
		  	   "UTC time offset"
	          ::= { svn 10 }	

      svnLogDStat OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
		  MAX-ACCESS read-only
		  STATUS  current
		  DESCRIPTION
		  	   "Log Daemon Status"
	          ::= { svnLogDaemon 1 }

      svnSysStartTime OBJECT-TYPE
      		  SYNTAX  Unsigned32
		  MAX-ACCESS read-only
		  STATUS  current
		  DESCRIPTION
		           "System start time"
		  ::= { svn 12 }

      svnSysUniqId OBJECT-TYPE
      		  SYNTAX  DisplayString
		  MAX-ACCESS read-only
		  STATUS  current
		  DESCRIPTION
		  	   "System unique ID"
	          ::= { svn 13 }
      
      svnWebUIPort OBJECT-TYPE
	        SYNTAX  INTEGER
	        MAX-ACCESS read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Web UI port (0 - Not supported)"
	        ::= { svn 15 }
	
		-- Platforms
	
	svnPlatformInfo	  OBJECT IDENTIFIER ::= { svn 123 }
	
	supportedPlatforms   OBJECT IDENTIFIER ::= { svnPlatformInfo 1 }
	
	checkPointUTM-1450 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"UTM-1 450"
		::= { supportedPlatforms 1 }

	checkPointUTM-11050 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"UTM-1 1050"
		::= { supportedPlatforms 2 }

	checkPointUTM-12050 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"UTM-1 2050"
		::= { supportedPlatforms 3 }

	checkPointUTM-1130 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"UTM-1 130"
		::= { supportedPlatforms 4 }

	checkPointUTM-1270 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"UTM-1 270"
		::= { supportedPlatforms 5 }

	checkPointUTM-1570 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"UTM-1 570"
		::= { supportedPlatforms 6 }

	checkPointUTM-11070 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"UTM-1 1070"
		::= { supportedPlatforms 7 }

	checkPointUTM-12070 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"UTM-1 2070"
		::= { supportedPlatforms 8 }

	checkPointUTM-13070 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"UTM-1 3070"
		::= { supportedPlatforms 9 }

	checkPointPower-15070 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Power-1 5070"
		::= { supportedPlatforms 10 }

	checkPointPower-19070 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Power-1 9070"
		::= { supportedPlatforms 11 }

	checkPointPower-111000 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Power-1 11000"
		::= { supportedPlatforms 12 }

	checkPointSmart-15 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Smart-1 5"
		::= { supportedPlatforms 13 }

	checkPointSmart-125 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Smart-1 25"
		::= { supportedPlatforms 14 }

	checkPointSmart-150 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Smart-1 50"
		::= { supportedPlatforms 15 }

	checkPointSmart-1150 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Smart-1 150"
		::= { supportedPlatforms 16 }

	checkPointIP150 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"IP150"
		::= { supportedPlatforms 17 }

	checkPointIP280 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"IP280"
		::= { supportedPlatforms 18 }

	checkPointIP290 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"IP290"
		::= { supportedPlatforms 19 }

	checkPointIP390 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"IP390"
		::= { supportedPlatforms 20 }

	checkPointIP560 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"IP560"
		::= { supportedPlatforms 21 }

	checkPointIP690 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"IP690"
		::= { supportedPlatforms 22 }

	checkPointIP1280 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"IP1280"
		::= { supportedPlatforms 23 }

	checkPointIP2450 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"IP2450"
		::= { supportedPlatforms 24 }

	checkPointUNIVERGEUnifiedWall1000 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"UNIVERGE UnifiedWall 1000"
		::= { supportedPlatforms 25 }

	checkPointUNIVERGEUnifiedWall2000 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"UNIVERGE UnifiedWall 2000"
		::= { supportedPlatforms 26 }

	checkPointUNIVERGEUnifiedWall4000 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"UNIVERGE UnifiedWall 4000"
		::= { supportedPlatforms 27 }

	checkPointUNIVERGEUnifiedWall100 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"UNIVERGE UnifiedWall 100"
		::= { supportedPlatforms 28 }

	checkPointDLP-19571 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"DLP-1 9571"
		::= { supportedPlatforms 29 }

	checkPointDLP-12571 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"DLP-1 2571"
		::= { supportedPlatforms 30 }

	checkPointIPS-12076 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"IPS-1 2076"
		::= { supportedPlatforms 31 }

	checkPointIPS-15076 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"IPS-1 5076"
		::= { supportedPlatforms 32 }

	checkPointIPS-19076 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"IPS-1 9076"
		::= { supportedPlatforms 33 }

	checkPoint2200 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 2200"
		::= { supportedPlatforms 34 }

	checkPoint4200 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 4200"
		::= { supportedPlatforms 35 }

	checkPoint4400 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 4400"
		::= { supportedPlatforms 36 }

	checkPoint4600 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 4600"
		::= { supportedPlatforms 37 }

	checkPoint4800 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 4800"
		::= { supportedPlatforms 38 }

	checkPointTE250 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point TE250"
		::= { supportedPlatforms 39 }

	checkPoint12200 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 12200"
		::= { supportedPlatforms 40 }

	checkPoint12400 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 12400"
		::= { supportedPlatforms 41 }

	checkPoint12600 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 12600"
		::= { supportedPlatforms 42 }

	checkPointTE1000 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point TE1000"
		::= { supportedPlatforms 43 }

	checkPoint13500 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 13500"
		::= { supportedPlatforms 44 }

	checkPoint21400 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 21400"
		::= { supportedPlatforms 45 }

	checkPoint21600 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 21600"
		::= { supportedPlatforms 46 }

	checkPoint21700 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 21700"
		::= { supportedPlatforms 47 }

	checkPointVMware OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"VMware"
		::= { supportedPlatforms 48 }

	checkPointOpenServer OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Open Server"
		::= { supportedPlatforms 49 }

	checkPointSmart-1205 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Smart-1 205"
		::= { supportedPlatforms 50 }

	checkPointSmart-1210 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Smart-1 210"
		::= { supportedPlatforms 51 }

	checkPointSmart-1225 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Smart-1 225"
		::= { supportedPlatforms 52 }

	checkPointSmart-13050 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Smart-1 3050"
		::= { supportedPlatforms 53 }

	checkPointSmart-13150 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Smart-1 3150"
		::= { supportedPlatforms 54 }

	checkPoint13800 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 13800"
		::= { supportedPlatforms 55 }

	checkPoint21800 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 21800"
		::= { supportedPlatforms 56 }

	checkPointTE250X OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point TE250X"
		::= { supportedPlatforms 57 }

	checkPointTE1000X OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point TE1000X"
		::= { supportedPlatforms 58 }

	checkPointTE2000X OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point TE2000X"
		::= { supportedPlatforms 59 }

	checkPointTE100X OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point TE100X"
		::= { supportedPlatforms 60 }

	checkPoint23500 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 23500"
		::= { supportedPlatforms 61 }	


	checkPoint23800 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 23800"
		::= { supportedPlatforms 62 }	

	
	checkPoint15400 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 15400"
		::= { supportedPlatforms 63 }	
	
	checkPoint15600 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 15600"
		::= { supportedPlatforms 64 }	
	
	checkPoint3200 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 3200"
		::= { supportedPlatforms 65 }	
	
	checkPoint5200 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 5200"
		::= { supportedPlatforms 66 }	
	
	checkPoint5400 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 5400"
		::= { supportedPlatforms 67 }	
	
	checkPoint5600 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 5600"
		::= { supportedPlatforms 68 }	
	
	checkPoint5800 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 5800"
		::= { supportedPlatforms 69 }	
	
	checkPoint5900 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 5900"
		::= { supportedPlatforms 70 }	

	checkPoint3100 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 3100"
		::= { supportedPlatforms 71 }

	checkPoint5100 OBJECT-TYPE
		SYNTAX  DisplayString (SIZE (0..255))
		MAX-ACCESS  read-only
		STATUS  current
		DESCRIPTION
			"Check Point 5100"
		::= { supportedPlatforms 72 }

      svnLicensing OBJECT IDENTIFIER ::= { svn 18 }
      
      licensingTable OBJECT-TYPE
			  SYNTAX  SEQUENCE OF  LicensingEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current
			  DESCRIPTION ""
			  ::= { svnLicensing 1 }

	  licensingEntry OBJECT-TYPE
			  SYNTAX LicensingEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current 
			  DESCRIPTION ""
			  INDEX   { licensingIndex }
			  ::= { licensingTable 1 }
			  
	  LicensingEntry ::=
			  SEQUENCE {
				licensingIndex			Unsigned32,
				licensingID			Unsigned32,
				licensingBladeGUIOrder		Unsigned32,
				licensingBladeName		DisplayString,
				licensingState			DisplayString,
				licensingExpirationDate		Unsigned32,
				licensingImpact			DisplayString,
				licensingBladeActive		INTEGER,
				licensingTotalQuota		INTEGER,
				licensingUsedQuota		INTEGER
			  }

	  licensingIndex OBJECT-TYPE
			  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { licensingEntry 1 }  

	  licensingID OBJECT-TYPE
			  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { licensingEntry 2 } 
		
	  licensingBladeGUIOrder OBJECT-TYPE
		  	  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { licensingEntry 3 } 
					  	  	
	  licensingBladeName OBJECT-TYPE
			  SYNTAX  DisplayString
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { licensingEntry 4 }  

	  licensingState OBJECT-TYPE
			  SYNTAX  DisplayString
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { licensingEntry 5 }  

	  licensingExpirationDate OBJECT-TYPE
			  SYNTAX  Unsigned32
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { licensingEntry 6 }  

	  licensingImpact OBJECT-TYPE
			  SYNTAX  DisplayString
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { licensingEntry 7 }  

	  licensingBladeActive OBJECT-TYPE
			  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { licensingEntry 8 }
			  
	  licensingTotalQuota OBJECT-TYPE
			  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { licensingEntry 9 }
			  
	  licensingUsedQuota OBJECT-TYPE
			  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { licensingEntry 10 }
			  
	  licensingAssetInfo        OBJECT IDENTIFIER ::= { svnLicensing 2 }
	  
	  licensingAssetAccountId OBJECT-TYPE
			  SYNTAX  DisplayString
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { licensingAssetInfo 1 } 
			  
	  licensingAssetPackageDescription OBJECT-TYPE
			  SYNTAX  DisplayString
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { licensingAssetInfo 2 } 
	
	  licensingAssetContainerCK OBJECT-TYPE
			  SYNTAX  DisplayString
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { licensingAssetInfo 3 } 
			
	  licensingAssetCKSignature OBJECT-TYPE
			  SYNTAX  DisplayString
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { licensingAssetInfo 4 } 
			    
	  licensingAssetContainerSKU OBJECT-TYPE
			  SYNTAX  DisplayString
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { licensingAssetInfo 5 } 
			  
	  licensingAssetSupportLevel OBJECT-TYPE
		 	  SYNTAX  DisplayString
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { licensingAssetInfo 6 } 
			  
	  licensingAssetSupportExpiration OBJECT-TYPE
			  SYNTAX  DisplayString
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { licensingAssetInfo 7 } 
			  
	  licensingAssetActivationStatus OBJECT-TYPE
			  SYNTAX  DisplayString
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION ""
			  ::= { licensingAssetInfo 8 } 	  
			  
	  svnConnectivity OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS read-only
			  STATUS  current
			  DESCRIPTION
	        	   "User Center connectivity"
			  ::= { svn 19 }
              
       -- the svn interface table and stats
       
      svnNetStat	  OBJECT IDENTIFIER ::= { svn 50 } 	
                
	  svnNetIfTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  SvnNetIfTableEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION ""  
			  ::= { svnNetStat 1 }

	  svnNetIfTableEntry OBJECT-TYPE 
	          SYNTAX SvnNetIfTableEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current 
			  DESCRIPTION ""
			  INDEX   { svnNetIfIndex }
			  ::= { svnNetIfTable 1 }
               
	  SvnNetIfTableEntry ::= 
	          SEQUENCE {
				svnNetIfIndex 
				      Unsigned32,
				svnNetIfVsid
				      Unsigned32,
				svnNetIfName      
					  DisplayString,
				svnNetIfAddress  
				      IpAddress,
				svnNetIfMask
				      IpAddress,
				svnNetIfMTU
				      Unsigned32,
				svnNetIfState
				      INTEGER,
				svnNetIfMAC
				      DisplayString,
				svnNetIfDescription
				      DisplayString, 
				svnNetIfOperState
				      INTEGER,
				svnNetIfRXBytes
				      DisplayString,
				svnNetIfRXDrops
				      DisplayString,
				svnNetIfRXErrors
				      DisplayString,
				svnNetIfRXPackets
				      DisplayString,
				svnNetIfTXBytes
				      DisplayString,
				svnNetIfTXDrops
				      DisplayString,
				svnNetIfTXErrors
				      DisplayString,
				svnNetIfTXPackets
				      DisplayString
			  }

      svnNetIfIndex OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { svnNetIfTableEntry 1 }  
              
      svnNetIfVsid OBJECT-TYPE
      		  SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { svnNetIfTableEntry 2 }      
              
      svnNetIfName OBJECT-TYPE
      		  SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { svnNetIfTableEntry 3 }   
              
      svnNetIfAddress OBJECT-TYPE
      		  SYNTAX  IpAddress
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { svnNetIfTableEntry 4 }   
              
      svnNetIfMask OBJECT-TYPE
      		  SYNTAX  IpAddress
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { svnNetIfTableEntry 5 }
              
       svnNetIfMTU OBJECT-TYPE
      		  SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { svnNetIfTableEntry 6 }
      
       svnNetIfState OBJECT-TYPE
      		  SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { svnNetIfTableEntry 7 }
      
       svnNetIfMAC OBJECT-TYPE
      		  SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { svnNetIfTableEntry 8 }
       
        svnNetIfDescription OBJECT-TYPE
      		  SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { svnNetIfTableEntry 9 }		
              
       svnNetIfOperState OBJECT-TYPE
      		  SYNTAX  INTEGER
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { svnNetIfTableEntry 10 }
			  
		svnNetIfRXBytes OBJECT-TYPE
			SYNTAX  DisplayString (SIZE (0..255))
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { svnNetIfTableEntry 13 }

		svnNetIfRXDrops OBJECT-TYPE
			SYNTAX  DisplayString (SIZE (0..255))
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { svnNetIfTableEntry 14 }

		svnNetIfRXErrors OBJECT-TYPE
			SYNTAX  DisplayString (SIZE (0..255))
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { svnNetIfTableEntry 15 }

		svnNetIfRXPackets OBJECT-TYPE
			SYNTAX  DisplayString (SIZE (0..255))
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { svnNetIfTableEntry 16 }

		svnNetIfTXBytes OBJECT-TYPE
			SYNTAX  DisplayString (SIZE (0..255))
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { svnNetIfTableEntry 17 }

		svnNetIfTXDrops OBJECT-TYPE
			SYNTAX  DisplayString (SIZE (0..255))
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { svnNetIfTableEntry 18 }

		svnNetIfTXErrors OBJECT-TYPE
			SYNTAX  DisplayString (SIZE (0..255))
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { svnNetIfTableEntry 19 }

		svnNetIfTXPackets OBJECT-TYPE
			SYNTAX  DisplayString (SIZE (0..255))
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { svnNetIfTableEntry 20 }
      
      vsRoutingTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  VSRoutingEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION ""
			  ::= { svn 51 }

      vsRoutingEntry OBJECT-TYPE 
	          SYNTAX VSRoutingEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current 
			  DESCRIPTION ""
			  INDEX   { vsRoutingIndex }
			  ::= { vsRoutingTable 1 }
	                    
      VSRoutingEntry ::= 
	          SEQUENCE {
				vsRoutingIndex 
				      Unsigned32,
				vsRoutingDest
				      IpAddress,
				vsRoutingMask      
				      IpAddress,
				vsRoutingGateway  
				      IpAddress,
				vsRoutingIntrfName
				      DisplayString,
				vsRoutingVsId
				      Unsigned32
			  }

      vsRoutingIndex OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { vsRoutingEntry 1 }  
              
      vsRoutingDest OBJECT-TYPE
      	      SYNTAX  IpAddress
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { vsRoutingEntry 2 }      

      vsRoutingMask OBJECT-TYPE
      	      SYNTAX  IpAddress
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { vsRoutingEntry 3 }      

      vsRoutingGateway OBJECT-TYPE
      	      SYNTAX  IpAddress
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { vsRoutingEntry 4 }      

      vsRoutingIntrfName OBJECT-TYPE
      	      SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { vsRoutingEntry 5 }      

      vsRoutingVsId OBJECT-TYPE
      	      SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { vsRoutingEntry 6 }      

      svnStatCode OBJECT-TYPE
      		  SYNTAX  Unsigned32
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Status code"
      		  ::= { svn 101 }
 
      svnStatShortDescr OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Status short description"
      		  ::= { svn 102 }
      		  
      svnStatLongDescr OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Status long description"
      		  ::= { svn 103 }
      		  
      svnServicePack OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "SVN service pack"
      		  ::= { svn 999 }
      		  
      -- the Managment status
	  -- Overall statistics and state
	       
      mgProdName OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Product name"
      		  ::= { mngmt 1 }
      
      mgVerMajor OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Major version"
      		  ::= { mngmt 2 }
      		  
      mgVerMinor OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Minor version"
      		  ::= { mngmt 3 }
      		  
      mgBuildNumber OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Build number"
      		  ::= { mngmt 4 }
      		  
      mgActiveStatus OBJECT-TYPE
      		  SYNTAX  DisplayString
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Active status"
      		  ::= { mngmt 5 }     
      		  
      mgFwmIsAlive OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Managment is alive"
      		  ::= { mngmt 6 }

	  mgConnectedClientsTable OBJECT-TYPE	  
              SYNTAX  SEQUENCE OF  MgConnectedClientsEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION ""
			  ::= { mngmt 7 } 
			  
      mgMgmtHAJournals OBJECT-TYPE
      		  SYNTAX  DisplayString
      		  MAX-ACCESS read-only
			  STATUS  current  
      		  DESCRIPTION
	        	   "Management HA synchronization journals"
      		  ::= { mngmt 9 }

      mgIsLicenseViolation OBJECT-TYPE
      		  SYNTAX INTEGER
      		  MAX-ACCESS read-only
			  STATUS  current  
	          DESCRIPTION
	        	   "License violation detected"
      		  ::= { mngmt 10 }

      mgLicenseViolationMsg OBJECT-TYPE
      		  SYNTAX  DisplayString
      		  MAX-ACCESS read-only
			  STATUS  current  
	          DESCRIPTION
	        	   "License violation detected"
      		  ::= { mngmt 11 }     

	  mgConnectedClientsEntry OBJECT-TYPE 
	          SYNTAX MgConnectedClientsEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current 
			  DESCRIPTION ""
			  INDEX   { mgIndex }
			  ::= { mgConnectedClientsTable 1 }     
			  
	  MgConnectedClientsEntry ::= 
	          SEQUENCE {
				mgIndex 
				      Unsigned32,
				mgClientName
				      DisplayString,
				mgClientHost      
					  DisplayString,
				mgClientDbLock  
				      DisplayString,
				mgApplicationType
				      DisplayString 
			  }

      mgIndex OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { mgConnectedClientsEntry 1 } 
              
      mgClientName OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { mgConnectedClientsEntry 2 } 
              
      mgClientHost OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { mgConnectedClientsEntry 3 }
              
      mgClientDbLock OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { mgConnectedClientsEntry 4 } 
              
      mgApplicationType OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { mgConnectedClientsEntry 5 }   
	
	  mgLogServerInfo  OBJECT IDENTIFIER ::= { mngmt 14 }
	  
	  mgLSLogReceiveRate OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "MGMT Log Server Log Receive Rate"
			  ::= { mgLogServerInfo 1 }
			  
	  mgLSLogReceiveRatePeak OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "MGMT Log Server Log Receive Rate Peak"
			  ::= { mgLogServerInfo 2 } 	

	  mgLSLogReceiveRate10Min OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "MGMT Log Server Log Receive Rate (Last 10 Minutes)"
			  ::= { mgLogServerInfo 3 }
			  
	  mgConnectedGatewaysTable OBJECT-TYPE	  
              SYNTAX  SEQUENCE OF  MgLsConnectedGatewaysEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Connected Gateways Table"
			  ::= { mgLogServerInfo 4 }
			  
	  mgConnectedGatewaysEntry OBJECT-TYPE 
	          SYNTAX MgLsConnectedGatewaysEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current 
			  DESCRIPTION ""
			  INDEX   { mglsGWIndex }
			  ::= { mgConnectedGatewaysTable 1 } 

	  MgLsConnectedGatewaysEntry ::= 
	          SEQUENCE {
				mglsGWIndex 
				      Unsigned32,
				mglsGWIP
				      DisplayString,
				mglsGWState      
					  DisplayString,
				mglsGWLastLoginTime  
				      DisplayString,
				mglsGWLogReceiveRate
				      Unsigned32 
			  }

      mglsGWIndex OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { mgConnectedGatewaysEntry 1 } 
              
      mglsGWIP OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { mgConnectedGatewaysEntry 2 } 
              
      mglsGWState OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { mgConnectedGatewaysEntry 3 }
              
      mglsGWLastLoginTime OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { mgConnectedGatewaysEntry 4 } 
              
      mglsGWLogReceiveRate OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { mgConnectedGatewaysEntry 5 }  			  
              
	  mgIndexerInfo  OBJECT IDENTIFIER ::= { mgLogServerInfo 5 }
	  
	  mgIndexerInfoTotalReadLogs OBJECT-TYPE	  
              SYNTAX  DisplayString
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Total Read Logs"
			  ::= { mgIndexerInfo 1 }			  

	  mgIndexerInfoTotalUpdatesAndLogsIndexed OBJECT-TYPE	  
              SYNTAX  DisplayString
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Total Updates and Logs Indexed"
			  ::= { mgIndexerInfo 2 }
			  
	  mgIndexerInfoTotalReadLogsErrors OBJECT-TYPE	  
              SYNTAX  DisplayString
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Total Read Logs Errors"
			  ::= { mgIndexerInfo 3 }

	  mgIndexerInfoTotalUpdatesAndLogsIndexedErrors OBJECT-TYPE	  
              SYNTAX  DisplayString
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Total Updates and Logs Indexed Errors"
			  ::= { mgIndexerInfo 4 }

	  mgIndexerInfoUpdatesAndLogsIndexedRate OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Updates and Logs Indexed Rate"
			  ::= { mgIndexerInfo 5 }

	  mgIndexerInfoReadLogsRate OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Read Logs Rate"
			  ::= { mgIndexerInfo 6 }

	  mgIndexerInfoUpdatesAndLogsIndexedRate10min OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Updates and Logs Indexed Rate (10 min)"
			  ::= { mgIndexerInfo 7 }

	  mgIndexerInfoReadLogsRate10min OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Read Logs Rate  (10 min)"
			  ::= { mgIndexerInfo 8 }

	  mgIndexerInfoUpdatesAndLogsIndexedRate60min OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Updates and Logs Indexed Rate (60 min)"
			  ::= { mgIndexerInfo 9 }

	  mgIndexerInfoReadLogsRate60min OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Read Logs Rate (60 min)"
			  ::= { mgIndexerInfo 10 }


	  mgIndexerInfoUpdatesAndLogsIndexedRatePeak OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Updates and Logs Indexed Rate Peak"
			  ::= { mgIndexerInfo 11 }

	  mgIndexerInfoReadLogsRatePeak OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Read Logs Rate Peak"
			  ::= { mgIndexerInfo 12 }			  
			  
	  mgIndexerInfoReadLogsDelay OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Read Logs Delay"
			  ::= { mgIndexerInfo 13 }			  
			  
      mgStatCode OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Status code"
      		  ::= { mngmt 101 }
 
      mgStatShortDescr OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Status short description"
      		  ::= { mngmt 102 }
      		  
      mgStatLongDescr OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Status long description"
      		  ::= { mngmt 103 }
      		  
	  mgLSLogReceiveRate1Hour OBJECT-TYPE	  
		  SYNTAX  Unsigned32
		  MAX-ACCESS  not-accessible
		  STATUS  current  
		  DESCRIPTION "MGMT Log Server Log Receive Rate (Last Hour)"
		  ::= { mgLogServerInfo 6 }
			  
      -- the WAM Status
	  -- Overall statistics and state
	       
      wamPluginPerformance   OBJECT IDENTIFIER ::= { wam 6 }
	  wamPolicy              OBJECT IDENTIFIER ::= { wam 7 }
	  wamUagQueries          OBJECT IDENTIFIER ::= { wam 8 }
	  wamGlobalPerformance   OBJECT IDENTIFIER ::= { wam 9 }
	  
      wamProdName OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Product name"
      		  ::= { wam 1 }
      
      wamVerMajor OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Major version"
      		  ::= { wam 2 }
      		  
      wamVerMinor OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Minor version"
      		  ::= { wam 3 }
      		  
      wamState OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "WAM state"
      		  ::= { wam 4 }

      wamName OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "WAM name"
      		  ::= { wam 5 }

      wamStatCode OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "WAM status code"
      		  ::= { wam 101 }
 
      wamStatShortDescr OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "WAM status short description"
      		  ::= { wam 102 }
      		  
      wamStatLongDescr OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "WAM status full description"
      		  ::= { wam 103 }

      wamAcceptReq OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Plugin accepted requests"
      		  ::= { wamPluginPerformance 1 }
      		  
      wamRejectReq OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Plugin rejected requests"
      		  ::= { wamPluginPerformance 2 }

      wamPolicyName OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Policy name"
      		  ::= { wamPolicy 1 }

      wamPolicyUpdate OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Last update time"
      		  ::= { wamPolicy 2 }

      wamUagHost OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "UAG host name"
      		  ::= { wamUagQueries 1 }
      		  
      wamUagIp OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "UAG IP address"
      		  ::= { wamUagQueries 2 }

      wamUagPort OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "UAG port"
      		  ::= { wamUagQueries 3 }

      wamUagNoQueries OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "UAG no. of queries"
      		  ::= { wamUagQueries 4 }

      wamUagLastQuery OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "UAG last query time"
      		  ::= { wamUagQueries 5 }

      wamOpenSessions OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Open sessions"
      		  ::= { wamGlobalPerformance 1 }

      wamLastSession OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Last open session time"
      		  ::= { wamGlobalPerformance 2 }


      -- the Desktop Policy Server Status
	  -- Overall statistics and state
	       	  
      dtpsProdName OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Product name"
      		  ::= { dtps 1 }
      
      dtpsVerMajor OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Major version"
      		  ::= { dtps 2 }
      		  
      dtpsVerMinor OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Minor version"
      		  ::= { dtps 3 }
      		  
      dtpsLicensedUsers OBJECT-TYPE
      		  SYNTAX  Unsigned32
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Licensed users"
      		  ::= { dtps 4 }

      dtpsConnectedUsers OBJECT-TYPE
      		  SYNTAX  Unsigned32
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Connected users"
      		  ::= { dtps 5 }

      dtpsStatCode OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Status code"
      		  ::= { dtps 101 }
 
      dtpsStatShortDescr OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Status short description"
      		  ::= { dtps 102 }
      		  
      dtpsStatLongDescr OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Status long description"
      		  ::= { dtps 103 }

      -- the Log Server Status
	  -- Overall statistics and state
	       
      lsProdName OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Product name"
      		  ::= { ls 1 }
      
      lsVerMajor OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Major version"
      		  ::= { ls 2 }
      		  
      lsVerMinor OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Minor version"
      		  ::= { ls 3 }
      		  
      lsBuildNumber OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Build number"
      		  ::= { ls 4 }
      		        		  
      lsFwmIsAlive OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Log Server is alive"
      		  ::= { ls 5 }
			  
	  lsLoggingInfo  OBJECT IDENTIFIER ::= { ls 14 }
	  
	  lsLogReceiveRate OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Log Server Log Receive Rate"
			  ::= { lsLoggingInfo 1 }
			  
	  lsLogReceiveRatePeak OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Log Server Log Receive Rate Peak"
			  ::= { lsLoggingInfo 2 } 	

	  lsLogReceiveRate10Min OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Log Server Log Receive Rate (Last 10 Minutes)"
			  ::= { lsLoggingInfo 3 } 			  
			  
	  lsConnectedGatewaysTable OBJECT-TYPE	  
              SYNTAX  SEQUENCE OF  LsConnectedGatewaysEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Connected Gateways Table"
			  ::= { lsLoggingInfo 4 }
			  
	  lsConnectedGatewaysEntry OBJECT-TYPE 
	          SYNTAX LsConnectedGatewaysEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current 
			  DESCRIPTION ""
			  INDEX   { lsGWIndex }
			  ::= { lsConnectedGatewaysTable 1 } 
			  
	  LsConnectedGatewaysEntry ::= 
	          SEQUENCE {
				lsGWIndex 
				      Unsigned32,
				lsGWIP
				      DisplayString,
				lsGWState      
					  DisplayString,
				lsGWLastLoginTime  
				      DisplayString,
				lsGWLogReceiveRate
				      Unsigned32 
			  }
			  
      lsGWIndex OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { lsConnectedGatewaysEntry 1 } 
              
      lsGWIP OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { lsConnectedGatewaysEntry 2 } 
              
      lsGWState OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { lsConnectedGatewaysEntry 3 }
              
      lsGWLastLoginTime OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { lsConnectedGatewaysEntry 4 } 
              
      lsGWLogReceiveRate OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { lsConnectedGatewaysEntry 5 }  

	  lsIndexerInfo  OBJECT IDENTIFIER ::= { lsLoggingInfo 5 }
	  
	  lsIndexerInfoTotalReadLogs OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Total Read Logs"
			  ::= { lsIndexerInfo 1 }			  

	  lsIndexerInfoTotalUpdatesAndLogsIndexed OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Total Updates and Logs Indexed"
			  ::= { lsIndexerInfo 2 }
			  
	  lsIndexerInfoTotalReadLogsErrors OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Total Read Logs Errors"
			  ::= { lsIndexerInfo 3 }

	  lsIndexerInfoTotalUpdatesAndLogsIndexedErrors OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Total Updates and Logs Indexed Errors"
			  ::= { lsIndexerInfo 4 }

	  lsIndexerInfoUpdatesAndLogsIndexedRate OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Updates and Logs Indexed Rate"
			  ::= { lsIndexerInfo 5 }

	  lsIndexerInfoReadLogsRate OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Read Logs Rate"
			  ::= { lsIndexerInfo 6 }

	  lsIndexerInfoUpdatesAndLogsIndexedRatePeak OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Updates and Logs Indexed Rate Peak"
			  ::= { lsIndexerInfo 7 }

	  lsIndexerInfoReadLogsRatePeak OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Read Logs Rate Peak"
			  ::= { lsIndexerInfo 8 }
			  
	  lsLogReceiveRate1Hour OBJECT-TYPE	  
              SYNTAX  Unsigned32
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION "Log Server Log Receive Rate (Last Hour)"
			  ::= { lsLoggingInfo 6 } 			  
			  
      lsStatCode OBJECT-TYPE
      		  SYNTAX  INTEGER
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Status code"
      		  ::= { ls 101 }
 
      lsStatShortDescr OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Status short description"
      		  ::= { ls 102 }
      		  
      lsStatLongDescr OBJECT-TYPE
      		  SYNTAX  DisplayString (SIZE (0..255))
      		  MAX-ACCESS read-only 
      		  STATUS  current
	          DESCRIPTION
	        	   "Status long description"
      		  ::= { ls 103 }
      		  
	  lsConnectedClientsTable OBJECT-TYPE	  
              SYNTAX  SEQUENCE OF  LSConnectedClientsEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION ""
			  ::= { ls 7 } 
			  
	  lsConnectedClientsEntry OBJECT-TYPE 
	          SYNTAX LSConnectedClientsEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current 
			  DESCRIPTION ""
			  INDEX   { lsIndex }
			  ::= { lsConnectedClientsTable 1 }     
			  
	  LSConnectedClientsEntry ::= 
	          SEQUENCE {
				lsIndex 
				      Unsigned32,
				lsClientName
				      DisplayString,
				lsClientHost      
					  DisplayString,
				lsClientDbLock  
				      DisplayString,
				lsApplicationType
				      DisplayString 
			  }

      lsIndex OBJECT-TYPE
              SYNTAX  Unsigned32
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { lsConnectedClientsEntry 1 } 
              
      lsClientName OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { lsConnectedClientsEntry 2 } 
              
      lsClientHost OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { lsConnectedClientsEntry 3 }
              
      lsClientDbLock OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { lsConnectedClientsEntry 4 } 
              
      lsApplicationType OBJECT-TYPE
              SYNTAX  DisplayString
              MAX-ACCESS read-only
              STATUS  current
              DESCRIPTION ""
              ::= { lsConnectedClientsEntry 5 }     
--               
			  -- the GX status
	  -- Overall statistics and state
	        
      gxInfo            OBJECT IDENTIFIER ::= { gx 1 }
	  gxCreateInfo      OBJECT IDENTIFIER ::= { gx 5 }
	  gxDeleteInfo      OBJECT IDENTIFIER ::= { gx 6 }
	  gxUpdateInfo      OBJECT IDENTIFIER ::= { gx 7 }
	  gxPathMngInfo     OBJECT IDENTIFIER ::= { gx 8 }
	  gxGpduInfo        OBJECT IDENTIFIER ::= { gx 9 }
	  gxInitiateInfo    OBJECT IDENTIFIER ::= { gx 10 }
	  gxGTPv2CreateInfo OBJECT IDENTIFIER ::= { gx 11 }
	  gxGTPv2DeleteInfo OBJECT IDENTIFIER ::= { gx 12 }
	  gxGTPv2UpdateInfo OBJECT IDENTIFIER ::= { gx 13 }
	  gxGTPv2PathMngInfo OBJECT IDENTIFIER ::= { gx 14 }
	  gxGTPv2CmdInfo OBJECT IDENTIFIER ::= { gx 15 }
	  

	  gxProdName OBJECT-TYPE
	        SYNTAX  DisplayString (SIZE (0..255))
	        MAX-ACCESS  read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Product name"
	        ::= { gxInfo 1 } 

	  gxProdVersion OBJECT-TYPE
	        SYNTAX  DisplayString (SIZE (0..255))
	        MAX-ACCESS  read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Product Version"
	        ::= { gxInfo 2 }   

	  gxProdVerMajor OBJECT-TYPE
	        SYNTAX  INTEGER
	        MAX-ACCESS  read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Major version"
	        ::= { gx 2 }   

	  gxProdVerMinor OBJECT-TYPE
	        SYNTAX  INTEGER
	        MAX-ACCESS  read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Minor version"
	        ::= { gx 3 } 
			
	  gxBuild OBJECT-TYPE
	        SYNTAX  INTEGER
	        MAX-ACCESS  read-only
	        STATUS  current
	        DESCRIPTION
	        	   "GX build number"
	        ::= { gx 4 }     

	  gxCreateSinceInstall OBJECT-TYPE
	        SYNTAX  INTEGER
	        MAX-ACCESS  read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Total success create contexts since install policy"
	        ::= { gxCreateInfo 1 }
	        
	  gxActContxt OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total active contexts"
			  ::= { gxCreateInfo 2 }

	  gxDropPlicyCreate OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total dropped create messages due to policy violation"
			  ::= { gxCreateInfo 3 }

	  gxDropMalformedReqCreate OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total dropped create messages due to malformed create-request"
			  ::= { gxCreateInfo 4 }

	  gxDropMalformedRespCreate OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total dropped create messages due to malformed create-response"
			  ::= { gxCreateInfo 5 }

	  gxExpiredCreate OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total time-out expired create messages "
			  ::= { gxCreateInfo 6 }

	  gxBadCauseCreate OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total bad cause create messages"
			  ::= { gxCreateInfo 7	 }
			  
	  gxSecondaryNsapiEntries OBJECT-TYPE
	  
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total entries in gtp_secondary_nsapi"
			  ::= { gxCreateInfo 8	 }
			  
	  gxActv0v1PdnConns OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total active v0 v1 PDN connections"
			  ::= { gxCreateInfo 11 }
			  
	  gxTunnelApnsEntries OBJECT-TYPE
	  
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total entries in gtp_tunnel_apns"
			  ::= { gxCreateInfo 12	 }
			  
	  gxTunnelsEntries OBJECT-TYPE
	  
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total entries in gtp_tunnels"
			  ::= { gxCreateInfo 13	 }

	  gxDeleteSinceInstall OBJECT-TYPE
	        SYNTAX  INTEGER
	        MAX-ACCESS  read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Total success delete messages since install policy"
	        ::= { gxDeleteInfo 1 }
	        
	  gxDropOutOfContxtDelete OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total dropped out of context delete messages"
			  ::= { gxDeleteInfo 2 }


	  gxDropMalformedReqDelete OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total dropped delete messages due to malformed delete-request"
			  ::= { gxDeleteInfo 3 }

	  gxDropMalformedRespDelete OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total dropped delete messages due to malformed delete-response"
			  ::= { gxDeleteInfo 4 }

	  gxExpiredDelete OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total time-out expired delete messages "
			  ::= { gxDeleteInfo 5 }

	  gxBadCauseDelete OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total bad cause delete messages"
			  ::= { gxDeleteInfo 6	 }
		
	  gxUpdateSinceInstall OBJECT-TYPE
	        SYNTAX  INTEGER
	        MAX-ACCESS  read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Total success update messages since install policy"
	        ::= { gxUpdateInfo 1 }
	        
	  gxDropOutOfContxtUpdate OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total dropped out of context update messages"
			  ::= { gxUpdateInfo 2 }


	  gxDropMalformedReqUpdate OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total dropped update messages due to malformed update-request"
			  ::= { gxUpdateInfo 3 }

	  gxDropMalformedRespUpdate OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total dropped update messages due to malformed update-response"
			  ::= { gxUpdateInfo 4 }

	  gxExpiredUpdate OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total time-out expired update messages "
			  ::= { gxUpdateInfo 5 }

	  gxBadCauseUpdate OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total bad cause update messages"
			  ::= { gxUpdateInfo 6	 }
			 
	  gxEchoSinceInstall OBJECT-TYPE
	        SYNTAX  INTEGER
	        MAX-ACCESS  read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Total success echo messages since install policy"
	        ::= { gxPathMngInfo 1 }
	        
	  gxVnspSinceInstall OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total success vnsp messages since install policy"
			  ::= { gxPathMngInfo 2 }


	  gxDropPolicyEcho OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total dropped echo messages due to policy violation"
			  ::= { gxPathMngInfo 3 }

	  gxDropMalformedReqEcho OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total dropped echo messages due to malformed echo-request"
			  ::= { gxPathMngInfo 4 }

	  gxDropMalformedRespEcho OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total dropped echo messages due to malformed echo-response"
			  ::= { gxPathMngInfo 5 }

	  gxExpiredEcho OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total time-out expired echo messages "
			  ::= { gxPathMngInfo 6}

	  gxDropVnsp OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total dropped vnsp messages"
			  ::= { gxPathMngInfo 7	 }

  	  gxGtpPathEntries OBJECT-TYPE
	  		   SYNTAX  INTEGER
			   MAX-ACCESS  read-only
			   STATUS  current
			   DESCRIPTION
			  		 "Total gtp path entries"
			   ::= { gxPathMngInfo 8 }			  

		
	  gxGpdu1MinAvgRate OBJECT-TYPE
	        SYNTAX  INTEGER
	        MAX-ACCESS  read-only
	        STATUS  current
	        DESCRIPTION
	        	   "GPDU 1 minute average packet rate"
	        ::= { gxGpduInfo 1 }
	        
	  gxDropOutOfContxtGpdu OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total dropped out of context GPDU's "
			  ::= { gxGpduInfo 2 }


	  gxDropAnti-spoofingGpdu OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total dropped GPDU's due to anti-spoofing"
			  ::= { gxGpduInfo 3 }

	  gxDropMs-MsGpdu OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total dropped GPDU's due to ms-ms violation "
			  ::= { gxGpduInfo 4 }

	  gxDropBadSeqGpdu OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total dropped GPDU's due bad sequence"
			  ::= { gxGpduInfo 5 }

	  gxDropBadGpdu OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total dropped bad GPDU's"
			  ::= { gxGpduInfo 6}
	
	gxGpduExpiredTunnel OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total expired GTP tunnels"
			  ::= { gxGpduInfo 7}
	
	gxInitiateSinceInstall OBJECT-TYPE
	  		   SYNTAX  INTEGER
			   MAX-ACCESS  read-only
			   STATUS  current
			   DESCRIPTION
			  		 "Total successive initate activations since last install policy"
			   ::= { gxInitiateInfo 1}		  
	
	gxDropInitiationReq	  OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total initate activation request messages droped "
			  ::= { gxInitiateInfo 2}

	  gxDropInitiationResp OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total initate activation response messages droped"
			  ::= { gxInitiateInfo 3}

  	  gxExpiredInitiateAct OBJECT-TYPE
	  		   SYNTAX  INTEGER
			   MAX-ACCESS  read-only
			   STATUS  current
			   DESCRIPTION
			  		 "Total time-out expired initate activations messages"
			   ::= { gxInitiateInfo 4}

	gxGTPv2CreateSessionSinceInstall OBJECT-TYPE
			SYNTAX  INTEGER
			MAX-ACCESS  read-only
			STATUS  current
			DESCRIPTION
				   "Total success create session messages since install policy"
			::= { gxGTPv2CreateInfo 1 }

	gxGTPv2CreateBearerSinceInstall OBJECT-TYPE
			SYNTAX  INTEGER
			MAX-ACCESS  read-only
			STATUS  current
			DESCRIPTION
				   "Total success create bearer messages since install policy"
			::= { gxGTPv2CreateInfo 2 }

	  gxGTPv2ExpiredCreateSession OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total time-out expired create session messages "
			  ::= { gxGTPv2CreateInfo 3 }

	  gxGTPv2ExpiredCreateBearer OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total time-out expired create bearer messages "
			  ::= { gxGTPv2CreateInfo 4 }
			  
	  gxGTPv2DropMalformedCreateSessionReq OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped create session request messages due to malformed create-request"
		  ::= { gxGTPv2CreateInfo 5 }
		  
	  gxGTPv2DropMalformedCreateSessionResp OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped create session response messages due to malformed create-response"
		  ::= { gxGTPv2CreateInfo 6 }
		  
	  gxGTPv2DropMalformedCreateBearerReq OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped create bearer request messages due to malformed create-request"
		  ::= { gxGTPv2CreateInfo 7 }
		  
	  gxGTPv2DropMalformedCreateBearerResp OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped create bearer response messages due to malformed create-response"
		  ::= { gxGTPv2CreateInfo 8 }

	  gxGTPv2DropPolicyCreateSession OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped create session messages due to policy violation"
		  ::= { gxGTPv2CreateInfo 9 }

	  gxGTPv2DropPolicyCreateBearer OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped create bearer messages due to policy violation"
		  ::= { gxGTPv2CreateInfo 10 }

	gxGTPv2ActPDN OBJECT-TYPE
			SYNTAX  INTEGER
			MAX-ACCESS  read-only
			STATUS  current
			DESCRIPTION
				   "Total active GTPv2 PDN connections"
			::= { gxGTPv2CreateInfo 11 }

	gxGTPv2ActDataBearers OBJECT-TYPE
			SYNTAX  INTEGER
			MAX-ACCESS  read-only
			STATUS  current
			DESCRIPTION
				   "Total active data bearers"
			::= { gxGTPv2CreateInfo 12 }

	gxGTPv2DeleteSessionSinceInstall OBJECT-TYPE
			SYNTAX  INTEGER
			MAX-ACCESS  read-only
			STATUS  current
			DESCRIPTION
				   "Total success delete session messages since install policy"
			::= { gxGTPv2DeleteInfo 1 }

	gxGTPv2DeleteBearerSinceInstall OBJECT-TYPE
			SYNTAX  INTEGER
			MAX-ACCESS  read-only
			STATUS  current
			DESCRIPTION
				   "Total success delete bearer messages since install policy"
			::= { gxGTPv2DeleteInfo 2 }
			
	  gxGTPv2ExpiredDeleteSession OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total time-out expired delete session messages "
		  ::= { gxGTPv2DeleteInfo 3 }
		  
	  gxGTPv2ExpiredDeleteBearer OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total time-out expired delete bearer messages "
		  ::= { gxGTPv2DeleteInfo 4 }
		  
	  gxGTPv2DropMalformedDeleteSessionReq OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped delete session request messages due to malformed delete-request"
		  ::= { gxGTPv2DeleteInfo 5 }
		  
	  gxGTPv2DropMalformedDeleteSessionResp OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped delete session response messages due to malformed delete-response"
		  ::= { gxGTPv2DeleteInfo 6 }
		  
	  gxGTPv2DropMalformedDeleteBearerReq OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped delete bearer request messages due to malformed delete-request"
		  ::= { gxGTPv2DeleteInfo 7 }
		  
	  gxGTPv2DropMalformedDeleteBearerResp OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped delete bearer response messages due to malformed delete-response"
		  ::= { gxGTPv2DeleteInfo 8 }

	  gxGTPv2DropPolicyDeleteSession OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped delete session messages due to policy violation"
		  ::= { gxGTPv2DeleteInfo 9 }

	  gxGTPv2DropPolicyDeleteBearer OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped delete bearer messages due to policy violation"
		  ::= { gxGTPv2DeleteInfo 10 }

	gxGTPv2UpdateBearerSinceInstall OBJECT-TYPE
			SYNTAX  INTEGER
			MAX-ACCESS  read-only
			STATUS  current
			DESCRIPTION
				   "Total success update bearer messages since install policy"
			::= { gxGTPv2UpdateInfo 1 }
			
	 gxGTPv2ExpiredUpdateBearer OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total time-out expired update bearer messages "
			  ::= { gxGTPv2UpdateInfo 2 }

	gxGTPv2ModifyBearerSinceInstall OBJECT-TYPE
			SYNTAX  INTEGER
			MAX-ACCESS  read-only
			STATUS  current
			DESCRIPTION
				   "Total success modify bearer messages since install policy"
			::= { gxGTPv2UpdateInfo 3 }
			
	 gxGTPv2ExpiredModifyBearer OBJECT-TYPE
	  		  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total time-out expired modify bearer messages "
			  ::= { gxGTPv2UpdateInfo 4 }		  
			  
	  gxGTPv2DropMalformedUpdateBearerReq OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped update bearer request messages due to malformed update-request"
		  ::= { gxGTPv2UpdateInfo 5 }
		  
	  gxGTPv2DropMalformedUpdateBearerResp OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped update bearer response messages due to malformed update-response"
		  ::= { gxGTPv2UpdateInfo 6 }
		  
	  gxGTPv2DropMalformedModifyBearerReq OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped modify bearer request messages due to malformed modify-request"
		  ::= { gxGTPv2UpdateInfo 7 }
		  
	  gxGTPv2DropMalformedModifyBearerResp OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped modify bearer response messages due to malformed modify-response"
		  ::= { gxGTPv2UpdateInfo 8 }

	  gxGTPv2DropPolicyUpdateBearer OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped update bearer messages due to policy violation"
		  ::= { gxGTPv2UpdateInfo 9 }

	  gxGTPv2DropPolicyModifyBearer OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped modify bearer messages due to policy violation"
		  ::= { gxGTPv2UpdateInfo 10 }

	  gxGTPv2EchoSinceInstall OBJECT-TYPE
	        SYNTAX  INTEGER
	        MAX-ACCESS  read-only
	        STATUS  current
	        DESCRIPTION
	        	   "Total success gtpv2 echo messages since install policy"
	        ::= { gxGTPv2PathMngInfo 1 }
	        
	  gxGTPv2VnspSinceInstall OBJECT-TYPE
			  SYNTAX  INTEGER
			  MAX-ACCESS  read-only
			  STATUS  current
			  DESCRIPTION
					 "Total success gtpv2 vnsp messages since install policy"
			  ::= { gxGTPv2PathMngInfo 2 }
			  
	  gxGTPv2ExpiredEcho OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total time-out expired gtpv2 echo messages "
		  ::= { gxGTPv2PathMngInfo 3 }

	  gxGTPv2DropMalformedEchoReq OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped echo request messages due to malformed echo-request"
		  ::= { gxGTPv2PathMngInfo 4 }
		  
	  gxGTPv2DropMalformedEchoResp OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped echo response messages due to malformed echo-response"
		  ::= { gxGTPv2PathMngInfo 5 }		  

	  gxGTPv2DropPolicyEcho OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total dropped echo messages due to policy violation"
		  ::= { gxGTPv2PathMngInfo 6 }
		  
	  gxGTPv2ModifyBearerCmdSinceInstall OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total success gtpv2 modify bearer command messages since install policy"
		  ::= { gxGTPv2CmdInfo 1 }
		  
	  gxGTPv2ModifyBearerFailIndSinceInstall OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total success gtpv2 modify bearer failure indication messages since install policy"
		  ::= { gxGTPv2CmdInfo 2 }
		  
	  gxGTPv2DeleteBearerCmdSinceInstall OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total success gtpv2 delete bearer command messages since install policy"
		  ::= { gxGTPv2CmdInfo 3 }
		  
	  gxGTPv2DeleteBearerFailIndSinceInstall OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total success gtpv2 delete bearer failure indication messages since install policy"
		  ::= { gxGTPv2CmdInfo 4 }
		  
	  gxGTPv2BearerResourceCmdSinceInstall OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total success gtpv2 bearer resource command messages since install policy"
		  ::= { gxGTPv2CmdInfo 5 }
		  
	  gxGTPv2BearerResourceFailIndSinceInstall OBJECT-TYPE
		  SYNTAX  INTEGER
		  MAX-ACCESS  read-only
		  STATUS  current
		  DESCRIPTION
				 "Total success gtpv2 bearer resource failure indication messages since install policy"
		  ::= { gxGTPv2CmdInfo 6 }

 		-- the Smart Defense Status

      asmAttacks     OBJECT IDENTIFIER ::= { smartDefense 1 }
	  asmLayer3      OBJECT IDENTIFIER ::= { asmAttacks 1 }

	  asmLayer4      OBJECT IDENTIFIER ::= { asmAttacks 2 }
	  	asmTCP       	OBJECT IDENTIFIER ::= { asmLayer4 1 }
	  		asmSynatk        OBJECT IDENTIFIER ::= { asmTCP 1 }
	  		asmSmallPmtu     OBJECT IDENTIFIER ::= { asmTCP 2 }
	  		asmSeqval        OBJECT IDENTIFIER ::= { asmTCP 3 }
	  	asmUDP        OBJECT IDENTIFIER ::= { asmLayer4 2 }
	  	asmScans      OBJECT IDENTIFIER ::= { asmLayer4 3 }
	  		asmHostPortScan     OBJECT IDENTIFIER ::= { asmScans 1 }
	  		asmIPSweep  	    OBJECT IDENTIFIER ::= { asmScans 2 }

	  asmLayer5      OBJECT IDENTIFIER ::= { asmAttacks 3 }
		  asmHTTP        OBJECT IDENTIFIER ::= { asmLayer5 1 }
		  	asmHttpWorms              OBJECT IDENTIFIER ::= { asmHTTP 1 }
		  	asmHttpFormatViolatoin    OBJECT IDENTIFIER ::= { asmHTTP 2 }
		  	asmHttpAsciiViolation     OBJECT IDENTIFIER ::= { asmHTTP 3 }
		  	asmHttpP2PHeaderFilter    OBJECT IDENTIFIER ::= { asmHTTP 4 }
		  asmCIFS        OBJECT IDENTIFIER ::= { asmLayer5 2 }
			asmCIFSWorms                OBJECT IDENTIFIER ::= { asmCIFS 1 }
			asmCIFSNullSession          OBJECT IDENTIFIER ::= { asmCIFS 2 }
			asmCIFSBlockedPopUps        OBJECT IDENTIFIER ::= { asmCIFS 3 }
			asmCIFSBlockedCommands      OBJECT IDENTIFIER ::= { asmCIFS 4 }
			asmCIFSPasswordLengthViolations     OBJECT IDENTIFIER ::= { asmCIFS 5 }
		  asmP2P        OBJECT IDENTIFIER ::= { asmLayer5 3 }
			asmP2POtherConAttempts      OBJECT IDENTIFIER ::= { asmP2P 1 }
			asmP2PKazaaConAttempts      OBJECT IDENTIFIER ::= { asmP2P 2 }
			asmP2PeMuleConAttempts      OBJECT IDENTIFIER ::= { asmP2P 3 }
			asmP2PGnutellaConAttempts   OBJECT IDENTIFIER ::= { asmP2P 4 }
			asmP2PSkypeCon              OBJECT IDENTIFIER ::= { asmP2P 5 }
			asmP2PBitTorrentCon         OBJECT IDENTIFIER ::= { asmP2P 6 }



	  
	  asmSynatkSynAckTimeout  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "SYN attack - Syn-Ack Timeout"
            ::= { asmSynatk 1 } 

	  asmSynatkSynAckReset  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "SYN attack - Syn-Ack Reset"
            ::= { asmSynatk 2 } 

	  asmSynatkModeChange  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "SYN attack  - Mode Change"
            ::= { asmSynatk 3 } 

	  asmSynatkCurrentMode  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "SYN attack  - Current Mode"
            ::= { asmSynatk 4 } 

	  asmSynatkNumberofunAckedSyns OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "SYN attack  - Number of un-Acked Syns"
            ::= { asmSynatk 5 } 

	  smallPMTUNumberOfAttacks OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Small PMTU - Number of attacks"
            ::= { asmSmallPmtu 1 } 

	  smallPMTUValueOfMinimalMTUsize OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Small PMTU - Value of Minimal MTU size"
            ::= { asmSmallPmtu 2 } 

	  sequenceVerifierInvalidAck OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Sequence Verifier - Invalid Ack"
            ::= { asmSeqval 1 } 

	  sequenceVerifierInvalidSequence OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Sequence Verifier - Invalid Sequence"
            ::= { asmSeqval 2 } 

	  sequenceVerifierInvalidretransmit OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Sequence Verifier - Invalid retransmit"
            ::= { asmSeqval 3 } 

	  httpWorms OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "HTTP worms"
            ::= { asmHttpWorms 1 } 

	  numOfhostPortScan OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "host port scan"
            ::= { asmHostPortScan 1 } 

	  numOfIpSweep OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "host port scan"
            ::= { asmIPSweep 1 } 

	  httpURLLengthViolation OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "HTTP - URL Length Violations"
            ::= { asmHttpFormatViolatoin 1 } 

	  httpHeaderLengthViolations OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "HTTP - Header Length Violations"
            ::= { asmHttpFormatViolatoin 2 } 

	  httpMaxHeaderReached OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "HTTP - Maximum Headers Reached"
            ::= { asmHttpFormatViolatoin 3 } 

	numOfHttpASCIIViolations OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "HTTP - ASCII Violations"
            ::= { asmHttpAsciiViolation 1 } 

	numOfHttpP2PHeaders OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "HTTP - Peer To Peer Headers"
            ::= { asmHttpP2PHeaderFilter 1 } 

	numOfCIFSworms OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "num of CIFS worms"
            ::= { asmCIFSWorms 1 } 

	numOfCIFSNullSessions OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "num of CIFS Null Sessions"
            ::= { asmCIFSNullSession 1 } 


	numOfCIFSBlockedPopUps OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "num of CIFS Blocked Pop-Ups"
            ::= { asmCIFSBlockedPopUps 1 } 

	numOfCIFSBlockedCommands OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "num of CIFS Blocked Commands"
            ::= { asmCIFSBlockedCommands 1 } 

	numOfCIFSPasswordLengthViolations OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "num of CIFS PasswordLength Violations"
            ::= { asmCIFSPasswordLengthViolations 1 } 

	numOfP2POtherConAttempts OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "num of P2P Other Con Attempts"
            ::= { asmP2POtherConAttempts 1 } 

	numOfP2PKazaaConAttempts OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "num of P2P Kazaa Con Attempts"
            ::= { asmP2PKazaaConAttempts 1 } 

	numOfP2PeMuleConAttempts OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "num of P2P eMule Con Attempts"
            ::= { asmP2PeMuleConAttempts 1 } 

	numOfGnutellaConAttempts OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "num of P2P Gnutella Con Attempts"
            ::= { asmP2PGnutellaConAttempts 1 } 

	numOfP2PSkypeCon OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "num of P2P Skype Con"
            ::= { asmP2PSkypeCon 1 } 

	numOfBitTorrentCon OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "num of P2P Bit Torrent Con"
            ::= { asmP2PBitTorrentCon 1 } 
                                             		                                                                                                            
       
         --the AntiVirus ( avi ) status       
          
	aviEngines    		OBJECT IDENTIFIER ::= { avi 1 } 
    	aviTopViruses 		OBJECT IDENTIFIER ::= { avi 2 } 
    	aviTopEverViruses	OBJECT IDENTIFIER ::= { avi 3 } 
    	aviServices		OBJECT IDENTIFIER ::= { avi 4 } 
    
    		aviServicesHTTP		OBJECT IDENTIFIER ::= { aviServices 1 }
    		aviServicesFTP		OBJECT IDENTIFIER ::= { aviServices 2 }
    		aviServicesSMTP		OBJECT IDENTIFIER ::= { aviServices 3 } 
    		aviServicesPOP3		OBJECT IDENTIFIER ::= { aviServices 4 }
          
	aviStatCode OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Status code"
			::= {avi 101 }
	
	aviStatShortDescr OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current   
			DESCRIPTION
				"Status short description"
			::= {avi 102 }
			
	aviStatLongDescr OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Status long description"
			::= {avi 103 }
                                

	aviEngineTable OBJECT-TYPE
      		SYNTAX  SEQUENCE OF AviEngineEntry
			MAX-ACCESS  not-accessible
			STATUS  current
			DESCRIPTION
				"Table of Anti Virus engines installed. Each engine entry contains information about this engine."
			::= {aviEngines 1 }
                                      
                                 
	aviEngineEntry OBJECT-TYPE
	  		SYNTAX  AviEngineEntry
	  		MAX-ACCESS  not-accessible
	  		STATUS  current
	  		DESCRIPTION ""
	  		INDEX   { aviEngineIndex }
	  		::= { aviEngineTable 1 }                                      
		
	AviEngineEntry ::=
      		SEQUENCE {
      			aviEngineIndex		
		          			Unsigned32,
		        aviEngineName
		        			DisplayString,
        	        aviEngineVer
         		  			DisplayString,
         		aviEngineDate
         					INTEGER,
         		aviSignatureName
         					DisplayString,
         		aviSignatureVer
         					DisplayString,
         		aviSignatureDate
         					INTEGER,
         		aviLastSigCheckTime
         					INTEGER,
         		aviLastSigLocation
         					DisplayString,
         		aviLastLicExp
         					DisplayString				
	        } 
	         
		aviEngineIndex OBJECT-TYPE
		              SYNTAX  Unsigned32
		              MAX-ACCESS read-only
		              STATUS  current
		              DESCRIPTION ""
		              ::= { aviEngineEntry 1 } 
		              
		aviEngineName OBJECT-TYPE
		              SYNTAX  DisplayString
		              MAX-ACCESS read-only
		              STATUS  current
		              DESCRIPTION ""
		              ::= { aviEngineEntry 2 }
		
		aviEngineVer OBJECT-TYPE
		              SYNTAX  DisplayString
		              MAX-ACCESS read-only
		              STATUS  current
		              DESCRIPTION ""
		              ::= { aviEngineEntry 3 }
		
		aviEngineDate OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current
		              DESCRIPTION ""
		              ::= { aviEngineEntry 4 }
		
		aviSignatureName OBJECT-TYPE
		              SYNTAX  DisplayString
		              MAX-ACCESS read-only
		              STATUS  current
		              DESCRIPTION ""
		              ::= { aviEngineEntry 5 }
		
		aviSignatureVer OBJECT-TYPE
		              SYNTAX  DisplayString
		              MAX-ACCESS read-only
		              STATUS  current
		              DESCRIPTION ""
		              ::= { aviEngineEntry 6 }
		
		aviSignatureDate OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current
		              DESCRIPTION ""
		              ::= { aviEngineEntry 7 }
		
		aviLastSigCheckTime OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current
		              DESCRIPTION ""
		              ::= { aviEngineEntry 8 }
		
		aviLastSigLocation OBJECT-TYPE
		              SYNTAX  DisplayString
		              MAX-ACCESS read-only
		              STATUS  current
		              DESCRIPTION ""
		              ::= { aviEngineEntry 9 }
		
		aviLastLicExp OBJECT-TYPE
			      SYNTAX  DisplayString
		              MAX-ACCESS read-only
		              STATUS  current
		              DESCRIPTION ""
		              ::= { aviEngineEntry 10 }
              
   
	aviTopVirusesTable OBJECT-TYPE
      		SYNTAX  SEQUENCE OF AviTopVirusesEntry
			MAX-ACCESS  not-accessible
			STATUS  current
			DESCRIPTION
				"Table for virus count of the most frequent viruses found in the last hour"
			::= {aviTopViruses 1 }
                                      
                                 
	aviTopVirusesEntry OBJECT-TYPE
	  		SYNTAX  AviTopVirusesEntry
	  		MAX-ACCESS  not-accessible
	  		STATUS  current
	  		DESCRIPTION ""
	  		INDEX   { aviTopVirusesIndex }
	  		::= { aviTopVirusesTable 1 }                                      
		
	AviTopVirusesEntry ::=
      		SEQUENCE {
      			aviTopVirusesIndex		
		          			Unsigned32,
		        aviTopVirusesName
		        			DisplayString,
		        aviTopVirusesCnt		
		          			INTEGER
                				
	         } 
	         
		aviTopVirusesIndex OBJECT-TYPE
				SYNTAX  Unsigned32
		              	MAX-ACCESS read-only
		              	STATUS  current
		              	DESCRIPTION
		              	  	"Virus index"
		              	::= { aviTopVirusesEntry 1 } 
              
		aviTopVirusesName OBJECT-TYPE
				SYNTAX  DisplayString
		              	MAX-ACCESS read-only
		              	STATUS  current
		              	DESCRIPTION
		              	  	"Virus Name"
		              	::= { aviTopVirusesEntry 2 } 						

		aviTopVirusesCnt OBJECT-TYPE
				SYNTAX  INTEGER
				MAX-ACCESS read-only
				STATUS  current  
				DESCRIPTION
					"Virus hit count"
				::= { aviTopVirusesEntry 3 } 
	                                               
                                               
		aviTopEverVirusesTable OBJECT-TYPE
	    		SYNTAX  SEQUENCE OF AviTopEverVirusesEntry
				MAX-ACCESS  not-accessible
				STATUS  current
				DESCRIPTION
					"Table for virus count of the most frequent viruses found from last system restart"
				::= {aviTopEverViruses 1 }
                                      
                                 
		aviTopEverVirusesEntry OBJECT-TYPE
	  		SYNTAX  AviTopEverVirusesEntry
	  		MAX-ACCESS  not-accessible
	  		STATUS  current
	  		DESCRIPTION ""
	  		INDEX   { aviTopEverVirusesIndex }
	  		::= { aviTopEverVirusesTable 1 }                                      
		
    	AviTopEverVirusesEntry ::=
      		SEQUENCE {
      			aviTopEverVirusesIndex		
		          			Unsigned32,
		        aviTopEverVirusesName
		        			DisplayString,
		        aviTopEverVirusesCnt		
		          			INTEGER
	         } 
	         
		aviTopEverVirusesIndex OBJECT-TYPE
		              SYNTAX  Unsigned32
		              MAX-ACCESS read-only
		              STATUS  current
		              DESCRIPTION ""
		              ::= { aviTopEverVirusesEntry 1 } 
		              
		aviTopEverVirusesName OBJECT-TYPE
		              SYNTAX  DisplayString
		              MAX-ACCESS read-only
		              STATUS  current
		              DESCRIPTION ""
		              ::= { aviTopEverVirusesEntry 2 }
		
		aviTopEverVirusesCnt OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current
		              DESCRIPTION ""
		              ::= { aviTopEverVirusesEntry 3 }   
              
                         
	aviHTTPState OBJECT-TYPE
      			SYNTAX  INTEGER 
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"State of the Anti Virus engine that inspects HTTP"
			::= { aviServicesHTTP 1 }

	aviHTTPLastVirusName OBJECT-TYPE
      			SYNTAX  DisplayString 
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Last virus found over HTTP"
			::= { aviServicesHTTP 2 }

	aviHTTPLastVirusTime OBJECT-TYPE
      			SYNTAX  INTEGER 
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { aviServicesHTTP 3 }
                                      
	aviHTTPTopVirusesTable OBJECT-TYPE
      			SYNTAX  SEQUENCE OF AviHTTPTopVirusesEntry
			MAX-ACCESS  not-accessible
			STATUS  current
			DESCRIPTION
				"Table for virus count of the most frequent viruses found over HTTP in the last day
				 or from last system restart (the latest of the two)"
			::= {aviServicesHTTP 4 }
                                                                       
	aviHTTPTopVirusesEntry OBJECT-TYPE
	  		SYNTAX  AviHTTPTopVirusesEntry
	  		MAX-ACCESS  not-accessible
	  		STATUS  current
	  		DESCRIPTION ""
	  		INDEX   { aviHTTPTopVirusesIndex }
	  		::= { aviHTTPTopVirusesTable 1 }                                      
		
	AviHTTPTopVirusesEntry ::=
      		SEQUENCE {
      			aviHTTPTopVirusesIndex		
		          			Unsigned32,
		        aviHTTPTopVirusesName
		        			DisplayString,
		        aviHTTPTopVirusesCnt		
		          			INTEGER
                				
	         } 
	         
		aviHTTPTopVirusesIndex OBJECT-TYPE
			SYNTAX  Unsigned32
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
		        ::= { aviHTTPTopVirusesEntry 1 } 		              
		    	
		aviHTTPTopVirusesName OBJECT-TYPE
			SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { aviHTTPTopVirusesEntry 2 }
		
		aviHTTPTopVirusesCnt OBJECT-TYPE
			SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { aviHTTPTopVirusesEntry 3 }     
              
              
              
	aviFTPState OBJECT-TYPE
		SYNTAX  INTEGER 
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION
			"State of the Anti Virus engine that inspects FTP"
		::= { aviServicesFTP 1 }

	aviFTPLastVirusName OBJECT-TYPE
      		SYNTAX  DisplayString 
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION
			"Last virus found over FTP"
		::= { aviServicesFTP 2 }

	aviFTPLastVirusTime OBJECT-TYPE
      		SYNTAX  INTEGER 
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION ""
		::= { aviServicesFTP 3 }
                                     
	aviFTPTopVirusesTable OBJECT-TYPE
      		SYNTAX  SEQUENCE OF AviFTPTopVirusesEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"Table for virus count of the most frequent viruses found over FTP in the last day
			 or from last system restart (the latest of the two)"
		::= {aviServicesFTP 4 }
                                                                       
	aviFTPTopVirusesEntry OBJECT-TYPE
	  	SYNTAX  AviFTPTopVirusesEntry
	  	MAX-ACCESS  not-accessible
	  	STATUS  current
	  	DESCRIPTION ""
	  	INDEX   { aviFTPTopVirusesIndex }
	  	::= { aviFTPTopVirusesTable 1 }                                      
		
	AviFTPTopVirusesEntry ::=
      		SEQUENCE {
      			aviFTPTopVirusesIndex		
		          			Unsigned32,
		        aviFTPTopVirusesName
		        			DisplayString,
		        aviFTPTopVirusesCnt		
		          			INTEGER
                				
	         } 
	         
		aviFTPTopVirusesIndex OBJECT-TYPE
			SYNTAX  Unsigned32
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { aviFTPTopVirusesEntry 1 } 
		              
		aviFTPTopVirusesName OBJECT-TYPE
			SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { aviFTPTopVirusesEntry 2 }
		
		aviFTPTopVirusesCnt OBJECT-TYPE
			SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { aviFTPTopVirusesEntry 3 } 


	aviSMTPState OBJECT-TYPE
      		SYNTAX  INTEGER 
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION
			"State of the Anti Virus engine that inspects SMTP"
		::= { aviServicesSMTP 1 }

	aviSMTPLastVirusName OBJECT-TYPE
      		SYNTAX  DisplayString 
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION
			"Last virus found over SMTP"
		::= { aviServicesSMTP 2 }

	aviSMTPLastVirusTime OBJECT-TYPE
      		SYNTAX  INTEGER 
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION ""
		::= { aviServicesSMTP 3 }
                                      
	aviSMTPTopVirusesTable OBJECT-TYPE
      		SYNTAX  SEQUENCE OF AviSMTPTopVirusesEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"Table for virus count of the most frequent viruses found over SMTP in the last day
			 or from last system restart (the latest of the two)"
		::= {aviServicesSMTP 4 }
                                                                       
	aviSMTPTopVirusesEntry OBJECT-TYPE
	  	SYNTAX  AviSMTPTopVirusesEntry
	  	MAX-ACCESS  not-accessible
	  	STATUS  current
	  	DESCRIPTION ""
	  	INDEX   { aviSMTPTopVirusesIndex }
	  	::= { aviSMTPTopVirusesTable 1 }                                      
		
	AviSMTPTopVirusesEntry ::=
      		SEQUENCE {
      			aviSMTPTopVirusesIndex		
		          			Unsigned32,
		        aviSMTPTopVirusesName
		        			DisplayString,
		        aviSMTPTopVirusesCnt		
		          			INTEGER
                				
	         } 
	         
		aviSMTPTopVirusesIndex OBJECT-TYPE
			SYNTAX  Unsigned32
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { aviSMTPTopVirusesEntry 1 } 
		              
		aviSMTPTopVirusesName OBJECT-TYPE
			SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { aviSMTPTopVirusesEntry 2 }
		
		aviSMTPTopVirusesCnt OBJECT-TYPE
			SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { aviSMTPTopVirusesEntry 3 }    
                                
    
	aviPOP3State OBJECT-TYPE
      		SYNTAX  INTEGER 
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION
			"State of the Anti Virus engine that inspects POP3"
		::= { aviServicesPOP3 1 }

	aviPOP3LastVirusName OBJECT-TYPE
      		SYNTAX  DisplayString 
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION
			"Last virus found over POP3"
		::= { aviServicesPOP3 2 }

	aviPOP3LastVirusTime OBJECT-TYPE
      		SYNTAX  INTEGER 
		MAX-ACCESS read-only
		STATUS  current
		DESCRIPTION ""
		::= { aviServicesPOP3 3 }
                                      
	aviPOP3TopVirusesTable OBJECT-TYPE
      		SYNTAX  SEQUENCE OF AviPOP3TopVirusesEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"Table for virus count of the most frequent viruses found over POP3 in the last day
			 or from last system restart (the latest of the two)"
		::= {aviServicesPOP3 4 }
                                                                       
	aviPOP3TopVirusesEntry OBJECT-TYPE
		SYNTAX  AviPOP3TopVirusesEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION ""
		INDEX   { aviPOP3TopVirusesIndex }
	  	::= { aviPOP3TopVirusesTable 1 }                                      
		
	AviPOP3TopVirusesEntry ::=
      		SEQUENCE {
      			aviPOP3TopVirusesIndex		
		          			Unsigned32,
		        aviPOP3TopVirusesName
		        			DisplayString,
		        aviPOP3TopVirusesCnt		
		          			INTEGER        				
	         } 
	         
		aviPOP3TopVirusesIndex OBJECT-TYPE
			SYNTAX  Unsigned32
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { aviPOP3TopVirusesEntry 1 } 
		              
		aviPOP3TopVirusesName OBJECT-TYPE
			SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { aviPOP3TopVirusesEntry 2 }
		
		aviPOP3TopVirusesCnt OBJECT-TYPE
			SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { aviPOP3TopVirusesEntry 3 } 

        
        
    --Eventia statuses: both Eventia Analyzer and Correlation Unit
    --cpsemd is the Analyzer Server, and cpsead is the Correlation Unit
        
        cpsemd    		OBJECT IDENTIFIER ::= { eventiaAnalyzer 1 }  
    	cpsead	 		OBJECT IDENTIFIER ::= { eventiaAnalyzer 2 } 


	--cpsemd statuses:

	cpsemdStatCode OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Status code"
			::= {cpsemd 101 }
		
	cpsemdStatShortDescr OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current   
			DESCRIPTION
				"Status short description"
			::= {cpsemd 102 }
			
	cpsemdStatLongDescr OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Status long description"
			::= {cpsemd 103 }
	
	cpsemdProcAlive OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"cpsemd Process is alive"
			::= {cpsemd 1 }
	
	cpsemdNewEventsHandled OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"New events handled"
			::= {cpsemd 2 }
	
	cpsemdUpdatesHandled OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Number of updates handled"
			::= {cpsemd 3 }
			
	cpsemdLastEventTime OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Last processed event time"
			::= {cpsemd 4 }
	
	cpsemdCurrentDBSize OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Current database size"
			::= {cpsemd 5 }	
			
	cpsemdDBCapacity OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Database capacity"
			::= {cpsemd 6 }

	cpsemdNumEvents OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Number of events in database"
			::= {cpsemd 7 }

	cpsemdDBDiskSpace OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Available database disk space"
			::= {cpsemd 8 }
			
	
	cpsemdCorrelationUnitTable OBJECT-TYPE
      		SYNTAX  SEQUENCE OF CpsemdCorrelationUnitEntry
    		MAX-ACCESS  not-accessible
			STATUS  current
			DESCRIPTION
				"Table for Correlation Units details"
			::= {cpsemd 9 }
                                                                       
	cpsemdDBIsFull OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"1 - Database is full; 0 - Database is not full"
			::= {cpsemd 10 }
			
	cpsemdCorrelationUnitEntry OBJECT-TYPE
		SYNTAX  CpsemdCorrelationUnitEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION ""
		INDEX   { cpsemdCorrelationUnitIndex }
	  	::= { cpsemdCorrelationUnitTable 1 }                                      
		
	CpsemdCorrelationUnitEntry ::=
      		SEQUENCE {
      			cpsemdCorrelationUnitIndex		
		          			Unsigned32,
		        cpsemdCorrelationUnitIP
		        			DisplayString,
		        cpsemdCorrelationUnitLastRcvdTime		
		          			DisplayString,
		        cpsemdCorrelationUnitNumEventsRcvd
		         			INTEGER,
		        cpsemdConnectionDuration	
		        			INTEGER
	         } 
	         
		cpsemdCorrelationUnitIndex OBJECT-TYPE
			SYNTAX  Unsigned32
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { cpsemdCorrelationUnitEntry 1 } 
		              
		cpsemdCorrelationUnitIP OBJECT-TYPE
			SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Correlation Unit IP"
			::= { cpsemdCorrelationUnitEntry 2 }
		
		cpsemdCorrelationUnitLastRcvdTime OBJECT-TYPE
			SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Time of last received event"
			::= { cpsemdCorrelationUnitEntry 3 }	
	
		cpsemdCorrelationUnitNumEventsRcvd OBJECT-TYPE
			SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Number of events received"
			::= { cpsemdCorrelationUnitEntry 4 }	
						
		cpsemdConnectionDuration OBJECT-TYPE
			SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Connection duration (to the Correlation Unit)"
			::= { cpsemdCorrelationUnitEntry 5 }

	--cpsead statuses:

	cpseadStatCode OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Status code"
			::= {cpsead 101 }
		
	cpseadStatShortDescr OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current   
			DESCRIPTION
				"Status short description"
			::= {cpsead 102 }
			
	cpseadStatLongDescr OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Status long description"
			::= {cpsead 103 }

	
	cpseadProcAlive OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"cpsead Process is alive"
			::= {cpsead 1 }
	
	cpseadConnectedToSem OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Connected to SEM (is the Correlation Unit connected to the Analyzer Server)"
			::= {cpsead 2 }

	cpseadNumProcessedLogs OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Number of logs processed"
			::= {cpsead 3 }

	
	cpseadJobsTable OBJECT-TYPE
      		SYNTAX  SEQUENCE OF CpseadJobsEntry
			MAX-ACCESS  not-accessible
			STATUS  current
			DESCRIPTION
				"Jobs Table"
			::= {cpsead 4 }
                                                                       
	cpseadJobsEntry OBJECT-TYPE
		SYNTAX  CpseadJobsEntry
			MAX-ACCESS  not-accessible
			STATUS  current
			DESCRIPTION ""
			INDEX   { cpseadJobIndex }
		  	::= { cpseadJobsTable 1 }                                      
		
	CpseadJobsEntry ::=
      		SEQUENCE {
      			cpseadJobIndex		
		          			Unsigned32,
		        cpseadJobID
		        			DisplayString,
		        cpseadJobName		
		          			DisplayString,
		        cpseadJobState
		         			DisplayString,
		        cpseadJobIsOnline
		        			INTEGER,
		        cpseadJobLogServer
		        			DisplayString,		        			
		       	cpseadJobDataType		
		          			DisplayString,
		        cpseadConnectedToLogServer
		        			INTEGER,
		        cpseadNumAnalyzedLogs		
		          			DisplayString,
		        cpseadFileName
		         			DisplayString,
		        cpseadFileCurrentPosition
		        			DisplayString,
		        cpseadStateDescriptionCode
		        			DisplayString,
		        cpseadStateDescription
		        			DisplayString
	         } 
	         
		cpseadJobIndex OBJECT-TYPE
			SYNTAX  Unsigned32
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION ""
			::= { cpseadJobsEntry 1 } 
		              
		cpseadJobID OBJECT-TYPE
			SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Job ID"
			::= { cpseadJobsEntry 2 }
		
		cpseadJobName OBJECT-TYPE
			SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Job name"
			::= { cpseadJobsEntry 3 }	
	
		cpseadJobState OBJECT-TYPE
			SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Job state"
			::= { cpseadJobsEntry 4 }	
						
		cpseadJobIsOnline OBJECT-TYPE
			SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Is job online"
			::= { cpseadJobsEntry 5 }

		cpseadJobLogServer OBJECT-TYPE
			SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				" Log Server (the IP of the log server from which the job is reading logs)"
			::= { cpseadJobsEntry 6 }
			
		cpseadJobDataType OBJECT-TYPE
			SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Data type (the type of date being read: log or audit)"
			::= { cpseadJobsEntry 7 } 
		              
		cpseadConnectedToLogServer OBJECT-TYPE
			SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Connected to Log Server (is the Correlation Unit connected to the Log Server)"
			::= { cpseadJobsEntry 8 }
		
		cpseadNumAnalyzedLogs OBJECT-TYPE
			SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Number of logs analyzed"
			::= { cpseadJobsEntry 9 }	
	
		cpseadFileName OBJECT-TYPE
			SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"File Name (the name of the file from which the job is reading logs)"
			::= { cpseadJobsEntry 10 }	
						
		cpseadFileCurrentPosition OBJECT-TYPE
			SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"current position in the file"
			::= { cpseadJobsEntry 11 }

		cpseadStateDescriptionCode OBJECT-TYPE
			SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"state description code (code for the state description in the next OID)"
			::= { cpseadJobsEntry 12 }
			
		cpseadStateDescription OBJECT-TYPE
			SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"state description (provides more info regarding the job's state (OID 4), for instance details errors.)"
			::= { cpseadJobsEntry 13 }			

	cpseadNoFreeDiskSpace OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"no free disk space (is there not enough free disk space on the Correlation Unit)"
			::= {cpsead 5 }

	

        --the Url Filtering ( uf ) status       
          
	ufEngine		OBJECT IDENTIFIER ::= { uf 1 } 
	ufSS			OBJECT IDENTIFIER ::= { uf 2 } 
   	        
	ufStatCode OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Status code"
			::= {uf 101 }
	
	ufStatShortDescr OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current   
			DESCRIPTION
				"Status short description"
			::= {uf 102 }
			
	ufStatLongDescr OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Status long description"
			::= {uf 103 }

    
    	ufEngineName OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Engine name"
			::= {ufEngine 1 }

    	ufEngineVer OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Engine version"
			::= {ufEngine 2 }

	ufEngineDate OBJECT-TYPE
      		SYNTAX  INTEGER 
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Engine build date"
			::= {ufEngine 3 }
                                                                                                                
	ufSignatureDate OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Creation date of the signature-set used by the engine"
			::= {ufEngine 4 }
                                      
	ufSignatureVer OBJECT-TYPE
      		SYNTAX  DisplayString 
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Version of the signature"
			::= {ufEngine 5 }
                                      
	ufLastSigCheckTime OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Last time there was a check for new signature "
			::= {ufEngine 6 }
                                      
	ufLastSigLocation OBJECT-TYPE
      		SYNTAX  DisplayString 
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Last signature origine (either from Remote site or from SmartCenter)"
			::= {ufEngine 7 }
                                      
	ufLastLicExp OBJECT-TYPE
      		SYNTAX  DisplayString 
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Expiration date of the subscription for Web Filtering updates"
			::= {ufEngine 8 }
                                      


	ufIsMonitor OBJECT-TYPE
      		SYNTAX  DisplayString 
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Monitor-Only Running mode (ON or OFF)"
			::= {ufSS 1 }

	ufScannedCnt OBJECT-TYPE
      		SYNTAX  INTEGER   
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Total number of scanned URLs"  
			::= {ufSS 2 }

	ufBlockedCnt OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Number of blocked URLs"
			::= {ufSS 3 }

	ufTopBlockedCatTable OBJECT-TYPE
      		SYNTAX  SEQUENCE OF UfTopBlockedCatEntry
			MAX-ACCESS  not-accessible
			STATUS  current
			DESCRIPTION
				"Table for counting of the most frequent categories blocked in the last day"
			::= {ufSS 4 }
                                      
                                 
     	ufTopBlockedCatEntry OBJECT-TYPE
	  		SYNTAX  UfTopBlockedCatEntry
	  		MAX-ACCESS  not-accessible
	  		STATUS  current
	  		DESCRIPTION ""
	  		INDEX   { ufTopBlockedCatIndex }
	  		::= { ufTopBlockedCatTable 1 }                                      
		
	UfTopBlockedCatEntry ::=
      		SEQUENCE {
      			ufTopBlockedCatIndex		
		          			Unsigned32,
		        ufTopBlockedCatName
		        			DisplayString,
        		ufTopBlockedCatCnt
         		  			INTEGER
	         } 
	         
		 ufTopBlockedCatIndex OBJECT-TYPE
		              SYNTAX  Unsigned32
		              MAX-ACCESS read-only
		              STATUS  current
		              DESCRIPTION
		              		  "Blocked Category index"
		              ::= { ufTopBlockedCatEntry 1 } 
		              
		ufTopBlockedCatName OBJECT-TYPE
		              SYNTAX  DisplayString
		              MAX-ACCESS read-only
		              STATUS  current     
		              DESCRIPTION
		              		  "Blocked Category name"
		              ::= { ufTopBlockedCatEntry 2 }
		
		ufTopBlockedCatCnt OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current             
		              DESCRIPTION
		              		  "Blocked Category count"
		              ::= { ufTopBlockedCatEntry 3 }                              
                                      
          
	ufTopBlockedSiteTable OBJECT-TYPE
      		SYNTAX  SEQUENCE OF UfTopBlockedSiteEntry
			MAX-ACCESS  not-accessible
			STATUS  current
			DESCRIPTION
				"Table for counting of the most frequent sites blocked in the last day"
			::= {ufSS 5 }
                                      
                                 
	ufTopBlockedSiteEntry OBJECT-TYPE
	  		SYNTAX  UfTopBlockedSiteEntry
	  		MAX-ACCESS  not-accessible
	  		STATUS  current
	  		DESCRIPTION ""
	  		INDEX   { ufTopBlockedSiteIndex }
	  		::= { ufTopBlockedSiteTable 1 }                                      
		
	UfTopBlockedSiteEntry ::=
      		SEQUENCE {
      			ufTopBlockedSiteIndex		
		          			Unsigned32,
		        ufTopBlockedSiteName
		        			DisplayString,
        		ufTopBlockedSiteCnt
         		  			INTEGER
	         } 
	         
		ufTopBlockedSiteIndex OBJECT-TYPE
		              SYNTAX  Unsigned32
		              MAX-ACCESS read-only
		              STATUS  current  
		              DESCRIPTION
		              		  "Blocked URL index"            
		              ::= { ufTopBlockedSiteEntry 1 } 
		              
		ufTopBlockedSiteName OBJECT-TYPE
		              SYNTAX  DisplayString
		              MAX-ACCESS read-only
		              STATUS  current
		              DESCRIPTION
		              		  "Blocked URL name"
		              ::= { ufTopBlockedSiteEntry 2 }
		
		ufTopBlockedSiteCnt OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current
		              DESCRIPTION
		              		  "Blocked URL count"
		              ::= { ufTopBlockedSiteEntry 3 }   
     
 	
	ufTopBlockedUserTable OBJECT-TYPE
      		SYNTAX  SEQUENCE OF UfTopBlockedUserEntry
			MAX-ACCESS  not-accessible
			STATUS  current
			DESCRIPTION
				"Table for counting blocked URLs of the most frequent blocked users in the last day"
			::= {ufSS 6 }
                                      
                                 
	ufTopBlockedUserEntry OBJECT-TYPE
	  		SYNTAX  UfTopBlockedUserEntry
	  		MAX-ACCESS  not-accessible
	  		STATUS  current
	  		DESCRIPTION ""
	  		INDEX   { ufTopBlockedUserIndex }
	  		::= { ufTopBlockedUserTable 1 }                                      
		
	UfTopBlockedUserEntry ::=
      		SEQUENCE {
      			ufTopBlockedUserIndex		
		          			Unsigned32,
		        ufTopBlockedUserName
		        			DisplayString,
        	        ufTopBlockedUserCnt
         		  			INTEGER
	         } 
	         
		ufTopBlockedUserIndex OBJECT-TYPE
		              SYNTAX  Unsigned32
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Blocked User index"
		              ::= { ufTopBlockedUserEntry 1 } 
		              
		ufTopBlockedUserName OBJECT-TYPE
		              SYNTAX  DisplayString
		              MAX-ACCESS read-only
		              STATUS  current
		              DESCRIPTION 
		              		  "Blocked User name"
		              ::= { ufTopBlockedUserEntry 2 }
		
		ufTopBlockedUserCnt OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current           
		              DESCRIPTION 
		              		  "Blocked User count"
		              ::= { ufTopBlockedUserEntry 3 }     
    
    --Anti Spam (mail security) statuses:  

	msProductName OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Product Name"
			::= { ms 1 }

	msMajorVersion OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Major Version"
			::= { ms 2 }

 	msMinorVersion OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Minor Version"
			::= { ms 3 }
    
	msBuildNumber OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Build Number"
			::= { ms 4 }

 	msVersionStr OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Version String"
			::= { ms 5 }
	                                            
	--Spam Statuses subtree	                                            
	msSpam	OBJECT IDENTIFIER ::= { ms 6 }

	msSpamNumScannedEmails	OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of scanned emails"
			::= { msSpam 1 }
 
 	msSpamNumSpamEmails	OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of spam emails"
			::= { msSpam 2 }

	msSpamNumHandledSpamEmails	OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of handled spam emails"
			::= { msSpam 3 }
             
	    --spam controls subtree
	    msSpamControls  OBJECT IDENTIFIER ::= { msSpam 4 }
	 
		msSpamControlsSpamEngine	OBJECT-TYPE
	      		SYNTAX  INTEGER
				MAX-ACCESS read-only
				STATUS  current 
				DESCRIPTION
					"Number of spam emails by Anti Spam engine"
				::= { msSpamControls 1 } 
	 
		msSpamControlsIpRepuatation	OBJECT-TYPE
	      		SYNTAX  INTEGER
				MAX-ACCESS read-only
				STATUS  current 
				DESCRIPTION
					"Number of spam emails by IP reputation service"
				::= { msSpamControls 2 } 
	
		msSpamControlsSPF OBJECT-TYPE
	      		SYNTAX  INTEGER
				MAX-ACCESS read-only
				STATUS  current 
				DESCRIPTION
					"Number of spam emails by SPF"
				::= { msSpamControls 3 } 
	
		msSpamControlsDomainKeys OBJECT-TYPE
	      		SYNTAX  INTEGER
				MAX-ACCESS read-only
				STATUS  current 
				DESCRIPTION
					"Number of spam emails by Domain Keys"
				::= { msSpamControls 4 } 
	
		msSpamControlsRDNS OBJECT-TYPE
	      		SYNTAX  INTEGER
				MAX-ACCESS read-only
				STATUS  current 
				DESCRIPTION
					"Number of spam emails by Reverse DNS"
				::= { msSpamControls 5 } 
	
		msSpamControlsRBL OBJECT-TYPE
	      		SYNTAX  INTEGER
				MAX-ACCESS read-only
				STATUS  current 
				DESCRIPTION
					"Number of spam emails by RBLs (Including Received headers and URLs)"
				::= { msSpamControls 6 } 
   
	msExpirationDate OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Subscription expiration date"
			::= { ms 7 }

    msEngineVer OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti Spam Engine Version"
			::= { ms 8 }

     msEngineDate OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti Spam Engine Build Date"
			::= { ms 9 }
   
 	msStatCode OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Status code"
			::= { ms 101 }
		
	msStatShortDescr OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current   
			DESCRIPTION
				"Status short description"
			::= { ms 102 }
			
	msStatLongDescr OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Status long description"
			::= { ms 103 }

	msServicePack OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Service Pack"
			::= { ms 999 }

	
	
	--voip statuses:   

 	voipProductName OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Product Name"
			::= {voip 1 }

	
 	voipMajorVersion OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Major Version"
			::= {voip 2 }

 	voipMinorVersion OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Minor Version"
			::= {voip 3 }

 	voipBuildNumber OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Build Number"
			::= {voip 4 }

 	voipVersionStr OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Version String"
			::= {voip 5 }
    
    -- Denial Of Service Statuses Subtree
 	voipDOS		OBJECT IDENTIFIER ::= { voip 6 }
	voipDOSSip		OBJECT IDENTIFIER ::= { voipDOS 1 }
	voipDOSSipNetwork		OBJECT IDENTIFIER ::= { voipDOSSip 1 }
	    
    voipDOSSipNetworkReqInterval OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Total Number of SIP Requests to The Internal Network per Interval: Interval (seconds)"
			::= {voipDOSSipNetwork 1 }

    voipDOSSipNetworkReqConfThreshold OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Total Number of SIP Requests to The Internal Network per Interval: Configured Threshold"
			::= {voipDOSSipNetwork 2 }
    
    voipDOSSipNetworkReqCurrentVal OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Total Number of SIP Requests to The Internal Network per Interval: Current Value"
			::= {voipDOSSipNetwork 3 }
    
    voipDOSSipNetworkRegInterval OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Total number of 'REGISTER' Requests to The Internal Network per Interval: Interval (seconds)"
			::= {voipDOSSipNetwork 4 }
    
    voipDOSSipNetworkRegConfThreshold OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Total number of 'REGISTER' Requests to The Internal Network per Interval: Configured Threshold"
			::= {voipDOSSipNetwork 5 }
    
	voipDOSSipNetworkRegCurrentVal OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Total number of 'REGISTER' Requests to The Internal Network per Interval: Current Value"
			::= {voipDOSSipNetwork 6 }
	
	voipDOSSipNetworkCallInitInterval OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Total Number of SIP Call Initiations to The Internal Network per Interval: Interval (seconds)"
			::= {voipDOSSipNetwork 7 }
	
	voipDOSSipNetworkCallInitConfThreshold OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Total Number of SIP Call Initiations to The Internal Network per Interval: Configured Threshold"
			::= {voipDOSSipNetwork 8 }
	
	voipDOSSipNetworkCallInitICurrentVal OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Total Number of SIP Call Initiations to The Internal Network per Interval: Current Value"
			::= {voipDOSSipNetwork 9 }
	
	
	voipDOSSipRateLimitingTable OBJECT-TYPE
      		SYNTAX  SEQUENCE OF VoipDOSSipRateLimitingEntry
			MAX-ACCESS  not-accessible
			STATUS  current
			DESCRIPTION
				"Table for counting blocked URLs of the most frequent blocked users in the last day"
			::= {voipDOSSip 2 }
                                      
                                 
	voipDOSSipRateLimitingEntry OBJECT-TYPE
	  		SYNTAX  VoipDOSSipRateLimitingEntry
	  		MAX-ACCESS  not-accessible
	  		STATUS  current
	  		DESCRIPTION ""
	  		INDEX   { voipDOSSipRateLimitingTableIndex }
	  		::= { voipDOSSipRateLimitingTable 1 }                                      
		
	VoipDOSSipRateLimitingEntry ::=
      		SEQUENCE {
      			voipDOSSipRateLimitingTableIndex		
		          			Unsigned32,  
				voipDOSSipRateLimitingTableIpAddress		          			
		          			INTEGER,
 				voipDOSSipRateLimitingTableInterval
		          			INTEGER,
 				voipDOSSipRateLimitingTableConfThreshold
		          			INTEGER,
				voipDOSSipRateLimitingTableNumDOSSipRequests
		          			INTEGER,
				voipDOSSipRateLimitingTableNumTrustedRequests
		          			INTEGER,
				voipDOSSipRateLimitingTableNumNonTrustedRequests
		          			INTEGER,
				voipDOSSipRateLimitingTableNumRequestsfromServers
		          			INTEGER
	         }                  
	         
		voipDOSSipRateLimitingTableIndex OBJECT-TYPE
		              SYNTAX  Unsigned32
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Rate Limiting Defense for Internal SIP Servers: Table Index"
		              ::= { voipDOSSipRateLimitingEntry 1 } 

		voipDOSSipRateLimitingTableIpAddress OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Rate Limiting Defense for Internal SIP Servers: Ip Address"
		              ::= { voipDOSSipRateLimitingEntry 2 } 	    
		
		voipDOSSipRateLimitingTableInterval OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Rate Limiting Defense for Internal SIP Servers: Interval (Seconds)"
		              ::= { voipDOSSipRateLimitingEntry 3 } 	    
		
		voipDOSSipRateLimitingTableConfThreshold OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Rate Limiting Defense for Internal SIP Servers: Configured Threshold for Number of SIP Requests"
		              ::= { voipDOSSipRateLimitingEntry 4 } 	    							          			

 		voipDOSSipRateLimitingTableNumDOSSipRequests OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Rate Limiting Defense for Internal SIP Servers: Number of SIP Requests"
		              ::= { voipDOSSipRateLimitingEntry 5 }  
		              
		voipDOSSipRateLimitingTableNumTrustedRequests OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Rate Limiting Defense for Internal SIP Servers: Number of SIP Requests from Trusted Users"
		              ::= { voipDOSSipRateLimitingEntry 6 } 
		              
		voipDOSSipRateLimitingTableNumNonTrustedRequests OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Rate Limiting Defense for Internal SIP Servers: Number of SIP Requests from Non-Trusted Users"
		              ::= { voipDOSSipRateLimitingEntry 7 }               		
		              
		voipDOSSipRateLimitingTableNumRequestsfromServers OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Rate Limiting Defense for Internal SIP Servers: Number of SIP Requests from Non-Trusted Users"
		              ::= { voipDOSSipRateLimitingEntry 8 }  					
		   
	         
	-- Call Admission Control Subtree
 	voipCAC		OBJECT IDENTIFIER ::= { voip 7 }
	voipCACConcurrentCalls		OBJECT IDENTIFIER ::= { voipCAC 1 }
	
	voipCACConcurrentCallsConfThreshold OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Number of Concurrent Calls: Configured Threshold"
			::= {voipCACConcurrentCalls 1 }
			
	voipCACConcurrentCallsCurrentVal OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Number of Concurrent Calls: Configured Threshold"
			::= {voipCACConcurrentCalls 2 }					

	voipStatCode OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current
			DESCRIPTION
				"Status code"
			::= {voip 101 }
		
	voipStatShortDescr OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current   
			DESCRIPTION
				"Status short description"
			::= {voip 102 }
			
	voipStatLongDescr OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Status long description"
			::= {voip 103 }

	voipServicePack OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Service Pack"
			::= {voip 999 }   
			
		--identityAwareness statuses:   

 	identityAwarenessProductName OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Product Name"
			::= {identityAwareness 1 }

	
 	identityAwarenessAuthUsers OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of users authenticated to the gateway"
			::= {identityAwareness 2 }

 	identityAwarenessUnAuthUsers OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of unauthenticated guests"
			::= {identityAwareness 3 }

 	identityAwarenessAuthUsersKerberos OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of authenticated users by Kerberos method"
			::= {identityAwareness 4 }

 	identityAwarenessAuthMachKerberos OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of authenticated machines by Kerberos method"
			::= {identityAwareness 5 }
			
	identityAwarenessAuthUsersPass OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of authenticated users by User name and password method"
			::= {identityAwareness 6 }
			
	identityAwarenessAuthUsersADQuery OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of authenticated users by ADQuery method"
			::= {identityAwareness 7 }

    identityAwarenessAuthMachADQuery OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of authenticated machines by ADQuery method"
			::= {identityAwareness 8 }
    
    identityAwarenessLoggedInAgent OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of users that are logged in with agents"
			::= {identityAwareness 9 } 
	
	identityAwarenessLoggedInCaptivePortal OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of users that are logged in with Captive Portal"
			::= {identityAwareness 10 }
			
	identityAwarenessLoggedInADQuery OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of users that are logged in with ADQuery"
			::= {identityAwareness 11 }
			
	identityAwarenessAntiSpoffProtection OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of users with an active Traffic Anti-Spoffing protection"
			::= {identityAwareness 12 }  
			
	identityAwarenessSuccUserLoginKerberos OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of successfull Kerberos user login attempts"
			::= {identityAwareness 13 } 
			
	identityAwarenessSuccMachLoginKerberos OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of successfull Kerberos machine login attempts"
			::= {identityAwareness 14 }
			
	identityAwarenessSuccUserLoginPass OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of successfull User Name And Password login attempts"
			::= {identityAwareness 15 }
			
	identityAwarenessSuccUserLoginADQuery OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of successfull ADQuery user login attempts"
			::= {identityAwareness 16 }  
			
	identityAwarenessSuccMachLoginADQuery OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of successfull ADQuery machine login attempts"
			::= {identityAwareness 17 }
			
	identityAwarenessUnSuccUserLoginKerberos OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of unsuccessfull Kerberos user login attempts"
			::= {identityAwareness 18 }

    identityAwarenessUnSuccMachLoginKerberos OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of unsuccessfull Kerberos machine login attempts"
			::= {identityAwareness 19 }
			
	identityAwarenessUnSuccUserLoginPass OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of unsuccessfull User Name And Password login attempts"
			::= {identityAwareness 20 }
			
	identityAwarenessSuccUserLDAP OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of successful LDAP queries"
			::= {identityAwareness 21 }
			
	identityAwarenessUnSuccUserLDAP OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of unsuccessful LDAP queries"
			::= {identityAwareness 22 } 
	
	identityAwarenessDataTrans OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"How much data did  gateway transmitted"
			::= {identityAwareness 23 }
    
   	identityAwarenessDistributedEnvTable OBJECT-TYPE
     		SYNTAX  SEQUENCE OF IdentityAwarenessDistributedEnvEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"Table for distributed environments"
		::= {identityAwareness 24 }
                                      
                                 
	identityAwarenessDistributedEnvEntry OBJECT-TYPE
	  		SYNTAX  IdentityAwarenessDistributedEnvEntry
	  		MAX-ACCESS  not-accessible
	  		STATUS  current
	  		DESCRIPTION ""
	  		INDEX   { identityAwarenessDistributedEnvTableIndex }
	  		::= { identityAwarenessDistributedEnvTable 1 }                                      
		
	IdentityAwarenessDistributedEnvEntry ::=
      		SEQUENCE {
      			identityAwarenessDistributedEnvTableIndex		
		          			Unsigned32,  
				identityAwarenessDistributedEnvTableGwName		          			
		          			DisplayString,
 				identityAwarenessDistributedEnvTableDisconnections
		          			INTEGER,
 				identityAwarenessDistributedEnvTableBruteForceAtt
		          			INTEGER,
				identityAwarenessDistributedEnvTableStatus
		          			INTEGER,
				identityAwarenessDistributedEnvTableIsLocal
		          			INTEGER	         
		        }                  
	         
		identityAwarenessDistributedEnvTableIndex OBJECT-TYPE
		              SYNTAX  Unsigned32
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Distributed Environments: Table Index"
		              ::= { identityAwarenessDistributedEnvEntry 1 } 

		identityAwarenessDistributedEnvTableGwName OBJECT-TYPE
		              SYNTAX  DisplayString
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Distributed Environments: GW Name"
		              ::= { identityAwarenessDistributedEnvEntry 2 } 	    
		
		identityAwarenessDistributedEnvTableDisconnections OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Distributed Environments: Number of disconnections"
		              ::= { identityAwarenessDistributedEnvEntry 3 } 	    
		
		identityAwarenessDistributedEnvTableBruteForceAtt OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Distributed Environments: Number of brute force attack detected"
		              ::= { identityAwarenessDistributedEnvEntry 4 } 	    							          			

 		identityAwarenessDistributedEnvTableStatus OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Distributed Environments: Status
		              		   Possible values include:
			  		           Ok		0							   
							   Error	2"
		              ::= { identityAwarenessDistributedEnvEntry 5 }  
		              
		identityAwarenessDistributedEnvTableIsLocal OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Distributed Environments: Is local
		              		   Possible values include:
			  		           False	0
			  		           True		1"
		              ::= { identityAwarenessDistributedEnvEntry 6 } 
		
		identityAwarenessADQueryStatusTable OBJECT-TYPE
     		SYNTAX  SEQUENCE OF IdentityAwarenessADQueryStatusEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"Table for AD Query status"
		::= {identityAwareness 25 }
                                      
                                 
	identityAwarenessADQueryStatusEntry OBJECT-TYPE
	  		SYNTAX  IdentityAwarenessADQueryStatusEntry
	  		MAX-ACCESS  not-accessible
	  		STATUS  current
	  		DESCRIPTION ""
	  		INDEX   { identityAwarenessADQueryStatusTableIndex }
	  		::= { identityAwarenessADQueryStatusTable 1 }                                      
		
	IdentityAwarenessADQueryStatusEntry ::=
      		SEQUENCE {
      			identityAwarenessADQueryStatusTableIndex		
		          			Unsigned32,  
				identityAwarenessADQueryStatusCurrStatus		          			
		          			INTEGER,
 				identityAwarenessADQueryStatusDomainName
		          			DisplayString,
 				identityAwarenessADQueryStatusDomainIP
		          			IpAddress,
				identityAwarenessADQueryStatusEvents
		          			INTEGER         
		        }                  
	         
		identityAwarenessADQueryStatusTableIndex OBJECT-TYPE
		              SYNTAX  Unsigned32
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "AD Query Status: Table Index"
		              ::= { identityAwarenessADQueryStatusEntry 1 } 

		identityAwarenessADQueryStatusCurrStatus OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "AD Query Status: Current status
		              		   Possible values include:
			  		           Ok					0
			  		           Bad Credentials		1
			  		           Connectivity Error	2
			  		           Internal Error		3
			  		           Connection Time Out	4"
		              ::= { identityAwarenessADQueryStatusEntry 2 } 	    
		
		identityAwarenessADQueryStatusDomainName OBJECT-TYPE
		              SYNTAX  DisplayString
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "AD Query Status: Domain name"
		              ::= { identityAwarenessADQueryStatusEntry 3 } 	    
		
		identityAwarenessADQueryStatusDomainIP OBJECT-TYPE
		              SYNTAX  IpAddress
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "AD Query Status: Domain IP"
		              ::= { identityAwarenessADQueryStatusEntry 4 } 	    							          			

 		identityAwarenessADQueryStatusEvents OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "AD Query Status: Number of events"
		              ::= { identityAwarenessADQueryStatusEntry 5 }  
		           	         
       	identityAwarenessRADIUSAccounting OBJECT-TYPE
      		SYNTAX  Unsigned32
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of identities logged in with RADIUS Accounting"
			::= {identityAwareness 39 } 
			
		identityAwarenessIdentityCollectorActiveDirectory OBJECT-TYPE
      		SYNTAX  Unsigned32
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of identities logged in with Identity Collector Active Directory"
			::= {identityAwareness 40 } 
			
		identityAwarenessIdentityCollectorCiscoISE OBJECT-TYPE
      		SYNTAX  Unsigned32
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of identities logged in with Identity Collector Cisco ISE"
			::= {identityAwareness 41 }

		identityAwarenessTerminalServer OBJECT-TYPE
      		SYNTAX  Unsigned32
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of identities logged in with terminal server"
			::= {identityAwareness 42 }

		identityAwarenessRemoteAccess OBJECT-TYPE
      		SYNTAX  Unsigned32
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of identities logged in with Remote Access"
			::= {identityAwareness 43 }

		identityAwarenessIdentityWebAPI OBJECT-TYPE
      		SYNTAX  Unsigned32
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Number of identities logged in with Identity Web API"
			::= {identityAwareness 44 }  			
	   
	   identityAwarenessStatus OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Identity Awarness Status code"
			::= {identityAwareness 101 }
			
	   identityAwarenessStatusShortDesc OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Identity Awarness Status - Short Description"
			::= {identityAwareness 102 }
	
	   identityAwarenessStatusLongDesc OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Identity Awarness Status  - Long Description"
			::= {identityAwareness 103 }
       
       	--applicationControl statuses:
    
    applicationControlSubscription OBJECT IDENTIFIER ::= { applicationControl 1 }   
			
	applicationControlSubscriptionStatus OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Subscription status
				 Possible values include:
	           	 valid, expired, about-to-expire, not-associated, unknown"
			::= {applicationControlSubscription 1 }
			
	applicationControlSubscriptionExpDate OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Application Control subscription expiration date"
			::= {applicationControlSubscription 2 }

    applicationControlSubscriptionDesc OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Subscription description"
			::= {applicationControlSubscription 3 }
			
	applicationControlUpdate OBJECT IDENTIFIER ::= { applicationControl 2 }   
			
	applicationControlUpdateStatus OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Update status
				 Possible values include:
	           	 failed, up-to-date, new, degrade, unknown"
			::= {applicationControlUpdate 1 }
			
	applicationControlUpdateDesc OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Update description"
			::= {applicationControlUpdate 2 }

    applicationControlNextUpdate OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Next update description"
			::= {applicationControlUpdate 3 }
			
	applicationControlVersion OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Application Control database version"
			::= {applicationControlUpdate 4 }
							
	applicationControlStatusCode OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Application Control Status code"
			::= {applicationControl 101 }
			
   applicationControlStatusShortDesc OBJECT-TYPE
     		SYNTAX  DisplayString
		MAX-ACCESS read-only
		STATUS  current 
		DESCRIPTION
			"Application Control Status - Short Description"
		::= {applicationControl 102 }

   applicationControlStatusLongDesc OBJECT-TYPE
     		SYNTAX  DisplayString
		MAX-ACCESS read-only
		STATUS  current 
		DESCRIPTION
			"Application Control Status  - Long Description"
		::= {applicationControl 103 }  
			
--DLP statuses:
    
    exchangeAgents OBJECT IDENTIFIER ::= { dlp 1 }   
    
    exchangeAgentsTable OBJECT-TYPE
     		SYNTAX  SEQUENCE OF ExchangeAgentsStatusEntry
		MAX-ACCESS  not-accessible
		STATUS  current
		DESCRIPTION
			"Table for exchange agents status"
		::= {exchangeAgents 1 }
                                      
                                 
	exchangeAgentsStatusEntry OBJECT-TYPE
	  		SYNTAX  ExchangeAgentsStatusEntry
	  		MAX-ACCESS  not-accessible
	  		STATUS  current
	  		DESCRIPTION ""
	  		INDEX   { exchangeAgentsStatusTableIndex }
	  		::= { exchangeAgentsTable 1 }                                      
		
	ExchangeAgentsStatusEntry ::=
      		SEQUENCE {
      			exchangeAgentsStatusTableIndex		
		          			Unsigned32,  
				exchangeAgentName		          			
		          			DisplayString,
 				exchangeAgentStatus
		          			DisplayString,
		        exchangeAgentTotalMsg
		          			INTEGER,
		        exchangeAgentTotalScannedMsg
		          			INTEGER,
                exchangeAgentDroppedMsg
		          			INTEGER,
                exchangeAgentUpTime
		          			INTEGER,
                exchangeAgentTimeSinceLastMsg
		          			INTEGER,
                exchangeAgentQueueLen
		          			INTEGER,
                exchangeQueueLen
		          			INTEGER,
                exchangeAgentAvgTimePerMsg
		          			DisplayString,
                exchangeAgentAvgTimePerScannedMsg
		          			DisplayString, 
		        exchangeAgentVersion
		          			DisplayString,
		        exchangeCPUUsage
		          			DisplayString,
		        exchangeMemoryUsage
		          			DisplayString, 
		        exchangeAgentPolicyTimeStamp
		          			INTEGER           
		        }                  
	         
		exchangeAgentsStatusTableIndex OBJECT-TYPE
		              SYNTAX  Unsigned32
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Exchange agents: Table Index"
		              ::= { exchangeAgentsStatusEntry 1 }
		
		exchangeAgentName OBJECT-TYPE
		              SYNTAX  DisplayString
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Exchange agent name"
		              ::= { exchangeAgentsStatusEntry 2 }  
		              
		exchangeAgentStatus OBJECT-TYPE
		              SYNTAX  DisplayString
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Exchange agent status"
		              ::= { exchangeAgentsStatusEntry 3 }  

		exchangeAgentTotalMsg OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Exchange agent total messages"
		              ::= { exchangeAgentsStatusEntry 4 } 	    
		
		exchangeAgentTotalScannedMsg OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Exchange agent total scanned messages"
		              ::= { exchangeAgentsStatusEntry 5 }
		              
		exchangeAgentDroppedMsg OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Exchange agent dropped messages"
		              ::= { exchangeAgentsStatusEntry 6 }
		
		exchangeAgentUpTime OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Number of seconds that the exchange agent is up"
		              ::= { exchangeAgentsStatusEntry 7 }
		
		exchangeAgentTimeSinceLastMsg OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "Number of seconds since the last message passed through the exchange agent"
		              ::= { exchangeAgentsStatusEntry 8 }

		exchangeAgentQueueLen OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "The current exchange agent message queue length"
		              ::= { exchangeAgentsStatusEntry 9 }
        
        exchangeQueueLen OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "The cumulative exchange server queue length"
		              ::= { exchangeAgentsStatusEntry 10 }
		                                                
		                                                
		exchangeAgentAvgTimePerMsg OBJECT-TYPE
		              SYNTAX  DisplayString
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "The average (for all messages) latency added by the exchange agent"
		              ::= { exchangeAgentsStatusEntry 11 }                                               
		                                                
		exchangeAgentAvgTimePerScannedMsg OBJECT-TYPE
		              SYNTAX  DisplayString
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "The average (for scanned messages only) latency added by the exchange agent"
		              ::= { exchangeAgentsStatusEntry 12 }                                               
		
		exchangeAgentVersion OBJECT-TYPE
		              SYNTAX  DisplayString
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "The version of the exchange agent"
		              ::= { exchangeAgentsStatusEntry 13 }	                                              
		              
		exchangeCPUUsage OBJECT-TYPE
		              SYNTAX  DisplayString
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "The exchange server CPU usage in percentage"
		              ::= { exchangeAgentsStatusEntry 14 }
		              
		exchangeMemoryUsage OBJECT-TYPE
		              SYNTAX  DisplayString
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "The exchange server memory usage in percentage"
		              ::= { exchangeAgentsStatusEntry 15 }
		 	                                   
		exchangeAgentPolicyTimeStamp OBJECT-TYPE
		              SYNTAX  INTEGER
		              MAX-ACCESS read-only
		              STATUS  current 
		              DESCRIPTION 
		              		  "The time of the last policy retrieved by the exchange agent from the gateway"
		              ::= { exchangeAgentsStatusEntry 16 }
		
	dlpVersionString OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Version"
			::= {dlp 11 }
	
	dlpLicenseStatus OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"License status"
			::= {dlp 12 }
			
	dlpLdapStatus OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"LDAP Status"
			::= {dlp 13 }
	
	dlpTotalScans OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Traffic scans"
			::= {dlp 14 }
			
	dlpSMTPScans OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Scanned e-mails"
			::= {dlp 15 }
			
	dlpSMTPIncidents OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"E-mail incidents"
			::= {dlp 16 }
			
	dlpLastSMTPScan OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Last E-mail scan"
			::= {dlp 17 }
			
	dlpNumQuarantined OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Quarantined messages"
			::= {dlp 18 }
			
	dlpQrntMsgsSize OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Size of quarantined messages"
			::= {dlp 19 }
			
	dlpSentEMails OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Sent e-mails"
			::= {dlp 20 }
			
	dlpExpiredEMails OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Expired e-mails"
			::= {dlp 21 }
			
	dlpDiscardEMails OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Discarded e-mails"
			::= {dlp 22 }
			
	dlpPostfixQLen OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Postfix queue length"
			::= {dlp 23 }
			
	dlpPostfixErrors OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Postfix errors"
			::= {dlp 24 }
			
	dlpPostfixQOldMsg OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"E-mails in queue older than 1 hour"
			::= {dlp 25 }
			
	dlpPostfixQMsgsSz OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Size of messages in queue"
			::= {dlp 26 }
			
	dlpPostfixQFreeSp OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Free space in queue"
			::= {dlp 27 }
			
	dlpQrntFreeSpace OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Free space for quarantine"
			::= {dlp 28 }
			
	dlpQrntStatus OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Quarantine status"
			::= {dlp 29 }
			
	dlpHttpScans OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"HTTP scans"
			::= {dlp 30 }
			
	dlpHttpIncidents OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"HTTP incidents"
			::= {dlp 31 }
	
	dlpHttpLastScan OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"HTTP last scan"
			::= {dlp 32 }
	
	dlpFtpScans OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"FTP scans"
			::= {dlp 33 }
			
	dlpFtpIncidents OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"FTP incidents"
			::= {dlp 34 }
			
	dlpFtpLastScan OBJECT-TYPE
     		SYNTAX  DisplayString
		MAX-ACCESS read-only
		STATUS  current 
		DESCRIPTION
			"FTP last scan"
		::= {dlp 35 }
		
	dlpBypassStatus OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Bypass status"
			::= {dlp 36 }
				
	dlpUserCheckClnts OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"UserCheck clients"
			::= {dlp 37 }
			
	dlpLastPolStatus OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Last policy install status"
			::= {dlp 38 }
		
	dlpStatusCode OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"DLP Status code"
			::= {dlp 101 }
			
   dlpStatusShortDesc OBJECT-TYPE
     		SYNTAX  DisplayString
		MAX-ACCESS read-only
		STATUS  current 
		DESCRIPTION
			"DLP Status - Short Description"
		::= {dlp 102 }

   dlpStatusLongDesc OBJECT-TYPE
     		SYNTAX  DisplayString
		MAX-ACCESS read-only
		STATUS  current 
		DESCRIPTION
			"DLP Status  - Long Description"
		::= {dlp 103 }  

			
	--thresholds statuses:      
	
	  thresholdPolicy  OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "The name of the policy"
            ::= { thresholds 1 }                
                
                
      thresholdState  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "State of the thresholds engine: Ok (1), Error (0)"
            ::= { thresholds 2 }
            
      thresholdStateDesc  OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Description for the state"
            ::= { thresholds 3 } 
            
      thresholdEnabled  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Number of enabled thresholds (Monitoring statuses)"
            ::= { thresholds 4 } 
            
       thresholdActive  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Number of active thresholds (Generating alerts)"
            ::= { thresholds 5 }
            
       thresholdEventsSinceStartup  OBJECT-TYPE
            SYNTAX  INTEGER
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Number of activated thresholds since startup"
            ::= { thresholds 6 }     

                                                        	  
	  
	  thresholdActiveEventsTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  ThresholdActiveEventsEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION ""  
			   ::= { thresholds 7 }        
			   
			   
	  thresholdActiveEventsEntry OBJECT-TYPE
	          SYNTAX  ThresholdActiveEventsEntry
	          MAX-ACCESS  not-accessible
	          STATUS  current
	          DESCRIPTION ""
	          INDEX   { thresholdActiveEventsIndex }
	          ::= { thresholdActiveEventsTable 1 } 	
		
      ThresholdActiveEventsEntry ::=
              SEQUENCE {                               
                 thresholdActiveEventsIndex
                      Unsigned32,
                 thresholdActiveEventName
                      DisplayString,
                 thresholdActiveEventCategory
					  DisplayString,
				 thresholdActiveEventSeverity
					  INTEGER,
				 thresholdActiveEventSubject
					  DisplayString,
				 thresholdActiveEventSubjectValue
					  DisplayString,
				 thresholdActiveEventActivationTime
					  DisplayString,
				 thresholdActiveEventState
					  DisplayString
              }
              
      thresholdActiveEventsIndex OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { thresholdActiveEventsEntry 1 }   
            
      thresholdActiveEventName OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
			DESCRIPTION
	  		        "Name of the threshold"            
            ::= { thresholdActiveEventsEntry 2 } 
            
      thresholdActiveEventCategory OBJECT-TYPE
            SYNTAX  DisplayString 
            MAX-ACCESS read-only
            STATUS  current 
            DESCRIPTION
	  		        "The category of the threshold"
            ::= { thresholdActiveEventsEntry 3 }   
            
      thresholdActiveEventSeverity OBJECT-TYPE
            SYNTAX  INTEGER 
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Severity of the threshold: Cleared (0), Low (1), Medium (2), High (3), Critical (4)"
            ::= { thresholdActiveEventsEntry 4 }
            
       thresholdActiveEventSubject OBJECT-TYPE
            SYNTAX  DisplayString 
            MAX-ACCESS read-only
            STATUS  current 
            DESCRIPTION
	  		        "The name of the status been checked by the threshold"
            ::= { thresholdActiveEventsEntry 5 } 
            
       thresholdActiveEventSubjectValue OBJECT-TYPE
            SYNTAX  DisplayString 
            MAX-ACCESS read-only
            STATUS  current 
            DESCRIPTION
	  		        "The value of the status been checked by the threshold"
            ::= { thresholdActiveEventsEntry 6 }
            
       thresholdActiveEventActivationTime OBJECT-TYPE
            SYNTAX  DisplayString 
            MAX-ACCESS read-only
            STATUS  current 
            DESCRIPTION
	  		        "The activation time of the event"
            ::= { thresholdActiveEventsEntry 7 }  
          
       thresholdActiveEventState OBJECT-TYPE
            SYNTAX  DisplayString 
            MAX-ACCESS read-only
            STATUS  current 
            DESCRIPTION
	  		        "The state of the event [Active\Clearing]"
            ::= { thresholdActiveEventsEntry 8 }      
            
       thresholdDestinationsTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  ThresholdDestinationsEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION ""  
			   ::= { thresholds 8 }        
			   
			   
	  thresholdDestinationsEntry OBJECT-TYPE
	          SYNTAX  ThresholdDestinationsEntry
	          MAX-ACCESS  not-accessible
	          STATUS  current
	          DESCRIPTION ""
	          INDEX   { thresholdDestinationIndex }
	          ::= { thresholdDestinationsTable 1 } 	
		
      ThresholdDestinationsEntry ::=
              SEQUENCE {                               
                 thresholdDestinationIndex
                      Unsigned32,
                 thresholdDestinationName
                      DisplayString,
                 thresholdDestinationType
					  DisplayString,
				 thresholdSendingState
					  INTEGER,
				 thresholdSendingStateDesc
					  DisplayString,
				 thresholdAlertCount
					  INTEGER
              }
              
      thresholdDestinationIndex OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { thresholdDestinationsEntry 1 }   
            
      thresholdDestinationName OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
			DESCRIPTION
	  		        "Name of the destination"            
            ::= { thresholdDestinationsEntry 2 } 
            
      thresholdDestinationType OBJECT-TYPE
            SYNTAX  DisplayString 
            MAX-ACCESS read-only
            STATUS  current 
            DESCRIPTION
	  		        "The type of the destination"
            ::= { thresholdDestinationsEntry 3 }   
            
      thresholdSendingState OBJECT-TYPE
            SYNTAX  INTEGER 
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Alerts transmission status: Ok (0), Error (1)"
            ::= { thresholdDestinationsEntry 4 }
            
       thresholdSendingStateDesc OBJECT-TYPE
            SYNTAX  DisplayString 
            MAX-ACCESS read-only
            STATUS  current 
            DESCRIPTION
	  		        "Description for the alert transmission state"
            ::= { thresholdDestinationsEntry 5 } 
            
       thresholdAlertCount OBJECT-TYPE
            SYNTAX  INTEGER 
            MAX-ACCESS read-only
            STATUS  current 
            DESCRIPTION
	  		        "Number of alerts transmitted to the destination"
            ::= { thresholdDestinationsEntry 6 }  
            
       thresholdErrorsTable OBJECT-TYPE
	          SYNTAX  SEQUENCE OF  ThresholdErrorsEntry
			  MAX-ACCESS  not-accessible
			  STATUS  current  
			  DESCRIPTION ""  
			   ::= { thresholds 9 }        
			   
			   
	  thresholdErrorsEntry OBJECT-TYPE
	          SYNTAX  ThresholdErrorsEntry
	          MAX-ACCESS  not-accessible
	          STATUS  current
	          DESCRIPTION ""
	          INDEX   { thresholdErrorIndex }
	          ::= { thresholdErrorsTable 1 } 	
		
      ThresholdErrorsEntry ::=
              SEQUENCE {                               
                 thresholdErrorIndex
                      Unsigned32,
                 thresholdName
                      DisplayString,
                 thresholdThresholdOID
					  DisplayString,
				 thresholdErrorDesc
					  DisplayString,
				 thresholdErrorTime
					  DisplayString
			}
              
      thresholdErrorIndex OBJECT-TYPE
            SYNTAX  Unsigned32
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION ""
            ::= { thresholdErrorsEntry 1 }   
            
      thresholdName OBJECT-TYPE
            SYNTAX  DisplayString
            MAX-ACCESS read-only
            STATUS  current
			DESCRIPTION
	  		        "Name of the threshold"            
            ::= { thresholdErrorsEntry 2 } 
            
      thresholdThresholdOID OBJECT-TYPE
            SYNTAX  DisplayString 
            MAX-ACCESS read-only
            STATUS  current 
            DESCRIPTION
	  		        "OID of the thresholds alert"
            ::= { thresholdErrorsEntry 3 }   
            
      thresholdErrorDesc OBJECT-TYPE
            SYNTAX  DisplayString 
            MAX-ACCESS read-only
            STATUS  current
            DESCRIPTION
	  		        "Description of the error"
            ::= { thresholdErrorsEntry 4 }
            
       thresholdErrorTime OBJECT-TYPE
            SYNTAX  DisplayString 
            MAX-ACCESS read-only
            STATUS  current 
            DESCRIPTION
	  		        "The occurrence time of the error"
            ::= { thresholdErrorsEntry 5 } 
            
            
	--advanced urlf statuses:
    
    advancedUrlFilteringSubscription OBJECT IDENTIFIER ::= { advancedUrlFiltering 1 }   
			
	advancedUrlFilteringSubscriptionStatus OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Subscription status
				 Possible values include:
	           	 valid, expired, about-to-expire, not-associated, unknown"
			::= {advancedUrlFilteringSubscription 1 }
			
	advancedUrlFilteringSubscriptionExpDate OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Application Control subscription expiration date"
			::= {advancedUrlFilteringSubscription 2 }

    advancedUrlFilteringSubscriptionDesc OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Subscription description"
			::= {advancedUrlFilteringSubscription 3 }
			
	advancedUrlFilteringUpdate OBJECT IDENTIFIER ::= { advancedUrlFiltering 2 }   
			
	advancedUrlFilteringUpdateStatus OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Update status
				 Possible values include:
	           	 failed, up-to-date, new, degrade, unknown"
			::= {advancedUrlFilteringUpdate 1 }
			
	advancedUrlFilteringUpdateDesc OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Update description"
			::= {advancedUrlFilteringUpdate 2 }

    advancedUrlFilteringNextUpdate OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Next update description"
			::= {advancedUrlFilteringUpdate 3 }
			
	advancedUrlFilteringVersion OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Url Filtering database version"
			::= {advancedUrlFilteringUpdate 4 }
							
	advancedUrlFilteringRADStatus OBJECT IDENTIFIER ::= { advancedUrlFiltering 3 }
	
	advancedUrlFilteringRADStatusCode OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"RAD Status code"
			::= {advancedUrlFilteringRADStatus 1 }
   
			
	advancedUrlFilteringRADStatusDesc OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"RAD status description"
			::= {advancedUrlFilteringRADStatus 2 }
							
	advancedUrlFilteringStatusCode OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Application Control Status code"
			::= {advancedUrlFiltering 101 }
			
   advancedUrlFilteringStatusShortDesc OBJECT-TYPE
     		SYNTAX  DisplayString
		MAX-ACCESS read-only
		STATUS  current 
		DESCRIPTION
			"Application Control Status - Short Description"
		::= {advancedUrlFiltering 102 }

   advancedUrlFilteringStatusLongDesc OBJECT-TYPE
     		SYNTAX  DisplayString
		MAX-ACCESS read-only
		STATUS  current 
		DESCRIPTION
			"Application Control Status  - Long Description"
		::= {advancedUrlFiltering 103 }  
		
  	--AMW statuses:
    
    antiBotSubscription OBJECT IDENTIFIER ::= { amw 2 }   
			
	antiBotSubscriptionStatus OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti-Bot subscription status
				 Possible values include:
	           	 valid, expired, about-to-expire, not-associated, unknown"
			::= {antiBotSubscription 1 }
			
	antiBotSubscriptionExpDate OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti-Bot subscription expiration date"
			::= {antiBotSubscription 2 }

    antiBotSubscriptionDesc OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti-Bot subscription description"
			::= {antiBotSubscription 3 }
			
	antiVirusSubscription OBJECT IDENTIFIER ::= { amw 3 }   
			
	antiVirusSubscriptionStatus OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti-Virus subscription status
				 Possible values include:
	           	 valid, expired, about-to-expire, not-associated, unknown"
			::= {antiVirusSubscription 1 }
			
	antiVirusSubscriptionExpDate OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti-Virus subscription expiration date"
			::= {antiVirusSubscription 2 }

    antiVirusSubscriptionDesc OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti-Virus subscription description"
			::= {antiVirusSubscription 3 }

	antiSpamSubscription OBJECT IDENTIFIER ::= { amw 4 }   
			
	antiSpamSubscriptionStatus OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti-Spam subscription status
				 Possible values include:
	           	 valid, expired, about-to-expire, not-associated, unknown"
			::= {antiSpamSubscription 1 }
			
	antiSpamSubscriptionExpDate OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti-Spam subscription expiration date"
			::= {antiSpamSubscription 2 }

    antiSpamSubscriptionDesc OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti-Spam subscription description"
			::= {antiSpamSubscription 3 }
			
	amwABUpdate OBJECT IDENTIFIER ::= { amw 1 }   
			
	amwABUpdateStatus OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti-Malware update status
				 Possible values include:
	           	 failed, up-to-date, new, degrade, unknown"
			::= {amwABUpdate 1 }
			
	amwABUpdateDesc OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti-Malware update description"
			::= {amwABUpdate 2 }

    amwABNextUpdate OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti-Malware next update description"
			::= {amwABUpdate 3 }
			
	amwABVersion OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti-Malware database version"
			::= {amwABUpdate 4 }
			
	amwAVUpdate OBJECT IDENTIFIER ::= { amw 5 }   
			
	amwAVUpdateStatus OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti-Virus update status
				 Possible values include:
	           	 failed, up-to-date, new, degrade, unknown"
			::= {amwAVUpdate 1 }
			
	amwAVUpdateDesc OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti-Virus update description"
			::= {amwAVUpdate 2 }

    amwAVNextUpdate OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti-Virus next update description"
			::= {amwAVUpdate 3 }
			
	amwAVVersion OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti-Virus database version"
			::= {amwAVUpdate 4 }
							
	amwStatusCode OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti-Bot & Anti-Virus Status code"
			::= {amw 101 }
			
   amwStatusShortDesc OBJECT-TYPE
     		SYNTAX  DisplayString
		MAX-ACCESS read-only
		STATUS  current 
		DESCRIPTION
			"Anti-Bot & Anti-Virus Status - Short Description"
		::= {amw 102 }

   amwStatusLongDesc OBJECT-TYPE
     		SYNTAX  DisplayString
		MAX-ACCESS read-only
		STATUS  current 
		DESCRIPTION
			"Anti-Bot & Anti-Virus Status  - Long Description"
		::= {amw 103 }  

  	--Threat-Emulation statuses:
    
	teSubscriptionStatus OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Subscription status
				 Possible values include:
	           	 up-to-date, expired, about-to-expire, not-associated, unknown"
			::= {te 25 }
	
	teCloudSubscriptionStatus OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Cloud subscription status
				 Possible values include:
	           	 up-to-date, expired, about-to-expire, not-associated, unknown"
			::= {te 26 }
			
	teSubscriptionExpDate OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Threat-Emulation subscription expiration date"
			::= {te 20 }

    teSubscriptionDesc OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Threat-Emulation Subscription description"
			::= {te 27 }
			
	teUpdateStatus OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Anti-Malware update status
				 Possible values include:
	           	 failed, up-to-date, new, degrade, downloading, unknown"
			::= {te 16 }
			
	teUpdateDesc OBJECT-TYPE
      		SYNTAX  DisplayString
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Threat-Emulation update description"
			::= {te 17 }
	
	teStatusCode OBJECT-TYPE
      		SYNTAX  INTEGER
			MAX-ACCESS read-only
			STATUS  current 
			DESCRIPTION
				"Threat-Emulation Status code"
			::= {te 101 }
			
   teStatusShortDesc OBJECT-TYPE
     		SYNTAX  DisplayString
		MAX-ACCESS read-only
		STATUS  current 
		DESCRIPTION
			"Threat-Emulation Status - Short Description"
		::= {te 102 }

   teStatusLongDesc OBJECT-TYPE
     		SYNTAX  DisplayString
		MAX-ACCESS read-only
		STATUS  current 
		DESCRIPTION
			"Threat-Emulation Status  - Long Description"
		::= {te 103 }  
		
	--treatExtarction statuses:
	
	treatExtarctionSubscription OBJECT IDENTIFIER ::= { treatExtarction 1 }   
	
	treatExtarctionSubscriptionStatus OBJECT-TYPE
      		SYNTAX  DisplayString
		MAX-ACCESS  read-only
		STATUS  current 
		DESCRIPTION
			"Threat Extraction subscription status
			Possible values include:
	           	 valid, expired, about-to-expire, not-associated, unknown"
		::= {treatExtarctionSubscription 1 }
			
	treatExtarctionSubscriptionExpDate OBJECT-TYPE
      		SYNTAX  DisplayString
		MAX-ACCESS  read-only
		STATUS  current 
		DESCRIPTION
			"Threat Extraction subscription expiration date"
		::= {treatExtarctionSubscription 2 }

    	treatExtarctionSubscriptionDesc OBJECT-TYPE
      		SYNTAX  DisplayString
		MAX-ACCESS  read-only
		STATUS  current 
		DESCRIPTION
			"Threat Extraction subscription description"
		::= {treatExtarctionSubscription 3 }
										
	treatExtarctionStatistics OBJECT IDENTIFIER ::= { treatExtarction 2 }   

    	treatExtarctionTotalScannedAttachments OBJECT-TYPE
      		SYNTAX  INTEGER
		MAX-ACCESS  read-only
		STATUS  current 
		DESCRIPTION
			"Total scanned attachments"
		::= {treatExtarctionStatistics 1 }
		
    	treatExtarctionCleanedAttachments OBJECT-TYPE
      		SYNTAX  INTEGER
		MAX-ACCESS  read-only
		STATUS  current 
		DESCRIPTION
			"Cleaned attachments"
		::= {treatExtarctionStatistics 2 }

    	treatExtarctionOriginalAttachmentsAccesses OBJECT-TYPE
      		SYNTAX  INTEGER
		MAX-ACCESS  read-only
		STATUS  current 
		DESCRIPTION
			"Original attachments accesses"
		::= {treatExtarctionStatistics 3 }
										
	treatExtarctionStatusCode OBJECT-TYPE
      		SYNTAX  INTEGER
		MAX-ACCESS  read-only
		STATUS  current 
		DESCRIPTION
			"Threat Extraction Status code"
		::= {treatExtarction 101 }
			
	treatExtarctionStatusShortDesc OBJECT-TYPE
     		SYNTAX  DisplayString
		MAX-ACCESS  read-only
		STATUS  current 
		DESCRIPTION
			"Threat Extraction Status - Short Description"
		::= {treatExtarction 102 }

   	treatExtarctionStatusLongDesc OBJECT-TYPE
     		SYNTAX  DisplayString
		MAX-ACCESS  read-only
		STATUS  current 
		DESCRIPTION
			"Threat Extraction Status - Long Description"
		::= {treatExtarction 103 }  

END

