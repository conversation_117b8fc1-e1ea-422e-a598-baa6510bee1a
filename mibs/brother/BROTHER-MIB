-- Brother MIB
-- (C) Copyright 1999-2009 Brother Industries, Ltd. All rights reserved
-- ver.091.1

-- BROTHER OBJECT IDENTIFIER ::= { iso org(3) dod(6) internet(1) private(4) enterprises(1) 2435 }

BROTHER-MIB DEFINITIONS ::= BEGIN

IMPORTS
	enterprises, IpAddress, Counter
		FROM RFC1155-SMI
	DisplayString
		FROM RFC1213-MIB
	DateAndTime
		FROM SNMPv2-TC
	OBJECT-TYPE
		FROM RFC-1212
	Ipv6Address
		FROM IPV6-TC;



brother			OBJECT IDENTIFIER ::= { enterprises 2435 }
nm			OBJECT IDENTIFIER ::= { brother 2 }
system			OBJECT IDENTIFIER ::= { nm 3 }
interface			OBJECT IDENTIFIER ::= { nm 4 }
net-peripheral			OBJECT IDENTIFIER ::= { system 9 }
net-printer			OBJECT IDENTIFIER ::= { net-peripheral 1 }
net-MFP			OBJECT IDENTIFIER ::= { net-peripheral 2 }
netPML			OBJECT IDENTIFIER ::= { net-peripheral 4 }
generalDeviceStatus			OBJECT IDENTIFIER ::= { net-printer 1 }
fax-setup			OBJECT IDENTIFIER ::= { net-MFP 1 }
scanner-setup			OBJECT IDENTIFIER ::= { net-MFP 11 }
mfpCapability			OBJECT IDENTIFIER ::= { net-MFP 101 }
mfpgeneral-setup			OBJECT IDENTIFIER ::= { net-MFP 111 }
netPMLmgmt			OBJECT IDENTIFIER ::= { netPML 2 }
status			OBJECT IDENTIFIER ::= { generalDeviceStatus 2 }
autodial			OBJECT IDENTIFIER ::= { fax-setup 1 }
fax-general			OBJECT IDENTIFIER ::= { fax-setup 2 }
onetouchDial			OBJECT IDENTIFIER ::= { autodial 1 }
speedDial			OBJECT IDENTIFIER ::= { autodial 2 }
scanToInfo			OBJECT IDENTIFIER ::= { scanner-setup 1 }
mcGeneral			OBJECT IDENTIFIER ::= { mfpCapability 1 }
mcFax			OBJECT IDENTIFIER ::= { mfpCapability 2 }
mcScanner			OBJECT IDENTIFIER ::= { mfpCapability 3 }
mcgRemoteSetup			OBJECT IDENTIFIER ::= { mcGeneral 11 }
mcfGeneral			OBJECT IDENTIFIER ::= { mcFax 1 }
mcfNetFaxShare			OBJECT IDENTIFIER ::= { mcFax 11 }
mcfNetPcFaxRx			OBJECT IDENTIFIER ::= { mcFax 12 }
mcfFaxInfomation			OBJECT IDENTIFIER ::= { mcFax 101 }
mcsNetScanner			OBJECT IDENTIFIER ::= { mcScanner 11 }
mcsNetSKy			OBJECT IDENTIFIER ::= { mcScanner 12 }
tonerlow			OBJECT IDENTIFIER ::= { status 10 }
device			OBJECT IDENTIFIER ::= { netPMLmgmt 1 }
destination-subsystem1			OBJECT IDENTIFIER ::= { device 1 }
sleep			OBJECT IDENTIFIER ::= { destination-subsystem1 1 }
autoc			OBJECT IDENTIFIER ::= { destination-subsystem1 2 }
simm			OBJECT IDENTIFIER ::= { destination-subsystem1 4 }
specification			OBJECT IDENTIFIER ::= { simm 1 }
simmkind0			OBJECT IDENTIFIER ::= { specification 1 }
simmkind1			OBJECT IDENTIFIER ::= { specification 2 }
simmkind2			OBJECT IDENTIFIER ::= { specification 3 }
simmkind3			OBJECT IDENTIFIER ::= { specification 4 }
bio1-explanation			OBJECT IDENTIFIER ::= { simm 3 }
determined			OBJECT IDENTIFIER ::= { bio1-explanation 1 }
destination-subsystem2			OBJECT IDENTIFIER ::= { device 2 }
printerjob			OBJECT IDENTIFIER ::= { destination-subsystem2 1 }
jobend			OBJECT IDENTIFIER ::= { printerjob 1 }
destination-subsystem3			OBJECT IDENTIFIER ::= { device 3 }
prt-setup			OBJECT IDENTIFIER ::= { destination-subsystem3 3 }
prt-condition			OBJECT IDENTIFIER ::= { prt-setup 1 }
destination-subsystem4			OBJECT IDENTIFIER ::= { device 4 }
print-engine			OBJECT IDENTIFIER ::= { destination-subsystem4 1 }
prt-density			OBJECT IDENTIFIER ::= { print-engine 1 }
status-prt-eng			OBJECT IDENTIFIER ::= { print-engine 2 }
tray			OBJECT IDENTIFIER ::= { print-engine 3 }
economy			OBJECT IDENTIFIER ::= { print-engine 6 }
manual			OBJECT IDENTIFIER ::= { tray 1 }
traysize			OBJECT IDENTIFIER ::= { tray 3 }
mp			OBJECT IDENTIFIER ::= { traysize 1 }
cassette			OBJECT IDENTIFIER ::= { traysize 2 }
cassette2			OBJECT IDENTIFIER ::= { traysize 3 }
cassette3			OBJECT IDENTIFIER ::= { traysize 4 }
cassette4			OBJECT IDENTIFIER ::= { traysize 5 }
brorg			OBJECT IDENTIFIER ::= { device 5 }
printersetup			OBJECT IDENTIFIER ::= { brorg 1 }
pagesetup			OBJECT IDENTIFIER ::= { brorg 2 }
fontsetup			OBJECT IDENTIFIER ::= { brorg 3 }
controlpanel			OBJECT IDENTIFIER ::= { brorg 4 }
printerinfomation			OBJECT IDENTIFIER ::= { brorg 5 }
printerstatus			OBJECT IDENTIFIER ::= { brorg 6 }
secret			OBJECT IDENTIFIER ::= { brorg 7 }
adminsetting			OBJECT IDENTIFIER ::= { brorg 8 }
general			OBJECT IDENTIFIER ::= { printersetup 1 }
advanced			OBJECT IDENTIFIER ::= { printersetup 2 }
mail			OBJECT IDENTIFIER ::= { printersetup 3 }
finisher			OBJECT IDENTIFIER ::= { printersetup 4 }
catch-tray			OBJECT IDENTIFIER ::= { printersetup 5 }
autoff			OBJECT IDENTIFIER ::= { advanced 7 }
buzzer			OBJECT IDENTIFIER ::= { advanced 22 }
smallPaperSize			OBJECT IDENTIFIER ::= { advanced 51 }
trayPriority			OBJECT IDENTIFIER ::= { advanced 52 }
carbonCopy			OBJECT IDENTIFIER ::= { advanced 53 }
mediaFix			OBJECT IDENTIFIER ::= { advanced 54 }
directprint			OBJECT IDENTIFIER ::= { advanced 60 }
pictbridge			OBJECT IDENTIFIER ::= { advanced 61 }
colorcorrection			OBJECT IDENTIFIER ::= { advanced 62 }
pcl			OBJECT IDENTIFIER ::= { pagesetup 1 }
ps			OBJECT IDENTIFIER ::= { pagesetup 2 }
gl			OBJECT IDENTIFIER ::= { pagesetup 3 }
epson			OBJECT IDENTIFIER ::= { pagesetup 4 }
ibm			OBJECT IDENTIFIER ::= { pagesetup 5 }
margin-p			OBJECT IDENTIFIER ::= { pcl 1 }
auto-p			OBJECT IDENTIFIER ::= { pcl 2 }
pen1			OBJECT IDENTIFIER ::= { gl 1 }
pen2			OBJECT IDENTIFIER ::= { gl 2 }
pen3			OBJECT IDENTIFIER ::= { gl 3 }
pen4			OBJECT IDENTIFIER ::= { gl 4 }
pen5			OBJECT IDENTIFIER ::= { gl 5 }
pen6			OBJECT IDENTIFIER ::= { gl 6 }
margin-e			OBJECT IDENTIFIER ::= { epson 1 }
auto-e			OBJECT IDENTIFIER ::= { epson 2 }
margin-i			OBJECT IDENTIFIER ::= { ibm 1 }
auto-i			OBJECT IDENTIFIER ::= { ibm 2 }
reset			OBJECT IDENTIFIER ::= { controlpanel 1 }
test			OBJECT IDENTIFIER ::= { controlpanel 2 }
panellock			OBJECT IDENTIFIER ::= { controlpanel 3 }
key			OBJECT IDENTIFIER ::= { controlpanel 4 }
panelinfo			OBJECT IDENTIFIER ::= { controlpanel 5 }
version			OBJECT IDENTIFIER ::= { printerinfomation 3 }
errorHistory			OBJECT IDENTIFIER ::= { printerinfomation 51 }
printPages			OBJECT IDENTIFIER ::= { printerinfomation 52 }
capability			OBJECT IDENTIFIER ::= { printerinfomation 53 }
countinfo			OBJECT IDENTIFIER ::= { printerinfomation 54 }
copies			OBJECT IDENTIFIER ::= { capability 1 }
orientation			OBJECT IDENTIFIER ::= { capability 2 }
paper			OBJECT IDENTIFIER ::= { capability 3 }
mediatype			OBJECT IDENTIFIER ::= { capability 4 }
resolution			OBJECT IDENTIFIER ::= { capability 5 }
pfkit			OBJECT IDENTIFIER ::= { countinfo 1 }
scancount			OBJECT IDENTIFIER ::= { countinfo 2 }
clockfunction			OBJECT IDENTIFIER ::= { adminsetting 1 }
npCard			OBJECT IDENTIFIER ::= { interface 3 }
npSys			OBJECT IDENTIFIER ::= { npCard 1 }
npConfig			OBJECT IDENTIFIER ::= { npSys 1 }
adminCapa			OBJECT IDENTIFIER ::= { npSys 99 }
userSetting			OBJECT IDENTIFIER ::= { npSys 100 }
verify			OBJECT IDENTIFIER ::= { npSys 101 }
npTcp			OBJECT IDENTIFIER ::= { npCard 6 }
lpd			OBJECT IDENTIFIER ::= { npTcp 99 }
banner			OBJECT IDENTIFIER ::= { lpd 1 }
npCtl			OBJECT IDENTIFIER ::= { npCard 7 }
etherN			OBJECT IDENTIFIER ::= { npCtl 99 }
eNet			OBJECT IDENTIFIER ::= { etherN 1 }
npPort			OBJECT IDENTIFIER ::= { npCard 13 }
funa			OBJECT IDENTIFIER ::= { npPort 10 }
npSet			OBJECT IDENTIFIER ::= { npCard 99 }
dns			OBJECT IDENTIFIER ::= { npSet 1 }
pushstatus			OBJECT IDENTIFIER ::= { npSet 2 }
pjl			OBJECT IDENTIFIER ::= { npSet 3 }
eMailReports			OBJECT IDENTIFIER ::= { npSet 4 }
pjlinfo			OBJECT IDENTIFIER ::= { pjl 1 }
priadmin			OBJECT IDENTIFIER ::= { pushstatus 2 }
secadmin			OBJECT IDENTIFIER ::= { pushstatus 3 }
brnetConfig			OBJECT IDENTIFIER ::= { npCard 1240 }
brconfig			OBJECT IDENTIFIER ::= { brnetConfig 1 }
brcontrol			OBJECT IDENTIFIER ::= { brnetConfig 2 }
brport			OBJECT IDENTIFIER ::= { brnetConfig 3 }
brservice			OBJECT IDENTIFIER ::= { brnetConfig 4 }
brprotocol			OBJECT IDENTIFIER ::= { brnetConfig 5 }
brfirmware			OBJECT IDENTIFIER ::= { brnetConfig 6 }
brlat			OBJECT IDENTIFIER ::= { brprotocol 1 }
brtcpip			OBJECT IDENTIFIER ::= { brprotocol 2 }
brnetware			OBJECT IDENTIFIER ::= { brprotocol 3 }
brappletalk			OBJECT IDENTIFIER ::= { brprotocol 4 }
brbanyan			OBJECT IDENTIFIER ::= { brprotocol 5 }
bremail			OBJECT IDENTIFIER ::= { brprotocol 6 }
brdlc			OBJECT IDENTIFIER ::= { brprotocol 7 }
brnetbeui			OBJECT IDENTIFIER ::= { brprotocol 8 }
bripp			OBJECT IDENTIFIER ::= { brprotocol 9 }
brntsend			OBJECT IDENTIFIER ::= { brprotocol 10 }
brnetConfigOpt			OBJECT IDENTIFIER ::= { npCard 2435 }
broriginalprotocol			OBJECT IDENTIFIER ::= { brnetConfigOpt 5 }
broriginaltcpip			OBJECT IDENTIFIER ::= { broriginalprotocol 2 }
broriginalftp			OBJECT IDENTIFIER ::= { broriginalprotocol 10 }
broriginalupnp			OBJECT IDENTIFIER ::= { broriginalprotocol 11 }
broriginalapipa			OBJECT IDENTIFIER ::= { broriginalprotocol 12 }
broriginalmdns			OBJECT IDENTIFIER ::= { broriginalprotocol 13 }
broriginalLAA			OBJECT IDENTIFIER ::= { broriginalprotocol 14 }
broriginalIPv6			OBJECT IDENTIFIER ::= { broriginalprotocol 15 }
broriginaltelnet			OBJECT IDENTIFIER ::= { broriginalprotocol 16 }
broriginalEWS			OBJECT IDENTIFIER ::= { broriginalprotocol 17 }
broriginalSNMP			OBJECT IDENTIFIER ::= { broriginalprotocol 18 }
broriginalldap			OBJECT IDENTIFIER ::= { broriginalprotocol 19 }
broriginalTFTP			OBJECT IDENTIFIER ::= { broriginalprotocol 20 }
broriginalHTTPS			OBJECT IDENTIFIER ::= { broriginalprotocol 21 }
broriginalLPD			OBJECT IDENTIFIER ::= { broriginalprotocol 22 }
broriginalRawPort			OBJECT IDENTIFIER ::= { broriginalprotocol 23 }
broriginalLLTD			OBJECT IDENTIFIER ::= { broriginalprotocol 24 }
broriginalWebServices			OBJECT IDENTIFIER ::= { broriginalprotocol 25 }
broriginalLLMNR			OBJECT IDENTIFIER ::= { broriginalprotocol 26 }
broriginalSecurity			OBJECT IDENTIFIER ::= { broriginalprotocol 100 }
broriginalinternetsetting			OBJECT IDENTIFIER ::= { brnetConfigOpt 10 }
broriginalproxy			OBJECT IDENTIFIER ::= { broriginalinternetsetting 1 }
broriginalOtherSetting			OBJECT IDENTIFIER ::= { brnetConfigOpt 20 }
broriginalJobTermination			OBJECT IDENTIFIER ::= { broriginalOtherSetting 1 }
broriginalSNMPTrap			OBJECT IDENTIFIER ::= { broriginalOtherSetting 2 }
broriginalLegacy			OBJECT IDENTIFIER ::= { broriginalOtherSetting 3 }
brSecurityGeneralStatus			OBJECT IDENTIFIER ::= { broriginalSecurity 1 }
brSecurityGeneralSetup			OBJECT IDENTIFIER ::= { broriginalSecurity 2 }
brSecurityDeviceNegotiation			OBJECT IDENTIFIER ::= { broriginalSecurity 10 }
wireless			OBJECT IDENTIFIER ::= { npCard 100 }
wlInfo			OBJECT IDENTIFIER ::= { wireless 1 }
wlCapability			OBJECT IDENTIFIER ::= { wlInfo 1 }
wlGeneralInfo			OBJECT IDENTIFIER ::= { wlInfo 2 }
wlNetSearch			OBJECT IDENTIFIER ::= { wlInfo 11 }
wlAOSS			OBJECT IDENTIFIER ::= { wlInfo 12 }
wlSES			OBJECT IDENTIFIER ::= { wlInfo 13 }
wlWPS			OBJECT IDENTIFIER ::= { wlInfo 14 }
wlSetup			OBJECT IDENTIFIER ::= { wireless 11 }
wlGeneral			OBJECT IDENTIFIER ::= { wlSetup 1 }
wlAdvanced			OBJECT IDENTIFIER ::= { wlSetup 5 }
wlAssociate			OBJECT IDENTIFIER ::= { wlSetup 11 }
wlWEP			OBJECT IDENTIFIER ::= { wlAssociate 101 }
wlWPA			OBJECT IDENTIFIER ::= { wlAssociate 102 }
wlTKIP			OBJECT IDENTIFIER ::= { wlAssociate 103 }
wlLEAP			OBJECT IDENTIFIER ::= { wlAssociate 104 }
wlStatus			OBJECT IDENTIFIER ::= { wireless 21 }
wlGeneralStatus			OBJECT IDENTIFIER ::= { wlStatus 1 }
npMultiCards			OBJECT IDENTIFIER ::= { interface 4 }
npMultiIFSet			OBJECT IDENTIFIER ::= { npMultiCards 99 }
brMultiIFdns			OBJECT IDENTIFIER ::= { npMultiIFSet 1 }
brnetMultiIFConfig			OBJECT IDENTIFIER ::= { npMultiCards 1240 }
brMultiIFconfig			OBJECT IDENTIFIER ::= { brnetMultiIFConfig 1 }
brMultiIFcontrol			OBJECT IDENTIFIER ::= { brnetMultiIFConfig 2 }
brMultiIFservice			OBJECT IDENTIFIER ::= { brnetMultiIFConfig 4 }
brMultiIFprotocol			OBJECT IDENTIFIER ::= { brnetMultiIFConfig 5 }
brMultiIFtcpip			OBJECT IDENTIFIER ::= { brMultiIFprotocol 2 }
brMultiIFnetbeui			OBJECT IDENTIFIER ::= { brMultiIFprotocol 8 }
brMultiIForiginalapipa			OBJECT IDENTIFIER ::= { brMultiIFprotocol 12 }
brMultiIForiginalLAA			OBJECT IDENTIFIER ::= { brMultiIFprotocol 14 }
brMultiIForiginalIPv6			OBJECT IDENTIFIER ::= { brMultiIFprotocol 15 }
brMultiIForiginalWebServices			OBJECT IDENTIFIER ::= { brMultiIFprotocol 16 }
funclock			OBJECT IDENTIFIER ::= { advanced 63 }
broriginalKerberos			OBJECT IDENTIFIER ::= { broriginalprotocol 27 }
broriginalCIFS			OBJECT IDENTIFIER ::= { broriginalprotocol 28 }
broriginalSNTP			OBJECT IDENTIFIER ::= { broriginalprotocol 29 }
firmwareupdatekeyword			OBJECT IDENTIFIER ::= { printerinfomation 55 }
autocountreset			OBJECT IDENTIFIER ::= { funclock 31 }
wlSimpleWizard			OBJECT IDENTIFIER ::= { wlInfo 15 }

brJamPlace  OBJECT-TYPE
	SYNTAX  INTEGER (0..4)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Location of JAM.
         0:No Jam
         1:Jam Trays
         2:Jam Inside
         3:Jam Rear
         4:Jam Duplex unit"
	::= { status 9 }

brToner1Low  OBJECT-TYPE
	SYNTAX  INTEGER (0..3)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Black Toner status.
         0:Toner Full
         1:Toner Low
         2:No Toner Cartridge
         3:Toner Empty"
	::= { tonerlow 1 }

brToner2Low  OBJECT-TYPE
	SYNTAX  INTEGER (0..3)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Cyan Toner status.
         0:Toner Ready
         1:Toner Low
         2:No Toner Cartridge
         3:Toner Empty"
	::= { tonerlow 2 }

brToner3Low  OBJECT-TYPE
	SYNTAX  INTEGER (0..3)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Magenta Toner status.
         0:Toner Ready
         1:Toner Low
         2:No Toner Cartridge
         3:Toner Empty"
	::= { tonerlow 3 }

brToner4Low  OBJECT-TYPE
	SYNTAX  INTEGER (0..3)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Yellow Toner status.
         0:Toner Ready
         1:Toner Low
         2:No Toner Cartridge
         3:Toner Empty"
	::= { tonerlow 4 }

brieee1284id  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"IEEE1284 device ID"
	::= { generalDeviceStatus 7 }

brttt1  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"unused"
	::= { generalDeviceStatus 10 }

brOneTouchDialCount  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Maximum number of One touch dial."
	::= { onetouchDial 1 }

brOneTouchDialTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrOneTouchDialEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents One touch dial."
	::= { onetouchDial 2 }

brOneTouchDialEntry  OBJECT-TYPE
	SYNTAX  BrOneTouchDialEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the OneTouchDial table. Rows cannot be created or deleted."
	INDEX  { brOneTouchDialIndex }
	::= { brOneTouchDialTable 1 }

BrOneTouchDialEntry ::= 
	SEQUENCE {
		brOneTouchDialIndex
			INTEGER,
		brOneTouchDialData
			OCTET STRING
	}

brOneTouchDialIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The One touch dial number."
	::= { brOneTouchDialEntry 1 }

brOneTouchDialData  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The One Touch dial data."
	::= { brOneTouchDialEntry 2 }

brSpeedDialCount  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Maximum number of Speed dial."
	::= { speedDial 1 }

brSpeedDialTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrSpeedDialEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents Speed dial."
	::= { speedDial 2 }

brSpeedDialEntry  OBJECT-TYPE
	SYNTAX  BrSpeedDialEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the speedDial table. Rows cannot be created or deleted."
	INDEX  { brSpeedDialIndex }
	::= { brSpeedDialTable 1 }

BrSpeedDialEntry ::= 
	SEQUENCE {
		brSpeedDialIndex
			INTEGER,
		brSpeedDialData
			OCTET STRING
	}

brSpeedDialIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The Speed dial number."
	::= { brSpeedDialEntry 1 }

brSpeedDialData  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Speed dial Data."
	::= { brSpeedDialEntry 2 }

brDialDataAllClear  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Clear the all data of Onetouch dial and Speed dial."
	::= { autodial 3 }

brFaxReceiveMode  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Fax Receive Mode
         0:Auto
         1:Manual
         2:FaxTel
         3:TAD"
	::= { fax-general 1 }

brRingDelayCount  OBJECT-TYPE
	SYNTAX  INTEGER (0..4)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Ring Delay Count"
	::= { fax-general 2 }

brOwnFaxNumber  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Own Fax Number"
	::= { fax-general 3 }

brRegisterKeyInfo  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Add the ScanTo information.
         If device receives GET request, device should reply fix value 
         TRUE(writable), FALSE(not writable) to the client."
	::= { scanToInfo 1 }

brUnRegisterKeyInfo  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Remove the ScanTo information.
         If device receives GET request, device should reply fix value 
         TRUE (writable), FALSE (not writable) to the client."
	::= { scanToInfo 2 }

brNetSKeyReceiverAddress  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..63) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"IP Address or the name of the S-Key Receiver."
	::= { scanToInfo 5 }

brNetRemoteSetUpSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Net Remote Setup function is not supported. 
         1 indicates the Net Remote Setup function is implemented by the firmware"
	::= { mcgRemoteSetup 1 }

brNetRemoteSetUpEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Net Remote Setup is not enabled.
         1 indicates that the Net Remote Setup is enabled.
         Writing a non-zero value will enable the Net Remote Setup
         if it is supported by the firmware."
	::= { mcgRemoteSetup 2 }

brFaxSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Fax function is not supported. 
         1 indicates the Fax function is implemented by the firmware"
	::= { mcfGeneral 1 }

brIFaxSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the IFax function is not supported. 
         1 indicates the IFax function is implemented by the firmware"
	::= { mcfGeneral 3 }

brNetFaxShareSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Net Fax Share is not supported. 
         1 indicates the Net Fax Share is implemented by the firmware"
	::= { mcfNetFaxShare 1 }

brNetFaxShareEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Net Fax Share is not enabled.
         1 indicates that the Net Fax Share is enabled.
         Writing a non-zero value will enable the Net Fax Share
         if it is supported by the firmware."
	::= { mcfNetFaxShare 2 }

brNetPcFaxRxSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Net PC Fax Rx is not supported. 
         1 indicates the Net PC Fax Rx is implemented by the firmware"
	::= { mcfNetPcFaxRx 1 }

brNetPcFaxRxEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Net PC Fax Rx is not enabled.
         1 indicates that the Net PC Fax Rx is enabled.
         Writing a non-zero value will enable the Net PC Fax Rx
         if it is supported by the firmware."
	::= { mcfNetPcFaxRx 2 }

brNetRegisterPcFaxInfo  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Add the PcFax information.
         If device receives GET request, device should reply fix value 
         TRUE(writable), FALSE(not writable) to the client."
	::= { mcfNetPcFaxRx 3 }

brPhoneNumberLastFax  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..20) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Phone Number of Last Fax"
	::= { mcfFaxInfomation 1 }

brPagesSentLastFax  OBJECT-TYPE
	SYNTAX  Counter
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Pages Sent in Last Fax"
	::= { mcfFaxInfomation 2 }

brTimestampLastFax  OBJECT-TYPE
	SYNTAX  DateAndTime
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Timestamp of Last Fax"
	::= { mcfFaxInfomation 3 }

brFaxHeaderInfo  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..50) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Fax Header Infomation"
	::= { mcfFaxInfomation 4 }

brNetScannerSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Net Scanner is not supported. 
         1 indicates the Net Scanner is implemented by the firmware"
	::= { mcsNetScanner 1 }

brNetScannerEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Net Scanner is not enabled.
         1 indicates that the Net Scanner is enabled.
         Writing a non-zero value will enable the Net Scanner
         if it is supported by the firmware."
	::= { mcsNetScanner 2 }

brNetSKeySupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Net S-Key is not supported. 
         1 indicates the Net S-Key is implemented by the firmware"
	::= { mcsNetSKy 1 }

brNetSKeyEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Net S-Key is not enabled.
         1 indicates that the Net S-Key is enabled.
         Writing a non-zero value will enable the Net S-Key
         if it is supported by the firmware."
	::= { mcsNetSKy 2 }

brServiceMode  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Service Mode"
	::= { mfpgeneral-setup 1 }

brLockMode  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Lock Mode
         0:normal
         1:Customer
         2:Lock"
	::= { mfpgeneral-setup 2 }

brActivityReportSetting  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Activity Report Setting
         0:off
         1:every 1hour
         3:every 3hour
         6:every 6hour
         12:every 12hour
         24:every 24hour
         48:every 2day
         96:every 4day
         240:every Sunday
         241:every Monday
         242:every Tueseday
         243:every Wednesday
         244:every Thurseday
         245:every Friday
         246:every Saturday
         255:interval 50FAX"
	::= { mfpgeneral-setup 3 }

brpowerstime  OBJECT-TYPE
	SYNTAX  INTEGER (1..2147483647)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"PowerSave Timeout value. 1 to 2147483647 (sec)"
	::= { sleep 1 }

brautocont  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Auto Continue.
         1:Off
         2:On"
	::= { autoc 7 }

brsimmtype0  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"SIMM type on slot0.
         0:  unconfirmed : 0 MB
         1:  empty
         2:  unconfirmed
         3:  No Supported : 0 MB
         4:  Read Only Memory
         5:  Volatility RAM : size MB
         6:  Un Volatility RAM : size MB
         7:  Flash Memory : size MB
         8:  Disk Drive : 0 MB
         9:  RAM/ROM : 0MB
         10: Loading Paper Device
         11: Out Paper Device
         12: unconfirmed : 0 MB  ato ha onaji"
	::= { simmkind0 4 }

brsimmsize0  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"SIMM size on slot0"
	::= { simmkind0 5 }

brsimmtype1  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"SIMM type on slot1.
         0:  unconfirmed : 0 MB
         1:  empty
         2:  unconfirmed
         3:  No Supported : 0 MB
         4:  Read Only Memory
         5:  Volatility RAM : size MB
         6:  Un Volatility RAM : size MB
         7:  Flash Memory : size MB
         8:  Disk Drive : 0 MB
         9:  RAM/ROM : 0MB
         10: Loading Paper Device
         11: Out Paper Device
         12: unconfirmed : 0 MB  ato ha onaji"
	::= { simmkind1 4 }

brsimmsize1  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"SIMM size on slot1"
	::= { simmkind1 5 }

brsimmtype2  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"SIMM type on slot2.
         0:  unconfirmed : 0 MB
         1:  empty
         2:  unconfirmed
         3:  No Supported : 0 MB
         4:  Read Only Memory
         5:  Volatility RAM : size MB
         6:  Un Volatility RAM : size MB
         7:  Flash Memory : size MB
         8:  Disk Drive : 0 MB
         9:  RAM/ROM : 0MB
         10: Loading Paper Device
         11: Out Paper Device
         12: unconfirmed : 0 MB  ato ha onaji"
	::= { simmkind2 4 }

brsimmsize2  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"SIMM size on slot2."
	::= { simmkind2 5 }

brsimmtype3  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"SIMM type on slot3.
         0:  unconfirmed : 0 MB
         1:  empty
         2:  unconfirmed
         3:  No Supported : 0 MB
         4:  Read Only Memory
         5:  Volatility RAM : size MB
         6:  Un Volatility RAM : size MB
         7:  Flash Memory : size MB
         8:  Disk Drive : 0 MB
         9:  RAM/ROM : 0MB
         10: Loading Paper Device
         11: Out Paper Device
         12: unconfirmed : 0 MB  ato ha onaji"
	::= { simmkind3 4 }

brsimmsize3  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"SIMM type on slot3."
	::= { simmkind3 5 }

brTBD1  OBJECT-TYPE
	SYNTAX  INTEGER (1..15)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"unused"
	::= { determined 4 }

brtimeout  OBJECT-TYPE
	SYNTAX  INTEGER (1..2147483647)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Job Timeout. 1 to 2147483647(sec)"
	::= { jobend 1 }

brTBD2  OBJECT-TYPE
	SYNTAX  INTEGER (1..15)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"unused"
	::= { jobend 5 }

brpersonality  OBJECT-TYPE
	SYNTAX  INTEGER {
		pcl(1),
		hpgl(2),
		ps(4),
		auto(5),
		ibm(6),
		epson(7),
		hbp(8)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Printer Emulation setting.
         1:PCL
         2:HPGL
         4:PS
         5:AUTO
         6:IBM
         7:EPSON
         8:HBP"
	::= { prt-condition 1 }

brorientation  OBJECT-TYPE
	SYNTAX  INTEGER (0..4)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Default Paper Orientation.
         1:portrait
         2:landscape"
	::= { prt-condition 2 }

brcopies  OBJECT-TYPE
	SYNTAX  INTEGER (1..999)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Default Copy Pages. 1-999 pages"
	::= { prt-condition 4 }

brTBD3  OBJECT-TYPE
	SYNTAX  INTEGER (1..15)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"unused"
	::= { prt-condition 6 }

brresolution  OBJECT-TYPE
	SYNTAX  INTEGER (0..1200)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Default Resolution. 300, 600 or 1200dpi"
	::= { prt-condition 8 }

brpageprotect  OBJECT-TYPE
	SYNTAX  INTEGER (0..6)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Page Protect mode
         1:Off
         3:Auto
         4:Letter
         5:Legal
         6:A4"
	::= { prt-condition 10 }

brlines  OBJECT-TYPE
	SYNTAX  INTEGER (5..128)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"lines per page. 5-128"
	::= { prt-condition 11 }

brpaper  OBJECT-TYPE
	SYNTAX  INTEGER {
		executive(1),
		letter(2),
		legal(3),
		a6(24),
		a5(25),
		a4(26),
		a3ISO(27),
		b5JIS(45),
		b4JIS(46),
		monarch(80),
		com10(81),
		dl(90),
		c5(91),
		b6(99),
		b5(100),
		ledger(890),
		a3PLUS(891),
		letterShortEdge(892),
		a4ShortEdge(893),
		a4LONG(894),
		executiveShortEdge(895),
		b5ISOShortEdge(896),
		custom(897),
		a4Letter(898),
		b5Executive(899),
		envelopes(900),
		dll(901),
		hagaki(902),
		folio(903),
		organaizerJ(904),
		organaizerK(905),
		organaizerL(906),
		organaizerM(907),
		userdefined(908),
		detectsensor(911),
		inches3x5(920),
		envelopesY4(921),
		largestEnvelopesTheWest(922),
		prc16k195x270(925),
		a5l(923),
		b6JIS(924),
		prc16k184x260(926),
		prc16k197x273(927),
		auto(1001)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Default Paper size.
         -1:No Casette
         1:Executive
         2:Letter
         3:Legal
         24:A6
         25:A5
         26:A4
         27:A3(ISO)
         45:B5(JIS)
         46:B4(JIS)
         80:Monarch
         81:COM10
         90:DL
         91:C5
         99:B6
         100:B5
         890:LEDGER
         891:A3PLUS(13x19)
         892:Letter Short Edge
         893:A4 Short Edge
         894:A4LONG
         895:Executive Short Edge
         896:B5(ISO) Short Edge
         897:CUSTOM (ANY)
         898:A4/Letter
         899:B5/Executive
         900:Envelopes
         901:DL-L
         902:HAGAKI
         903:FOLIO
         904:ORGANIZER J
         905:ORGANIZER K
         906:ORGANIZER L
         907:ORGANIZER M
         908:USER DEFINED
         911:Detect Sensor
         920:3x5(inches)
         921:Envelopes Y4
         922:Largest envelopes of the West
         923:A5L
         924:B6JIS
         925:16K195x270
         926:16K184x260
         927:197x273
         999:Other
         1000:No Casette
         1001:Auto"
	::= { prt-condition 13 }

brpapertype  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Default Media type.
         0:Regular
         1:Thick paper
         2:Thicker paper
         3:Transparency
         4:Thin paper
         5:Bond paper
         6:ENVELOPES
         7:ENV. THICK
         8:ENV. THIN
         9:RECYCLED
         10:HAGAKI
         11:LABEL
         1nd (default) Tray PaperType"
	::= { prt-condition 22 }

brpapertype2  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Default Media type.
         0:Regular
         1:Thick paper
         2:Thicker paper
         3:Transparency
         4:Thin paper
         5:Bond paper
         6:ENVELOPES
         7:ENV. THICK
         8:ENV. THIN
         9:RECYCLED
         10:HAGAKI
         11:LABEL
         2nd Tray PaperType"
	::= { prt-condition 23 }

brpapertypeMP  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Default Media type.
         0:Regular
         1:Thick paper
         2:Thicker paper
         3:Transparency
         4:Thin paper
         5:Bond paper
         6:ENVELOPES
         7:ENV. THICK
         8:ENV. THIN
         9:RECYCLED
         10:HAGAKI
         11:LABEL
         1nd (default) Tray PaperType"
	::= { prt-condition 26 }

brdensity  OBJECT-TYPE
	SYNTAX  INTEGER (-15..15)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Print Density. -15..15"
	::= { prt-density 5 }

brmanualfeed  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Manualfeed mode.
         1:Off
         2:On"
	::= { manual 4 }

brmpsize  OBJECT-TYPE
	SYNTAX  INTEGER {
		executive(1),
		letter(2),
		legal(3),
		a6(24),
		a5(25),
		a4(26),
		a3ISO(27),
		b5JIS(45),
		b4JIS(46),
		monarch(80),
		com10(81),
		dl(90),
		c5(91),
		b6(99),
		b5(100),
		ledger(890),
		a3PLUS(891),
		letterShortEdge(892),
		a4ShortEdge(893),
		a4LONG(894),
		executiveShortEdge(895),
		b5ISOShortEdge(896),
		custom(897),
		a4Letter(898),
		b5Executive(899),
		envelopes(900),
		dll(901),
		hagaki(902),
		folio(903),
		organaizerJ(904),
		organaizerK(905),
		organaizerL(906),
		organaizerM(907),
		userdefined(908),
		detectsensor(911),
		inches3x5(920),
		envelopesY4(921),
		largestEnvelopesTheWest(922)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"MP Tray paper size
         -1:No Casette
         1:Executive
         2:Letter
         3:Legal
         24:A6
         25:A5
         26:A4
         27:A3(ISO)
         45:B5(JIS)
         46:B4(JIS)
         80:Monarch
         81:COM10
         90:DL
         91:C5
         99:B6
         100:B5
         890:LEDGER
         891:A3PLUS(13x19)
         892:Letter Short Edge
         893:A4 Short Edge
         894:A4LONG
         895:Executive Short Edge
         896:B5(ISO) Short Edge
         897:CUSTOM
         898:A4/Letter
         899:B5/Executive
         900:Envelopes
         901:DL-L
         902:HAGAKI
         903:FOLIO
         904:ORGANIZER J
         905:ORGANIZER K
         906:ORGANIZER L
         907:ORGANIZER M
         908:USER DEFINED
         911:Detect Sensor"
	::= { mp 1 }

brtray1size  OBJECT-TYPE
	SYNTAX  INTEGER {
		executive(1),
		letter(2),
		legal(3),
		a6(24),
		a5(25),
		a4(26),
		a3ISO(27),
		b5JIS(45),
		b4JIS(46),
		monarch(80),
		com10(81),
		dl(90),
		c5(91),
		b6(99),
		b5(100),
		ledger(890),
		a3PLUS(891),
		letterShortEdge(892),
		a4ShortEdge(893),
		a4LONG(894),
		executiveShortEdge(895),
		b5ISOShortEdge(896),
		custom(897),
		a4Letter(898),
		b5Executive(899),
		envelopes(900),
		dll(901),
		hagaki(902),
		folio(903),
		organaizerJ(904),
		organaizerK(905),
		organaizerL(906),
		organaizerM(907),
		userdefined(908),
		detectsensor(911),
		inches3x5(920),
		envelopesY4(921),
		largestEnvelopesTheWest(922)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Tray1 paper size
         -1:No Casette
         1:Executive
         2:Letter
         3:Legal
         24:A6
         25:A5
         26:A4
         27:A3(ISO)
         45:B5(JIS)
         46:B4(JIS)
         80:Monarch
         81:COM10
         90:DL
         91:C5
         99:B6
         100:B5
         890:LEDGER
         891:A3PLUS(13x19)
         892:Letter Short Edge
         893:A4 Short Edge
         894:A4LONG
         895:Executive Short Edge
         896:B5(ISO) Short Edge
         897:CUSTOM
         898:A4/Letter
         899:B5/Executive
         900:Envelopes
         901:DL-L
         902:HAGAKI
         903:FOLIO
         904:ORGANIZER J
         905:ORGANIZER K
         906:ORGANIZER L
         907:ORGANIZER M
         908:USER DEFINED
         911:Detect Sensor"
	::= { cassette 1 }

brtray2size  OBJECT-TYPE
	SYNTAX  INTEGER {
		executive(1),
		letter(2),
		legal(3),
		a6(24),
		a5(25),
		a4(26),
		a3ISO(27),
		b5JIS(45),
		b4JIS(46),
		monarch(80),
		com10(81),
		dl(90),
		c5(91),
		b6(99),
		b5(100),
		ledger(890),
		a3PLUS(891),
		letterShortEdge(892),
		a4ShortEdge(893),
		a4LONG(894),
		executiveShortEdge(895),
		b5ISOShortEdge(896),
		custom(897),
		a4Letter(898),
		b5Executive(899),
		envelopes(900),
		dll(901),
		hagaki(902),
		folio(903),
		organaizerJ(904),
		organaizerK(905),
		organaizerL(906),
		organaizerM(907),
		userdefined(908),
		detectsensor(911),
		inches3x5(920),
		envelopesY4(921),
		largestEnvelopesTheWest(922)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Tray2 paper size
         -1:No Casette
         1:Executive
         2:Letter
         3:Legal
         24:A6
         25:A5
         26:A4
         27:A3(ISO)
         45:B5(JIS)
         46:B4(JIS)
         80:Monarch
         81:COM10
         90:DL
         91:C5
         99:B6
         100:B5
         890:LEDGER
         891:A3PLUS(13x19)
         892:Letter Short Edge
         893:A4 Short Edge
         894:A4LONG
         895:Executive Short Edge
         896:B5(ISO) Short Edge
         897:CUSTOM
         898:A4/Letter
         899:B5/Executive
         900:Envelopes
         901:DL-L
         902:HAGAKI
         903:FOLIO
         904:ORGANIZER J
         905:ORGANIZER K
         906:ORGANIZER L
         907:ORGANIZER M
         908:USER DEFINED
         911:Detect Sensor"
	::= { cassette2 1 }

brtray3size  OBJECT-TYPE
	SYNTAX  INTEGER {
		executive(1),
		letter(2),
		legal(3),
		a6(24),
		a5(25),
		a4(26),
		a3ISO(27),
		b5JIS(45),
		b4JIS(46),
		monarch(80),
		com10(81),
		dl(90),
		c5(91),
		b6(99),
		b5(100),
		ledger(890),
		a3PLUS(891),
		letterShortEdge(892),
		a4ShortEdge(893),
		a4LONG(894),
		executiveShortEdge(895),
		b5ISOShortEdge(896),
		custom(897),
		a4Letter(898),
		b5Executive(899),
		envelopes(900),
		dll(901),
		hagaki(902),
		folio(903),
		organaizerJ(904),
		organaizerK(905),
		organaizerL(906),
		organaizerM(907),
		userdefined(908),
		detectsensor(911),
		inches3x5(920),
		envelopesY4(921),
		largestEnvelopesTheWest(922)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Tray3 paper size
         -1:No Casette
         1:Executive
         2:Letter
         3:Legal
         24:A6
         25:A5
         26:A4
         27:A3(ISO)
         45:B5(JIS)
         46:B4(JIS)
         80:Monarch
         81:COM10
         90:DL
         91:C5
         99:B6
         100:B5
         890:LEDGER
         891:A3PLUS(13x19)
         892:Letter Short Edge
         893:A4 Short Edge
         894:A4LONG
         895:Executive Short Edge
         896:B5(ISO) Short Edge
         897:CUSTOM
         898:A4/Letter
         899:B5/Executive
         900:Envelopes
         901:DL-L
         902:HAGAKI
         903:FOLIO
         904:ORGANIZER J
         905:ORGANIZER K
         906:ORGANIZER L
         907:ORGANIZER M
         908:USER DEFINED
         911:Detect Sensor"
	::= { cassette3 1 }

brtray4size  OBJECT-TYPE
	SYNTAX  INTEGER {
		executive(1),
		letter(2),
		legal(3),
		a6(24),
		a5(25),
		a4(26),
		a3ISO(27),
		b5JIS(45),
		b4JIS(46),
		monarch(80),
		com10(81),
		dl(90),
		c5(91),
		b6(99),
		b5(100),
		ledger(890),
		a3PLUS(891),
		letterShortEdge(892),
		a4ShortEdge(893),
		a4LONG(894),
		executiveShortEdge(895),
		b5ISOShortEdge(896),
		custom(897),
		a4Letter(898),
		b5Executive(899),
		envelopes(900),
		dll(901),
		hagaki(902),
		folio(903),
		organaizerJ(904),
		organaizerK(905),
		organaizerL(906),
		organaizerM(907),
		userdefined(908),
		detectsensor(911),
		inches3x5(920),
		envelopesY4(921),
		largestEnvelopesTheWest(922)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Tray4 paper size
         -1:No Casette
         1:Executive
         2:Letter
         3:Legal
         24:A6
         25:A5
         26:A4
         27:A3(ISO)
         45:B5(JIS)
         46:B4(JIS)
         80:Monarch
         81:COM10
         90:DL
         91:C5
         99:B6
         100:B5
         890:LEDGER
         891:A3PLUS(13x19)
         892:Letter Short Edge
         893:A4 Short Edge
         894:A4LONG
         895:Executive Short Edge
         896:B5(ISO) Short Edge
         897:CUSTOM
         898:A4/Letter
         899:B5/Executive
         900:Envelopes
         901:DL-L
         902:HAGAKI
         903:FOLIO
         904:ORGANIZER J
         905:ORGANIZER K
         906:ORGANIZER L
         907:ORGANIZER M
         908:USER DEFINED
         911:Detect Sensor"
	::= { cassette4 1 }

brret  OBJECT-TYPE
	SYNTAX  INTEGER (0..4)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"HRC mode.
         1:Off
         2:Bright
         3:Medium
         4:Dark"
	::= { economy 5 }

breconomode  OBJECT-TYPE
	SYNTAX  INTEGER (0..100)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Toner Save mode.
         100:Off
         0:On"
	::= { economy 7 }

brPrtGeneralEmulationTimeout  OBJECT-TYPE
	SYNTAX  INTEGER (1..99)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Emulation Timeout. 1- 99(sec)"
	::= { general 1 }

brPrtGeneralFeeder  OBJECT-TYPE
	SYNTAX  INTEGER (0..5)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Default Feeder mode.
         0:Auto
         1:MP Tray
         2:Tray1
         3:Tray2
         4:Tray3
         5:Tray4		"
	::= { general 2 }

brPrtGeneralPowerSave  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Power Save mode.
         1:Off
         2:On"
	::= { general 3 }

brPrtGeneralBuzzer  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Buzzer.
         1:Off
         2:On"
	::= { general 4 }

brPrtGeneralColor  OBJECT-TYPE
	SYNTAX  INTEGER (0..3)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Default Color mode
         1:Monochrome
         2:Color
         3:Auto"
	::= { general 5 }

brPrtGeneralDuplex  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Duplex mode
         1:Off(Simplex)
         2:On(Duplex)"
	::= { general 6 }

brPrtGeneralBinding  OBJECT-TYPE
	SYNTAX  INTEGER (0..3)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Binding type
         3:Short Edge
         2:Long Edge"
	::= { general 7 }

brPrtAdvancedPriority  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"FX/XL Priority.
         1:IBM XL
         2:EPSON FX"
	::= { advanced 1 }

brPrtAdvancedUseMPTrayFirst  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"MP Tray First
         1:No
         2:Yes"
	::= { advanced 2 }

brPrtAdvancedMPTrayFeed  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"MP Tray feed mode.
         1:Single
         2:Continuous"
	::= { advanced 3 }

brPrtAdvancedXOffset  OBJECT-TYPE
	SYNTAX  INTEGER (-500..500)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"X Offset. -500 to +500 pixel(600dpi)"
	::= { advanced 4 }

brPrtAdvancedYOffset  OBJECT-TYPE
	SYNTAX  INTEGER (-500..500)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Y Offset. -500 to +500 pixel(600dpi)"
	::= { advanced 5 }

brPrtAdvancedImageCompression  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Image Compression mode.
         0:Auto
         1:Off
         2:On"
	::= { advanced 6 }

brPrtAdvancedAutoFormFeed  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Auto Formfeed mode.
         1:Off
         2:On"
	::= { autoff 1 }

brPrtAdvancedAutoTimeout  OBJECT-TYPE
	SYNTAX  INTEGER (1..99)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Auto Formfeed timeout. 1-99 (sec)"
	::= { autoff 2 }

brPrtAdvancedFFSuppress  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"FF Suppress mode.
         1:Off
         2:On"
	::= { advanced 8 }

brPrtAdvancedTonerLowPrint  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Print mode on Toner Low.
         1:Stop
         2:Continue"
	::= { advanced 9 }

brPrtAdvancedPrintDensity  OBJECT-TYPE
	SYNTAX  INTEGER (0..15)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Print Density. 1-15"
	::= { advanced 10 }

brPrtAdvancedInputBuffer  OBJECT-TYPE
	SYNTAX  INTEGER (1..15)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Input Buffer size. 1-15 blocks"
	::= { advanced 11 }

brPrtAdvancedLanguage  OBJECT-TYPE
	SYNTAX  INTEGER {
		english(1),
		danish(2),
		dutch(3),
		finish(4),
		french(5),
		german(6),
		italian(7),
		norwegian(8),
		portuguse(9),
		swedish(10),
		spanish(11),
		turkish(12),
		polish(13),
		japanese(14),
		russian(15),
		czech(16),
		hungarian(17),
		romanian(18),
		bulgarian(19),
		slovakian(20),
		chinese(21),
		brazil(22)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"1:ENGLISH
         2:DANISH
         3:DUTCH
         4:FINNISH
         5:FRENCH
         6:GERMAN
         7:ITALIAN
         8:NORWEGIAN
         9:PORTUGUESE
         10:SWEDISH
         11:SPANISH
         12:TURKISH
         13:POLISH
         14:JAPANESE
         15:RUSSIAN
         16:CZECH
         17:HUNGARIAN
         18:ROMANIAN
         19:BULGARIAN
         20:SLOVAKIAN
         21:CHINESE
         22:BRAZIL"
	::= { advanced 12 }

brSecurePrintRAMSizeMax  OBJECT-TYPE
	SYNTAX  INTEGER (0..2147483647)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The Max of Secure print RAM Size"
	::= { advanced 13 }

brSecurePrintRAMSize  OBJECT-TYPE
	SYNTAX  INTEGER (0..2147483647)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Secure print RAM Size"
	::= { advanced 14 }

brPrtAdvancedJamRecovery  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		on(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Jam recovery setting"
	::= { advanced 15 }

brPrtAdvancedSleepIndication  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		dimmed(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"By enabling the Dimmed option, the status LED is dimmed while the printer is in sleep mode."
	::= { advanced 16 }

brPrtAdvancedDestination  OBJECT-TYPE
	SYNTAX  INTEGER {
		standardOutputTray(1),
		oct(2),
		octStack(3)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Destination"
	::= { advanced 17 }

brPrtAdvancedLowerLCD  OBJECT-TYPE
	SYNTAX  INTEGER {
		nonePage(1),
		counter(2),
		jobName(3)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Lower LCD"
	::= { advanced 18 }

brPrtAdvancedAutoOnline  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		on(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Auto Online"
	::= { advanced 19 }

brPrtAdvancedButtonRepeat  OBJECT-TYPE
	SYNTAX  INTEGER (1..20)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Button Repeat. 0.1-2.0"
	::= { advanced 20 }

brPrtAdvancedMessageScroll  OBJECT-TYPE
	SYNTAX  INTEGER (1..20)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Message Scroll. 1-10"
	::= { advanced 21 }

brPrtAdvancedLCDDensity  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"LCDDensity"
	::= { advanced 23 }

brPrtAdvancedErrorBuzzer  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		normal(2),
		special(3)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Error Buzzer"
	::= { buzzer 1 }

brPrtAdvancedPanelBuzzer  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		on(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Panel Buzzer"
	::= { buzzer 2 }

brPrtAdvancedBuzzerVolume  OBJECT-TYPE
	SYNTAX  INTEGER {
		high(1),
		low(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Buzzer Volume"
	::= { buzzer 3 }

brSmallPaperSizeMP  OBJECT-TYPE
	SYNTAX  INTEGER {
		executive(1),
		letter(2),
		legal(3),
		a6(24),
		a5(25),
		a4(26),
		a3ISO(27),
		b5JIS(45),
		b4JIS(46),
		monarch(80),
		com10(81),
		dl(90),
		c5(91),
		b6(99),
		b5(100),
		ledger(890),
		a3PLUS(891),
		letterShortEdge(892),
		a4ShortEdge(893),
		a4LONG(894),
		executiveShortEdge(895),
		b5ISOShortEdge(896),
		custom(897),
		a4Letter(898),
		b5Executive(899),
		envelopes(900),
		dll(901),
		hagaki(902),
		folio(903),
		organaizerJ(904),
		organaizerK(905),
		organaizerL(906),
		organaizerM(907),
		userdefined(908),
		detectsensor(911),
		inches3x5(920),
		envelopesY4(921),
		largestEnvelopesTheWest(922),
		noCasette(1000)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"MP Tray Small Paper size.
         1:Executive
         2:Letter
         3:Legal
         24:A6
         25:A5
         26:A4
         27:A3(ISO)
         45:B5(JIS)
         46:B4(JIS)
         80:Monarch
         81:COM10
         90:DL
         91:C5
         99:B6
         100:B5
         890:LEDGER
         891:A3PLUS(13x19)
         892:Letter Short Edge
         893:A4 Short Edge
         894:A4LONG
         895:Executive Short Edge
         896:B5(ISO) Short Edge
         897:CUSTOM
         898:A4/Letter
         899:B5/Executive
         900:Envelopes
         901:DL-L
         902:HAGAKI
         903:FOLIO
         904:ORGANIZER J
         905:ORGANIZER K
         906:ORGANIZER L
         907:ORGANIZER M
         908:USER DEFINED
         911:Detect Sensor
         1000:No Casette"
	::= { smallPaperSize 1 }

brSmallPaperSize1  OBJECT-TYPE
	SYNTAX  INTEGER {
		executive(1),
		letter(2),
		legal(3),
		a6(24),
		a5(25),
		a4(26),
		a3ISO(27),
		b5JIS(45),
		b4JIS(46),
		monarch(80),
		com10(81),
		dl(90),
		c5(91),
		b6(99),
		b5(100),
		ledger(890),
		a3PLUS(891),
		letterShortEdge(892),
		a4ShortEdge(893),
		a4LONG(894),
		executiveShortEdge(895),
		b5ISOShortEdge(896),
		custom(897),
		a4Letter(898),
		b5Executive(899),
		envelopes(900),
		dll(901),
		hagaki(902),
		folio(903),
		organaizerJ(904),
		organaizerK(905),
		organaizerL(906),
		organaizerM(907),
		userdefined(908),
		detectsensor(911),
		inches3x5(920),
		envelopesY4(921),
		largestEnvelopesTheWest(922),
		noCasette(1000)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Tray1 Small Paper size.
         1:Executive
         2:Letter
         3:Legal
         24:A6
         25:A5
         26:A4
         27:A3(ISO)
         45:B5(JIS)
         46:B4(JIS)
         80:Monarch
         81:COM10
         90:DL
         91:C5
         99:B6
         100:B5
         890:LEDGER
         891:A3PLUS(13x19)
         892:Letter Short Edge
         893:A4 Short Edge
         894:A4LONG
         895:Executive Short Edge
         896:B5(ISO) Short Edge
         897:CUSTOM
         898:A4/Letter
         899:B5/Executive
         900:Envelopes
         901:DL-L
         902:HAGAKI
         903:FOLIO
         904:ORGANIZER J
         905:ORGANIZER K
         906:ORGANIZER L
         907:ORGANIZER M
         908:USER DEFINED
         911:Detect Sensor
         1000:No Casette"
	::= { smallPaperSize 2 }

brSmallPaperSize2  OBJECT-TYPE
	SYNTAX  INTEGER {
		executive(1),
		letter(2),
		legal(3),
		a6(24),
		a5(25),
		a4(26),
		a3ISO(27),
		b5JIS(45),
		b4JIS(46),
		monarch(80),
		com10(81),
		dl(90),
		c5(91),
		b6(99),
		b5(100),
		ledger(890),
		a3PLUS(891),
		letterShortEdge(892),
		a4ShortEdge(893),
		a4LONG(894),
		executiveShortEdge(895),
		b5ISOShortEdge(896),
		custom(897),
		a4Letter(898),
		b5Executive(899),
		envelopes(900),
		dll(901),
		hagaki(902),
		folio(903),
		organaizerJ(904),
		organaizerK(905),
		organaizerL(906),
		organaizerM(907),
		userdefined(908),
		detectsensor(911),
		inches3x5(920),
		envelopesY4(921),
		largestEnvelopesTheWest(922),
		noCasette(1000)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Tray2 Small Paper size.
         1:Executive
         2:Letter
         3:Legal
         24:A6
         25:A5
         26:A4
         27:A3(ISO)
         45:B5(JIS)
         46:B4(JIS)
         80:Monarch
         81:COM10
         90:DL
         91:C5
         99:B6
         100:B5
         890:LEDGER
         891:A3PLUS(13x19)
         892:Letter Short Edge
         893:A4 Short Edge
         894:A4LONG
         895:Executive Short Edge
         896:B5(ISO) Short Edge
         897:CUSTOM
         898:A4/Letter
         899:B5/Executive
         900:Envelopes
         901:DL-L
         902:HAGAKI
         903:FOLIO
         904:ORGANIZER J
         905:ORGANIZER K
         906:ORGANIZER L
         907:ORGANIZER M
         908:USER DEFINED
         911:Detect Sensor
         1000:No Casette"
	::= { smallPaperSize 3 }

brSmallPaperSize3  OBJECT-TYPE
	SYNTAX  INTEGER {
		executive(1),
		letter(2),
		legal(3),
		a6(24),
		a5(25),
		a4(26),
		a3ISO(27),
		b5JIS(45),
		b4JIS(46),
		monarch(80),
		com10(81),
		dl(90),
		c5(91),
		b6(99),
		b5(100),
		ledger(890),
		a3PLUS(891),
		letterShortEdge(892),
		a4ShortEdge(893),
		a4LONG(894),
		executiveShortEdge(895),
		b5ISOShortEdge(896),
		custom(897),
		a4Letter(898),
		b5Executive(899),
		envelopes(900),
		dll(901),
		hagaki(902),
		folio(903),
		organaizerJ(904),
		organaizerK(905),
		organaizerL(906),
		organaizerM(907),
		userdefined(908),
		detectsensor(911),
		inches3x5(920),
		envelopesY4(921),
		largestEnvelopesTheWest(922),
		noCasette(1000)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Tray3 Small Paper size.
         1:Executive
         2:Letter
         3:Legal
         24:A6
         25:A5
         26:A4
         27:A3(ISO)
         45:B5(JIS)
         46:B4(JIS)
         80:Monarch
         81:COM10
         90:DL
         91:C5
         99:B6
         100:B5
         890:LEDGER
         891:A3PLUS(13x19)
         892:Letter Short Edge
         893:A4 Short Edge
         894:A4LONG
         895:Executive Short Edge
         896:B5(ISO) Short Edge
         897:CUSTOM
         898:A4/Letter
         899:B5/Executive
         900:Envelopes
         901:DL-L
         902:HAGAKI
         903:FOLIO
         904:ORGANIZER J
         905:ORGANIZER K
         906:ORGANIZER L
         907:ORGANIZER M
         908:USER DEFINED
         911:Detect Sensor
         1000:No Casette"
	::= { smallPaperSize 4 }

brSmallPaperSize4  OBJECT-TYPE
	SYNTAX  INTEGER {
		executive(1),
		letter(2),
		legal(3),
		a6(24),
		a5(25),
		a4(26),
		a3ISO(27),
		b5JIS(45),
		b4JIS(46),
		monarch(80),
		com10(81),
		dl(90),
		c5(91),
		b6(99),
		b5(100),
		ledger(890),
		a3PLUS(891),
		letterShortEdge(892),
		a4ShortEdge(893),
		a4LONG(894),
		executiveShortEdge(895),
		b5ISOShortEdge(896),
		custom(897),
		a4Letter(898),
		b5Executive(899),
		envelopes(900),
		dll(901),
		hagaki(902),
		folio(903),
		organaizerJ(904),
		organaizerK(905),
		organaizerL(906),
		organaizerM(907),
		userdefined(908),
		detectsensor(911),
		inches3x5(920),
		envelopesY4(921),
		largestEnvelopesTheWest(922),
		noCasette(1000)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Tray4 Small Paper size.
         1:Executive
         2:Letter
         3:Legal
         24:A6
         25:A5
         26:A4
         27:A3(ISO)
         45:B5(JIS)
         46:B4(JIS)
         80:Monarch
         81:COM10
         90:DL
         91:C5
         99:B6
         100:B5
         890:LEDGER
         891:A3PLUS(13x19)
         892:Letter Short Edge
         893:A4 Short Edge
         894:A4LONG
         895:Executive Short Edge
         896:B5(ISO) Short Edge
         897:CUSTOM
         898:A4/Letter
         899:B5/Executive
         900:Envelopes
         901:DL-L
         902:HAGAKI
         903:FOLIO
         904:ORGANIZER J
         905:ORGANIZER K
         906:ORGANIZER L
         907:ORGANIZER M
         908:USER DEFINED
         911:Detect Sensor
         1000:No Casette"
	::= { smallPaperSize 5 }

brPrtAdvancedTrayPriority  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The value of brTrayPriorityIndex corresponding to the default setting."
	::= { trayPriority 1 }

brTrayPriorityCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Maximum number of TrayPriority INDEX."
	::= { trayPriority 2 }

brTrayPriorityTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrTrayPriorityEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents TrayPriority setting."
	::= { trayPriority 3 }

brTrayPriorityEntry  OBJECT-TYPE
	SYNTAX  BrTrayPriorityEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the TrayPriority table. Rows cannot be created or deleted."
	INDEX  { brTrayPriorityIndex }
	::= { brTrayPriorityTable 1 }

BrTrayPriorityEntry ::= 
	SEQUENCE {
		brTrayPriorityIndex
			INTEGER,
		brTrayPriorityMember
			OCTET STRING
	}

brTrayPriorityIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..255)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number."
	::= { brTrayPriorityEntry 1 }

brTrayPriorityMember  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The TrayPriority member data.
         Format:
         valulen(1byte), value(valulen byte)"
	::= { brTrayPriorityEntry 2 }

brCarbonCopyMode  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		on(2),
		auto(3),
		parallel(4)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Carbon Copy mode setting."
	::= { carbonCopy 1 }

brCarbonCopies  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The copies setting of Carbon Copy function."
	::= { carbonCopy 2 }

brCarbonCopyTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrCarbonCopyEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents CarbonCopy setting."
	::= { carbonCopy 10 }

brCarbonCopyEntry  OBJECT-TYPE
	SYNTAX  BrCarbonCopyEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the CarbonCopy table. Rows cannot be created or deleted."
	INDEX  { brCarbonCopyIndex }
	::= { brCarbonCopyTable 1 }

BrCarbonCopyEntry ::= 
	SEQUENCE {
		brCarbonCopyIndex
			INTEGER,
		brCarbonCopyTray
			INTEGER,
		brCarbonCopyMacro
			INTEGER,
		brCarbonCopyMacroID
			INTEGER
	}

brCarbonCopyIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number."
	::= { brCarbonCopyEntry 1 }

brCarbonCopyTray  OBJECT-TYPE
	SYNTAX  INTEGER {
		tray1(1),
		tray2(2),
		tray3(3),
		tray4(4),
		mpTray(10),
		auto(11)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Copy Tray setting."
	::= { brCarbonCopyEntry 2 }

brCarbonCopyMacro  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		on(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Carbon Copy Macro Setting"
	::= { brCarbonCopyEntry 3 }

brCarbonCopyMacroID  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Carbon Copy Macro ID Setting"
	::= { brCarbonCopyEntry 4 }

brMediaFixTray1  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		regular(2),
		thick(3),
		thicker(4),
		transparency(5),
		thin(6),
		bond(7),
		envelopes(8),
		envThick(9),
		envThin(10),
		recycled(11)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Media Fix setting for tray1."
	::= { mediaFix 1 }

brMediaFixTray2  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		regular(2),
		thick(3),
		thicker(4),
		transparency(5),
		thin(6),
		bond(7),
		envelopes(8),
		envThick(9),
		envThin(10),
		recycled(11)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Media Fix setting for tray2."
	::= { mediaFix 2 }

brMediaFixTray3  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		regular(2),
		thick(3),
		thicker(4),
		transparency(5),
		thin(6),
		bond(7),
		envelopes(8),
		envThick(9),
		envThin(10),
		recycled(11)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Media Fix setting for tray3."
	::= { mediaFix 3 }

brMediaFixTray4  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		regular(2),
		thick(3),
		thicker(4),
		transparency(5),
		thin(6),
		bond(7),
		envelopes(8),
		envThick(9),
		envThin(10),
		recycled(11)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Media Fix setting for tray4."
	::= { mediaFix 4 }

brMediaFixMP  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		regular(2),
		thick(3),
		thicker(4),
		transparency(5),
		thin(6),
		bond(7),
		envelopes(8),
		envThick(9),
		envThin(10),
		recycled(11)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Media Fix setting for MPtray."
	::= { mediaFix 10 }

brDirectPrintPaperSize  OBJECT-TYPE
	SYNTAX  INTEGER {
		executive(1),
		letter(2),
		legal(3),
		a6(24),
		a5(25),
		a4(26),
		a3ISO(27),
		b5JIS(45),
		b4JIS(46),
		monarch(80),
		com10(81),
		dl(90),
		c5(91),
		b6(99),
		b5(100),
		ledger(890),
		a3PLUS(891),
		letterShortEdge(892),
		a4ShortEdge(893),
		a4LONG(894),
		executiveShortEdge(895),
		b5ISOShortEdge(896),
		custom(897),
		a4Letter(898),
		b5Executive(899),
		envelopes(900),
		dll(901),
		hagaki(902),
		folio(903),
		organaizerJ(904),
		organaizerK(905),
		organaizerL(906),
		organaizerM(907),
		userdefined(908),
		detectsensor(911),
		inches3x5(920),
		envelopesY4(921),
		largestEnvelopesTheWest(922),
		a5l(923),
		b6jis(924),
		prc16k195x270(925),
		prc16k184x260(926),
		prc16k197x273(927)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Default Paper size.
         -1:No Casette
         1:Executive
         2:Letter
         3:Legal
         24:A6
         25:A5
         26:A4
         27:A3(ISO)
         45:B5(JIS)
         46:B4(JIS)
         80:Monarch
         81:COM10
         90:DL
         91:C5
         99:B6
         100:B5
         890:LEDGER
         891:A3PLUS(13x19)
         892:Letter Short Edge
         893:A4 Short Edge
         894:A4LONG
         895:Executive Short Edge
         896:B5(ISO) Short Edge
         897:CUSTOM (ANY)
         898:A4/Letter
         899:B5/Executive
         900:Envelopes
         901:DL-L
         902:HAGAKI
         903:FOLIO
         904:ORGANIZER J
         905:ORGANIZER K
         906:ORGANIZER L
         907:ORGANIZER M
         908:USER DEFINED
         911:Detect Sensor
         920:3x5(inches)
         921:Envelopes Y4
         922:Largest envelopes of the West
         923:A5L
         924:B6JIS
         925:16K195x270
         926:16k184x260
         927:16k197x273"
	::= { directprint 1 }

brDirectPrintMediaType  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Default Media type.
         0:Regular
         1:Thick paper
         2:Thicker paper
         3:Transparency
         4:Thin paper
         5:Bond paper
         6:ENVELOPES
         7:ENV. THICK
         8:ENV. THIN
         9:RECYCLED
         10:HAGAKI
         11:LABEL
         1nd (default) Tray PaperType"
	::= { directprint 2 }

brDirectPrintMultipulPage  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { directprint 3 }

brDirectPrintOrientation  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { directprint 4 }

brDirectPrintCollate  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { directprint 5 }

brDirectPrintOutputColor  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { directprint 6 }

brDirectPrintPrintQuality  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { directprint 7 }

brDirectPrintPdfOption  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { directprint 8 }

brDirectPrintSetting  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		""
	::= { directprint 9 }

brDirectPrintPdfThumbnailType  OBJECT-TYPE
	SYNTAX  INTEGER {
		alternativeImage(1),
		reductionImage(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"PDF Thumbnail Type 
         1:AlternativeImage
         2:ReductionImage"
	::= { directprint 10 }

brPictBridgePaperSize  OBJECT-TYPE
	SYNTAX  INTEGER {
		executive(1),
		letter(2),
		legal(3),
		a6(24),
		a5(25),
		a4(26),
		a3ISO(27),
		b5JIS(45),
		b4JIS(46),
		monarch(80),
		com10(81),
		dl(90),
		c5(91),
		b6(99),
		b5(100),
		ledger(890),
		a3PLUS(891),
		letterShortEdge(892),
		a4ShortEdge(893),
		a4LONG(894),
		executiveShortEdge(895),
		b5ISOShortEdge(896),
		custom(897),
		a4Letter(898),
		b5Executive(899),
		envelopes(900),
		dll(901),
		hagaki(902),
		folio(903),
		organaizerJ(904),
		organaizerK(905),
		organaizerL(906),
		organaizerM(907),
		userdefined(908),
		detectsensor(911),
		inches3x5(920),
		envelopesY4(921),
		largestEnvelopesTheWest(922)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Default Paper size.
         -1:No Casette
         1:Executive
         2:Letter
         3:Legal
         24:A6
         25:A5
         26:A4
         27:A3(ISO)
         45:B5(JIS)
         46:B4(JIS)
         80:Monarch
         81:COM10
         90:DL
         91:C5
         99:B6
         100:B5
         890:LEDGER
         891:A3PLUS(13x19)
         892:Letter Short Edge
         893:A4 Short Edge
         894:A4LONG
         895:Executive Short Edge
         896:B5(ISO) Short Edge
         897:CUSTOM (ANY)
         898:A4/Letter
         899:B5/Executive
         900:Envelopes
         901:DL-L
         902:HAGAKI
         903:FOLIO
         904:ORGANIZER J
         905:ORGANIZER K
         906:ORGANIZER L
         907:ORGANIZER M
         908:USER DEFINED
         911:Detect Sensor
         920:3x5(inches)
         921:Envelopes Y4
         922:Largest envelopes of the West"
	::= { pictbridge 1 }

brPictBridgeOrientation  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { pictbridge 2 }

brPictBridgeDateTime  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { pictbridge 3 }

brPictBridgeFileName  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { pictbridge 4 }

brPictBridgePrintQuality  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { pictbridge 5 }

brPictBridgePrintSetting  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		""
	::= { pictbridge 6 }

brColorCalibration  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { colorcorrection 1 }

brColorCalibrationReset  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { colorcorrection 2 }

brAutoRegistRegistrate  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { colorcorrection 3 }

brAutoRegistSetInterval  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { colorcorrection 4 }

brRegistrationPrintChart  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { colorcorrection 5 }

brRegistrationXMagentaLeft  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { colorcorrection 6 }

brRegistrationXMagentaRight  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { colorcorrection 7 }

brRegistrationXCyanLeft  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { colorcorrection 8 }

brRegistrationXCyanRight  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { colorcorrection 9 }

brRegistrationXYellowLeft  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { colorcorrection 10 }

brRegistrationXYellowRight  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { colorcorrection 11 }

brRegistrationYMagenta  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { colorcorrection 12 }

brRegistrationYCyan  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { colorcorrection 13 }

brRegistrationYYellow  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { colorcorrection 14 }

brPrtMailbox  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Mailbox Installed of not"
	::= { mail 1 }

brPrtMailboxOutbin  OBJECT-TYPE
	SYNTAX  INTEGER (0..110)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Outbin"
	::= { mail 4 }

brPrtMailboxProtectGroup  OBJECT-TYPE
	SYNTAX  INTEGER (0..99)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Mailbox Protect"
	::= { mail 5 }

brPrtAvoidMailboxFullGroup  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Avoid Mailbox Full"
	::= { mail 6 }

brPrtMailboxProtectTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrPrtMailboxProtectEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"unused"
	::= { mail 2 }

brPrtMailboxProtectEntry  OBJECT-TYPE
	SYNTAX  BrPrtMailboxProtectEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"unused"
	INDEX  { brPrtMailboxProtectIndex }
	::= { brPrtMailboxProtectTable 1 }

BrPrtMailboxProtectEntry ::= 
	SEQUENCE {
		brPrtMailboxProtectIndex
			INTEGER,
		brPrtMailboxProtect
			INTEGER
	}

brPrtMailboxProtectIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"unused"
	::= { brPrtMailboxProtectEntry 1 }

brPrtMailboxProtect  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"unused"
	::= { brPrtMailboxProtectEntry 2 }

brPrtAvoidMailboxFullTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrPrtAvoidMailboxFullEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"unused"
	::= { mail 3 }

brPrtAvoidMailboxFullEntry  OBJECT-TYPE
	SYNTAX  BrPrtAvoidMailboxFullEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"unused"
	INDEX  { brPrtAvoidMailboxFullIndex }
	::= { brPrtAvoidMailboxFullTable 1 }

BrPrtAvoidMailboxFullEntry ::= 
	SEQUENCE {
		brPrtAvoidMailboxFullIndex
			INTEGER,
		brPrtAvoidMailboxFull
			INTEGER
	}

brPrtAvoidMailboxFullIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"unused"
	::= { brPrtAvoidMailboxFullEntry 1 }

brPrtAvoidMailboxFull  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"unused"
	::= { brPrtAvoidMailboxFullEntry 2 }

brPrtFinisher  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Finisher type
         0:no finisher
         1:FS5050
         2:FS5100"
	::= { finisher 1 }

brPrtCatchTray  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"catch tray Installed or not"
	::= { catch-tray 1 }

brPagePCLLeftMargin  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"PCL LeftMargin
         1270N	:0-145
         2400Ce	:0-176"
	::= { margin-p 1 }

brPagePCLRightMargin  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"PCL RightMargin
         1270N	:10-155
         2400Ce	:10-186"
	::= { margin-p 2 }

brPagePCLTopMargin  OBJECT-TYPE
	SYNTAX  INTEGER (0..200)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"PCL TopMargin
         0:0
         33:0.33
         50:0.5
         100:1.0
         150:1.5
         200:2.0"
	::= { margin-p 3 }

brPagePCLBottomMargin  OBJECT-TYPE
	SYNTAX  INTEGER (0..200)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"PCL BottomMargin
         0:0
         33:0.33
         50:0.5
         100:1.0
         150:1.5
         200:2.0"
	::= { margin-p 4 }

brPagePCLLines  OBJECT-TYPE
	SYNTAX  INTEGER (5..128)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"PCL Form Line 5-128"
	::= { margin-p 5 }

brPagePCLAutoLF  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"PCL AutoLF
         1:Off
         2:On"
	::= { auto-p 1 }

brPagePCLAutoCR  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"PCL AutoCR
         1:Off
         2:On"
	::= { auto-p 2 }

brPagePCLAutoWrap  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"PCL AutoWrap
         1:Off
         2:On"
	::= { auto-p 3 }

brPagePCLAutoSkip  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"PCL AutoSkip
         1:Off
         2:On"
	::= { auto-p 4 }

brPagePSPrintPSError  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		on(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"PrintPSError
         1:Off
         2:On"
	::= { ps 1 }

brPagePSKeepPCLFonts  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		on(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"KeepPCLFonts
         1:Off
         2:On"
	::= { ps 2 }

brPagePSCAPTsetting  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		on(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"PS CAPT setting
         1:Off
         2:On"
	::= { ps 3 }

brPageGLPen1Size  OBJECT-TYPE
	SYNTAX  INTEGER (0..16)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"GL PenSize 1-16"
	::= { pen1 1 }

brPageGLPen1GrayLevel  OBJECT-TYPE
	SYNTAX  INTEGER (0..5)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"GLPen GrayLevel
         0:15%
         1:30%
         2:45%
         3:75%
         4:90%
         5:100%"
	::= { pen1 2 }

brPageGLPen2Size  OBJECT-TYPE
	SYNTAX  INTEGER (0..16)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"GL PenSize 1-16"
	::= { pen2 1 }

brPageGLPen2GrayLevel  OBJECT-TYPE
	SYNTAX  INTEGER (0..5)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"GLPen GrayLevel
         0:15%
         1:30%
         2:45%
         3:75%
         4:90%
         5:100%"
	::= { pen2 2 }

brPageGLPen3Size  OBJECT-TYPE
	SYNTAX  INTEGER (0..16)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"GL PenSize 1-16"
	::= { pen3 1 }

brPageGLPen3GrayLevel  OBJECT-TYPE
	SYNTAX  INTEGER (0..5)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"GLPen GrayLevel
         0:15%
         1:30%
         2:45%
         3:75%
         4:90%
         5:100%"
	::= { pen3 2 }

brPageGLPen4Size  OBJECT-TYPE
	SYNTAX  INTEGER (0..16)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"GL PenSize 1-16"
	::= { pen4 1 }

brPageGLPen4GrayLevel  OBJECT-TYPE
	SYNTAX  INTEGER (0..5)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"GLPen GrayLevel
         0:15%
         1:30%
         2:45%
         3:75%
         4:90%
         5:100%"
	::= { pen4 2 }

brPageGLPen5Size  OBJECT-TYPE
	SYNTAX  INTEGER (0..16)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"GL PenSize 1-16"
	::= { pen5 1 }

brPageGLPen5GrayLevel  OBJECT-TYPE
	SYNTAX  INTEGER (0..5)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { pen5 2 }

brPageGLPen6Size  OBJECT-TYPE
	SYNTAX  INTEGER (0..16)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"GL PenSize 1-16"
	::= { pen6 1 }

brPageGLPen6GrayLevel  OBJECT-TYPE
	SYNTAX  INTEGER (0..5)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"GLPen GrayLevel
         0:15%
         1:30%
         2:45%
         3:75%
         4:90%
         5:100%"
	::= { pen6 2 }

brPageEPSONLeftMargin  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"EPSON LeftMargin
         1270N	:0-145
         2400Ce	:0-176"
	::= { margin-e 1 }

brPageEPSONRightMargin  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"EPSON RightMargin
         1270N	:10-155
         2400Ce	:10-186"
	::= { margin-e 2 }

brPageEPSONTopMargin  OBJECT-TYPE
	SYNTAX  INTEGER (0..200)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"EPSON TopMargin
         0:0
         33:0.33
         50:0.5
         100:1.0
         150:1.5
         200:2.0"
	::= { margin-e 3 }

brPageEPSONBottomMargin  OBJECT-TYPE
	SYNTAX  INTEGER (0..200)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"EPSON BottomMargin
         0:0
         33:0.33
         50:0.5
         100:1.0
         150:1.5
         200:2.0"
	::= { margin-e 4 }

brPageEPSONLines  OBJECT-TYPE
	SYNTAX  INTEGER (5..128)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"EPSON Form Line 5-128"
	::= { margin-e 5 }

brPageEPSONAutoLF  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"EPSON AutoLF
         1:Off
         2:On"
	::= { auto-e 1 }

brPageEPSONAutoMask  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"EPSON AutoMask
         1:Off
         2:On"
	::= { auto-e 5 }

brPageIBMLeftMargin  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"IBM LeftMargin
         1270N	:0-145
         2400Ce	:0-176"
	::= { margin-i 1 }

brPageIBMRightMargin  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"IBM LeftMargin
         1270N	:10-155
         2400Ce	:10-186"
	::= { margin-i 2 }

brPageIBMTopMargin  OBJECT-TYPE
	SYNTAX  INTEGER (0..200)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"IBM BottomMargin
         0:0
         33:0.33
         50:0.5
         100:1.0
         150:1.5
         200:2.0"
	::= { margin-i 3 }

brPageIBMBottomMargin  OBJECT-TYPE
	SYNTAX  INTEGER (0..200)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"IBM BottomMargin
         0:0
         33:0.33
         50:0.5
         100:1.0
         150:1.5
         200:2.0"
	::= { margin-i 4 }

brPageIBMLines  OBJECT-TYPE
	SYNTAX  INTEGER (5..128)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"IBM Form Line 5-128"
	::= { margin-i 5 }

brPageIBMAutoLF  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"IBM AutoLF
         1:Off
         2:On"
	::= { auto-i 1 }

brPageIBMAutoCR  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"IBM AutoCR
         1:Off
         2:On"
	::= { auto-i 2 }

brPageIBMAutoMask  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"IBM AutoMask
         1:Off
         2:On"
	::= { auto-i 5 }

brFontName  OBJECT-TYPE
	SYNTAX  INTEGER (0..99999)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"unused"
	::= { fontsetup 1 }

brFontPointSize  OBJECT-TYPE
	SYNTAX  INTEGER (4..99999)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"unused(400-99975)"
	::= { fontsetup 2 }

brFontPitch  OBJECT-TYPE
	SYNTAX  INTEGER (0..9999)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"unused(44-9999)"
	::= { fontsetup 3 }

brFontSymbolSet  OBJECT-TYPE
	SYNTAX  INTEGER (0..99999)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"unused"
	::= { fontsetup 4 }

brPanelResetUser  OBJECT-TYPE
	SYNTAX  INTEGER (0..100)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"User Reset"
	::= { reset 1 }

brPanelResetFactory  OBJECT-TYPE
	SYNTAX  INTEGER (0..100)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Factory Reset"
	::= { reset 2 }

brPanelTestConfiguration  OBJECT-TYPE
	SYNTAX  INTEGER (0..100)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Print Config page"
	::= { test 1 }

brPanelTestFontList  OBJECT-TYPE
	SYNTAX  INTEGER (0..100)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Print Fonts list"
	::= { test 2 }

brPanelTestTestPage  OBJECT-TYPE
	SYNTAX  INTEGER (0..100)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Print Test page"
	::= { test 3 }

brPanelTestDemoPage  OBJECT-TYPE
	SYNTAX  INTEGER (0..100)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Print Demo page
         0:Enable Print Demo page
         1:Disable Print Demo page"
	::= { test 4 }

brPanelLockPasswd  OBJECT-TYPE
	SYNTAX  INTEGER (0..100)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"unused"
	::= { panellock 1 }

brPanelLock  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		on(2)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The state of panel lock"
	::= { panellock 2 }

brPanelLockOn  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Panel Lock by setting a password."
	::= { panellock 3 }

brPanelLockOff  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Panel Lock is canceled by inputting a password.."
	::= { panellock 4 }

brPanelKeyOnline  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Press Online Key"
	::= { key 1 }

brPanelKeyFormFeed  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Press FormFeed Key"
	::= { key 2 }

brPanelKeyContinue  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Press Countinue Key"
	::= { key 3 }

brPanelKeyMode  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"unused"
	::= { key 4 }

brPanelKeyGo  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Press Go Key"
	::= { key 5 }

brPanelKeyJobCancel  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Press JobCancel Key"
	::= { key 6 }

brPanelKeyReprint  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Press Reprint Key"
	::= { key 7 }

brPanelKeySecure  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Press SecurePrint Key"
	::= { key 8 }

brLCDMode1  OBJECT-TYPE
	SYNTAX  INTEGER {
		fix(1),
		blink(2),
		scroll(3),
		blinkScroll(4)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"LCD Display Mode.
         1:fix
         2:blink
         3:scroll"
	::= { panelinfo 1 }

brLCDString1  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"LCD Display String of first line."
	::= { panelinfo 2 }

brLCDMode2  OBJECT-TYPE
	SYNTAX  INTEGER {
		fix(1),
		blink(2),
		scroll(3)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"LCD Display Mode.
         1:fix
         2:blink
         3:scroll"
	::= { panelinfo 3 }

brLCDString2  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"LCD Display String of second line."
	::= { panelinfo 4 }

brLCDString16fix  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"LCD Display String of 16fix."
	::= { panelinfo 5 }

brBackLightColor  OBJECT-TYPE
	SYNTAX  INTEGER {
		green(1),
		orange(2),
		red(3),
		notsupport(255)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"LCD Display Mode.
         0:Off
         1:Green
         2:Orange
         3:Red"
	::= { panelinfo 6 }

brLCDMode3  OBJECT-TYPE
	SYNTAX  INTEGER {
		fix(1),
		blink(2),
		scroll(3),
		blinkScroll(4)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"LCD Display Mode.
         1:fix
         2:blink
         3:scroll"
	::= { panelinfo 7 }

brLCDString3  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"LCD Display String of first line."
	::= { panelinfo 8 }

brLCDMode4  OBJECT-TYPE
	SYNTAX  INTEGER {
		fix(1),
		blink(2),
		scroll(3),
		blinkScroll(4)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"LCD Display Mode.
         1:fix
         2:blink
         3:scroll"
	::= { panelinfo 9 }

brLCDString4  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"LCD Display String of first line."
	::= { panelinfo 10 }

brLCDMode5  OBJECT-TYPE
	SYNTAX  INTEGER {
		fix(1),
		blink(2),
		scroll(3),
		blinkScroll(4)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"LCD Display Mode.
         1:fix
         2:blink
         3:scroll"
	::= { panelinfo 11 }

brLCDString5  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"LCD Display String of first line."
	::= { panelinfo 12 }

brLCDContrast  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"This value adjusts the contrast of the LCD Display."
	::= { panelinfo 13 }

brInfoSerialNumber  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Serial number of printer"
	::= { printerinfomation 1 }

brInfoType  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"unused"
	::= { printerinfomation 2 }

brInfoUpperMIBVer  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"unused (upper digit of the MIB version)"
	::= { version 1 }

brInfoLowerMIBVer  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"unused (lower digit of the MIB version)"
	::= { version 2 }

brInfoStatus  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Status message ID"
	::= { printerinfomation 4 }

brInfoNetVerUpStatus  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"unused"
	::= { printerinfomation 5 }

brInfoPrinterUStatus  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Status ID"
	::= { printerinfomation 6 }

brInfoPConSupported  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"BRPCon support
         0:not supported
         1:supported"
	::= { printerinfomation 7 }

brInfoMaintenance  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Maintenance info."
	::= { printerinfomation 8 }

brInfoModelNumber  OBJECT-TYPE
	SYNTAX  INTEGER {
		hl2400ce(4),
		hl3400CN(5),
		hl3260(6),
		hl2460(8),
		hl2600cn(9),
		hl3450cn(11),
		am(13),
		zlhe(14),
		zl2(15),
		jigen(16),
		aml(17),
		l4c(18),
		all(19),
		alLedModel(20),
		alLcdModel(21),
		hl4040(22),
		allchn(23),
		hl4050hl4070(24),
		all2(25),
		zlfb(101),
		zl(102),
		bh(103),
		bhfb(104),
		zlhs(105),
		bhhs(106),
		zl2fb(107),
		bhl2mfc(108),
		bhl2fb(109),
		zl2mfc(110),
		mini2(111),
		mini2adf(112),
		bh3fb(113),
		bh3mfc(114),
		allmfc(115),
		allfb(116),
		slow4c(117),
		mini2eColorLCD(118),
		mini2eColorLCDADF(119),
		alfb(120),
		dcp540(121),
		dcp750(122),
		mfc440(123),
		mfc665(124),
		mfc850(125),
		mfc860(126),
		mfc5460(127),
		mfc5860(128),
		dcp6150(129),
		dcp6260(130),
		dcp6460(131),
		dcp6860(132),
		dcp770(133),
		mfc480(134),
		acfbCIS(135),
		acfbCCD(136),
		mfc7440(137),
		mfc7840(138),
		mfc5490(139),
		mfc5890(140),
		mfc6490(141),
		dcp6690(142),
		mfc6890(143),
		dcp585(144),
		mfc490(145),
		mfc790(146),
		mfc990(147),
		mfc930(148)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Printer Model ID
         4:2400Ce
         5:3400CN
         6:3260
         8:2460
         9:2600cn
         11:3450cn
         13:am
         14:zlhe
         15:zl2
         16:jigen
         17:aml
         18:l4c
         19: ALL
         20: AL LED
         21: AL LCD
         22: HL-4040CN
         23: ALL China
         24: HL-4050CDN/HL-4070CDW
         25: ALL2
         101:ZL MFC FB type
         102:ZL MFC Normal type
         103:BH MFC Nornal type
         104:BH MFC FB type
         105:ZL MFC with HS
         106:BH MFC with HS
         107: ZL2FB
         108: BHL2MFC
         109: BHL2FB
         110: ZL2MFC
         111: Mini2
         112: Mini2 ADF
         113: BH3FB
         114: BH3MFC
         115: ALL-MFC
         116: ALL-FB
         117: SLow4C
         118: mini2eColorLCD,
         119: mini2eColorLCD ADF
         120: AL-FB
         121: DCP540
         122: DCP750
         123: MFC440
         124: MFC665
         125: MFC850
         126: MFC860
         127: MFC5460
         128: MFC5860
         129: DCP6150
         130: DCP6260
         131: MFC6460
         132: MFC6860
         133: DCP770
         134: MFC480
         135: AC-FB CIS
         136: AC-FB CCD
         137: MFC7440
         138: MFC7840
         139: MFC5490
         140: MFC5890
         141: MFC6490
         142: DCP6690
         143: MFC6890
         144: DCP585
         145: MFC490
         146: MFC790
         147: MFC990
         148: MFC930"
	::= { printerinfomation 9 }

brInfoCounter  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Page counter"
	::= { printerinfomation 10 }

brInfoNextCare  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Remaining life info."
	::= { printerinfomation 11 }

brInfoHDDSlot1  OBJECT-TYPE
	SYNTAX  INTEGER (0..6)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Card Slot1 status
         1:complete
         2:IO error
         3:memory error
         4:not found
         5:no device
         6:no file(format)"
	::= { printerinfomation 12 }

brInfoHDDSlot2  OBJECT-TYPE
	SYNTAX  INTEGER (0..6)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Card Slot2 status
         1:complete
         2:IO error
         3:memory error
         4:not found
         5:no device
         6:no file(format)"
	::= { printerinfomation 13 }

brInfoHDDInternal  OBJECT-TYPE
	SYNTAX  INTEGER (0..6)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Internal HDD info.
         1:complete
         2:IO error
         3:memory error
         4:not found
         5:no device
         6:no file(format)"
	::= { printerinfomation 14 }

brInfoHDDSize  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"unused"
	::= { printerinfomation 15 }

brInfoSolutionsCenterURL  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Brother Solutions Center's URL"
	::= { printerinfomation 16 }

brInfoDeviceRomVersion  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The device rom version"
	::= { printerinfomation 17 }

brInfoCoverage  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Coverage info."
	::= { printerinfomation 18 }

brInfoEstimatedPagesRemaining  OBJECT-TYPE
	SYNTAX  Counter
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Estimated pages remaining info."
	::= { printerinfomation 19 }

brInfoReplaceCount  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Replace count info."
	::= { printerinfomation 20 }

brInfoJamCount  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Jam count info."
	::= { printerinfomation 21 }

brInfoJamCountClear  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Jam count Clear."
	::= { printerinfomation 22 }

brInfoReplaceTime  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Replace count time info."
	::= { printerinfomation 23 }

brInfoDeviceSubRomVersion  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The PCL/PS ROM Version."
	::= { printerinfomation 24 }

brErrorHistoryCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Maximum number of  Error History."
	::= { errorHistory 1 }

brErrorHistoryTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrErrorHistoryEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents Error History."
	::= { errorHistory 2 }

brErrorHistoryEntry  OBJECT-TYPE
	SYNTAX  BrErrorHistoryEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the ErrorHistory table. Rows cannot be created or deleted."
	INDEX  { brErrorHistoryIndex }
	::= { brErrorHistoryTable 1 }

BrErrorHistoryEntry ::= 
	SEQUENCE {
		brErrorHistoryIndex
			INTEGER,
		brErrorHistoryDescription
			OCTET STRING
	}

brErrorHistoryIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"A unique value for the error history."
	::= { brErrorHistoryEntry 1 }

brErrorHistoryDescription  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The ErrorHistory Description."
	::= { brErrorHistoryEntry 2 }

brErrorHistoryAllClear  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"All clear Error History."
	::= { errorHistory 3 }

brCommunicationErrorHistoryCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Maximum number of  Communication Error History."
	::= { errorHistory 11 }

brCommunicationErrorHistoryTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrCommunicationErrorHistoryEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents Communication Error History."
	::= { errorHistory 12 }

brCommunicationErrorHistoryEntry  OBJECT-TYPE
	SYNTAX  BrCommunicationErrorHistoryEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the Communication ErrorHistory table. Rows cannot be created or deleted."
	INDEX  { brCommunicationErrorHistoryIndex }
	::= { brCommunicationErrorHistoryTable 1 }

BrCommunicationErrorHistoryEntry ::= 
	SEQUENCE {
		brCommunicationErrorHistoryIndex
			INTEGER,
		brCommunicationErrorHistoryDescription
			OCTET STRING
	}

brCommunicationErrorHistoryIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"A unique value for the Communication error history."
	::= { brCommunicationErrorHistoryEntry 1 }

brCommunicationErrorHistoryDescription  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The Communication Error History Description."
	::= { brCommunicationErrorHistoryEntry 2 }

brPrintPagesTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrPrintPagesEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents Print Pages."
	::= { printPages 1 }

brPrintPagesEntry  OBJECT-TYPE
	SYNTAX  BrPrintPagesEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the PrintPages table. Rows cannot be created or deleted."
	INDEX  { brPrintPagesIndex }
	::= { brPrintPagesTable 1 }

BrPrintPagesEntry ::= 
	SEQUENCE {
		brPrintPagesIndex
			INTEGER,
		brPrintPagesPaperSize
			INTEGER,
		brPrintPages
			Counter
	}

brPrintPagesIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"A unique value for the print pages."
	::= { brPrintPagesEntry 1 }

brPrintPagesPaperSize  OBJECT-TYPE
	SYNTAX  INTEGER {
		executive(1),
		letter(2),
		legal(3),
		a6(24),
		a5(25),
		a4(26),
		a3ISO(27),
		b5JIS(45),
		b4JIS(46),
		monarch(80),
		com10(81),
		dl(90),
		c5(91),
		b6(99),
		b5(100),
		ledger(890),
		a3PLUS(891),
		letterShortEdge(892),
		a4ShortEdge(893),
		a4LONG(894),
		executiveShortEdge(895),
		b5ISOShortEdge(896),
		custom(897),
		a4Letter(898),
		b5Executive(899),
		envelopes(900),
		dll(901),
		hagaki(902),
		folio(903),
		organaizerJ(904),
		organaizerK(905),
		organaizerL(906),
		organaizerM(907),
		userdefined(908),
		legalA4Long(909),
		b6A5A6(910),
		detectsensor(911),
		inches3x5(920),
		envelopesY4(921),
		largestEnvelopesTheWest(922),
		otherPage(999),
		b6JIS(924)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The paper size of print pages."
	::= { brPrintPagesEntry 2 }

brPrintPages  OBJECT-TYPE
	SYNTAX  Counter
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The count of Print pages."
	::= { brPrintPagesEntry 3 }

brPrintPagesMediaPlaceTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrPrintPagesMediaPlaceEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents Print Pages of Media Place."
	::= { printPages 11 }

brPrintPagesMediaPlaceEntry  OBJECT-TYPE
	SYNTAX  BrPrintPagesMediaPlaceEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the PrintPages Media Place table. Rows cannot be created or deleted."
	INDEX  { brPrintPagesMediaPlaceIndex }
	::= { brPrintPagesMediaPlaceTable 1 }

BrPrintPagesMediaPlaceEntry ::= 
	SEQUENCE {
		brPrintPagesMediaPlaceIndex
			INTEGER,
		brPrintPagesMediaPlaceType
			INTEGER,
		brPrintPagesMediaPlaceCounter
			Counter
	}

brPrintPagesMediaPlaceIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"A unique value for the print pages of media place."
	::= { brPrintPagesMediaPlaceEntry 1 }

brPrintPagesMediaPlaceType  OBJECT-TYPE
	SYNTAX  INTEGER {
		face(1),
		back(2)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The place of media."
	::= { brPrintPagesMediaPlaceEntry 2 }

brPrintPagesMediaPlaceCounter  OBJECT-TYPE
	SYNTAX  Counter
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The count of Print pages of media place."
	::= { brPrintPagesMediaPlaceEntry 3 }

brPrintPagesFuncTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrPrintPagesFuncEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents Print Pages of each function."
	::= { printPages 21 }

brPrintPagesFuncEntry  OBJECT-TYPE
	SYNTAX  BrPrintPagesFuncEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the PrintPages Func table. Rows cannot be created or deleted."
	INDEX  { brPrintPagesFuncIndex }
	::= { brPrintPagesFuncTable 1 }

BrPrintPagesFuncEntry ::= 
	SEQUENCE {
		brPrintPagesFuncIndex
			INTEGER,
		brPrintPagesFuncType
			INTEGER,
		brPrintPagesFuncCounter
			Counter
	}

brPrintPagesFuncIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"A unique value for the print pages of each function."
	::= { brPrintPagesFuncEntry 1 }

brPrintPagesFuncType  OBJECT-TYPE
	SYNTAX  INTEGER {
		pcPrinttotal(1),
		faxtotal(2),
		copytotal(3),
		copycolor(4),
		pcPrintcolor(5),
		faxcolor(6),
		pcPrintmono(7),
		faxmono(8),
		copymono(9)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The kind of function."
	::= { brPrintPagesFuncEntry 2 }

brPrintPagesFuncCounter  OBJECT-TYPE
	SYNTAX  Counter
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The count of Print pages of the function."
	::= { brPrintPagesFuncEntry 3 }

brPrintPagesPaperTypeTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrPrintPagesPaperTypeEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents Print Pages of Paper Type."
	::= { printPages 31 }

brPrintPagesPaperTypeEntry  OBJECT-TYPE
	SYNTAX  BrPrintPagesPaperTypeEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the PrintPages Paper Type table. Rows cannot be created or deleted."
	INDEX  { brPrintPagesPaperTypeIndex }
	::= { brPrintPagesPaperTypeTable 1 }

BrPrintPagesPaperTypeEntry ::= 
	SEQUENCE {
		brPrintPagesPaperTypeIndex
			INTEGER,
		brPrintPagesPaperTypeType
			INTEGER,
		brPrintPagesPaperTypeCounter
			Counter
	}

brPrintPagesPaperTypeIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..32767)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"A unique value for the print pages of paper type."
	::= { brPrintPagesPaperTypeEntry 1 }

brPrintPagesPaperTypeType  OBJECT-TYPE
	SYNTAX  INTEGER {
		regularthinrecycled(12),
		transparency(3),
		thickthickerbond(13),
		envelopesenvthickenvthin(14)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The type of paper."
	::= { brPrintPagesPaperTypeEntry 2 }

brPrintPagesPaperTypeCounter  OBJECT-TYPE
	SYNTAX  Counter
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The count of Print pages of paper type."
	::= { brPrintPagesPaperTypeEntry 3 }

brCapabilityCopiesMax  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The maximum value of copies"
	::= { copies 1 }

brCapabilityCopiesMin  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The minimum value of copies"
	::= { copies 2 }

brCapabilityOrientationCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number of support of orientation"
	::= { orientation 1 }

brCapabilityOrientationTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrCapabilityOrientationEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents capability of orientation."
	::= { orientation 2 }

brCapabilityOrientationEntry  OBJECT-TYPE
	SYNTAX  BrCapabilityOrientationEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the capability of orientation table. 
         Rows cannot be created or deleted."
	INDEX  { brCapabilityOrientationIndex }
	::= { brCapabilityOrientationTable 1 }

BrCapabilityOrientationEntry ::= 
	SEQUENCE {
		brCapabilityOrientationIndex
			INTEGER,
		brCapabilityOrientationName
			DisplayString
	}

brCapabilityOrientationIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"A unique value for the capability of orientation."
	::= { brCapabilityOrientationEntry 1 }

brCapabilityOrientationName  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The name of orientation currently supported."
	::= { brCapabilityOrientationEntry 2 }

brCapabilityPaperCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number of kinds of the paper currently supported"
	::= { paper 1 }

brCapabilityPaperTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrCapabilityPaperEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents capability of paper."
	::= { paper 2 }

brCapabilityPaperEntry  OBJECT-TYPE
	SYNTAX  BrCapabilityPaperEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the capability of paper table. 
         Rows cannot be created or deleted."
	INDEX  { brCapabilityPaperIndex }
	::= { brCapabilityPaperTable 1 }

BrCapabilityPaperEntry ::= 
	SEQUENCE {
		brCapabilityPaperIndex
			INTEGER,
		brCapabilityPaperName
			DisplayString
	}

brCapabilityPaperIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"A unique value for the capability of paper."
	::= { brCapabilityPaperEntry 1 }

brCapabilityPaperName  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The name of paper currently supported."
	::= { brCapabilityPaperEntry 2 }

brCapabilityMediatypeCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number of kinds of the mediatype currently supported"
	::= { mediatype 1 }

brCapabilityMediatypeTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrCapabilityMediatypeEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents capability of mediatype."
	::= { mediatype 2 }

brCapabilityMediatypeEntry  OBJECT-TYPE
	SYNTAX  BrCapabilityMediatypeEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the capability of mediatype table. 
         Rows cannot be created or deleted."
	INDEX  { brCapabilityMediatypeIndex }
	::= { brCapabilityMediatypeTable 1 }

BrCapabilityMediatypeEntry ::= 
	SEQUENCE {
		brCapabilityMediatypeIndex
			INTEGER,
		brCapabilityMediatypeName
			DisplayString
	}

brCapabilityMediatypeIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"A unique value for the capability of mediatype."
	::= { brCapabilityMediatypeEntry 1 }

brCapabilityMediatypeName  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The name of mediatype currently supported."
	::= { brCapabilityMediatypeEntry 2 }

brCapabilityResolutionCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number of kinds of the resolution currently supported"
	::= { resolution 1 }

brCapabilityResolutionTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrCapabilityResolutionEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents capability of resolution."
	::= { resolution 2 }

brCapabilityResolutionEntry  OBJECT-TYPE
	SYNTAX  BrCapabilityResolutionEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the capability of resolution table. 
         Rows cannot be created or deleted."
	INDEX  { brCapabilityResolutionIndex }
	::= { brCapabilityResolutionTable 1 }

BrCapabilityResolutionEntry ::= 
	SEQUENCE {
		brCapabilityResolutionIndex
			INTEGER,
		brCapabilityResolution
			INTEGER
	}

brCapabilityResolutionIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"A unique value for the capability of resolution."
	::= { brCapabilityResolutionEntry 1 }

brCapabilityResolution  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The value of resolution currently supported."
	::= { brCapabilityResolutionEntry 2 }

brPfKitIndexCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Maximum number of PF kit."
	::= { pfkit 1 }

brPfKitTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrPfKitEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents PF kit."
	::= { pfkit 2 }

brPfKitEntry  OBJECT-TYPE
	SYNTAX  BrPfKitEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the PfKit table. Rows cannot be created or deleted."
	INDEX  { brPfKitIndex }
	::= { brPfKitTable 1 }

BrPfKitEntry ::= 
	SEQUENCE {
		brPfKitIndex
			INTEGER,
		brPfKitType
			INTEGER,
		brPfKitCount
			Counter
	}

brPfKitIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"A unique value for the PF kit."
	::= { brPfKitEntry 1 }

brPfKitType  OBJECT-TYPE
	SYNTAX  INTEGER {
		pfkit1(1),
		pfkit2(2),
		pfkit3(3),
		pfkit4(4),
		pfkitmp(10),
		pfkitdx(20)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The PF kit ID."
	::= { brPfKitEntry 2 }

brPfKitCount  OBJECT-TYPE
	SYNTAX  Counter
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of the PF kit."
	::= { brPfKitEntry 3 }

brScanCountIndexCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Maximum number of Scan place."
	::= { scancount 1 }

brScanCountTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrScanCountEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents scan count."
	::= { scancount 2 }

brScanCountEntry  OBJECT-TYPE
	SYNTAX  BrScanCountEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the ScanCount table. Rows cannot be created or deleted."
	INDEX  { brScanCountIndex }
	::= { brScanCountTable 1 }

BrScanCountEntry ::= 
	SEQUENCE {
		brScanCountIndex
			INTEGER,
		brScanCountType
			INTEGER,
		brScanCountCounter
			Counter
	}

brScanCountIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"A unique value for the scan count place."
	::= { brScanCountEntry 1 }

brScanCountType  OBJECT-TYPE
	SYNTAX  INTEGER {
		adf(1),
		fb(2),
		adfdx(3)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The scan place ID."
	::= { brScanCountEntry 2 }

brScanCountCounter  OBJECT-TYPE
	SYNTAX  Counter
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of the scan."
	::= { brScanCountEntry 3 }

brStatusSleep  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Sleeping status
         0:ready
         1:sleep"
	::= { printerstatus 1 }

brSecretMPRetry  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"unused"
	::= { secret 1 }

brSecretReprint  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Secret Reprint mode
         0:Off
         111:On
         222:Job"
	::= { secret 2 }

brFontSetting  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		" Font Setting
         0:Brother Font
         1:Agfa Font"
	::= { secret 3 }

brFontSwitchOn  OBJECT-TYPE
	SYNTAX  INTEGER (0..999999)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		" Switch of Agfa Font On"
	::= { secret 4 }

brFontSwitchOff  OBJECT-TYPE
	SYNTAX  INTEGER (0..999999)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Switch of Agfa Font Off"
	::= { secret 5 }

brClockFuncTimeStyle  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Time format
         0:YMD
         1:MDY
         2:DMY"
	::= { clockfunction 1 }

brClockFuncSummerTime  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Summer time
         1:Off
         2:On"
	::= { clockfunction 2 }

brClockFuncTimeZone  OBJECT-TYPE
	SYNTAX  INTEGER (-24..24)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"TimeZone"
	::= { clockfunction 3 }

brClockFuncZoneSet  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"TimeZone enable
         1:Off
         2:On"
	::= { clockfunction 4 }

brFindPort  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { funa 1 }

brFindTime  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { funa 2 }

brAdminCapability  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"AdminCapability"
	::= { adminCapa 1 }

brUserPasswordVerify  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Submit the print server's user password for verification.
         An error will be returned if the password is incorrect."
	::= { userSetting 1 }

brUserPassword  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Set the print server's user password value."
	::= { userSetting 2 }

brpsVerifyPhysAddress  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..6) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"For verify physical address."
	::= { verify 1 }

brBasicSettingConfigured  OBJECT-TYPE
	SYNTAX  INTEGER {
		unconfigured(1),
		configured(2)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"This value indicates that the basic setting is configured or not.
         The condition entry of this value is model dependent."
	::= { npConfig 1 }

brLPDBannerPage  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"LPR banner page
         0: disabled.
         1: enabled.
         2: force to print."
	::= { banner 1 }

brENetModeSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"ENet Mode
         0:not supported
         1:supported"
	::= { eNet 1 }

brENetMode  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"ENet Mode
         0:Auto
         1:FD100B
         2:100Base
         3:FD10B
         4:10BASE"
	::= { eNet 2 }

brDNSSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"DNS resolver
         0:not supported
         1:supported
         This is the obsolete MIB that is not currently used."
	::= { dns 1 }

brPrimaryDNSIP  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Primary DNS IP Address.
         This is the obsolete MIB that is not currently used."
	::= { dns 2 }

brSecondaryDNSIP  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Secondary DNS IP Address
         This is the obsolete MIB that is not currently used."
	::= { dns 3 }

brDNSIPSetup  OBJECT-TYPE
	SYNTAX  INTEGER {
		static(1),
		auto(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"DNS mode
         1:static
         2:auto"
	::= { dns 4 }

brTCPIPConnectTime  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"TCP/IP connect time."
	::= { dns 5 }

brAdvancedDNSSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"DNS resolver
         0:not supported
         1:supported"
	::= { dns 6 }

brPrimaryDNSIPAddress  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Primary DNS IP Address"
	::= { dns 7 }

brSecondaryDNSIPAddress  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Secondary DNS IP Address"
	::= { dns 8 }

brPOP3ServerName  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..64) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The FQDN name of the POP3 server."
	::= { dns 9 }

brSMTPServerName  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..64) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The FQDN name of the SMTP server."
	::= { dns 10 }

brPushStatusSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Notification
         0:not supported
         1:supported"
	::= { pushstatus 1 }

brPriMailAddress  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Primary Mail Address"
	::= { priadmin 1 }

brPriError  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Primary Error"
	::= { priadmin 2 }

brSecMailAddress  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Secondary Mail Address"
	::= { secadmin 1 }

brSecError  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Secondary Error"
	::= { secadmin 2 }

brNotificationCount  OBJECT-TYPE
	SYNTAX  INTEGER (1..127)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Maximum number of  Notification Method."
	::= { pushstatus 4 }

brNotificationTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrNotificationEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents Notification Method."
	::= { pushstatus 5 }

brNotificationEntry  OBJECT-TYPE
	SYNTAX  BrNotificationEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the Notification table. Rows cannot be created or deleted."
	INDEX  { brNotificationIndex }
	::= { brNotificationTable 1 }

BrNotificationEntry ::= 
	SEQUENCE {
		brNotificationIndex
			INTEGER,
		brNotificationAddress
			OCTET STRING,
		brNotificationStatusGroup
			INTEGER,
		brNotificationShowURLInfo
			INTEGER,
		brNotificationErrorRule
			OCTET STRING,
		brNotificationRestoration
			INTEGER
	}

brNotificationIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number."
	::= { brNotificationEntry 1 }

brNotificationAddress  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Notification Address."
	::= { brNotificationEntry 2 }

brNotificationStatusGroup  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Notification Status group"
	::= { brNotificationEntry 3 }

brNotificationShowURLInfo  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The setting of show URL infomation at Notification mail."
	::= { brNotificationEntry 4 }

brNotificationErrorRule  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The setting of sending requirements for Notification."
	::= { brNotificationEntry 5 }

brNotificationRestoration  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		on(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The setting of Restoration Notification mail"
	::= { brNotificationEntry 6 }

brPrintersEmailaddress  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Printers Email Address."
	::= { pushstatus 6 }

brNotificationVersion  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The version of Notification"
	::= { pushstatus 7 }

brShowIPAddressInfo  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		on(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The setting of show IP address infomation at Notification mail."
	::= { pushstatus 8 }

brNotificationRuleTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrNotificationRuleEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents Notification Method."
	::= { pushstatus 50 }

brNotificationRuleEntry  OBJECT-TYPE
	SYNTAX  BrNotificationRuleEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the Notification rule table. Rows cannot be created or deleted."
	INDEX  { brNotificationIndex, brNotificationRuleIndex }
	::= { brNotificationRuleTable 1 }

BrNotificationRuleEntry ::= 
	SEQUENCE {
		brNotificationRuleIndex
			INTEGER,
		brNotificationStatusID
			INTEGER,
		brNotificationMainRule
			INTEGER,
		brNotificationRuleValue
			INTEGER
	}

brNotificationRuleIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number."
	::= { brNotificationRuleEntry 1 }

brNotificationStatusID  OBJECT-TYPE
	SYNTAX  INTEGER {
		coverOpen(1),
		jam(2),
		tonerLow(3),
		tonerEmpty(4),
		userConsumableWarning(5),
		userConsumableError(6),
		servicemanConsumableWarning(7),
		servicemanConsumableError(8),
		changeDrum(9),
		memoryFull(10),
		inputMediaError(11),
		outputFull(12),
		notInstalled(13),
		machineError(14),
		otherErrors(15)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Notification Status group ID"
	::= { brNotificationRuleEntry 2 }

brNotificationMainRule  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		everytime(2),
		times(3),
		minutes(4)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The main rule of Notification setting."
	::= { brNotificationRuleEntry 3 }

brNotificationRuleValue  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The value of Notification setting."
	::= { brNotificationRuleEntry 4 }

brPJLInfoOptionsTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrPJLInfoOptionsEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents PJLInfoOptions."
	::= { pjlinfo 1 }

brPJLInfoOptionsEntry  OBJECT-TYPE
	SYNTAX  BrPJLInfoOptionsEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the PJLInfoOptions table. Rows cannot be created or deleted."
	INDEX  { brPJLInfoOptionsIndex }
	::= { brPJLInfoOptionsTable 1 }

BrPJLInfoOptionsEntry ::= 
	SEQUENCE {
		brPJLInfoOptionsIndex
			INTEGER,
		brPJLInfoOptions
			OCTET STRING
	}

brPJLInfoOptionsIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number."
	::= { brPJLInfoOptionsEntry 1 }

brPJLInfoOptions  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..32) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The PJLInfoOptions."
	::= { brPJLInfoOptionsEntry 2 }

brPJLInfoIntrayconfigTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrPJLInfoIntrayconfigEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents PJLInfoIntrayconfig."
	::= { pjlinfo 2 }

brPJLInfoIntrayconfigEntry  OBJECT-TYPE
	SYNTAX  BrPJLInfoIntrayconfigEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the PJLInfoIntrayconfig table. Rows cannot be created or deleted."
	INDEX  { brPJLInfoIntrayconfigIndex }
	::= { brPJLInfoIntrayconfigTable 1 }

BrPJLInfoIntrayconfigEntry ::= 
	SEQUENCE {
		brPJLInfoIntrayconfigIndex
			INTEGER,
		brPJLInfoIntrayconfig
			OCTET STRING
	}

brPJLInfoIntrayconfigIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number."
	::= { brPJLInfoIntrayconfigEntry 1 }

brPJLInfoIntrayconfig  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..32) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The PJLInfoIntrayconfig."
	::= { brPJLInfoIntrayconfigEntry 2 }

brPJLInfoOuttrayconfigTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrPJLInfoOuttrayconfigEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents PJLInfoOuttrayconfig."
	::= { pjlinfo 3 }

brPJLInfoOuttrayconfigEntry  OBJECT-TYPE
	SYNTAX  BrPJLInfoOuttrayconfigEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the PJLInfoOuttrayconfig table. Rows cannot be created or deleted."
	INDEX  { brPJLInfoOuttrayconfigIndex }
	::= { brPJLInfoOuttrayconfigTable 1 }

BrPJLInfoOuttrayconfigEntry ::= 
	SEQUENCE {
		brPJLInfoOuttrayconfigIndex
			INTEGER,
		brPJLInfoOuttrayconfig
			OCTET STRING
	}

brPJLInfoOuttrayconfigIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number."
	::= { brPJLInfoOuttrayconfigEntry 1 }

brPJLInfoOuttrayconfig  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..32) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The PJLInfoOuttrayconfig."
	::= { brPJLInfoOuttrayconfigEntry 2 }

brPJLInfoDXconfigTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrPJLInfoDXconfigEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents PJLInfoDXconfig."
	::= { pjlinfo 4 }

brPJLInfoDXconfigEntry  OBJECT-TYPE
	SYNTAX  BrPJLInfoDXconfigEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the PJLInfoDXconfig table. Rows cannot be created or deleted."
	INDEX  { brPJLInfoDXconfigIndex }
	::= { brPJLInfoDXconfigTable 1 }

BrPJLInfoDXconfigEntry ::= 
	SEQUENCE {
		brPJLInfoDXconfigIndex
			INTEGER,
		brPJLInfoDXconfig
			OCTET STRING
	}

brPJLInfoDXconfigIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number."
	::= { brPJLInfoDXconfigEntry 1 }

brPJLInfoDXconfig  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..32) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The PJLInfoDXconfig."
	::= { brPJLInfoDXconfigEntry 2 }

brPJLInfoStorageconfigTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrPJLInfoStorageconfigEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents PJLInfoStorageconfig."
	::= { pjlinfo 5 }

brPJLInfoStorageconfigEntry  OBJECT-TYPE
	SYNTAX  BrPJLInfoStorageconfigEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the PJLInfoStorageconfig table. Rows cannot be created or deleted."
	INDEX  { brPJLInfoStorageconfigIndex }
	::= { brPJLInfoStorageconfigTable 1 }

BrPJLInfoStorageconfigEntry ::= 
	SEQUENCE {
		brPJLInfoStorageconfigIndex
			INTEGER,
		brPJLInfoStorageconfig
			OCTET STRING
	}

brPJLInfoStorageconfigIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number."
	::= { brPJLInfoStorageconfigEntry 1 }

brPJLInfoStorageconfig  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..32) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The PJLInfoStorageconfig."
	::= { brPJLInfoStorageconfigEntry 2 }

brEmailReportsSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"E-mail reports
         0:not supported
         1:supported"
	::= { eMailReports 1 }

brEmailReportsCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number of Administrator for E-mail reports."
	::= { eMailReports 2 }

brEmailReportsTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrEmailReportsEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents E-mail reports."
	::= { eMailReports 11 }

brEmailReportsEntry  OBJECT-TYPE
	SYNTAX  BrEmailReportsEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the BrEmailReportsEntry table. Rows cannot be created or deleted."
	INDEX  { brEmailReportsIndex }
	::= { brEmailReportsTable 1 }

BrEmailReportsEntry ::= 
	SEQUENCE {
		brEmailReportsIndex
			INTEGER,
		brEmailReportsAddress
			OCTET STRING,
		brEmailReportsFrequency
			INTEGER,
		brEmailReportsTime
			OCTET STRING,
		brEmailReportsWeek
			INTEGER,
		brEmailReportsDate
			INTEGER,
		brEmailReportsSendReportNow
			INTEGER,
		brEmailReportsSendReportatPowerOn
			INTEGER,
		brEmailReportsNoRTCFrequency
			INTEGER,
		brEmailReportsReportFormat
			INTEGER
	}

brEmailReportsIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..16)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number."
	::= { brEmailReportsEntry 1 }

brEmailReportsAddress  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..60) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The E-mail address of Administrator for E-mail reports."
	::= { brEmailReportsEntry 2 }

brEmailReportsFrequency  OBJECT-TYPE
	SYNTAX  INTEGER {
		daily(1),
		weekly(2),
		monthly(3)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The frequency of sending E-mail reports."
	::= { brEmailReportsEntry 3 }

brEmailReportsTime  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The time setting for E-mail reports."
	::= { brEmailReportsEntry 4 }

brEmailReportsWeek  OBJECT-TYPE
	SYNTAX  INTEGER {
		sunday(1),
		monday(2),
		tuesday(3),
		wednesday(4),
		thursday(5),
		friday(6),
		saturday(7)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The setting of day of the week for E-mail reports."
	::= { brEmailReportsEntry 5 }

brEmailReportsDate  OBJECT-TYPE
	SYNTAX  INTEGER (1..31)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The setting of day for E-mail reports when real time clock exists."
	::= { brEmailReportsEntry 6 }

brEmailReportsSendReportNow  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		on(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The setting of Send Report Now function for E-mail reports."
	::= { brEmailReportsEntry 7 }

brEmailReportsSendReportatPowerOn  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		on(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The setting of Send Report at PowerOn for E-mail reports."
	::= { brEmailReportsEntry 8 }

brEmailReportsNoRTCFrequency  OBJECT-TYPE
	SYNTAX  INTEGER (1..30)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The setting of day for E-mail reports when real time clock does not exists."
	::= { brEmailReportsEntry 9 }

brEmailReportsReportFormat  OBJECT-TYPE
	SYNTAX  INTEGER {
		plaintext(1),
		xml(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The setting of ReportFormat for E-mail reports."
	::= { brEmailReportsEntry 10 }

brpsNodeName  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..32) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The node name of this print server."
	::= { brconfig 1 }

brpsSerialNumber  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..32) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The serial number of this print server."
	::= { brconfig 2 }

brpsHardwareType  OBJECT-TYPE
	SYNTAX  INTEGER (0..32767)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Hardware type of the print server."
	::= { brconfig 3 }

brpsMainRevision  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..5) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Revision level of the Print Server firmware suitable
         for display. This object may be read but not written."
	::= { brconfig 4 }

brpsBootRevision  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..5) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Revision level of the boot firmware (if applicable) suitable
         for display. This object may be read but not written."
	::= { brconfig 5 }

brpsPasswordVerify  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Submit the print server's password for verification. An error
         will be returned if the password is incorrect."
	::= { brconfig 6 }

brpsPassword  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Set the print server's password value."
	::= { brconfig 7 }

brpsMIBVersion  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..5) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Revision level of the private MIB implemented by the device.
         For example:
         
         1.0
         
         This object may be read but not written."
	::= { brconfig 8 }

brpsOEMString  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(3) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"OEM string setting for the print server.
         This object may be read but not written."
	::= { brconfig 9 }

brpsMIBMajor  OBJECT-TYPE
	SYNTAX  INTEGER (0..9)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"MIB major version number."
	::= { brconfig 10 }

brpsMIBMinor  OBJECT-TYPE
	SYNTAX  INTEGER (0..99)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"MIB minor version number."
	::= { brconfig 11 }

brSupportedInfo  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..255) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brconfig 200 }

brpsServerDescription  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..32) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Product name of print server.
         For example: NC-3100h
         This object may be read but not written."
	::= { brconfig 12 }

brpsEnetMode  OBJECT-TYPE
	SYNTAX  INTEGER (0..4)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Ethernet type."
	::= { brconfig 13 }

brpsFlashROMSize  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Flash ROM Size of print server (MBytes)."
	::= { brconfig 14 }

brpsSNMPGetCommunity  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..32) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"SNMP Get Community Name."
	::= { brconfig 15 }

brpsSNMPJetAdmin  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the JetAdmin compatibility mode is not enabled. 
         1 indicates JetAdmin compatibility mode is enabled."
	::= { brconfig 16 }

brpsSNMPSetCommunity1  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..32) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"SNMP Set Community Name #1."
	::= { brconfig 17 }

brpsSNMPSetCommunity2  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..32) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"SNMP Set Community Name #2."
	::= { brconfig 18 }

brpsTestPage  OBJECT-TYPE
	SYNTAX  INTEGER (0..32767)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Setting any value generates a test page on all ports."
	::= { brcontrol 1 }

brpsSetDefault  OBJECT-TYPE
	SYNTAX  INTEGER (0..32767)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Setting the value 5AA5h (23205) resets all parameters to their factory default value."
	::= { brcontrol 2 }

brpsReset  OBJECT-TYPE
	SYNTAX  INTEGER (0..32767)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Setting the value 55AAh (21930) causes the print server to perform a soft reset."
	::= { brcontrol 3 }

brpsProtectModeEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the protect mode is not enabled. 1 indicates protect mode
         is enabled."
	::= { brcontrol 4 }

brpsProtectPassword  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..32) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"protect password."
	::= { brcontrol 5 }

brpsPortCount  OBJECT-TYPE
	SYNTAX  INTEGER (0..32)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Number of ports on this print server."
	::= { brport 1 }

brpsPortInfoTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrpsPortInfoEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents the attributes of a port."
	::= { brport 2 }

brpsPortInfoEntry  OBJECT-TYPE
	SYNTAX  BrpsPortInfoEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the port table. Rows cannot be created or deleted
         via direct SNMP operations."
	INDEX  { brpsPortIndex }
	::= { brpsPortInfoTable 1 }

BrpsPortInfoEntry ::= 
	SEQUENCE {
		brpsPortIndex
			INTEGER,
		brpsPortName
			DisplayString,
		brpsPortType
			INTEGER,
		brpsPortStatus
			INTEGER,
		brpsPortStatusString
			DisplayString,
		brpsPortProtocol
			INTEGER,
		brpsPortQueueSize
			INTEGER,
		brpsPortDescriptionString
			DisplayString,
		brpsPortInfoString
			DisplayString,
		brpsPortHTTPExtensions
			INTEGER,
		brpsPortSNMPExtensions
			INTEGER,
		brpsPortAttribute
			DisplayString,
		brpsPortBinaryMode
			INTEGER,
		brpsPortInhibitDatagramSupport
			INTEGER
	}

brpsPortIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..255)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The port number."
	::= { brpsPortInfoEntry 1 }

brpsPortName  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..32) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The port name."
	::= { brpsPortInfoEntry 2 }

brpsPortType  OBJECT-TYPE
	SYNTAX  INTEGER {
		parallel(1),
		serial(2)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Type of port."
	::= { brpsPortInfoEntry 3 }

brpsPortStatus  OBJECT-TYPE
	SYNTAX  INTEGER (0..31)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The port status as a 32 bit integer."
	::= { brpsPortInfoEntry 4 }

brpsPortStatusString  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..32) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The port status description. Reflects the printer's front panel display
         on internal print server interfaces."
	::= { brpsPortInfoEntry 5 }

brpsPortProtocol  OBJECT-TYPE
	SYNTAX  INTEGER (0..12)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Protocol currently printing on this port."
	::= { brpsPortInfoEntry 6 }

brpsPortQueueSize  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Number of protocols waiting to print on this port."
	::= { brpsPortInfoEntry 7 }

brpsPortDescriptionString  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..50) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The port description. Reflects the device ID of the printer
         connected to the port."
	::= { brpsPortInfoEntry 8 }

brpsPortInfoString  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..255) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The port status information. More information about the port status."
	::= { brpsPortInfoEntry 9 }

brpsPortHTTPExtensions  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the printer on this port does not support HTTP,
         1 indicates that the printer does support HTTP."
	::= { brpsPortInfoEntry 10 }

brpsPortSNMPExtensions  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the printer on this port does not support SNMP,
         1 indicates that the printer does support SNMP."
	::= { brpsPortInfoEntry 11 }

brpsPortAttribute  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..32) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Port attribute."
	::= { brpsPortInfoEntry 12 }

brpsPortBinaryMode  OBJECT-TYPE
	SYNTAX  INTEGER (0..3)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Port binary mode."
	::= { brpsPortInfoEntry 13 }

brpsPortInhibitDatagramSupport  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the inhibit datagram is not supported.
         1 indicates the inhibit datagram is supported."
	::= { brpsPortInfoEntry 14 }

brpsServiceCount  OBJECT-TYPE
	SYNTAX  INTEGER (0..128)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Number of services on this print server."
	::= { brservice 1 }

brpsServiceInfoTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrpsServiceInfoEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents the attributes of a service."
	::= { brservice 2 }

brpsServiceInfoEntry  OBJECT-TYPE
	SYNTAX  BrpsServiceInfoEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the services table. Rows cannot be created or deleted
         via direct SNMP operations."
	INDEX  { brpsServiceIndex }
	::= { brpsServiceInfoTable 1 }

BrpsServiceInfoEntry ::= 
	SEQUENCE {
		brpsServiceIndex
			INTEGER,
		brpsServiceName
			DisplayString,
		brpsServicePort
			INTEGER,
		brpsServiceFilter
			INTEGER,
		brpsServiceBOT
			INTEGER,
		brpsServiceEOT
			INTEGER,
		brpsServiceMatch
			INTEGER,
		brpsServiceReplace
			INTEGER,
		brpsServiceTCPPort
			INTEGER,
		brpsServiceNDSTree
			DisplayString,
		brpsServiceNDSContext
			OCTET STRING,
		brpsServiceVines
			DisplayString,
		brpsServiceObsolete
			INTEGER,
		brpsServiceNetwareServerCount
			INTEGER,
		brpsServiceReceiveOnly
			INTEGER,
		brpsServiceTCPQueued
			INTEGER,
		brpsServiceProtocolLAT
			INTEGER,
		brpsServiceProtocolTCPIP
			INTEGER,
		brpsServiceProtocolNetware
			INTEGER,
		brpsServiceProtocolAppleTalk
			INTEGER,
		brpsServiceProtocolBanyan
			INTEGER,
		brpsServiceProtocolDLC
			INTEGER,
		brpsServiceProtocolNetBEUI
			INTEGER,
		brpsServiceNetwareServerMode
			INTEGER,
		brpsServiceNetwareRemotePrinterNum
			INTEGER,
		brpsServiceProtocolIPP
			INTEGER,
		brpsServiceAppleTalkType
			DisplayString
	}

brpsServiceIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..32)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The service number."
	::= { brpsServiceInfoEntry 1 }

brpsServiceName  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..32) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The service name."
	::= { brpsServiceInfoEntry 2 }

brpsServicePort  OBJECT-TYPE
	SYNTAX  INTEGER (0..31)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The port number assigned to this service."
	::= { brpsServiceInfoEntry 3 }

brpsServiceFilter  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Filter number assigned to this service."
	::= { brpsServiceInfoEntry 4 }

brpsServiceBOT  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Beginning of text string number assigned to this service."
	::= { brpsServiceInfoEntry 5 }

brpsServiceEOT  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"End of text string number assigned to this service."
	::= { brpsServiceInfoEntry 6 }

brpsServiceMatch  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Match string number assigned to this service."
	::= { brpsServiceInfoEntry 7 }

brpsServiceReplace  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Replace string number assigned to this service."
	::= { brpsServiceInfoEntry 8 }

brpsServiceTCPPort  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"TCP Port number assigned to this service."
	::= { brpsServiceInfoEntry 9 }

brpsServiceNDSTree  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..48) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The NDS Tree."
	::= { brpsServiceInfoEntry 10 }

brpsServiceNDSContext  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..255) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The NDS Context in Unicode."
	::= { brpsServiceInfoEntry 11 }

brpsServiceVines  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..48) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Vines StreetTalk name for this service."
	::= { brpsServiceInfoEntry 12 }

brpsServiceObsolete  OBJECT-TYPE
	SYNTAX  INTEGER (0..127)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Obsolete parameter that is no longer used"
	::= { brpsServiceInfoEntry 13 }

brpsServiceNetwareServerCount  OBJECT-TYPE
	SYNTAX  INTEGER (0..127)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The number of Netware servers enabled on this service. Modifying the
         value will truncate the server list to the specified number of servers.
         If the value set is greater than the number of servers currently on the
         list, then the list will be unchanged."
	::= { brpsServiceInfoEntry 14 }

brpsServiceReceiveOnly  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"When enabled, the service will only receive data, any printer
         responses will be discarded."
	::= { brpsServiceInfoEntry 15 }

brpsServiceTCPQueued  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"When enabled, the raw TCP connections to the port will be
         queued while waiting for the port to become available"
	::= { brpsServiceInfoEntry 16 }

brpsServiceProtocolLAT  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"When enabled, the LAT protocol will be available on this service."
	::= { brpsServiceInfoEntry 17 }

brpsServiceProtocolTCPIP  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"When enabled, the TCP/IP protocol will be available on this service."
	::= { brpsServiceInfoEntry 18 }

brpsServiceProtocolNetware  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"When enabled, the Netware protocol will be available on this service."
	::= { brpsServiceInfoEntry 19 }

brpsServiceProtocolAppleTalk  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"When enabled, the AppleTalk protocol will be available on this service."
	::= { brpsServiceInfoEntry 20 }

brpsServiceProtocolBanyan  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"When enabled, the Banyan protocol will be available on this service."
	::= { brpsServiceInfoEntry 21 }

brpsServiceProtocolDLC  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"When enabled, the DLC protocol will be available on this service."
	::= { brpsServiceInfoEntry 22 }

brpsServiceProtocolNetBEUI  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"When enabled, the NetBEUI protocol will be available on this service."
	::= { brpsServiceInfoEntry 23 }

brpsServiceNetwareServerMode  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Netware mode for this service. 0 = Queue Server, 1 = Remote Printer"
	::= { brpsServiceInfoEntry 24 }

brpsServiceNetwareRemotePrinterNum  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Remote Printer Number for this service"
	::= { brpsServiceInfoEntry 25 }

brpsServiceProtocolIPP  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"When enabled, the IPP protocol will be available on this service."
	::= { brpsServiceInfoEntry 26 }

brpsServiceAppleTalkType  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..32) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"AppleTalk type."
	::= { brpsServiceInfoEntry 27 }

brpsServiceStringLimit  OBJECT-TYPE
	SYNTAX  INTEGER (1..127)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Maximum number of service strings supported by the print server."
	::= { brservice 3 }

brpsServiceStringInfoTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrpsServiceStringInfoEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents the service BOT/EOT/MATCH/REPLACE strings defined."
	::= { brservice 4 }

brpsServiceStringInfoEntry  OBJECT-TYPE
	SYNTAX  BrpsServiceStringInfoEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the service string table. Rows cannot be created or deleted
         via direct SNMP operations."
	INDEX  { brpsServiceStringIndex }
	::= { brpsServiceStringInfoTable 1 }

BrpsServiceStringInfoEntry ::= 
	SEQUENCE {
		brpsServiceStringIndex
			INTEGER,
		brpsServiceString
			DisplayString
	}

brpsServiceStringIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..255)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The string number."
	::= { brpsServiceStringInfoEntry 1 }

brpsServiceString  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..32) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The character string definition suitable for displaying."
	::= { brpsServiceStringInfoEntry 2 }

brpsServiceStringCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Number of service strings currently defined on the print server."
	::= { brservice 5 }

brpsServiceFilterCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Number of service filters implemented by the device."
	::= { brservice 6 }

brpsLATSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the LAT protocol is not supported. 1 indicates LAT
         support is implemented by the firmware"
	::= { brlat 1 }

brpsLATEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the LAT protocol is not enabled. 1 indicates that the LAT
         protocol is enabled. Writing a non-zero value will enable the LAT
         protocol if it is supported by the firmware."
	::= { brlat 2 }

brpsLATCircuitTimer  OBJECT-TYPE
	SYNTAX  INTEGER (10..499)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Minimum time (in milliseconds) between transmissions to the host. The
         default of 10 normally provides the best performance."
	::= { brlat 3 }

brpsLATKeepAliveTimer  OBJECT-TYPE
	SYNTAX  INTEGER (0..60000)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"How often in seconds a packet is transmitted to keep a connection alive."
	::= { brlat 4 }

brpsLATReceiveBufferMax  OBJECT-TYPE
	SYNTAX  INTEGER (0..5)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"LAT receive buffer max size."
	::= { brlat 5 }

brpsLATTransmitBufferMax  OBJECT-TYPE
	SYNTAX  INTEGER (0..5)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"LAT transmit buffer max size."
	::= { brlat 6 }

brpsLATTimeout  OBJECT-TYPE
	SYNTAX  INTEGER (0..499)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"LAT inactivity timeout (sec)."
	::= { brlat 7 }

brpsLATGroup  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..7) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"LAT group membership (mm-[nn])."
	::= { brlat 8 }

brpsTCPIPSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the TCP/IP protocol is not supported. 1 indicates TCP/IP
         support is implemented by the firmware"
	::= { brtcpip 1 }

brpsTCPIPEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the TCP/IP protocol is not enabled. 1 indicates that the
         TCP/IP protocol is enabled. Writing a non-zero value will enable the
         TCP/IP protocol if it is supported by the firmware."
	::= { brtcpip 2 }

brpsTCPIPAddress  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"IP Address assigned to the print server."
	::= { brtcpip 3 }

brpsTCPIPSubnetMask  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Subnet mask assigned to the print server."
	::= { brtcpip 4 }

brpsTCPIPGateway  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Gateway/Router address assigned to the print server."
	::= { brtcpip 5 }

brpsTCPIPMethod  OBJECT-TYPE
	SYNTAX  INTEGER (0..7)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Method used to determine the IP Address. AUTO tries all protocols.
         STATIC uses the configured address. DHCP, BOOTP, and RARP use only
         that protocol."
	::= { brtcpip 6 }

brpsTCPIPTimeout  OBJECT-TYPE
	SYNTAX  INTEGER (0..32767)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Timeout used for LPD connections."
	::= { brtcpip 7 }

brpsTCPIPBootTries  OBJECT-TYPE
	SYNTAX  INTEGER (0..32767)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Number of attempts to use BOOTP, RARP, and DHCP."
	::= { brtcpip 8 }

brpsTCPIPMaxWindow  OBJECT-TYPE
	SYNTAX  INTEGER (1500..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Maximum window size for TCP connections. Due to resource limitations
         setting the window to values greater than 10240 is not recommended."
	::= { brtcpip 9 }

brpsTCPIPRARPNoSubnet  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"If enabled, do not set the subnet mask based on the RARP response."
	::= { brtcpip 10 }

brpsTCPIPRARPNoGateway  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"If enabled, do not set the gateway/router address based on the RARP response."
	::= { brtcpip 11 }

brpsTCPIPUpdate  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(6) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Hardware MAC address to be updated. This value is write only. Will
         read back as zeros."
	::= { brtcpip 12 }

brpsTCPIPBanner  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the LPR banner print is not enabled.
         1 indicates that the LPR banner print is enabled."
	::= { brtcpip 13 }

brpsTCPIPFastTimeoutEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the fast timeout is not enabled. 
         1 indicates that the fast timeout is enabled."
	::= { brtcpip 14 }

brpsTCPIPLPRRetryEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the LPR print retry is not enabled. 
         1 indicates that the LPR print retry is enabled."
	::= { brtcpip 15 }

brpsTCPIPUseMethod  OBJECT-TYPE
	SYNTAX  INTEGER (0..7)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Method type that used set up TCP/IP configuration."
	::= { brtcpip 16 }

brpsTCPIPMethodServer  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"If TCP/IP configuration is setted up by any protocol server, 
         this entry indicates the IP address of the server."
	::= { brtcpip 17 }

brpsTCPIPAccessTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrpsTCPIPAccessEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents the list of IP address of node that has 
         parmittion for access.Up to 16 Netware addresses can be enabled."
	::= { brtcpip 18 }

brpsTCPIPAccessEntry  OBJECT-TYPE
	SYNTAX  BrpsTCPIPAccessEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the TCP/IP access table. Rows cannot be created or deleted
         via direct SNMP operations."
	INDEX  { brpsTCPIPAccessIndex }
	::= { brpsTCPIPAccessTable 1 }

BrpsTCPIPAccessEntry ::= 
	SEQUENCE {
		brpsTCPIPAccessIndex
			INTEGER,
		brpsTCPIPAccessNodeAddress
			IpAddress,
		brpsTCPIPAccessSubnetMask
			IpAddress
	}

brpsTCPIPAccessIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..16)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The TCP/IP access table's index."
	::= { brpsTCPIPAccessEntry 1 }

brpsTCPIPAccessNodeAddress  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"IP address of node that has parmittion for access."
	::= { brpsTCPIPAccessEntry 2 }

brpsTCPIPAccessSubnetMask  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Subnet mask of node that has parmittion for access."
	::= { brpsTCPIPAccessEntry 3 }

brpsAdvancedTCPIPAccessSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Advanced TCP/IP Access is not supported. 
         1 indicates Advanced TCP/IP Access support is implemented 
         by the firmware"
	::= { brtcpip 19 }

brpsAdvancedTCPIPAccessEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Advanced TCP/IP Access is not enabled. 
         1 indicates that the Advanced TCP/IP Access is enabled."
	::= { brtcpip 20 }

brpsAdvancedTCPIPAccessAdministratorIPAddress  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Administrator IPAddress."
	::= { brtcpip 21 }

brpsAdvancedTCPIPAccessSetting  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Advanced TCP/IP Access is reject. 
         1 indicates that the Advanced TCP/IP Access is Accept."
	::= { brtcpip 22 }

brpsNetwareSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Netware protocol is not supported. 1 indicates Netware
         support is implemented by the firmware. 2 indicates that Netware NEST NDS
         support is implemented by the firmware."
	::= { brnetware 1 }

brpsNetwareEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Netware protocol is not enabled. 1 indicates that the
         Netware protocol is enabled. Writing a non-zero value will enable the
         Netware protocol if it is supported by the firmware."
	::= { brnetware 2 }

brpsNetwareFrameType  OBJECT-TYPE
	SYNTAX  INTEGER (0..4)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The frame type to be used as follows: 0 = Automatic, 1 = Ethernet 802.3,
         2 = Ethernet II, 3 = Ethernet 802.2, 4 = Ethernet SNAP."
	::= { brnetware 3 }

brpsNetwarePollFreq  OBJECT-TYPE
	SYNTAX  INTEGER (2..60)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"File server polling frequency in seconds."
	::= { brnetware 4 }

brpsNetwareAdvFreq  OBJECT-TYPE
	SYNTAX  INTEGER (0..210)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Print server advertising frequency in seconds."
	::= { brnetware 5 }

brpsNetwarePassword  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..32) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Netware login password."
	::= { brnetware 6 }

brpsNetwareRestart  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Setting any value will restart the Netware protocol."
	::= { brnetware 7 }

brpsNetwareServerTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrpsNetwareServerEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents the list of enabled Netware servers.
         Up to 16 Netware servers can be enabled. If the service is
         configured for remote printer mode, then only the first
         enabled server is used and it should specify a print server."
	::= { brnetware 8 }

brpsNetwareServerEntry  OBJECT-TYPE
	SYNTAX  BrpsNetwareServerEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the Netware server table. Rows cannot be created or deleted
         via direct SNMP operations."
	INDEX  { brpsServiceIndex, brpsNetwareServerIndex }
	::= { brpsNetwareServerTable 1 }

BrpsNetwareServerEntry ::= 
	SEQUENCE {
		brpsNetwareServerIndex
			INTEGER,
		brpsNetwareServerName
			DisplayString
	}

brpsNetwareServerIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..16)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The Netware server number."
	::= { brpsNetwareServerEntry 1 }

brpsNetwareServerName  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..48) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Netware server name. Specifies a print server if the service is
         in remote printer mode, or a file server if the service is in
         queue server mode"
	::= { brpsNetwareServerEntry 2 }

brpsNetwarePasswordSet  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Returns 1 if the Netware login password is set, 0 if not set."
	::= { brnetware 9 }

brpsNDSSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates NDS is not supported. 1 indicates NDS is implemented
         by the firmware"
	::= { brnetware 10 }

brpsNetwareEtherIINetInfo  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..255) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Ethernet II network information."
	::= { brnetware 11 }

brpsNetwareEtherIICount  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Ethernet II network counter"
	::= { brnetware 12 }

brpsNetware8022NetInfo  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..255) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"IEEE 802.2 network information."
	::= { brnetware 13 }

brpsNetware8022Count  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"IEEE 802.2 network counter."
	::= { brnetware 14 }

brpsNetware8023NetInfo  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..255) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"IEEE 802.3 network information."
	::= { brnetware 15 }

brpsNetware8023Count  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"IEEE 802.3 network information."
	::= { brnetware 16 }

brpsNetwareSNAPNetInfo  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..255) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"SNAP network information."
	::= { brnetware 17 }

brpsNetwareSNAPCount  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"SNAP network count."
	::= { brnetware 18 }

brpsNetwareServicingServerName  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..48) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Netware server name."
	::= { brnetware 19 }

brpsNetwareServicingQueueName  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..48) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Netware queue name."
	::= { brnetware 20 }

brpsNetwareServicingServerCount  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Number of netware servers."
	::= { brnetware 21 }

brpsNetwareServicingQueueCount  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Number of netware queues."
	::= { brnetware 22 }

brpsNetwarePrintJob  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Number of netware print job."
	::= { brnetware 23 }

brpsAppleTalkSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the AppleTalk protocol is not supported. 1 indicates AppleTalk
         support is implemented by the firmware"
	::= { brappletalk 1 }

brpsAppleTalkEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the AppleTalk protocol is not enabled. 1 indicates that the
         AppleTalk protocol is enabled. Writing a non-zero value will enable the
         AppleTalk protocol if it is supported by the firmware."
	::= { brappletalk 2 }

brpsAppleTalkZone  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The AppleTalk zone the print server belongs to."
	::= { brappletalk 3 }

brpsAppleTalkPrintJob  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of AppleTalk print job."
	::= { brappletalk 4 }

brpsAppleTalkReadByte  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of read bytes."
	::= { brappletalk 5 }

brpsAppleTalkWriteByte  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of write bytes."
	::= { brappletalk 6 }

brpsAppleTalkReadError  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of read error."
	::= { brappletalk 7 }

brpsAppleTalkWriteError  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of write error."
	::= { brappletalk 8 }

brpsBanyanSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Banyan protocol is not supported. 1 indicates Banyan
         support is implemented by the firmware"
	::= { brbanyan 1 }

brpsBanyanEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Banyan protocol is not enabled. 1 indicates that the
         Banyan protocol is enabled. Writing a non-zero value will enable the
         Banyan protocol if is is supported by the firmware."
	::= { brbanyan 2 }

brpsBanyanLoginName  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..32) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Banyan login name."
	::= { brbanyan 3 }

brpsBanyanPassword  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..16) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Banyan login password."
	::= { brbanyan 4 }

brpsBanyanHopCount  OBJECT-TYPE
	SYNTAX  INTEGER (0..32767)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Number of hops between the print server and the Banyan file server."
	::= { brbanyan 5 }

brpsBanyanTimeout  OBJECT-TYPE
	SYNTAX  INTEGER (0..32767)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Timeout in seconds for a Banyan print job."
	::= { brbanyan 6 }

brpsBanyanPasswordSet  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Returns 1 if the Banyan login password is set, 0 if not set."
	::= { brbanyan 7 }

brpsBanyanIPNetworkID1  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(8) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"IP Network ID."
	::= { brbanyan 8 }

brpsBanyanIPNetworkID2  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(4) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"IP Network ID."
	::= { brbanyan 9 }

brpsBanyanRouter1  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(8) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Router ID."
	::= { brbanyan 10 }

brpsBanyanRouter2  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(4) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Router ID."
	::= { brbanyan 11 }

brpsBanyanIPPacket  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of IP Packet."
	::= { brbanyan 12 }

brpsBanyanErrorCS  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of CS error."
	::= { brbanyan 13 }

brpsBanyanErrorPT  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of PT error."
	::= { brbanyan 14 }

brpsBanyanErrorLE  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of LE error."
	::= { brbanyan 15 }

brpsBanyanPrintServerStatus  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..255) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Banyan print server status."
	::= { brbanyan 16 }

brpsBanyanServerAddress1  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(8) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Banyan server address."
	::= { brbanyan 17 }

brpsBanyanServerAddress2  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(4) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Banyan server address."
	::= { brbanyan 18 }

brpsBanyanIPCConnectionInformation  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..255) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"IPC connection information."
	::= { brbanyan 19 }

brpsBanyanIPCSequenceError  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"IPC sequence error."
	::= { brbanyan 20 }

brpsBanyanIPCListen  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..255) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"IPC listen."
	::= { brbanyan 21 }

brpsBanyanSPPConnectionInformation  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..255) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"SPP connection information."
	::= { brbanyan 22 }

brpsBanyanSPPSequenceError  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"SPP sequence error."
	::= { brbanyan 23 }

brpsBanyanSPPListen  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..255) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"SPP listen."
	::= { brbanyan 24 }

brpsEmailSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the POP3/SMTP email protocols are not supported. 1 indicates email
         support is implemented by the firmware"
	::= { bremail 1 }

brpsEmailEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the POP3/SMTP email protocols are not enabled. 1 indicates that the
         email protocols are enabled. Writing a non-zero value will enable the
         email protocols if they are supported by the firmware."
	::= { bremail 2 }

brpsPOP3Address  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The IP Address of the POP3 server."
	::= { bremail 3 }

brpsSMTPAddress  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The IP Address of the SMTP server."
	::= { bremail 4 }

brpsPOP3Name  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The POP3 mailbox name."
	::= { bremail 5 }

brpsPOP3Password  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The POP3 mailbox password."
	::= { bremail 6 }

brpsPOP3PollFreq  OBJECT-TYPE
	SYNTAX  INTEGER (10..32767)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The POP3 polling frequency in seconds."
	::= { bremail 7 }

brpsPOP3Timeout  OBJECT-TYPE
	SYNTAX  INTEGER (10..32767)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The POP3 timeout in minutes."
	::= { bremail 8 }

brpsPOP3PasswordSet  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Returns 1 if the POP3 password is set, 0 if not set."
	::= { bremail 9 }

brpsPOP3TotalMessage  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of POP3 message."
	::= { bremail 10 }

brpsPOP3TotalConnect  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of POP3 connect."
	::= { bremail 11 }

brpsPOP3TotalConnectFailure  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of POP3 connect error."
	::= { bremail 12 }

brpsPOP3TotalConnectionLost  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of POP3 connection lost."
	::= { bremail 13 }

brpsPOP3TotalUserFailure  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of POP3 user error."
	::= { bremail 14 }

brpsPOP3TotalPasswordFailure  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of POP3 pass error."
	::= { bremail 15 }

brpsPOP3TotalIOError  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of POP3 I/O error."
	::= { bremail 16 }

brpsSMTPTotalMessage  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of SMTP message."
	::= { bremail 17 }

brpsSMTPTotalConnect  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of SMTP connect."
	::= { bremail 18 }

brpsSMTPTotalConnectFailure  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of SMTP connect error."
	::= { bremail 19 }

brpsSMTPTotalRecvFromFailure  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of SMTP recvfrom error."
	::= { bremail 20 }

brpsSMTPTotalSendToFailure  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of SMTP recvfrom error."
	::= { bremail 21 }

brpsPOP3Supported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the POP3 protocol is not supported.
         1 indicates the POP3 protocol is implemented by the firmware"
	::= { bremail 101 }

brpsSMTPServerAuthMethod  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the user is not authenticated to the SMTP Server.
         1 indicates the user is authenticated to the SMTP Server using SMTP-AUTH.
         2 indicates the user is authenticated to the SMTP Server using POP before SMTP."
	::= { bremail 102 }

brpsSMTPAUTHUsername  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..60) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The username of SMTP server's account."
	::= { bremail 103 }

brpsSMTPAUTHPassword  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..32) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The password of SMTP server's account."
	::= { bremail 104 }

brpsSMTPAUTHPasswordSet  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Returns 1 if the SMTP server login password is set, 0 if not set."
	::= { bremail 105 }

brpsSmtpAUTHTimeout  OBJECT-TYPE
	SYNTAX  INTEGER (10..32767)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The SMTP-AUTH timeout in seconds."
	::= { bremail 106 }

brpsPOPbeforeSMTPWait  OBJECT-TYPE
	SYNTAX  INTEGER (0..32767)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The wait time in mili seconds between POP logging in and SMTP sending."
	::= { bremail 107 }

brpsAPOPEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the APOP is not enabled.
         1 indicates the APOP is enabled."
	::= { bremail 108 }

brpsSMTPEnhancedAuthSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..7)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Bit0 indicates 'no authentication'.
         Bit1 indicates 'SMTP-AUTH'.
         Bit2 indicates 'POP before SMTP'."
	::= { bremail 150 }

brpsAPOPSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the APOP is not supported.
         1 indicates the APOP is supported."
	::= { bremail 151 }

brpsEmailSendTestSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Email Sending Test is not supported.
         1 indicates the Email Sending Test is supported."
	::= { bremail 152 }

brpsEmailRecvTestSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Email Receiving Test is not supported.
         1 indicates the Email Receiving Test is supported."
	::= { bremail 153 }

brpsChangeSMTPPortSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates to change the SMTP port number is not supported.
         1 indicates to change the SMTP port number is supported."
	::= { bremail 154 }

brpsSMTPPortNumber  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The port number of connection to SMTP server."
	::= { bremail 155 }

brpsChangePOP3PortSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates to change the POP3 port number is not supported.
         1 indicates to change the POP3 port number is supported."
	::= { bremail 156 }

brpsPOP3PortNumber  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The port number of connection to POP3 server."
	::= { bremail 157 }

brpsTmpSMTPServerName  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..64) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Temporary Setting for Email Sending Test.
         The FQDN name of the SMTP server."
	::= { bremail 170 }

brpsTmpSMTPServerAuthMethod  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Temporary Setting for Email Sending Test.
         0 indicates the user is not authenticated to the SMTP Server.
         1 indicates the user is authenticated to the SMTP Server using SMTP-AUTH.
         2 indicates the user is authenticated to the SMTP Server using POP before SMTP."
	::= { bremail 171 }

brpsTmpSMTPAUTHUsername  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..60) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Temporary Setting for Email Sending Test.
         The username of SMTP server's account."
	::= { bremail 172 }

brpsTmpSMTPAUTHPassword  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..32) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Temporary Setting for Email Sending Test.
         The password of SMTP server's account."
	::= { bremail 173 }

brpsTmpPOP3ServerName  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..64) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Temporary Setting for Email Sending/Receiving Test.
         The FQDN name of the POP3 server."
	::= { bremail 174 }

brpsTmpPOP3Name  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Temporary Setting for Email Sending/Receiving Test.
         The POP3 mailbox name."
	::= { bremail 175 }

brpsTmpPOP3Password  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Temporary Setting for Email Sending/Receiving Test.
         The POP3 mailbox password."
	::= { bremail 176 }

brpsTmpPrintersEmailaddress  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Temporary Setting for Email Sending Test.
         The Printers Email Address"
	::= { bremail 177 }

brpsTmpAPOPEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Temporary Setting for Email Sending/Receiving Test.
         0 indicates the APOP is not enabled.
         1 indicates the APOP is enabled."
	::= { bremail 178 }

brpsTmpSMTPAUTHPasswordModified  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the SMTP server password is not modified.
         1 indicates the SMTP server password is modified."
	::= { bremail 179 }

brpsTmpPOP3PasswordModified  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the POP3 server password is not modified.
         1 indicates the POP3 server password is modified."
	::= { bremail 180 }

brpsTmpSMTPPortNumber  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Temporary Setting for Email Sending Test.
         The port number of connection to SMTP server."
	::= { bremail 181 }

brpsTmpPOP3PortNumber  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Temporary Setting for Email Sending/Receiving Test.
         The port number of connection to POP3 server."
	::= { bremail 182 }

brpsEmailSendTestMail  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the testmail is not sended at Email Sending Test.
         1 indicates the testmail is sended at Email Sending Test."
	::= { bremail 200 }

brpsEmailTestDestinationAddress  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..80) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The destination Email Address of Email Sending Test."
	::= { bremail 201 }

brpsEmailSendTestCall  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Email Sending Test is not called on. ('READY')
         1 indicates the Email Sending Test is called on. ('START')
         2 indicates the Email Sending Test is canceled. ('CANCEL')"
	::= { bremail 210 }

brpsEmailRecvTestCall  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Email Receiving Test is not called on. ('READY')
         1 indicates the Email Receiving Test is called. ('START')
         2 indicates the Email Receiving Test is canceled. ('CANCEL')"
	::= { bremail 211 }

brpsEmailSendRecvTestCall  OBJECT-TYPE
	SYNTAX  INTEGER (0..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Email Sending and Receiving Test is not called on. ('READY')
         1 indicates the Email Sending and Receiving Test is called. ('START')
         2 indicates the Email Sending and Receiving Test is canceled. ('CANCEL')"
	::= { bremail 212 }

brpsEmailTestResult  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"This Value is divided into 15-8bit and 7-0bit.
         15-8bit indicates Email Sending Test's Result.
         7-0bit indicates Email Receiving Test's Result.
         (However, the value from 0x00 to 0x04 is only set to 7-0bit.)
         0x00 indicates the Email Test is not running.
         0x01 indicates the Email Test is running. ('BUSY')
         0x02 indicates the Email Test is canceled.('CANCELED')
         0x03 indicates the Email Test is Locked by another Email Test process.(LOCKED)
         0x04 indicates the Email Test is not executed. -- unused
         0x10 indicates the Status 'Fatal Error is occurred during Email Testing'.
         0x20 indicates the Status 'Invalid value is set in SMTP Server Address'.
         0x21 indicates the Status 'Invalid value is set in SMTP-AUTH Account Name'.
         0x22 indicates the Status 'Invalid value is set in SMTP-AUTH Password'.
         0x30 indicates the Status 'Invalid value is set in POP3 Server Address'.
         0x31 indicates the Status 'Invalid value is set in POP3 Account Name'.
         0x32 indicates the Status 'Invalid value is set in POP3 Account Password'.
         0x40 indicates the Status 'Invalid value is set in POP3 Printer E-mail Address'.
         0x41 indicates the Status 'Invalid value is set in Destination E-mail Address'.
         0x50 indicates the Status 'Sending OK'.
         0x51 indicates the Status 'Cannot locate SMTP server'.
         0x52 indicates the Status 'Cannot connect to SMTP server'.
         0x53 indicates the Status 'Cannot authenticate with SMTP server by using method'.
         0x54 indicates the Status 'Authentication failure in SMTP server'.
         0x55 indicates the Status 'Email Sending Timeout' -- unused
         0x60 indicates the Status 'Receiving OK'.
         0x61 indicates the Status 'Cannot locate POP3 server'.
         0x62 indicates the Status 'Cannot connect to POP3 server'.
         0x63 indicates the Status 'Authentication failure in POP3 server'.
         0x64 indicates the Status 'Cannot authenticate with POP3 server by using method'.
         0x65 indicates the Status 'Email Receiving Timeout' -- unused
         0x66 indicates the Email Test is Locked by another Email process.
         0x70 indicates the Status 'Cannot resolve the host name (DNS Error)'."
	::= { bremail 220 }

brpsPOP3TotalAPOPFailure  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of POP3 APOP error."
	::= { bremail 221 }

brpsDLCSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the DLC protocol is not supported. 1 indicates DLC
         support is implemented by the firmware"
	::= { brdlc 1 }

brpsDLCEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the DLC protocol is not enabled. 1 indicates that the
         DLC protocol is enabled. Writing a non-zero value will enable the
         DLC protocol if is is supported by the firmware."
	::= { brdlc 2 }

brpsDLCPrintStatus  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..32) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"DLC print status."
	::= { brdlc 3 }

brpsDLCLLCState  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"LLC status."
	::= { brdlc 4 }

brpsDLCLLCConnectHost  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..32) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"LLC connected host name."
	::= { brdlc 5 }

brpsDLCLLCLastIFrame  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"LLC last valid I-frame."
	::= { brdlc 6 }

brpsDLCLLCRecvPacket  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Counter of LLC receive packets."
	::= { brdlc 7 }

brpsDLCLLCPortStatus  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..32) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Port status."
	::= { brdlc 8 }

brpsIPPSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the IPP protocol is not supported. 1 indicates IPP
         support is implemented by the firmware"
	::= { bripp 1 }

brpsIPPEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the IPP protocol is not enabled. 1 indicates that the IPP
         protocol is enabled. Writing a non-zero value will enable the IPP
         protocol if it is supported by the firmware."
	::= { bripp 2 }

brIPPRegularPortEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates that the IPP regular port is not enabled.
         1 indicates that the IPP regular port is enabled."
	::= { bripp 3 }

brIPPSSLPortEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates that the IPP SSL port is not enabled.
         1 indicates that the IPP SSL port is enabled."
	::= { bripp 4 }

brIPPOriginalPortEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates that the IPP original port is not enableed.
         1 indicates that the IPP original port is enabled."
	::= { bripp 5 }

brpsNtSendSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the NtSend protocol is not supported. 1 indicates NtSend
         support is implemented by the firmware"
	::= { brntsend 1 }

brpsNtSendEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the NtSend protocol is not enabled. 1 indicates that the
         NtSend protocol is enabled. Writing a non-zero value will enable the
         NtSend protocol if it is supported by the firmware."
	::= { brntsend 2 }

brpsFirmwareIPAddress  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The IP address of the host from which to TFTP GET the firmware."
	::= { brfirmware 1 }

brpsFirmwareHost  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Netware server name from which to fetch the firmware."
	::= { brfirmware 2 }

brpsFirmwareFile  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The firmware file to be reloaded."
	::= { brfirmware 3 }

brpsFirmwareReload  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Setting a value of 0x5A will cause the print server to begin attempts to
         reload the firmware within 5 seconds. Further attempts to read or modify
         the configuration are not recommended until after the firmware has been
         reloaded."
	::= { brfirmware 4 }

brpsFirmwareDescription  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Firmware version and date."
	::= { brfirmware 5 }

brpsFirmwareXModem  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Set 1 execute XMODEM mode."
	::= { brfirmware 6 }

brpsFirmwareAdvancedAddressSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Firmware Advanced Address is not supported. 
         1 indicates Firmware Advanced Address support is implemented 
         by the firmware"
	::= { brfirmware 7 }

brpsFirmwareAdvancedAddress  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..64) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The address of the host from which to TFTP GET the firmware."
	::= { brfirmware 8 }

brpsNetBEUISupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the NetBEUI protocol is not supported. 1 indicates NetBEUI
         support is implemented by the firmware"
	::= { brnetbeui 1 }

brpsNetBEUIEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the NetBEUI protocol is not enabled. 1 indicates that the
         NetBEUI protocol is enabled. Writing a non-zero value will enable the
         NetBEUI protocol if is is supported by the firmware."
	::= { brnetbeui 2 }

brpsNetBEUIDomain  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..15) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The NetBEUI domain name."
	::= { brnetbeui 3 }

brpsNetBIOSIPSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the NetBIOS IP protocol is not supported. 1 indicates NetBIOS IP
         support is implemented by the firmware"
	::= { brnetbeui 4 }

brpsNetBIOSIPEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the NetBIOS IP protocol is not enabled. 1 indicates that the
         NetBIOS IP protocol is enabled. Writing a non-zero value will enable the
         NetBIOS IP protocol if is is supported by the firmware."
	::= { brnetbeui 5 }

brpsNetBIOSIPMethod  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Method used to determine the IP Address of the WINS server.
         AUTO tries all protocols (currently only DHCP is tried).
         STATIC uses the configured address."
	::= { brnetbeui 6 }

brpsNetBIOSPrimaryWINSAddr  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The IP Address of the primary WINS server."
	::= { brnetbeui 7 }

brpsNetBIOSSecondaryWINSAddr  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The IP Address of the secondary WINS server."
	::= { brnetbeui 8 }

brpsNetBIOSPrintingSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the NetBIOS IP protocol printing is not supported. 
         1 indicates the NetBIOS IP protocol printing is supported."
	::= { brnetbeui 101 }

brLPDType  OBJECT-TYPE
	SYNTAX  INTEGER (0..3)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { broriginaltcpip 1 }

brFTPSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the FTP protocol is not supported. 1 indicates FTP
         support is implemented by the firmware"
	::= { broriginalftp 1 }

brFTPEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the FTP protocol is not enabled. 1 indicates that the FTP
         protocol is enabled. Writing a non-zero value will enable the FTP
         protocol if it is supported by the firmware."
	::= { broriginalftp 2 }

brUPnPSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the UPnP protocol is not supported. 
         1 indicates UPnP support is implemented by the firmware"
	::= { broriginalupnp 1 }

brUPnPEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the UPnP protocol is not enabled.
         1 indicates that the UPnP protocol is enabled.
         Writing a non-zero value will enable the UPnP
         protocol if it is supported by the firmware."
	::= { broriginalupnp 2 }

brAPIPASupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the APIPA protocol is not supported. 
         1 indicates APIPA support is implemented by the firmware"
	::= { broriginalapipa 1 }

brAPIPAEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the APIPA protocol is not enabled.
         1 indicates that the APIPA protocol is enabled.
         Writing a non-zero value will enable the APIPA
         protocol if it is supported by the firmware."
	::= { broriginalapipa 2 }

brmDNSSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the mDNS protocol is not supported. 
         1 indicates mDNS support is implemented by the firmware"
	::= { broriginalmdns 1 }

brmDNSEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the mDNS protocol is not enabled.
         1 indicates that the mDNS protocol is enabled.
         Writing a non-zero value will enable the mDNS
         protocol if it is supported by the firmware."
	::= { broriginalmdns 2 }

brmDNSPrinterName  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..64) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"mDNS Service Name"
	::= { broriginalmdns 3 }

brLAASupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"This value indicate if the locally administered addressing is supported.
         0: Not Supported
         1: Supported"
	::= { broriginalLAA 1 }

brLAAMacAddress  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(6) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"A locally administered address (LAA) that replaces the factory-assigned
         MAC address (UAA: universally administered address).
         If this setting is all-zero address (i.e. 6 bytes of zeros), the
         factory-assigned
         MAC address is used as the MAC address of the device.
         The LAA address must start with X2, X6, XA, or XE, where X is any
         hexadecimal digit 0 to F."
	::= { broriginalLAA 2 }

brIPv6Supported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the IPv6 protocol is not supported. 
         1 indicates IPv6 support is implemented by the firmware"
	::= { broriginalIPv6 1 }

brIPv6Enable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the IPv6 protocol is not enabled.
         1 indicates that the IPv6 protocol is enabled.
         Writing a non-zero value will enable the IPv6
         protocol if it is supported by the firmware."
	::= { broriginalIPv6 2 }

brIPv6Priority  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the IPv6 protocol is low priority.
         1 indicates that the IPv6 protocol is high priority."
	::= { broriginalIPv6 3 }

brtelnetSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the telnet protocol is not supported. 
         1 indicates telnet support is implemented by the firmware"
	::= { broriginaltelnet 1 }

brtelnetEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the telnet protocol is not enabled.
         1 indicates that the telnet protocol is enabled.
         Writing a non-zero value will enable the telnet
         protocol if it is supported by the firmware."
	::= { broriginaltelnet 2 }

brEWSSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the EWS protocol is not supported. 
         1 indicates EWS support is implemented by the firmware"
	::= { broriginalEWS 1 }

brEWSEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the EWS protocol is not enabled.
         1 indicates that the EWS protocol is enabled.
         Writing a non-zero value will enable the EWS
         protocol if it is supported by the firmware."
	::= { broriginalEWS 2 }

brEWSRegularPortEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates that the EWS regular port is not enabled.
         1 indicates that the EWS regular port is enabled."
	::= { broriginalEWS 3 }

brEWSSSLPortEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates that the EWS SSL port is not enabled.
         1 indicates that the EWS SSL port is enabled."
	::= { broriginalEWS 4 }

brSNMPSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the SNMP protocol is not supported. 
         1 indicates SNMP support is implemented by the firmware"
	::= { broriginalSNMP 1 }

brSNMPEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the SNMP protocol is not enabled.
         1 indicates that the SNMP protocol is enabled.
         Writing a non-zero value will enable the SNMP
         protocol if it is supported by the firmware."
	::= { broriginalSNMP 2 }

brTFTPSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the TFTP protocol is not supported. 
         1 indicates TFTP support is implemented by the firmware"
	::= { broriginalTFTP 1 }

brTFTPEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the TFTP protocol is not enabled.
         1 indicates that the TFTP protocol is enabled.
         Writing a non-zero value will enable the TFTP
         protocol if it is supported by the firmware."
	::= { broriginalTFTP 2 }

brHTTPSSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the HTTPS/IPPS protocol is not supported. 
         1 indicates HTTPS/IPPS support is implemented by the firmware"
	::= { broriginalHTTPS 1 }

brHTTPSEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the HTTPS/IPPS protocol is not enabled.
         1 indicates that the HTTPS/IPPS protocol is enabled.
         Writing a non-zero value will enable the HTTPS/IPPS
         protocol if it is supported by the firmware."
	::= { broriginalHTTPS 2 }

brLPDSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the LPD protocol is not supported. 
         1 indicates LPD support is implemented by the firmware"
	::= { broriginalLPD 1 }

brLPDEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the LPD protocol is not enabled.
         1 indicates that the LPD protocol is enabled.
         Writing a non-zero value will enable the LPD
         protocol if it is supported by the firmware."
	::= { broriginalLPD 2 }

brRawPortSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the RawPort protocol is not supported. 
         1 indicates RawPort support is implemented by the firmware"
	::= { broriginalRawPort 1 }

brRawPortEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the RawPort protocol is not enabled.
         1 indicates that the RawPort protocol is enabled.
         Writing a non-zero value will enable the RawPort
         protocol if it is supported by the firmware."
	::= { broriginalRawPort 2 }

brLLTDSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the LLTD protocol is not supported. 
         1 indicates LLTD support is implemented by the firmware"
	::= { broriginalLLTD 1 }

brLLTDEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the LLTD protocol is not enabled.
         1 indicates that the LLTD protocol is enabled.
         Writing a non-zero value will enable the LLTD
         protocol if it is supported by the firmware."
	::= { broriginalLLTD 2 }

brWebServicesSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the WebServices protocol is not supported. 
         1 indicates WebServices support is implemented by the firmware"
	::= { broriginalWebServices 1 }

brWebServicesEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the WebServices protocol is not enabled.
         1 indicates that the WebServices protocol is enabled.
         Writing a non-zero value will enable the WebServices
         protocol if it is supported by the firmware."
	::= { broriginalWebServices 2 }

brWebServicesRegularPortEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates that the WebServices regular port is not enabled.
         1 indicates that the WebServices regular port is enabled."
	::= { broriginalWebServices 3 }

brWebServicesSSLPortEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates that the WebServices SSL port is not enabled.
         1 indicates that the WebServices SSL port is enabled."
	::= { broriginalWebServices 4 }

brLLMNREnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the LLMNR protocol is not enabled.
         1 indicates that the LLMNR protocol is enabled."
	::= { broriginalLLMNR 1 }

brProxySupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Proxy is not supported. 
         1 indicates Proxy is implemented by the firmware"
	::= { broriginalproxy 1 }

brProxyEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Proxy is not enabled.
         1 indicates that the Proxy is enabled.
         Writing a non-zero value will enable the Proxy
         if it is supported by the firmware."
	::= { broriginalproxy 2 }

brProxyBypassServer  OBJECT-TYPE
	SYNTAX  INTEGER {
		off(1),
		on(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"on(2) indicates the bypass proxy server for local addresses."
	::= { broriginalproxy 3 }

brProxyServerCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Maximum number of Proxy Server INDEX."
	::= { broriginalproxy 11 }

brProxyServerInfoTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrProxyServerInfoEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents Proxy Server Info."
	::= { broriginalproxy 12 }

brProxyServerInfoEntry  OBJECT-TYPE
	SYNTAX  BrProxyServerInfoEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the ProxyServerInfo table. Rows cannot be created or deleted."
	INDEX  { brProxyServerInfoIndex }
	::= { brProxyServerInfoTable 1 }

BrProxyServerInfoEntry ::= 
	SEQUENCE {
		brProxyServerInfoIndex
			INTEGER,
		brProxyServerType
			INTEGER,
		brProxyServerName
			OCTET STRING,
		brProxyServerPort
			INTEGER
	}

brProxyServerInfoIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..16)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number."
	::= { brProxyServerInfoEntry 1 }

brProxyServerType  OBJECT-TYPE
	SYNTAX  INTEGER {
		http(1),
		secure(2),
		ftp(3),
		gopher(4),
		socks(5)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Proxy server ID."
	::= { brProxyServerInfoEntry 2 }

brProxyServerName  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..80) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Proxy server name."
	::= { brProxyServerInfoEntry 3 }

brProxyServerPort  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Proxy server port number."
	::= { brProxyServerInfoEntry 4 }

brJobTerminationSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Job termination setting is not supported. 
         1 indicates the Job termination setting is implemented by the firmware"
	::= { broriginalJobTermination 1 }

brJobTerminationEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Job termination setting is not enabled.
         1 indicates the Job termination setting is enabled."
	::= { broriginalJobTermination 2 }

brSNMPTrapTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrSNMPTrapEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { broriginalSNMPTrap 1 }

brSNMPTrapEntry  OBJECT-TYPE
	SYNTAX  BrSNMPTrapEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brSNMPTrapIndex }
	::= { brSNMPTrapTable 1 }

BrSNMPTrapEntry ::= 
	SEQUENCE {
		brSNMPTrapIndex
			INTEGER,
		brTCPIPServerAddress
			IpAddress
	}

brSNMPTrapIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..16)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number."
	::= { brSNMPTrapEntry 1 }

brTCPIPServerAddress  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Destination IP Address for SNMP Trap."
	::= { brSNMPTrapEntry 2 }

brLegacyCompatible  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { broriginalLegacy 1 }

brDeviceNegotiationEncryptVer  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brSecurityGeneralStatus 1 }

brpsServerCertificateNum  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brSecurityGeneralStatus 2 }

brDeviceNegotiationGetChallenge  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(20) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brSecurityDeviceNegotiation 1 }

brDeviceNegotiationConfirmPassword  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(60) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brSecurityDeviceNegotiation 2 }

brDeviceNegotiationChangePassword  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(48) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brSecurityDeviceNegotiation 3 }

brpsWLanDot11Supported  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Flags per bit.
         802.11a bit1=1
         802.11b bit2=1
         802.11g bit3=1"
	::= { wlCapability 1 }

brpsWLanAvailableChannel  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The available channel list."
	::= { wlCapability 2 }

brpsWLanCapabilityEncryptModeCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"index"
	::= { wlCapability 3 }

brpsWLanCapabilityEncryptModeTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrpsWLanCapabilityEncryptModeEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { wlCapability 4 }

brpsWLanCapabilityEncryptModeEntry  OBJECT-TYPE
	SYNTAX  BrpsWLanCapabilityEncryptModeEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brpsWLanCapabilityEncryptModeIndex }
	::= { brpsWLanCapabilityEncryptModeTable 1 }

BrpsWLanCapabilityEncryptModeEntry ::= 
	SEQUENCE {
		brpsWLanCapabilityEncryptModeIndex
			INTEGER,
		brpsWLanCapabilityEncryptModeType
			INTEGER,
		brpsWLanCapabilityEncryptModeDescription
			OCTET STRING,
		brpsWLanCapabilityEncryptModeSupported
			INTEGER
	}

brpsWLanCapabilityEncryptModeIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..255)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"index"
	::= { brpsWLanCapabilityEncryptModeEntry 1 }

brpsWLanCapabilityEncryptModeType  OBJECT-TYPE
	SYNTAX  INTEGER {
		none(1),
		wep(2),
		tkip(3),
		aes(4),
		ckip(5)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Encrypt Mode Type"
	::= { brpsWLanCapabilityEncryptModeEntry 2 }

brpsWLanCapabilityEncryptModeDescription  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Encrypt Mode description"
	::= { brpsWLanCapabilityEncryptModeEntry 3 }

brpsWLanCapabilityEncryptModeSupported  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0: Not Supported
         1: Supported"
	::= { brpsWLanCapabilityEncryptModeEntry 4 }

brpsWLanCapabilityAuthModeCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"index"
	::= { wlCapability 5 }

brpsWLanCapabilityAuthModeTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrpsWLanCapabilityAuthModeEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { wlCapability 6 }

brpsWLanCapabilityAuthModeEntry  OBJECT-TYPE
	SYNTAX  BrpsWLanCapabilityAuthModeEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brpsWLanCapabilitAuthModeIndex }
	::= { brpsWLanCapabilityAuthModeTable 1 }

BrpsWLanCapabilityAuthModeEntry ::= 
	SEQUENCE {
		brpsWLanCapabilitAuthModeIndex
			INTEGER,
		brpsWLanCapabilityAuthModeType
			INTEGER,
		brpsWLanCapabilityAuthModeDescription
			OCTET STRING,
		brpsWLanCapabilityAuthModeSupported
			INTEGER
	}

brpsWLanCapabilitAuthModeIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..255)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"index"
	::= { brpsWLanCapabilityAuthModeEntry 1 }

brpsWLanCapabilityAuthModeType  OBJECT-TYPE
	SYNTAX  INTEGER {
		opensystem(1),
		shardkey(2),
		wpa-psk(3),
		wpa-none(4),
		wpa(5),
		wpa2(6),
		leap(7),
		eapfast-none(8),
		eapfast-mschapv2(9),
		eapfast-gtc(10),
		eapfast-tls(11),
		wpa2-psk(12)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Auth Mode Type"
	::= { brpsWLanCapabilityAuthModeEntry 2 }

brpsWLanCapabilityAuthModeDescription  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Auth Mode description"
	::= { brpsWLanCapabilityAuthModeEntry 3 }

brpsWLanCapabilityAuthModeSupported  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0: Not Supported
         1: Supported"
	::= { brpsWLanCapabilityAuthModeEntry 4 }

brpsWLanCapabilityAuthEAPCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"index"
	::= { wlCapability 7 }

brpsWLanCapabilityAuthEAPTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrpsWLanCapabilityAuthEAPEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { wlCapability 8 }

brpsWLanCapabilityAuthEAPEntry  OBJECT-TYPE
	SYNTAX  BrpsWLanCapabilityAuthEAPEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brpsWLanCapabilityAuthEAPIndex }
	::= { brpsWLanCapabilityAuthEAPTable 1 }

BrpsWLanCapabilityAuthEAPEntry ::= 
	SEQUENCE {
		brpsWLanCapabilityAuthEAPIndex
			INTEGER,
		brpsWLanCapabilityAuthEAPType
			INTEGER,
		brpsWLanCapabilityAuthEAPDescription
			OCTET STRING,
		brpsWLanCapabilityAuthEAPSupported
			INTEGER,
		brpsWLanCapabilityAuthEAPSupportAuthentication
			INTEGER,
		brpsWLanCapabilityAuthEAPSupportEncryption
			INTEGER
	}

brpsWLanCapabilityAuthEAPIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"index"
	::= { brpsWLanCapabilityAuthEAPEntry 1 }

brpsWLanCapabilityAuthEAPType  OBJECT-TYPE
	SYNTAX  INTEGER {
		eap-md5(1),
		eap-tls(2),
		eap-ttls(3),
		peap(4),
		leap(5),
		eapfast-none(6),
		eapfast-mschapv2(7),
		eapfast-gtc(8),
		eapfast-tls(9)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Auth EAP Type"
	::= { brpsWLanCapabilityAuthEAPEntry 2 }

brpsWLanCapabilityAuthEAPDescription  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Auth EAP description"
	::= { brpsWLanCapabilityAuthEAPEntry 3 }

brpsWLanCapabilityAuthEAPSupported  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0: Not Supported
         1: Supported"
	::= { brpsWLanCapabilityAuthEAPEntry 4 }

brpsWLanDestination  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The destination of this NIC."
	::= { wlGeneralInfo 1 }

brpsWLanTransmitLevel  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The level of the transmitting power."
	::= { wlGeneralInfo 2 }

brpsPit3WLanTestStatus  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The test status of pit3 for WLAN."
	::= { wlGeneralInfo 3 }

brpsWLanNetSearchSupported  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Wireless network serch function is not supported. 
         1 indicates the Wireless network serch function is implemented by the firmware"
	::= { wlNetSearch 1 }

brpsAvailableWLanScan  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Read:	unsupported(1), supported(2)
         Set: 	<Format>= <1:Start | 2:Start w/ option>[;<sWLanName>;<sWLanCommMode>[;<sWLanChannel>]]"
	::= { wlNetSearch 2 }

brpsAvailableWLanScanWaitTime  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Scan Wait Time"
	::= { wlNetSearch 3 }

brpsAvailableWLanCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Available wireless LAN count."
	::= { wlNetSearch 10 }

brpsAvailableWLanTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrpsAvailableWLanEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents Available Wireless LAN Info."
	::= { wlNetSearch 11 }

brpsAvailableWLanEntry  OBJECT-TYPE
	SYNTAX  BrpsAvailableWLanEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the brpsAvailableWLanTable table. Rows cannot be created or deleted."
	INDEX  { brpsAvailableWLanIndex }
	::= { brpsAvailableWLanTable 1 }

BrpsAvailableWLanEntry ::= 
	SEQUENCE {
		brpsAvailableWLanIndex
			INTEGER,
		brpsAvailableWLanName
			OCTET STRING,
		brpsAvailableWLanMode
			INTEGER,
		brpsAvailableWLanCommMode
			INTEGER,
		brpsAvailableWLanChannel
			INTEGER,
		brpsAvailableWLanPowerLevel
			INTEGER,
		brpsAvailableWLanAuthMode
			INTEGER,
		brpsAvailableWLanEncryptMode
			INTEGER
	}

brpsAvailableWLanIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..255)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number."
	::= { brpsAvailableWLanEntry 1 }

brpsAvailableWLanName  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..32) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The available wireless LAN name."
	::= { brpsAvailableWLanEntry 2 }

brpsAvailableWLanMode  OBJECT-TYPE
	SYNTAX  INTEGER {
		dot11b-gAuto(1),
		dot11b(2),
		dot11g(3)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The available wireless LAN mode."
	::= { brpsAvailableWLanEntry 3 }

brpsAvailableWLanCommMode  OBJECT-TYPE
	SYNTAX  INTEGER {
		accesPoint(1),
		adhoc-Wi-Fi(2)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The available wireless LAN communication mode."
	::= { brpsAvailableWLanEntry 4 }

brpsAvailableWLanChannel  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The available wireless LAN channel."
	::= { brpsAvailableWLanEntry 5 }

brpsAvailableWLanPowerLevel  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The available wireless LAN power level."
	::= { brpsAvailableWLanEntry 6 }

brpsAvailableWLanAuthMode  OBJECT-TYPE
	SYNTAX  INTEGER {
		none(1),
		active(2)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The available wireless LAN authentication mode."
	::= { brpsAvailableWLanEntry 7 }

brpsAvailableWLanEncryptMode  OBJECT-TYPE
	SYNTAX  INTEGER {
		none(1),
		active(2),
		wep(3),
		tkip(4)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The available wireless LAN encryption mode."
	::= { brpsAvailableWLanEntry 8 }

brpsWLanAOSSSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates AOSS is not supported. 
         1 indicates AOSS is supported."
	::= { wlAOSS 1 }

brpsWLanAOSSIsRunnning  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"
"
	::= { wlAOSS 2 }

brpsWLanSESSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates SES is not supported. 
         1 indicates SES is supported."
	::= { wlSES 1 }

brpsWLanWPSSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates WPS is not supported. 
         1 indicates WPS is supported."
	::= { wlWPS 1 }

brpsWLanWPSResult  OBJECT-TYPE
	SYNTAX  INTEGER (-1..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0  indicates WPS is successful. 
         1  indicates WPS is not successful.
         -1 indicates WPS is being set up.
"
	::= { wlWPS 2 }

brpsWLanAPSetupMode  OBJECT-TYPE
	SYNTAX  INTEGER {
		false(1),
		true(2)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"AP setup mode status."
	::= { wlGeneral 1 }

brpsWLanMode  OBJECT-TYPE
	SYNTAX  INTEGER {
		dot11b-gAuto(1),
		dot11b(2),
		dot11g(3),
		dot11g-turbo(4)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN mode."
	::= { wlGeneral 2 }

brpsWLanName  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..32) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The SSID setting."
	::= { wlGeneral 3 }

brpsWLanCommMode  OBJECT-TYPE
	SYNTAX  INTEGER {
		infrastructure(1),
		ad-Hoc-WiFi(2),
		ad-Hoc(3)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN communication mode."
	::= { wlGeneral 4 }

brpsWLanChannel  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN channel setting."
	::= { wlGeneral 5 }

brpsWLanCtsMode  OBJECT-TYPE
	SYNTAX  INTEGER {
		auto(1),
		always(2),
		none(3)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN CTS mode setting."
	::= { wlAdvanced 1 }

brpsWLanCtsRate  OBJECT-TYPE
	SYNTAX  INTEGER (0..32767)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN CTS rate setting.
         However, the unit is 1/10."
	::= { wlAdvanced 2 }

brpsWLanCtsType  OBJECT-TYPE
	SYNTAX  INTEGER {
		only(1),
		rts-cts(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN CTS type setting."
	::= { wlAdvanced 3 }

brpsWLanRtsCtsThreshold  OBJECT-TYPE
	SYNTAX  INTEGER (256..2346)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN RTS/CTS threshold setting."
	::= { wlAdvanced 4 }

brpsWLanLengthThreshold  OBJECT-TYPE
	SYNTAX  INTEGER (256..2346)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN length threshold setting."
	::= { wlAdvanced 5 }

brpsWLanDataRetry  OBJECT-TYPE
	SYNTAX  INTEGER (0..15)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN DATA retyr setting."
	::= { wlAdvanced 6 }

brpsWLanTransmitPowerSetting  OBJECT-TYPE
	SYNTAX  INTEGER {
		full(1),
		half(2),
		quarter(3),
		one-eighth(4),
		minimum(5),
		off(6)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN transmit power setting."
	::= { wlAdvanced 7 }

brpsWLanDeviceType  OBJECT-TYPE
	SYNTAX  INTEGER {
		stationMode(1),
		accessPointMode(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN operation mode setting."
	::= { wlAdvanced 8 }

brpsWLanEncryptMode  OBJECT-TYPE
	SYNTAX  INTEGER {
		none(1),
		wep(2),
		tkip(3),
		aes(4)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN encrypt mode."
	::= { wlAssociate 1 }

brpsWLanAuthMode  OBJECT-TYPE
	SYNTAX  INTEGER {
		openSystem(1),
		sharedKey(2),
		wpa-psk(3),
		wpa-none(4),
		wpa(5),
		wpa2(6),
		leap(7),
		eapfast-none(8),
		eapfast-mschapv2(9),
		eapfast-gtc(10),
		eapfast-tls(11),
		wpa2-psk(12)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN authenticate mode."
	::= { wlAssociate 11 }

brpsWLanAuthEAP  OBJECT-TYPE
	SYNTAX  INTEGER {
		eapMD5(1),
		eapTLS(2),
		eapTTLS(3),
		peap(4),
		leap(5),
		eapfast-none(6),
		eapfast-mschapv2(7),
		eapfast-gtc(8),
		eapfast-tls(9)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN authenticate EAP."
	::= { wlAssociate 12 }

brpsWLanAuthUserID  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..32) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN authenticate user ID."
	::= { wlAssociate 13 }

brpsWLanAuthUserPass  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..32) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN authenticate user password."
	::= { wlAssociate 14 }

brpsWLanAssociate  OBJECT-TYPE
	SYNTAX  INTEGER {
		update(1),
		print(2),
		test(3),
		applyonly(4),
		simplewizard(5)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN associate."
	::= { wlAssociate 21 }

brpsWLanAssociateTestResult  OBJECT-TYPE
	SYNTAX  INTEGER {
		linkOK(1),
		noSuchWLan(2),
		invalidWLanNetKey(3),
		invalidAuthUserID(4),
		invalidAuthUserPass(5)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN associate test result.
         0 indicates cleared."
	::= { wlAssociate 22 }

brpsWLanAssociateResult  OBJECT-TYPE
	SYNTAX  INTEGER {
		linkOK(1),
		noSuchWLan(2),
		invalidWLanNetKey(3),
		invalidAuthUserID(4),
		invalidAuthUserPass(5)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN associate result.
         0 indicates cleared."
	::= { wlAssociate 23 }

brpsWLanAssociateTestSupported  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Wireless network Associate Test function 
         is not supported. 
         1 indicates the Wireless network Associate Test function 
         is implemented by the firmware"
	::= { wlAssociate 24 }

brpsWLanWepKeyDefaultIndex  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The value of brpsWLanWepKeyIndex corresponding to the default setting."
	::= { wlWEP 1 }

brpsWLanWepKeyTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrpsWLanWepKeyEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents WEP KEY Info."
	::= { wlWEP 11 }

brpsWLanWepKeyEntry  OBJECT-TYPE
	SYNTAX  BrpsWLanWepKeyEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the brpsWLanWepKeyTable table. Rows cannot be created or deleted."
	INDEX  { brpsWLanWepKeyIndex }
	::= { brpsWLanWepKeyTable 1 }

BrpsWLanWepKeyEntry ::= 
	SEQUENCE {
		brpsWLanWepKeyIndex
			INTEGER,
		brpsWLanWepKeySize
			INTEGER,
		brpsWLanWepKeyType
			INTEGER,
		brpsWLanWepKey
			OCTET STRING
	}

brpsWLanWepKeyIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..4)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number."
	::= { brpsWLanWepKeyEntry 1 }

brpsWLanWepKeySize  OBJECT-TYPE
	SYNTAX  INTEGER {
		size40(1),
		size104(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The WEP KEY size."
	::= { brpsWLanWepKeyEntry 2 }

brpsWLanWepKeyType  OBJECT-TYPE
	SYNTAX  INTEGER {
		hex(1),
		ascii(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The WEP KEY type."
	::= { brpsWLanWepKeyEntry 3 }

brpsWLanWepKey  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The WEP KEY."
	::= { brpsWLanWepKeyEntry 4 }

brpsWLanNetworkKey  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The WPA-PSK."
	::= { wlWPA 1 }

brpsWLanTKIPChangeInterval  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"TKIP Interval (s)"
	::= { wlTKIP 1 }

brpsWLanLEAPTimeout  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Leap timeout (s)"
	::= { wlLEAP 1 }

brpsWLanOperatingMode  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The mode which is actually operating.
         off(0),
         dot11a(1),
         dot11b(2),
         dot11g(3)"
	::= { wlGeneralStatus 1 }

brpsWLanRSSLevel  OBJECT-TYPE
	SYNTAX  INTEGER (0..5)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN RSS Level."
	::= { wlGeneralStatus 2 }

brpsWLanCommSpeed  OBJECT-TYPE
	SYNTAX  INTEGER (0..32767)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN communication speed.
         However, the unit is 1/10."
	::= { wlGeneralStatus 3 }

brpsWLanOperatingChannel  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN operating channel."
	::= { wlGeneralStatus 4 }

brpsWLanOperatingName  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN operating SSID Name."
	::= { wlGeneralStatus 5 }

brpsWLanOperatingCommMode  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN operating communication mode.
         off(0),
         infrastructure(1),
         ad-Hoc-WiFi(2),
         ad-Hoc(3)"
	::= { wlGeneralStatus 6 }

brpsWLanOperatingEncryptMode  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN operating encrypt mode.
         off(0),
         none(1),
         wep(2),
         tkip(3),
         aes(4),
         ckip(5)"
	::= { wlGeneralStatus 7 }

brpsWLanOperatingAuthMode  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN operating authenticate mode.
         off(0),
         openSystem(1),
         sharedKey(2),
         wpa-psk(3),
         wpa-none(4),
         wpa(5),
         wpa2(6),
         leap(7),
         eapfast-none(8),
         eapfast-mschapv2(9),
         eapfast-gtc(10),
         eapfast-tls(11),
         wpa2-psk(12)"
	::= { wlGeneralStatus 8 }

brpsWLanOperatingWepKeyDefaultIndex  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Wireless LAN operating WepKeyDefaultIndex."
	::= { wlGeneralStatus 9 }

brMultiIFSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Multi Interface is not supported. 
         1 indicates Multi Interface is supported."
	::= { brMultiIFconfig 1 }

brMultiIFActiveIF  OBJECT-TYPE
	SYNTAX  INTEGER {
		lan(1),
		wirelesslan(2),
		both(3)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"1 LAN
         2 Wireless LAN
         3 Both"
	::= { brMultiIFconfig 2 }

brMultiIFAllSetDefault  OBJECT-TYPE
	SYNTAX  INTEGER (0..32767)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Setting the value resets all parameters to their factory default 
         value."
	::= { brMultiIFconfig 3 }

brMultiIFCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"MultiIF Count"
	::= { brMultiIFconfig 4 }

brMultiIFConfigureTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrMultiIFConfigureEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brMultiIFconfig 5 }

brMultiIFConfigureEntry  OBJECT-TYPE
	SYNTAX  BrMultiIFConfigureEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brMultiIFConfigureIndex }
	::= { brMultiIFConfigureTable 1 }

BrMultiIFConfigureEntry ::= 
	SEQUENCE {
		brMultiIFConfigureIndex
			INTEGER,
		brMultiIFType
			INTEGER,
		brMultiIFDescription
			DisplayString,
		brMultiIFNodeName
			OCTET STRING,
		brMultiIFInterfaceEnable
			INTEGER,
		brMultiIFEnetMode
			INTEGER,
		brMultiIFHardwareType
			INTEGER,
		brMultiIFNodeType
			OCTET STRING,
		brMultiIFInterfaceEnableImmediate
			INTEGER
	}

brMultiIFConfigureIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..4)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number."
	::= { brMultiIFConfigureEntry 1 }

brMultiIFType  OBJECT-TYPE
	SYNTAX  INTEGER {
		lan(1),
		wirelesslan(2)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Interface Type"
	::= { brMultiIFConfigureEntry 2 }

brMultiIFDescription  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..64) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Interface Description"
	::= { brMultiIFConfigureEntry 3 }

brMultiIFNodeName  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"An administratively-assigned name for this
         managed node.  By convention, this is the node's
         fully-qualified domain name."
	::= { brMultiIFConfigureEntry 4 }

brMultiIFInterfaceEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Interfase is not enabled. 
         1 indicates the Interfase is enabled."
	::= { brMultiIFConfigureEntry 5 }

brMultiIFEnetMode  OBJECT-TYPE
	SYNTAX  INTEGER (0..4)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Ethernet type."
	::= { brMultiIFConfigureEntry 6 }

brMultiIFHardwareType  OBJECT-TYPE
	SYNTAX  INTEGER (0..32767)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Hardware type of the print server."
	::= { brMultiIFConfigureEntry 7 }

brMultiIFNodeType  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"A textual description of the entity.  This value
         should include the full name and version
         identification of the system's hardware type,
         software operating-system, and networking
         software.  It is mandatory that this only contain
         printable ASCII characters."
	::= { brMultiIFConfigureEntry 8 }

brMultiIFInterfaceEnableImmediate  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"
"
	::= { brMultiIFConfigureEntry 9 }

brMultiIFPrimaryInterface  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Primary Interface"
	::= { brMultiIFconfig 6 }

brMultiIFInterfaceInformation  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0bit Auto
         1bit manual
         2bit enable both
         3bit disable both"
	::= { brMultiIFconfig 7 }

brMultiIFDNSTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrMultiIFDNSEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brMultiIFdns 1 }

brMultiIFDNSEntry  OBJECT-TYPE
	SYNTAX  BrMultiIFDNSEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brMultiIFConfigureIndex }
	::= { brMultiIFDNSTable 1 }

BrMultiIFDNSEntry ::= 
	SEQUENCE {
		brMultiIFTCPIPDNSIPSetup
			INTEGER,
		brMultiIFPrimaryDNSIPAddress
			IpAddress,
		brMultiIFSecondaryDNSIPAddress
			IpAddress,
		brMultiIFTCPIPConnectTime
			INTEGER
	}

brMultiIFTCPIPDNSIPSetup  OBJECT-TYPE
	SYNTAX  INTEGER {
		static(1),
		auto(2)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"DNS mode
         1:static
         2:auto"
	::= { brMultiIFDNSEntry 1 }

brMultiIFPrimaryDNSIPAddress  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Primary DNS IP Address"
	::= { brMultiIFDNSEntry 2 }

brMultiIFSecondaryDNSIPAddress  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Secondary DNS IP Address"
	::= { brMultiIFDNSEntry 3 }

brMultiIFTCPIPConnectTime  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"TCP/IP connect time."
	::= { brMultiIFDNSEntry 4 }

brMultiIFControlTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrMultiIFControlEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brMultiIFcontrol 1 }

brMultiIFControlEntry  OBJECT-TYPE
	SYNTAX  BrMultiIFControlEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brMultiIFConfigureIndex }
	::= { brMultiIFControlTable 1 }

BrMultiIFControlEntry ::= 
	SEQUENCE {
		brMultiIFSetDefault
			INTEGER
	}

brMultiIFSetDefault  OBJECT-TYPE
	SYNTAX  INTEGER (0..32767)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Setting the value resets all parameters to their factory default 
         value."
	::= { brMultiIFControlEntry 1 }

brMultiIFServiceTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrMultiIFServiceEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brMultiIFservice 1 }

brMultiIFServiceEntry  OBJECT-TYPE
	SYNTAX  BrMultiIFServiceEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brMultiIFConfigureIndex }
	::= { brMultiIFServiceTable 1 }

BrMultiIFServiceEntry ::= 
	SEQUENCE {
		brMultiIFServiceCount
			INTEGER,
		brMultiIFServiceStringLimit
			INTEGER,
		brMultiIFServiceStringCount
			INTEGER,
		brMultiIFServiceFilterCount
			INTEGER
	}

brMultiIFServiceCount  OBJECT-TYPE
	SYNTAX  INTEGER (0..128)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Number of services on this print server."
	::= { brMultiIFServiceEntry 1 }

brMultiIFServiceStringLimit  OBJECT-TYPE
	SYNTAX  INTEGER (1..127)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Maximum number of service strings supported by the print server."
	::= { brMultiIFServiceEntry 2 }

brMultiIFServiceStringCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Number of service strings currently defined on the print server."
	::= { brMultiIFServiceEntry 3 }

brMultiIFServiceFilterCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Number of service filters implemented by the device."
	::= { brMultiIFServiceEntry 4 }

brMultiIFServiceInfoTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrMultiIFServiceInfoEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents the attributes of a service."
	::= { brMultiIFservice 2 }

brMultiIFServiceInfoEntry  OBJECT-TYPE
	SYNTAX  BrMultiIFServiceInfoEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the services table. Rows cannot be created or deleted
         via direct SNMP operations."
	INDEX  { brMultiIFConfigureIndex, brMultiIFServiceIndex }
	::= { brMultiIFServiceInfoTable 1 }

BrMultiIFServiceInfoEntry ::= 
	SEQUENCE {
		brMultiIFServiceIndex
			INTEGER,
		brMultiIFServiceName
			DisplayString,
		brMultiIFServicePort
			INTEGER,
		brMultiIFServiceFilter
			INTEGER,
		brMultiIFServiceBOT
			INTEGER,
		brMultiIFServiceEOT
			INTEGER,
		brMultiIFServiceMatch
			INTEGER,
		brMultiIFServiceReplace
			INTEGER,
		brMultiIFServiceTCPPort
			INTEGER,
		brMultiIFServiceNDSTree
			DisplayString,
		brMultiIFServiceNDSContext
			OCTET STRING,
		brMultiIFServiceVines
			DisplayString,
		brMultiIFServiceObsolete
			INTEGER,
		brMultiIFServiceNetwareServerCount
			INTEGER,
		brMultiIFServiceReceiveOnly
			INTEGER,
		brMultiIFServiceTCPQueued
			INTEGER,
		brMultiIFServiceProtocolLAT
			INTEGER,
		brMultiIFServiceProtocolTCPIP
			INTEGER,
		brMultiIFServiceProtocolNetware
			INTEGER,
		brMultiIFServiceProtocolAppleTalk
			INTEGER,
		brMultiIFServiceProtocolBanyan
			INTEGER,
		brMultiIFServiceProtocolDLC
			INTEGER,
		brMultiIFServiceProtocolNetBEUI
			INTEGER,
		brMultiIFServiceNetwareServerMode
			INTEGER,
		brMultiIFServiceNetwareRemotePrinterNum
			INTEGER,
		brMultiIFServiceProtocolIPP
			INTEGER,
		brMultiIFServiceAppleTalkType
			DisplayString
	}

brMultiIFServiceIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..32)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The service number."
	::= { brMultiIFServiceInfoEntry 1 }

brMultiIFServiceName  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..32) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The service name."
	::= { brMultiIFServiceInfoEntry 2 }

brMultiIFServicePort  OBJECT-TYPE
	SYNTAX  INTEGER (0..31)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The port number assigned to this service."
	::= { brMultiIFServiceInfoEntry 3 }

brMultiIFServiceFilter  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Filter number assigned to this service."
	::= { brMultiIFServiceInfoEntry 4 }

brMultiIFServiceBOT  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Beginning of text string number assigned to this service."
	::= { brMultiIFServiceInfoEntry 5 }

brMultiIFServiceEOT  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"End of text string number assigned to this service."
	::= { brMultiIFServiceInfoEntry 6 }

brMultiIFServiceMatch  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Match string number assigned to this service."
	::= { brMultiIFServiceInfoEntry 7 }

brMultiIFServiceReplace  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Replace string number assigned to this service."
	::= { brMultiIFServiceInfoEntry 8 }

brMultiIFServiceTCPPort  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"TCP Port number assigned to this service."
	::= { brMultiIFServiceInfoEntry 9 }

brMultiIFServiceNDSTree  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..48) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The NDS Tree."
	::= { brMultiIFServiceInfoEntry 10 }

brMultiIFServiceNDSContext  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..255) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The NDS Context in Unicode."
	::= { brMultiIFServiceInfoEntry 11 }

brMultiIFServiceVines  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..48) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Vines StreetTalk name for this service."
	::= { brMultiIFServiceInfoEntry 12 }

brMultiIFServiceObsolete  OBJECT-TYPE
	SYNTAX  INTEGER (0..127)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Obsolete parameter that is no longer used"
	::= { brMultiIFServiceInfoEntry 13 }

brMultiIFServiceNetwareServerCount  OBJECT-TYPE
	SYNTAX  INTEGER (0..127)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The number of Netware servers enabled on this service. Modifying the
         value will truncate the server list to the specified number of servers.	 If the value set is greater than the number of servers currently on the	 list, then the list will be unchanged."
	::= { brMultiIFServiceInfoEntry 14 }

brMultiIFServiceReceiveOnly  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"When enabled, the service will only receive data, any printer
         responses will be discarded."
	::= { brMultiIFServiceInfoEntry 15 }

brMultiIFServiceTCPQueued  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"When enabled, the raw TCP connections to the port will be
         queued while waiting for the port to become available"
	::= { brMultiIFServiceInfoEntry 16 }

brMultiIFServiceProtocolLAT  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"When enabled, the LAT protocol will be available on this service."
	::= { brMultiIFServiceInfoEntry 17 }

brMultiIFServiceProtocolTCPIP  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"When enabled, the TCP/IP protocol will be available on this service."
	::= { brMultiIFServiceInfoEntry 18 }

brMultiIFServiceProtocolNetware  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"When enabled, the Netware protocol will be available on this service."
	::= { brMultiIFServiceInfoEntry 19 }

brMultiIFServiceProtocolAppleTalk  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"When enabled, the AppleTalk protocol will be available on this service."
	::= { brMultiIFServiceInfoEntry 20 }

brMultiIFServiceProtocolBanyan  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"When enabled, the Banyan protocol will be available on this service."
	::= { brMultiIFServiceInfoEntry 21 }

brMultiIFServiceProtocolDLC  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"When enabled, the DLC protocol will be available on this service."
	::= { brMultiIFServiceInfoEntry 22 }

brMultiIFServiceProtocolNetBEUI  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"When enabled, the NetBEUI protocol will be available on this service."
	::= { brMultiIFServiceInfoEntry 23 }

brMultiIFServiceNetwareServerMode  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Netware mode for this service. 0 = Queue Server, 1 = Remote Printer"
	::= { brMultiIFServiceInfoEntry 24 }

brMultiIFServiceNetwareRemotePrinterNum  OBJECT-TYPE
	SYNTAX  INTEGER (0..255)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Remote Printer Number for this service"
	::= { brMultiIFServiceInfoEntry 25 }

brMultiIFServiceProtocolIPP  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"When enabled, the IPP protocol will be available on this service."
	::= { brMultiIFServiceInfoEntry 26 }

brMultiIFServiceAppleTalkType  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..32) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"AppleTalk type."
	::= { brMultiIFServiceInfoEntry 27 }

brMultiIFServiceStringInfoTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrMultiIFServiceStringInfoEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents the service BOT/EOT/MATCH/REPLACE strings defined."
	::= { brMultiIFservice 3 }

brMultiIFServiceStringInfoEntry  OBJECT-TYPE
	SYNTAX  BrMultiIFServiceStringInfoEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the service string table. Rows cannot be created or deleted
         via direct SNMP operations."
	INDEX  { brMultiIFConfigureIndex, brMultiIFServiceStringIndex }
	::= { brMultiIFServiceStringInfoTable 1 }

BrMultiIFServiceStringInfoEntry ::= 
	SEQUENCE {
		brMultiIFServiceStringIndex
			INTEGER,
		brMultiIFServiceString
			DisplayString
	}

brMultiIFServiceStringIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..255)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The string number."
	::= { brMultiIFServiceStringInfoEntry 1 }

brMultiIFServiceString  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..32) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The character string definition suitable for displaying."
	::= { brMultiIFServiceStringInfoEntry 2 }

brMultiIFTCPIPTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrMultiIFTCPIPEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brMultiIFtcpip 1 }

brMultiIFTCPIPEntry  OBJECT-TYPE
	SYNTAX  BrMultiIFTCPIPEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brMultiIFConfigureIndex }
	::= { brMultiIFTCPIPTable 1 }

BrMultiIFTCPIPEntry ::= 
	SEQUENCE {
		brMultiIFTCPIPAddress
			IpAddress,
		brMultiIFTCPIPSubnetMask
			IpAddress,
		brMultiIFTCPIPGateway
			IpAddress,
		brMultiIFTCPIPMethod
			INTEGER,
		brMultiIFTCPIPUpdate
			OCTET STRING,
		brMultiIFTCPIPTimeout
			INTEGER,
		brMultiIFTCPIPBootTries
			INTEGER,
		brMultiIFTCPIPRARPNoSubnet
			INTEGER,
		brMultiIFTCPIPRARPNoGateway
			INTEGER,
		brMultiIFTCPIPUseMethod
			INTEGER,
		brMultiIFTCPIPMethodServer
			IpAddress
	}

brMultiIFTCPIPAddress  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"IP Address assigned to the print server."
	::= { brMultiIFTCPIPEntry 1 }

brMultiIFTCPIPSubnetMask  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Subnet mask assigned to the print server."
	::= { brMultiIFTCPIPEntry 2 }

brMultiIFTCPIPGateway  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Gateway/Router address assigned to the print server"
	::= { brMultiIFTCPIPEntry 3 }

brMultiIFTCPIPMethod  OBJECT-TYPE
	SYNTAX  INTEGER (0..7)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Method used to determine the IP Address. AUTO tries all protocols.
         STATIC uses the configured address. DHCP, BOOTP, and RARP use only
         that protocol."
	::= { brMultiIFTCPIPEntry 4 }

brMultiIFTCPIPUpdate  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(6) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Hardware MAC address to be updated. This value is write only. Will
         read back as zeros."
	::= { brMultiIFTCPIPEntry 5 }

brMultiIFTCPIPTimeout  OBJECT-TYPE
	SYNTAX  INTEGER (0..32767)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Timeout used for LPD connections."
	::= { brMultiIFTCPIPEntry 6 }

brMultiIFTCPIPBootTries  OBJECT-TYPE
	SYNTAX  INTEGER (0..32767)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Number of attempts to use BOOTP, RARP, and DHCP."
	::= { brMultiIFTCPIPEntry 7 }

brMultiIFTCPIPRARPNoSubnet  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"If enabled, do not set the subnet mask based on the RARP response."
	::= { brMultiIFTCPIPEntry 8 }

brMultiIFTCPIPRARPNoGateway  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"If enabled, do not set the gateway/router address based on the RARP response."
	::= { brMultiIFTCPIPEntry 9 }

brMultiIFTCPIPUseMethod  OBJECT-TYPE
	SYNTAX  INTEGER (0..7)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Method type that used set up TCP/IP configuration."
	::= { brMultiIFTCPIPEntry 10 }

brMultiIFTCPIPMethodServer  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"If TCP/IP configuration is setted up by any protocol server, 
         this entry indicates the IP address of the server."
	::= { brMultiIFTCPIPEntry 11 }

brMultiIFNetBIOSTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrMultiIFNetBIOSEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brMultiIFnetbeui 1 }

brMultiIFNetBIOSEntry  OBJECT-TYPE
	SYNTAX  BrMultiIFNetBIOSEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brMultiIFConfigureIndex }
	::= { brMultiIFNetBIOSTable 1 }

BrMultiIFNetBIOSEntry ::= 
	SEQUENCE {
		brMultiIFNetBIOSIPMethod
			INTEGER,
		brMultiIFTCPIPNetBIOSPrimaryWINSAddr
			IpAddress,
		brMultiIFTCPIPNetBIOSSecondaryWINSAddr
			IpAddress,
		brMultiIFNetBEUIDomain
			DisplayString
	}

brMultiIFNetBIOSIPMethod  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Method used to determine the IP Address of the WINS server.
         AUTO tries all protocols (currently only DHCP is tried).
         STATIC uses the configured address."
	::= { brMultiIFNetBIOSEntry 1 }

brMultiIFTCPIPNetBIOSPrimaryWINSAddr  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The IP Address of the primary WINS server."
	::= { brMultiIFNetBIOSEntry 2 }

brMultiIFTCPIPNetBIOSSecondaryWINSAddr  OBJECT-TYPE
	SYNTAX  IpAddress
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The IP Address of the secondary WINS server."
	::= { brMultiIFNetBIOSEntry 3 }

brMultiIFNetBEUIDomain  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..15) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The NetBEUI domain name."
	::= { brMultiIFNetBIOSEntry 4 }

brMultiIFAPIPATable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrMultiIFAPIPAEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brMultiIForiginalapipa 1 }

brMultiIFAPIPAEntry  OBJECT-TYPE
	SYNTAX  BrMultiIFAPIPAEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brMultiIFConfigureIndex }
	::= { brMultiIFAPIPATable 1 }

BrMultiIFAPIPAEntry ::= 
	SEQUENCE {
		brMultiIFAPIPASupported
			INTEGER,
		brMultiIFAPIPAEnable
			INTEGER
	}

brMultiIFAPIPASupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the APIPA protocol is not supported. 
         1 indicates APIPA support is implemented by the firmware"
	::= { brMultiIFAPIPAEntry 1 }

brMultiIFAPIPAEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the APIPA protocol is not enabled.
         1 indicates that the APIPA protocol is enabled.
         Writing a non-zero value will enable the APIPA
         protocol if it is supported by the firmware."
	::= { brMultiIFAPIPAEntry 2 }

brMultiIFLAATable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrMultiIFLAAEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brMultiIForiginalLAA 1 }

brMultiIFLAAEntry  OBJECT-TYPE
	SYNTAX  BrMultiIFLAAEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brMultiIFConfigureIndex }
	::= { brMultiIFLAATable 1 }

BrMultiIFLAAEntry ::= 
	SEQUENCE {
		brMultiIFLAASupported
			INTEGER,
		brMultiIFLAAMacAddress
			OCTET STRING
	}

brMultiIFLAASupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"This value indicate if the locally administered addressing is supported.
         0: Not Supported
         1: Supported"
	::= { brMultiIFLAAEntry 1 }

brMultiIFLAAMacAddress  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(6) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"A locally administered address (LAA) that replaces the factory-assigned	 MAC address (UAA: universally administered address).
         If this setting is all-zero address (i.e. 6 bytes of zeros), the
         factory-assigned
         MAC address is used as the MAC address of the device.
         The LAA address must start with X2, X6, XA, or XE, where X is any
         hexadecimal digit 0 to F."
	::= { brMultiIFLAAEntry 2 }

brMultiIFIPv6AddressCountTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrMultiIFIPv6AddressCountEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brMultiIForiginalIPv6 1 }

brMultiIFIPv6AddressCountEntry  OBJECT-TYPE
	SYNTAX  BrMultiIFIPv6AddressCountEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brMultiIFConfigureIndex }
	::= { brMultiIFIPv6AddressCountTable 1 }

BrMultiIFIPv6AddressCountEntry ::= 
	SEQUENCE {
		brMultiIFIPv6AddressCount
			INTEGER
	}

brMultiIFIPv6AddressCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"IPv6Address Counter."
	::= { brMultiIFIPv6AddressCountEntry 1 }

brMultiIFIPv6AddressTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrMultiIFIPv6AddressEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brMultiIForiginalIPv6 2 }

brMultiIFIPv6AddressEntry  OBJECT-TYPE
	SYNTAX  BrMultiIFIPv6AddressEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brMultiIFConfigureIndex, brMultiIFIPv6AddressIndex }
	::= { brMultiIFIPv6AddressTable 1 }

BrMultiIFIPv6AddressEntry ::= 
	SEQUENCE {
		brMultiIFIPv6AddressIndex
			INTEGER,
		brMultiIFIPv6Address
			Ipv6Address,
		brMultiIFIPv6UseMethod
			INTEGER,
		brMultiIFIPv6MethodServer
			DisplayString
	}

brMultiIFIPv6AddressIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..16)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"index"
	::= { brMultiIFIPv6AddressEntry 1 }

brMultiIFIPv6Address  OBJECT-TYPE
	SYNTAX  Ipv6Address
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brMultiIFIPv6AddressEntry 2 }

brMultiIFIPv6UseMethod  OBJECT-TYPE
	SYNTAX  INTEGER (0..7)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Method type that used set up TCP/IP configuration.
         0: Unknown (Information not supported)
         1: Manually
         2: DHCPv6
         3: Stateless Address Auto-configuration
         4: Reserved
         5: Link Local"
	::= { brMultiIFIPv6AddressEntry 3 }

brMultiIFIPv6MethodServer  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..64) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"If TCP/IP configuration is setted up by any protocol server, 
         this entry indicates the IP address of the server."
	::= { brMultiIFIPv6AddressEntry 4 }

brMultiIFIPv6StaticAddressCountTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrMultiIFIPv6StaticAddressCountEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brMultiIForiginalIPv6 3 }

brMultiIFIPv6StaticAddressCountEntry  OBJECT-TYPE
	SYNTAX  BrMultiIFIPv6StaticAddressCountEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brMultiIFConfigureIndex }
	::= { brMultiIFIPv6StaticAddressCountTable 1 }

BrMultiIFIPv6StaticAddressCountEntry ::= 
	SEQUENCE {
		brMultiIFIPv6StaticAddressCount
			INTEGER
	}

brMultiIFIPv6StaticAddressCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"IPv6StaticAddress Counter."
	::= { brMultiIFIPv6StaticAddressCountEntry 1 }

brMultiIFIPv6StaticAddressTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrMultiIFIPv6StaticAddressEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brMultiIForiginalIPv6 4 }

brMultiIFIPv6StaticAddressEntry  OBJECT-TYPE
	SYNTAX  BrMultiIFIPv6StaticAddressEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brMultiIFConfigureIndex, brMultiIFIPv6StaticAddressIndex }
	::= { brMultiIFIPv6StaticAddressTable 1 }

BrMultiIFIPv6StaticAddressEntry ::= 
	SEQUENCE {
		brMultiIFIPv6StaticAddressIndex
			INTEGER,
		brMultiIFIPv6StaticAddressEnable
			INTEGER,
		brMultiIFIPv6StaticAddress
			Ipv6Address,
		brMultiIFIPv6StaticAddressPrefixLength
			INTEGER
	}

brMultiIFIPv6StaticAddressIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..16)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"index"
	::= { brMultiIFIPv6StaticAddressEntry 1 }

brMultiIFIPv6StaticAddressEnable  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"index"
	::= { brMultiIFIPv6StaticAddressEntry 2 }

brMultiIFIPv6StaticAddress  OBJECT-TYPE
	SYNTAX  Ipv6Address
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"IPv6 Static Address"
	::= { brMultiIFIPv6StaticAddressEntry 3 }

brMultiIFIPv6StaticAddressPrefixLength  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"IPv6Address Prefix Length"
	::= { brMultiIFIPv6StaticAddressEntry 4 }

brMultiIFDNSIPv6AddressTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrMultiIFDNSIPv6AddressEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brMultiIForiginalIPv6 5 }

brMultiIFDNSIPv6AddressEntry  OBJECT-TYPE
	SYNTAX  BrMultiIFDNSIPv6AddressEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brMultiIFConfigureIndex }
	::= { brMultiIFDNSIPv6AddressTable 1 }

BrMultiIFDNSIPv6AddressEntry ::= 
	SEQUENCE {
		brMultiIFPrimaryDNSIPv6Address
			Ipv6Address,
		brMultiIFSecondaryDNSIPv6Address
			Ipv6Address
	}

brMultiIFPrimaryDNSIPv6Address  OBJECT-TYPE
	SYNTAX  Ipv6Address
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Primary DNS IPv6 Address"
	::= { brMultiIFDNSIPv6AddressEntry 1 }

brMultiIFSecondaryDNSIPv6Address  OBJECT-TYPE
	SYNTAX  Ipv6Address
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Secondary DNS IPv6 Address"
	::= { brMultiIFDNSIPv6AddressEntry 2 }

brMultiIFWebServicesTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrMultiIFWebServicesEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brMultiIForiginalWebServices 1 }

brMultiIFWebServicesEntry  OBJECT-TYPE
	SYNTAX  BrMultiIFWebServicesEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brMultiIFConfigureIndex }
	::= { brMultiIFWebServicesTable 1 }

BrMultiIFWebServicesEntry ::= 
	SEQUENCE {
		brMultiIFWebServicesName
			DisplayString,
		brMultiIFWebServicesInstanceID
			INTEGER,
		brMultiIFWebServicesMetadataVersion
			INTEGER
	}

brMultiIFWebServicesName  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..64) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brMultiIFWebServicesEntry 1 }

brMultiIFWebServicesInstanceID  OBJECT-TYPE
	SYNTAX  INTEGER (1..2147483647)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brMultiIFWebServicesEntry 2 }

brMultiIFWebServicesMetadataVersion  OBJECT-TYPE
	SYNTAX  INTEGER (1..2147483647)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brMultiIFWebServicesEntry 3 }

brLdapSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the LDAP client is not supported. 
         1 indicates the LDAP client is implemented by the firmware"
	::= { broriginalldap 1 }

brLdapEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the LDAP client is not enabled.
         1 indicates that the LDAP client is enabled.
         Writing a non-zero value will enable the Ldap
         if it is supported by the firmware."
	::= { broriginalldap 2 }

brLdapTimeout  OBJECT-TYPE
	SYNTAX  INTEGER (1..32767)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"timeout value (sec) for LDAP server."
	::= { broriginalldap 3 }

brLdapTimeoutSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the LDAP timeout is not supported. 
         1 indicates the LDAP timeout is implemented by the firmware"
	::= { broriginalldap 4 }

brLdapServerCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Maximum number of LDAP Server INDEX."
	::= { broriginalldap 11 }

brLdapServerInfoTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrLdapServerInfoEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents LDAP Server Info."
	::= { broriginalldap 12 }

brLdapServerInfoEntry  OBJECT-TYPE
	SYNTAX  BrLdapServerInfoEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the LdapServerInfo table. Rows cannot be created or deleted."
	INDEX  { brLdapServerInfoIndex }
	::= { brLdapServerInfoTable 1 }

BrLdapServerInfoEntry ::= 
	SEQUENCE {
		brLdapServerInfoIndex
			INTEGER,
		brLdapServerName
			DisplayString,
		brLdapServerPort
			INTEGER,
		brLdapServerAuth
			INTEGER,
		brLdapServerUserDN
			OCTET STRING,
		brLdapServerPassword
			OCTET STRING,
		brLdapServerPasswordSet
			INTEGER,
		brLdapServerBaseDN
			OCTET STRING,
		brLdapServerAttrEMail
			DisplayString,
		brLdapServerAttrFAXNumber
			DisplayString,
		brLdapServerAttrDetail1
			DisplayString,
		brLdapServerAttrDetail2
			DisplayString,
		brLdapServerAttrDetail3
			DisplayString,
		brLdapServerAttrDetail4
			DisplayString,
		brLdapServerAttrDetailEnable1
			INTEGER,
		brLdapServerAttrDetailEnable2
			INTEGER,
		brLdapServerAttrDetailEnable3
			INTEGER,
		brLdapServerAttrDetailEnable4
			INTEGER,
		brLdapKerberosServerName
			DisplayString,
		brLdapKerberosServerPort
			INTEGER
	}

brLdapServerInfoIndex  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number."
	::= { brLdapServerInfoEntry 1 }

brLdapServerName  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..64) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The LDAP server name."
	::= { brLdapServerInfoEntry 2 }

brLdapServerPort  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The LDAP server port number."
	::= { brLdapServerInfoEntry 3 }

brLdapServerAuth  OBJECT-TYPE
	SYNTAX  INTEGER {
		anonymous(1),
		simple(2),
		kerberos(3)
	}
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The LDAP server authentication method."
	::= { brLdapServerInfoEntry 4 }

brLdapServerUserDN  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..255) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The LDAP server user DN."
	::= { brLdapServerInfoEntry 5 }

brLdapServerPassword  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..32) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The LDAP server password."
	::= { brLdapServerInfoEntry 6 }

brLdapServerPasswordSet  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Returns 1 if the LDAP server password is set, 0 if not set."
	::= { brLdapServerInfoEntry 7 }

brLdapServerBaseDN  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..255) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The LDAP server base DN."
	::= { brLdapServerInfoEntry 8 }

brLdapServerAttrEMail  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..64) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The LDAP server attribute or e-mail."
	::= { brLdapServerInfoEntry 10 }

brLdapServerAttrFAXNumber  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..64) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The LDAP server attribute or facsimile telephone number."
	::= { brLdapServerInfoEntry 11 }

brLdapServerAttrDetail1  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..64) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The LDAP server attribute or detail 1."
	::= { brLdapServerInfoEntry 12 }

brLdapServerAttrDetail2  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..64) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The LDAP server attribute or detail 2."
	::= { brLdapServerInfoEntry 13 }

brLdapServerAttrDetail3  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..64) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The LDAP server attribute or detail 3."
	::= { brLdapServerInfoEntry 14 }

brLdapServerAttrDetail4  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..64) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The LDAP server attribute or detail 4."
	::= { brLdapServerInfoEntry 15 }

brLdapServerAttrDetailEnable1  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Enable flag of the LDAP server attribute of detail 1."
	::= { brLdapServerInfoEntry 16 }

brLdapServerAttrDetailEnable2  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Enable flag of the LDAP server attribute of detail 2."
	::= { brLdapServerInfoEntry 17 }

brLdapServerAttrDetailEnable3  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Enable flag of the LDAP server attribute of detail 3."
	::= { brLdapServerInfoEntry 18 }

brLdapServerAttrDetailEnable4  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Enable flag of the LDAP server attribute of detail 4."
	::= { brLdapServerInfoEntry 19 }

brLdapServerAttrNameCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Maximum number of LDAP Server Attribute of name INDEX."
	::= { broriginalldap 21 }

brLdapServerAttrNameTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrLdapServerAttrNameEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents LDAP Server Attribute of name."
	::= { broriginalldap 22 }

brLdapServerAttrNameEntry  OBJECT-TYPE
	SYNTAX  BrLdapServerAttrNameEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the LdapServerAttributeName table. Rows cannot be created or deleted."
	INDEX  { brLdapServerInfoIndex, brLdapServerAttrNameIndex }
	::= { brLdapServerAttrNameTable 1 }

BrLdapServerAttrNameEntry ::= 
	SEQUENCE {
		brLdapServerAttrNameIndex
			INTEGER,
		brLdapServerAttrName
			DisplayString
	}

brLdapServerAttrNameIndex  OBJECT-TYPE
	SYNTAX  INTEGER (0..4)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number."
	::= { brLdapServerAttrNameEntry 1 }

brLdapServerAttrName  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..64) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The LDAP server attribute of name."
	::= { brLdapServerAttrNameEntry 2 }

brLdapSetDefault  OBJECT-TYPE
	SYNTAX  INTEGER (0..32767)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Setting the value resets all LDAP parameters to their factory default 
         value."
	::= { broriginalldap 99 }

brFuncLockSettingInit  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Init Function Lock Setting."
	::= { funclock 1 }

brFuncLockAdminPassword  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Get or Set the administrator password of Function Lock."
	::= { funclock 2 }

brFuncLock  OBJECT-TYPE
	SYNTAX  INTEGER (1..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Get or Set the status of Function Lock."
	::= { funclock 3 }

brFuncLockPublicFuncCount  OBJECT-TYPE
	SYNTAX  INTEGER (0..2147483647)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Return the number of functions to be locked on the public mode."
	::= { funclock 4 }

brFuncLockUserCount  OBJECT-TYPE
	SYNTAX  INTEGER (0..2147483647)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Return the number of users to be locked on function lock."
	::= { funclock 6 }

brFuncLockUserTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrFuncLockUserEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { funclock 7 }

brFuncLockPublicTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrFuncLockPublicEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { funclock 5 }

brFuncLockPublicEntry  OBJECT-TYPE
	SYNTAX  BrFuncLockPublicEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brFuncLockPublicFuncIndex }
	::= { brFuncLockPublicTable 1 }

BrFuncLockPublicEntry ::= 
	SEQUENCE {
		brFuncLockPublicFuncIndex
			INTEGER,
		brFuncLockPublicFuncMember
			INTEGER,
		brFuncLockPublicFuncSupported
			INTEGER,
		brFuncLockPublicFuncEnable
			INTEGER
	}

brFuncLockUserEntry  OBJECT-TYPE
	SYNTAX  BrFuncLockUserEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brFuncLockUserIndex }
	::= { brFuncLockUserTable 1 }

BrFuncLockUserEntry ::= 
	SEQUENCE {
		brFuncLockUserIndex
			INTEGER,
		brFuncLockUserName
			DisplayString,
		brFuncLockUserPassword
			DisplayString
	}

brFuncLockPublicFuncIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..2147483647)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Return the index value of functions to be locked on the public mode."
	::= { brFuncLockPublicEntry 1 }

brFuncLockPublicFuncMember  OBJECT-TYPE
	SYNTAX  INTEGER {
		copycolor(1),
		copybr(2),
		faxtx(3),
		faxrx(4),
		scan(5),
		print(6),
		pcc(7)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Return the member of functions to be locked on the public mode."
	::= { brFuncLockPublicEntry 2 }

brFuncLockPublicFuncSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Return the status of availableness of functions to be locked on the public mode."
	::= { brFuncLockPublicEntry 3 }

brFuncLockPublicFuncEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Get of Set the status of functions on the public mode."
	::= { brFuncLockPublicEntry 4 }

brFuncLockUserIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..2147483647)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Return the index value of users to be locked on function lock."
	::= { brFuncLockUserEntry 1 }

brFuncLockUserName  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Get or Set the user name of function lock."
	::= { brFuncLockUserEntry 2 }

brFuncLockUserPassword  OBJECT-TYPE
	SYNTAX  DisplayString
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Get or Set the user password of function lock."
	::= { brFuncLockUserEntry 3 }

brFuncLockUserFuncCount  OBJECT-TYPE
	SYNTAX  INTEGER (0..2147483647)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Return the number of users to be locked on the function lock."
	::= { funclock 8 }

brFuncLockUserFuncTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrFuncLockUserFuncEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { funclock 9 }

brFuncLockUserFuncEntry  OBJECT-TYPE
	SYNTAX  BrFuncLockUserFuncEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brFuncLockUserIndex, brFuncLockUserFuncIndex }
	::= { brFuncLockUserFuncTable 1 }

BrFuncLockUserFuncEntry ::= 
	SEQUENCE {
		brFuncLockUserFuncIndex
			INTEGER,
		brFuncLockUserFuncMember
			INTEGER,
		brFuncLockUserFuncSupported
			INTEGER,
		brFuncLockUserFuncEnable
			INTEGER
	}

brFuncLockUserFuncIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..2147483647)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Return the index value of users to be locked on user mode."
	::= { brFuncLockUserFuncEntry 1 }

brFuncLockUserFuncMember  OBJECT-TYPE
	SYNTAX  INTEGER {
		copycolor(1),
		copybr(2),
		faxtx(3),
		faxrx(4),
		scan(5),
		print(6),
		pcc(7)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Return the function numbers to be locked on user mode."
	::= { brFuncLockUserFuncEntry 2 }

brFuncLockUserFuncSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"Return the availableness of function to be locked on user mode."
	::= { brFuncLockUserFuncEntry 3 }

brFuncLockUserFuncEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Get or Set the lock status on the user mode."
	::= { brFuncLockUserFuncEntry 4 }

brFuncLockSetting  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Write the all information of function lock."
	::= { funclock 10 }

brNetRemoteSetUpFileFormat  OBJECT-TYPE
	SYNTAX  INTEGER {
		rms(1),
		rmd(2),
		rmsrmd(3)
	}
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"It is a file format of the Net Remote Setup function."
	::= { mcgRemoteSetup 3 }

brInfoAlertVersion  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"It indicates the version that prtAlertLocation is implemented correctly."
	::= { printerinfomation 25 }

brInfoBlackPrint  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"It indicates black only print enable/disable info
         0: disable
         1: enable"
	::= { printerinfomation 26 }

brpsWLanCapabilityAuthEAPSupportAuthentication  OBJECT-TYPE
	SYNTAX  INTEGER (0..15)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"put supported authentication protocol as bit.
         
         1bit:802.1X
         2bit:WPA
         3bit:WPA2
         4bit:CCKM"
	::= { brpsWLanCapabilityAuthEAPEntry 5 }

brpsWLanCapabilityAuthEAPSupportEncryption  OBJECT-TYPE
	SYNTAX  INTEGER (0..15)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"put supported encryption as bit.
         
         1bit:WEP
         2bit:CKIP
         3bit:TKIP
         4bit:AES"
	::= { brpsWLanCapabilityAuthEAPEntry 6 }

brLdapKerberosServerName  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..64) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Kerberos server name for LDAP."
	::= { brLdapServerInfoEntry 20 }

brLdapKerberosServerPort  OBJECT-TYPE
	SYNTAX  INTEGER (0..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Kerberos server port number for LDAP."
	::= { brLdapServerInfoEntry 21 }

brKerberosSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the Kerberos protocol is not supported. 
         1 indicates Kerberos support is implemented by the firmware"
	::= { broriginalKerberos 1 }

brCIFSSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the CIFS protocol is not supported. 
         1 indicates CIFS support is implemented by the firmware"
	::= { broriginalCIFS 1 }

brCIFSEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the CIFS protocol is not enabled.
         1 indicates that the CIFS protocol is enabled.
         Writing a non-zero value will enable the CIFS
         protocol if it is supported by the firmware."
	::= { broriginalCIFS 2 }

brSNMPV3Supported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates  SNMPv3  is not supported. 
         1 indicates SNMPv3 is supported."
	::= { broriginalSNMP 10 }

brSNMPCommMode  OBJECT-TYPE
	SYNTAX  INTEGER (1..3)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"1 indicates  v1/v2c read-write access  is enable. 
         2 indicates v3 read-write access is enable. 
         3 indicates v1/v2c read-only access and v3 read-write access is enable."
	::= { broriginalSNMP 11 }

brSNMPV3UserName  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..32) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The username of SNMPv3."
	::= { broriginalSNMP 12 }

brSNMPV3KeyType  OBJECT-TYPE
	SYNTAX  INTEGER (1..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"1 indicates that the key type of SNMPv3 is ASCII. 
         2 indicates that the key type of SNMPv3 is HEX"
	::= { broriginalSNMP 13 }

brSNMPV3AuthKey  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..16) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The authentication key of SNMPv3."
	::= { broriginalSNMP 14 }

brSNMPV3AuthPassword  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..16) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The authentication password of SNMPv3."
	::= { broriginalSNMP 15 }

brSNMPV3PrivKey  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..16) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The privacy key of SNMPv3."
	::= { broriginalSNMP 16 }

brSNMPV3PrivPassword  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(1..16) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The privacy password of SNMPv3."
	::= { broriginalSNMP 17 }

brSNMPV3ContextName  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..32) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The contextname of SNMPv3."
	::= { broriginalSNMP 18 }

brSNTPCSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the SNTP is not supported. 
         1 indicates SNTP is supported."
	::= { broriginalSNTP 1 }

brSNTPCEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"0 indicates the SNTP is not disabled.
         1 indicates that the SNTP is enabled.
"
	::= { broriginalSNTP 2 }

brSNTPCSyncMethod  OBJECT-TYPE
	SYNTAX  INTEGER (1..2)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"1 indicates Synchronous Method is AUTO.
         2 indicates  Synchronous Method is STATIC.
"
	::= { broriginalSNTP 4 }

brSNTPCIntervalMin  OBJECT-TYPE
	SYNTAX  INTEGER (1..168)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The minimum value of synchronous interval."
	::= { broriginalSNTP 5 }

brSNTPCInterval  OBJECT-TYPE
	SYNTAX  INTEGER (1..168)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The synchronous interval is used for synchronous method INTERVAL."
	::= { broriginalSNTP 6 }

brSNTPCSyncResult  OBJECT-TYPE
	SYNTAX  INTEGER (1..4)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The result of the last checking time.
         1: Success
         2: Synchronizing
         3: Failed
         4: Not yet checking"
	::= { broriginalSNTP 7 }

brSNTPCPrimaryServerName  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..64) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The primary server name of SNTP."
	::= { broriginalSNTP 8 }

brSNTPCPrimaryServerPort  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The port number of primary SNTP server."
	::= { broriginalSNTP 9 }

brSNTPCSecondaryServerName  OBJECT-TYPE
	SYNTAX  DisplayString ( SIZE(0..64) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The secondary server name of SNTP."
	::= { broriginalSNTP 10 }

brSNTPCSecondaryServerPort  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The port number of secondary SNTP server."
	::= { broriginalSNTP 11 }

brSNTPCServerMethod  OBJECT-TYPE
	SYNTAX  INTEGER (1..3)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"1 indicates SNTP Server Method is AUTO.
         2 indicates  SNTP Server Method is STATIC.
         3 indicates  SNTP Server Method is PRESET.
"
	::= { broriginalSNTP 3 }

brPJLInfoFirmwareUpdateconfigTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrPJLInfoFirmwareUpdateconfigEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"This table represents PJLInfoFirmwareUpdateconfig."
	::= { pjlinfo 6 }

brPJLInfoFirmwareUpdateconfigEntry  OBJECT-TYPE
	SYNTAX  BrPJLInfoFirmwareUpdateconfigEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the PJLInfoFirmwareUpdateconfig table. Rows cannot be created or deleted."
	INDEX  { brPJLInfoFirmwareUpdateconfigIndex }
	::= { brPJLInfoFirmwareUpdateconfigTable 1 }

BrPJLInfoFirmwareUpdateconfigEntry ::= 
	SEQUENCE {
		brPJLInfoFirmwareUpdateconfigIndex
			INTEGER,
		brPJLInfoFirmwareUpdateconfig
			OCTET STRING
	}

brPJLInfoFirmwareUpdateconfigIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..65535)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number."
	::= { brPJLInfoFirmwareUpdateconfigEntry 1 }

brPJLInfoFirmwareUpdateconfig  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..255) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The PJLInfoFirmwareUpdateconfig."
	::= { brPJLInfoFirmwareUpdateconfigEntry 2 }

brFirmwareUpdateKeywordCount  OBJECT-TYPE
	SYNTAX  INTEGER (1..4)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number of Firmware for update the device."
	::= { firmwareupdatekeyword 1 }

brFirmwareUpdateKeywordTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrFirmwareUpdateKeywordEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"The table represents the number of Firmware for Update."
	::= { firmwareupdatekeyword 2 }

brFirmwareUpdateKeywordEntry  OBJECT-TYPE
	SYNTAX  BrFirmwareUpdateKeywordEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		"A row in the FirmwareUpdateKeyword table. Rows cannot be created or deleted."
	INDEX  { brFirmwareUpdateKeywordIndex }
	::= { brFirmwareUpdateKeywordTable 1 }

BrFirmwareUpdateKeywordEntry ::= 
	SEQUENCE {
		brFirmwareUpdateKeywordIndex
			INTEGER,
		brFirmwareUpdateKeyword
			OCTET STRING
	}

brFirmwareUpdateKeywordIndex  OBJECT-TYPE
	SYNTAX  INTEGER (0..4)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brFirmwareUpdateKeywordEntry 1 }

brFirmwareUpdateKeyword  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..255) )
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The keyword for firemware update the device."
	::= { brFirmwareUpdateKeywordEntry 2 }

brFuncLockUserPrintPageCounterReset  OBJECT-TYPE
	SYNTAX  INTEGER (0..0)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Reset all user's counter of Color/Mono print page."
	::= { funclock 20 }

brFuncLockPublicPrintLimitEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"This MIB represents Enable/Disable of print limit setting for public user."
	::= { funclock 21 }

brFuncLockPublicPrintPageMax  OBJECT-TYPE
	SYNTAX  INTEGER (1..999999)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Maximum of print limited for public user."
	::= { funclock 22 }

brFuncLockUserPrintPageTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrFuncLockUserPrintPageEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { funclock 25 }

brFuncLockUserPrintPageEntry  OBJECT-TYPE
	SYNTAX  BrFuncLockUserPrintPageEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brFuncLockUserPrintPageIndex }
	::= { brFuncLockUserPrintPageTable 1 }

BrFuncLockUserPrintPageEntry ::= 
	SEQUENCE {
		brFuncLockUserPrintPageCountMono
			INTEGER,
		brFuncLockUserPrintPageCountColor
			INTEGER,
		brFuncLockUserPrintPageIndex
			INTEGER,
		brFuncLockUserPrintPageLimitEnable
			INTEGER,
		brFuncLockUserPrintPageMax
			INTEGER,
		brFuncLockUserPrintPageCountMonoLast
			INTEGER,
		brFuncLockUserPrintPageCountColorLast
			INTEGER
	}

brFuncLockUserPrintPageCountMono  OBJECT-TYPE
	SYNTAX  INTEGER (0..999999)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The Counter of Monochrome Print Page for user."
	::= { brFuncLockUserPrintPageEntry 3 }

brFuncLockUserPrintPageCountColor  OBJECT-TYPE
	SYNTAX  INTEGER (0..999999)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The counter of color print page for user."
	::= { brFuncLockUserPrintPageEntry 4 }

brFuncLockPublicPrintPageCountColor  OBJECT-TYPE
	SYNTAX  INTEGER (0..999999)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The counter of color print page for public user."
	::= { funclock 24 }

brFuncLockPcLoginNameAuthEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"This MIB represents Enable/Disable of PC Login Authentication setting."
	::= { funclock 26 }

brFuncLockPcLoginNameAuthCount  OBJECT-TYPE
	SYNTAX  INTEGER
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The number of PC Login Name."
	::= { funclock 27 }

brFuncLockPcLoginNameTable  OBJECT-TYPE
	SYNTAX  SEQUENCE OF BrFuncLockPcLoginNameEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	::= { funclock 28 }

brFuncLockPcLoginNameEntry  OBJECT-TYPE
	SYNTAX  BrFuncLockPcLoginNameEntry
	ACCESS  not-accessible
	STATUS  mandatory
	DESCRIPTION
		""
	INDEX  { brFuncLockPcLoginNameAuthIndex }
	::= { brFuncLockPcLoginNameTable 1 }

BrFuncLockPcLoginNameEntry ::= 
	SEQUENCE {
		brFuncLockPcLoginNameAuthIndex
			INTEGER,
		brFuncLockPcLoginName
			OCTET STRING,
		brFuncLockPcLoginNameAuthID
			INTEGER
	}

brFuncLockPcLoginNameAuthIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..50)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brFuncLockPcLoginNameEntry 1 }

brFuncLockPcLoginName  OBJECT-TYPE
	SYNTAX  OCTET STRING
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The PC Login Name."
	::= { brFuncLockPcLoginNameEntry 2 }

brFuncLockPcLoginNameAuthID  OBJECT-TYPE
	SYNTAX  INTEGER (1..25)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"PC Login ID for PC Login name."
	::= { brFuncLockPcLoginNameEntry 3 }

brFuncLockPublicPrintPageCountMono  OBJECT-TYPE
	SYNTAX  INTEGER (0..999999)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"The counter of monochrome print page."
	::= { funclock 23 }

brFuncLockUserPrintPageIndex  OBJECT-TYPE
	SYNTAX  INTEGER (1..2147483647)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brFuncLockUserPrintPageEntry 1 }

brFuncLockUserPrintPageLimitEnable  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Maximum of Print Page setting for user."
	::= { brFuncLockUserPrintPageEntry 2 }

brFuncLockUserPrintPageMax  OBJECT-TYPE
	SYNTAX  INTEGER (1..999999)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"The Maximum of print limited for user."
	::= { brFuncLockUserPrintPageEntry 5 }

brFuncLockPublicPrintPageCountMonoLast  OBJECT-TYPE
	SYNTAX  INTEGER (0..999999)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		""
	::= { funclock 29 }

brFuncLockPublicPrintPageCountColorLast  OBJECT-TYPE
	SYNTAX  INTEGER (0..999999)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		""
	::= { funclock 30 }

brFuncLockUserPrintPageCountMonoLast  OBJECT-TYPE
	SYNTAX  INTEGER (0..999999)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brFuncLockUserPrintPageEntry 6 }

brFuncLockUserPrintPageCountColorLast  OBJECT-TYPE
	SYNTAX  INTEGER (0..999999)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		""
	::= { brFuncLockUserPrintPageEntry 7 }

brFuncLockAutoCountResetFrequency  OBJECT-TYPE
	SYNTAX  INTEGER (1..4)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { autocountreset 1 }

brFuncLockAutoCountResetTime  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(2..2) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { autocountreset 2 }

brFuncLockAutoCountResetWeek  OBJECT-TYPE
	SYNTAX  INTEGER (1..7)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { autocountreset 3 }

brFuncLockAutoCountResetDate  OBJECT-TYPE
	SYNTAX  INTEGER (1..31)
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		""
	::= { autocountreset 4 }

brWlanSimpleWizardSupported  OBJECT-TYPE
	SYNTAX  INTEGER (0..1)
	ACCESS  read-only
	STATUS  mandatory
	DESCRIPTION
		"WLAN Simple Wizard support information.
         0 indicates Simple Wizard is not supported. 
         1 indicates Simple Wizard is supported."
	::= { wlSimpleWizard 1 }

brWlanSimpleWizardPassword  OBJECT-TYPE
	SYNTAX  OCTET STRING ( SIZE(0..64) )
	ACCESS  read-write
	STATUS  mandatory
	DESCRIPTION
		"Simple Wizard Password.
         Max size 64"
	::= { wlSimpleWizard 2 }

END