stages:
  - trigger

before_script:
  - apt-get update && apt-get install -y curl
#
#trigger_pipeline_development:
#  stage: trigger
#  script:
#    - curl -X POST -F token=$DEV_ENV_BUILD_TRIGGER_TOKEN -F ref=main https://gitlab.com/api/v4/projects/$TRIGGER_TARGET_PROJECT_ID/trigger/pipeline
#  only:
#    - development

trigger_pipeline_staging:
  stage: trigger
#  trigger:
#    project: $TRIGGER_TARGET_PROJECT_ID
#    branch: main
#    strategy: depend
#    variables:
#      CI_COMMIT_MESSAGE: "[build] Triggered from upstream pipeline"
#  script:
#    - curl -X POST -F token=$DEV_ENV_BUILD_TRIGGER_TOKEN -F ref=main https://gitlab.com/api/v4/projects/$TRIGGER_TARGET_PROJECT_ID/trigger/pipeline

  script:
    - |
      curl --request POST \
        --form "token=$DEV_ENV_BUILD_TRIGGER_TOKEN" \
        --form "ref=main" \
        --form "variables[CI_COMMIT_MESSAGE]=[build] Triggered from upstream pipeline" \
        "https://gitlab.com/api/v4/projects/$TRIGGER_TARGET_PROJECT_ID/trigger/pipeline"
  only:
    - staging
