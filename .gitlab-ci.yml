#include:
#  - local: 'environments/development/.gitlab-ci.yml'
#    rules:
#      - if: $CI_COMMIT_BRANCH == "development"
#  - local: 'environments/staging/.gitlab-ci.yml'
#    rules:
#      - if: $CI_COMMIT_BRANCH == "staging"
#  - local: 'environments/production/.gitlab-ci.yml'
#    rules:
#      - if: $CI_COMMIT_BRANCH == "main"
#
#workflow:
#  rules:
#    - if: $CI_COMMIT_BRANCH == "development"
#    - if: $CI_COMMIT_BRANCH == "staging"
#    - if: $CI_COMMIT_BRANCH == "main"
#
#after_script:
#  - echo "Done!"


image: docker:latest

services:
  - docker:dind

stages:
  - build
#  - push

variables:
  DOCKER_DRIVER: overlay2
  IMAGE_LIBRENMS_STAGING_NAME: registry.gitlab.com/arranetworks/web/deploy/nms:staging
  IMAGE_WIREGUARD_STAGING_NAME: registry.gitlab.com/arranetworks/web/deploy/nms/wireguard:staging

before_script:
  - echo "=== BUILD INFORMATION ==="
  - echo "Commit message: $CI_COMMIT_MESSAGE"
  - echo "Original commit SHA: ${ORIGINAL_COMMIT_SHA:-N/A}"
  - echo "Original project: ${ORIGINAL_PROJECT:-N/A}"
  - echo "Original branch: ${ORIGINAL_BRANCH:-N/A}"
  - echo "Current pipeline: $CI_PIPELINE_ID"
  - echo "========================="
  - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY

build_librenms:
  stage: build
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /\[build\]/          # trigger prin mesaj
    - when: never                                    # skip în alte cazuri
  script:
    - cp environments/staging/librenms/librenms.env.example environments/staging/librenms/librenms.env
    - cp environments/staging/librenms/msmtpd.env.example environments/staging/librenms/msmtpd.env
    - cp environments/staging/.env.example environments/staging/.env
    - docker compose -f environments/staging/docker-compose.yml build librenms --build-arg SOURCE_CODE_BRANCH=staging --build-arg DEPLOY_TOKEN_USERNAME=$DOCKER_ARG_DEPLOY_TOKEN_USERNAME --build-arg DEPLOY_TOKEN_PASSWORD=$DOCKER_ARG_DEPLOY_TOKEN_PASSWORD --no-cache
    - docker image ls -a
    - docker tag $IMAGE_LIBRENMS_STAGING_NAME $IMAGE_LIBRENMS_STAGING_NAME
    - docker push $IMAGE_LIBRENMS_STAGING_NAME

build_wireguard:
  stage: build
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /\[build\]/          # trigger prin mesaj
    - when: never                                    # skip în alte cazuri
  script:
    - docker compose -f environments/staging/docker-compose.yml build wireguard --no-cache
    - docker image ls -a
    - docker tag $IMAGE_WIREGUARD_STAGING_NAME $IMAGE_WIREGUARD_STAGING_NAME
    - docker push $IMAGE_WIREGUARD_STAGING_NAME

after_script:
  - docker logout $CI_REGISTRY

