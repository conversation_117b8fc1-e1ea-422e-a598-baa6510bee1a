<?php

namespace Arra\Billing\Services\WHMCS\InitBilling;

use Arra\Billing\Models\ArraBillingRole;
use Arra\Billing\Models\ArraDeviceGroupBillingSettings;
use Arra\Billing\Services\WHMCS\Integration\WhmcsConnector;
use Arra\Billing\Services\WHMCS\Integration\WhmcsDtoRequest;
use Arra\Billing\Traits\BillingTrait;
use Exception;
use Illuminate\Support\Facades\Log;
use Throwable;

class InitBillingService
{
    use BillingTrait;
    public function __construct(private readonly WhmcsConnector $connector)
    {

    }

    /**
     * @throws Exception
     */
    public function execute(int $billingSettingsID): ?array
    {
        $arraBillingSettings = ArraDeviceGroupBillingSettings::whereHas('deviceGroup')->with(['deviceGroup'])->findOrFail($billingSettingsID);
        $permissions = [];
        $roleName = '';

        if (!empty($arraBillingSettings->billing_role_id)) {
            $role = ArraBillingRole::with(['permissions'])->find($arraBillingSettings->billing_role_id);
            if ($role) {
                $roleName = $role->name;
                $permissions = $role->permissions->pluck('billing_platform_permission_id')->toArray();
            }
        }
        $params = [
            'action' => 'init_billing',
            'group_name'  => "group_".$arraBillingSettings->device_group_id,
            'group_id' => $arraBillingSettings->device_group_id,
            'tax_name'  => $arraBillingSettings->tax_name,
            'name'  => $arraBillingSettings->tax_name,
            'taxrate' => $arraBillingSettings->billing_percentage,
            'billing_url'  => $arraBillingSettings->billing_url,
            'billing_role_id' => $arraBillingSettings->billing_role_id,
            'billing_role_name' => $roleName,
            'billing_role_permissions' => $permissions,
            'taxation_type' => $arraBillingSettings->tax_type,
            'radius_desk_user_id' => $arraBillingSettings->radius_desk_permanent_user_id,
            'radius_desk_realm_id' => $arraBillingSettings->radius_desk_realm_id,
            'smtp_host' => $arraBillingSettings->smtp_host,
            'smtp_username' => $arraBillingSettings->smtp_username,
            'smtp_password' => $arraBillingSettings->smtp_password,
            'smtp_port' => $arraBillingSettings->smtp_port,
            'emails_from_name' => $arraBillingSettings->email_from_name,
            'emails_from_email' => $arraBillingSettings->email_from_address,
            'timestamp' => time()
        ];
        $endpoint = 'index.php?m=arranetworkscore';
        $whmcsDtoRequest = new WhmcsDtoRequest(
            $arraBillingSettings->billing_url,
            $endpoint,
            [
                'token' => self::encryptData($params, $arraBillingSettings->billing_key),
                'decoded_token' => json_encode($params),
            ]
        );
        return $this->connector->execute($whmcsDtoRequest);
    }
}
