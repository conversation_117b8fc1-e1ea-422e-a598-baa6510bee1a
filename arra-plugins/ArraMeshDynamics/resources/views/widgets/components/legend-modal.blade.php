<div class="modal fade" id="legendModal" tabindex="-1" role="dialog" aria-labelledby="legendModalLabel" aria-hidden="true">
    <div class="modal-dialog  modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <div class="modal-legend-wrap">

                    <h3>Link Lines</h3>
                    <div class="table-responsive">
                        <table class="table table-striped table-dark">
                            <tbody>
                            <tr>
                                <td width="120px">Mesh 1</td>
                                <td width="140px">
                                    <div class="svg-an">
                                        <svg pointer-events="none" class="" width="1000" height="20"><g><path class="leaflet-interactive" stroke="#f72194" stroke-opacity="0.5" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" fill="none" d="M0 14L200 14"></path><path class="leaflet-ant-path leaflet-interactive" stroke="#f2d1e3" stroke-opacity="0.5" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="10,20" fill="none" d="M0 14L200 14" style="animation-duration: 14s;" data-animated="true"></path></g></svg>
                                    </div>
                                </td>
                                <td>Mesh 1 radio link line</td>
                            </tr>

                            <tr>
                                <td width="120px">Mesh 2</td>
                                <td width="140px">

                                    <div class="svg-an">
                                        <svg pointer-events="none" class="" width="1000" height="20"><g><path class="leaflet-interactive" stroke="#008C00" stroke-opacity="0.5" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" fill="none" d="M0 14L200 14"></path><path class="leaflet-ant-path leaflet-interactive" stroke="#c9f5c9" stroke-opacity="0.5" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="10,20" fill="none" d="M0 14L200 14" style="animation-duration: 14s;" data-animated="true"></path></g></svg>
                                    </div>

                                </td>
                                <td>Mesh 2 radio link line</td>
                            </tr>
                            <tr>
                                <td width="120px">Mesh 3</td>
                                <td width="140px">



                                        <div class="svg-an">
                                            <svg pointer-events="none" class="" width="1000" height="20"><g><path class="leaflet-interactive" stroke-opacity="0.5" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" fill="none" d="M0 14L200 14" stroke="#1c86e1"></path><path class="leaflet-ant-path leaflet-interactive" stroke-opacity="0.5" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="10,20" fill="none" d="M0 14L200 14" style="animation-duration: 14s;" data-animated="true" stroke="#1ba9f5"></path></g></svg>
                                        </div>


                                </td>
                                <td>Mesh 3 radio link line</td>
                            </tr>
                            <tr>
                                <td width="120px">Split</td>
                                <td width="140px">

                                    <div class="svg-an-hh">

                                        <div class="svg-an">
                                            <svg pointer-events="none" class="" width="1000" height="20"><g><path class="leaflet-interactive" stroke="#f72194" stroke-opacity="0.5" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" fill="none" d="M0 14L200 14"></path><path class="leaflet-ant-path leaflet-interactive" stroke="#f2d1e3" stroke-opacity="0.5" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="10,20" fill="none" d="M0 14L200 14" style="animation-duration: 14s;" data-animated="true"></path></g></svg>
                                        </div>

                                        <div class="svg-an">
                                            <svg pointer-events="none" class="" width="1000" height="20"><g><path class="leaflet-interactive" stroke="#008C00" stroke-opacity="0.5" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" fill="none" d="M0 14L200 14"></path><path class="leaflet-ant-path leaflet-interactive" stroke="#c9f5c9" stroke-opacity="0.5" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="10,20" fill="none" d="M0 14L200 14" style="animation-duration: 14s;" data-animated="true"></path></g></svg>
                                        </div>
                                    </div>

                                </td>
                                <td>Cross link connection</td>
                            </tr>

                            <tr>
                                <td width="120px">Alternate</td>
                                <td width="140px">

                                    <div class="svg-an">
                                        <svg pointer-events="none" class="" width="1000" height="20"><g><path class="" stroke="#5F5F5FFF" stroke-opacity="0.5" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="10,20" fill="none" d="M0 14L200 14"></path></g></svg>
                                    </div>

                                </td>
                                <td>Alternate radio link that can be used</td>
                            </tr>

                            <tr>
                                <td width="120px">Alternate 1</td>
                                <td width="140px">

                                    <div class="svg-an">
                                        <svg pointer-events="none" class="" width="1000" height="20"><g><path class="" stroke="#f2d1e3" stroke-opacity="0.5" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="10,20" fill="none" d="M0 14L200 14" ></path></g></svg>
                                    </div>

                                </td>
                                <td>Mouse over shows alternate in it's mesh link color</td>
                            </tr>

                            <tr>
                                <td width="120px">Alternate 2</td>
                                <td width="140px">
                                    <div class="svg-an">
                                        <svg pointer-events="none" class="" width="1000" height="20"><g><path class="" stroke="#c9f5c9" stroke-opacity="0.5" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="10,20" fill="none" d="M0 14L200 14"></path></g></svg>
                                    </div>

                                </td>
                                <td>Mouse over shows alternate in it's mesh link color</td>
                            </tr>

                            <tr>
                                <td width="120px">Alternate 3</td>
                                <td width="140px">
                                    <div class="svg-an">
                                        <svg pointer-events="none" class="" width="1000" height="20"><g><path class="" stroke="#1ba9f5" stroke-opacity="0.5" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="10,20" fill="none" d="M0 14L200 14"></path></g></svg>
                                    </div>

                                </td>
                                <td>Mouse over shows alternate in it's mesh link color</td>
                            </tr>

                            <tr>
                                <td width="120px">Last Known</td>
                                <td width="140px">

                                    <div class="svg-an">
                                        <svg pointer-events="none" class="" width="1000" height="20"><g><path class="leaflet-interactive" stroke="#008C00" stroke-opacity="0.5" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" fill="none" d="M0 14L200 14"></path><path stroke="#c9f5c9" stroke-opacity="0.5" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="10,20" fill="none" d="M0 14L200 14"></path></g></svg>
                                    </div>

                                </td>
                                <td>Static link lines show last known connection path</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>


                    <h3 style="margin-top: 30px;">Node Status</h3>
                    <div class="table-responsive">
                        <table class="table table-striped table-dark">
                            <tbody>
                            <tr>
                                <td valign="middle" width="140px">OK</td>
                                <td width="120px" align="middle"><div class="awesome-marker-icon-green awesome-marker" style="position: relative" tabindex="0"></div></td>
                                <td>No Problems are being reported</td>
                            </tr>

                            <tr>
                                <td valign="middle" width="140px">Warning</td>
                                <td width="120px" align="middle"><div class="awesome-marker-icon-orange awesome-marker" style="position: relative" tabindex="0"></div></td>
                                <td>Intermitent connectivity problems</td>
                            </tr>

                            <tr>
                                <td valign="middle" width="140px">Critical</td>
                                <td width="120px" align="middle"><div class="awesome-marker-icon-red awesome-marker" style="position: relative" tabindex="0"></div></td>
                                <td>Not currently connected</td>
                            </tr>

                            <tr>
                                <td valign="middle" width="140px">Backhaul Node</td>
                                <td width="120px" align="middle"><div class="awesome-marker-icon-marker-animated awesome-marker" style="position: relative" tabindex="0"></div></td>
                                <td>This node is directly connected to backhaul</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                </div>

            </div>

        </div>
    </div>
</div>
