@if(count($devices) > 0)
    <table class="table table-condensed">
        <thead>
            <tr style="background: transparent !important;">
                <th>
                    @if($meshType == 'arra-mesh1')
                        <span class="mesh-stats-title"><span class="status-circle deeppink"></span> Mesh 1</span>
                    @else
                        <span class="mesh-stats-title"><span class="status-circle mdot green"></span> Mesh 2</span>
                    @endif
                </th>
                <th>Signal</th>
                <th style="text-align: right"><button onclick="changeOrderType('{{$meshType}}','signal')"><img src="{{asset('images/arra/ret-arrow.jpg')}}" alt="" /></button></th>
                <th class="modulation-break">Modulation</th>
                <th style="text-align: right"><button onclick="changeOrderType('{{$meshType}}','modulation')"><img src="{{asset('images/arra/ret-arrow.jpg')}}" alt="" /></button></th>
            </tr>
        </thead>
        <tbody>
            @foreach($devices as $device)
                <input type="hidden" class="mesh_device_host" value="{{$device['device_id']}}" />
                <tr style="background: transparent !important;">
                    <td style="min-width: 110px">
                        <span class="mesh-stats-mesh-id @if($device['had_error']) red @elseif($device['is_connect']) white @endif" >
                            {{Str::replace('amr-fran-','',$device['sysname'])}}
                        </span>
                        <span class="mesh-stats-id-sm">
                            @if($device['had_error'])
                                <span class="clr-red">{{$device['grey']['ch']}} ch</span><br>
                            @else
                                {{$device['grey']['ch']}} ch<br>
                            @endif
                            {{$device['grey']['bw']}} bw<br>
                            {{$device['grey']['db']}} db
                        </span>
                    </td>
                    <td align="right" valign="middle">
                        <span class="clr-lighter">{{$device['white']['snr']}}</span> <br> <span class="clr-lighter">{{$device['white']['signal']}}/{{$device['white']['noise-floor']}}</span>
                    </td>
                    <td align="right" valign="middle">
                       {{$device['grey']['snr']}} <br> {{$device['grey']['signal']}}/{{$device['white']['noise-floor']}}
                    </td>
                    <td align="right" valign="middle" style="min-width: 50px">
                        <span class="clr-lighter">{{$device['white']['rx']}} <span class="data-span-small">Rx</span></span> <br> <span class="clr-lighter">{{$device['white']['tx']}} <span class="data-span-small">Tx</span></span>
                    </td>
                    <td align="right" valign="middle" style="min-width: 50px">
                        {{$device['grey']['tx']}} <span class="data-span-small">Tx</span> <br> {{$device['grey']['rx']}} <span class="data-span-small">Rx</span>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
@endif
