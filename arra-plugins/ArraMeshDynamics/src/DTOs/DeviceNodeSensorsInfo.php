<?php

namespace Arra\MeshDynamics\DTOs;

class DeviceNodeSensorsInfo
{
    public function __construct(
        private readonly float|string $ch,
        private readonly float|string $bw,
        private readonly float|string $db,
        private readonly float|string $noiseFloor,
        private readonly float|string $snr,
        private readonly float|string $tx,
        private readonly float|string $rx,
    )
    {

    }

    public function getCh(): float|string
    {
        return $this->ch;
    }

    public function getBw(): float|string
    {
        return $this->bw;
    }

    public function getDb(): float|string
    {
        return $this->db;
    }

    public function getNoiseFloor(): float|string
    {
        return $this->noiseFloor;
    }

    public function getSnr(): float|string
    {
        return $this->snr;
    }

    public function getTx(): float|string
    {
        return $this->tx;
    }

    public function getRx(): float|string
    {
        return $this->rx;
    }
}
