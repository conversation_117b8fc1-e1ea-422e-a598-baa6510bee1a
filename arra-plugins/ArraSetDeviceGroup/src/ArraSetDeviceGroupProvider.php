<?php

namespace Arra\SetDeviceGroup;

use Arra\SetDeviceGroup\Services\AddDeviceToGroupService;
use Arra\SetDeviceGroup\Services\SetDeviceAndLocationService;
use Arra\SetDeviceGroup\Services\SetDeviceGroupService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Foundation\Console\AboutCommand;
use Illuminate\Support\ServiceProvider;
use LibreNMS\Interfaces\Plugins\PluginManagerInterface;
use Illuminate\Contracts\Foundation\Application;

class ArraSetDeviceGroupProvider extends ServiceProvider
{
    /**
     * Bootstrap any package services.
     */
    public function boot(PluginManagerInterface $pluginManager): void
    {
        $pluginName = 'arra-set-device-group';

        // register hooks with LibreNMS (if any are desired)
        // if no hooks are defined, LibreNMS may delete the plugin from the ui
        // if you don't want any specific hooks, you can just register a settings hook
        // $pluginManager->publishHook($pluginName, MenuEntryHook::class, MenuEntry::class);
        //$pluginManager->publishHook($pluginName, SettingsHook::class, Settings::class);

        if (! $pluginManager->pluginEnabled($pluginName)) {
            return; // if plugin is disabled, don't boot
        }

        AboutCommand::add('Arra Set Device Group', fn (): array => ['Version' => '1.0.0']);

        $this->loadRoutesFrom(__DIR__.'/../routes/web.php');
        $this->loadViewsFrom(__DIR__.'/../resources/views', $pluginName);
        $this->loadMigrationsFrom(__DIR__.'/../database/migrations');

        $this->publishes([
            // __DIR__.'/../public' => public_path('vendor/example-plugin'), // files that can be published publicly
            // __DIR__.'/../config/config.php' => config_path('example-plugin.php'),
        ]);
    }

    /**
     * @throws BindingResolutionException
     */
    public function register(): void
    {
        $this->bindDI();
    }

    private function bindDI(): void
    {
        $this->app->bindIf(SetDeviceGroupService::class, function (Application $app) {
            return new SetDeviceGroupService(
                $app->make(SetDeviceAndLocationService::class),
                $app->make(AddDeviceToGroupService::class)
            );
        });

    }
}
