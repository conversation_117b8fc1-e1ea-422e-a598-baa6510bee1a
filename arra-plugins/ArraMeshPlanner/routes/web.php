<?php

use Arra\MeshPlanner\Http\Controllers\MeshPlannerProjectController;
use Illuminate\Support\Facades\Route;

Route::middleware(['web', 'auth'])->namespace('Arra\MeshPlanner\Http\Controllers')->group(function (): void {
    Route::get('/arra/mesh-planner', [MeshPlannerProjectController::class, 'index'])->name('arra-mesh-planner.index');
    Route::get('/arra/mesh-planner/{project}', [MeshPlannerProjectController::class, 'edit'])->name('arra-mesh-planner.edit');
    Route::post('/arra/mesh-planner/{project}/import', [MeshPlannerProjectController::class, 'import'])->name('arra-mesh-planner.import');
    Route::get('/arra/mesh-planner/{project}/export', [MeshPlannerProjectController::class, 'export'])->name('arra-mesh-planner.export');
});
