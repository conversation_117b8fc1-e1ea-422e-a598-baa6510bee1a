<?php

namespace Arra\MeshPlanner\Api\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreLinesAndNodesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'points' => 'sometimes|array',
            'lines' => 'sometimes|array',
            'project' => 'required|array',
            'poi' => 'sometimes|array'
        ];
    }

    protected function prepareForValidation(): void
    {
        $data = json_decode($this->content, true);

        $points  = (isset($data['points']) && !empty($data['points']))? $data['points']  : [];
        $lines   = (isset($data['lines']) && !empty($data['lines'])) ? $data['lines']  : [];
        $project = (isset($data['project']) && !empty($data['project'])) ? $data['project']: [];
        $poi = (isset($data['poi']) && !empty($data['poi'])) ? $data['poi']: [];

        $this->replace([
            'points' => $points,
            'lines' => $lines,
            'project' => $project,
            'poi' => $poi,
        ]);
    }
}
