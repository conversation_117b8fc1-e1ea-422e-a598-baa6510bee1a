<?php

namespace Arra\MeshPlanner\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ImportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'file' => [
                'required',
                function ($attribute, $value, $fail) {
                    $getFileExtension = $value->getClientOriginalExtension();
                    if ($getFileExtension !== 'kml') {
                        $fail("The file extension must be '.kml'");
                    }
                }
            ]
        ];
    }
}
