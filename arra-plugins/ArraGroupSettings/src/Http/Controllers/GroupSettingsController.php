<?php

namespace Arra\GroupSettings\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Device;
use Arra\GroupSettings\Http\Requests\UpdateGroupSettingsRequest;
use Arra\GroupSettings\Models\ArraDeviceGroupByodDevice;
use Arra\GroupSettings\Models\ArraGroupSettings;
use Arra\GroupSettings\Repositories\ArraByodDeviceRepository;
use Arra\GroupSettings\Repositories\ArraGroupSettingsRepository;
use Arra\GroupSettings\Services\ByodDevice\CreateByodDeviceService;
use Arra\GroupSettings\Services\ByodDevice\DeleteByodDeviceService;
use Arra\GroupSettings\Services\ByodDevice\UpdateByodDeviceService;
use Arra\GroupSettings\Services\UpdateGroupSettingsService;
use Arra\GroupSettings\Services\WHMCS\UpdateBillingInfo\UpdateBillingInfoService;
use Arra\GroupSettings\Services\WHMCS\UpdateBillingProducts\UpdateBillingProductsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;

class GroupSettingsController extends Controller
{
    public function index()
    {
        $groupSettings = ArraGroupSettings::whereHas('deviceGroup')->with(['deviceGroup'])->get();
        return view('arra-group-settings::index', [
            'groupSettings' => $groupSettings
        ]);
    }

    public function edit()
    {
        $savedSuccess = false;
        $userDeviceGroup = auth()->user()->deviceGroups()->first();
        if (!$userDeviceGroup) {
            abort(403, 'User does not have a device group assigned!');
        }
        $groupSettings = ArraGroupSettings::whereHas('deviceGroup')
            ->with(['deviceGroup'])
            ->where('device_group_id', $userDeviceGroup->id)
            ->firstOrFail();

        if (session()->has('gs_edit_success')) {
            $savedSuccess = true;
            session()->forget('gs_edit_success');
        }
        $motvPackages = [];
        $motvConfigPackages = config("motv.groups.{$userDeviceGroup->id}");
        if (!empty($motvConfigPackages)) {
            $motvConfigPackages = $motvConfigPackages['motv_packages'];
            if (!empty($motvConfigPackages) && is_array($motvConfigPackages) && count($motvConfigPackages) > 0) {
                $motvPackages = $motvConfigPackages;
            }
        }
        //dd(ArraByodDeviceRepository::getDevicesByGroup($userDeviceGroup->id));
        return view('arra-group-settings::edit', [
            'groupSettings' => $groupSettings,
            'motvPackages' => $motvPackages,
            'group_BYOD_devices' => [
                'items_list' => ArraByodDeviceRepository::getDevicesByGroup($userDeviceGroup->id),
                'items_total' => ArraByodDeviceRepository::countDevicesByGroup($userDeviceGroup->id)
            ],
            'savedSuccess' => $savedSuccess
        ]);
    }

    /**
     * @throws \Exception
     */
    public function update(UpdateGroupSettingsRequest $request)
    {
        $userDeviceGroup = auth()->user()->deviceGroups()->first();

        if (!$userDeviceGroup || !$userDeviceGroup->id != $request->group_id) {
            Log::error('Group Settings Update Error: User '.auth()->user()->id.' does not have a device group assigned!');
            return response()->json([
                'message' => 'User does not have a device group assigned!'
            ], 503);
        }


        $groupSettings = ArraGroupSettings::whereHas('deviceGroup')
            ->with(['deviceGroup'])
            ->where('device_group_id', $userDeviceGroup->id)
            ->firstOrFail();

        $disk = config('filesystems.default') === 'local' ? 'public' : config('filesystems.default');
        $storage = Storage::disk($disk);
        $filesPath = 'group-settings/files/';
        $form_params = [];
        parse_str($request->data, $form_params);
        $fileNames = [
            'file_logo' => 'logo',
            'file_background_image' => 'background_image',
            'file_m_1' => '1_mt_file',
            'file_m_2' => '2_mt_file',
            'file_m_3' => '3_mt_file',
            'file_m_4' => '4_mt_file',
            'file_m_5' => '5_mt_file',
            'file_m_6' => '6_mt_file',
//            'file_m_7' => '7_mt_file',
//            'file_m_8' => '8_mt_file',
        ];
        foreach ($fileNames as $file => $name) {
            if ($request->hasFile($file)) {
                $extension = $request->file($file)->getClientOriginalExtension();
                $extension_array = ['jpg', 'jpeg', 'png', 'gif', 'svg'];
                switch ($name) {
                    case 'logo':
                        $filename = $form_params['group_id'].'.'.$extension;
                        break;
                    case 'background_image':
                        $filename = $form_params['group_id'].'_bk_img.'.$extension;
                        break;
                    case '1_mt_file':
                        $filename = $form_params['group_id'].'_m_1_img.'.$extension;
                        break;
                    case '2_mt_file':
                        $filename = $form_params['group_id'].'_m_2_img.'.$extension;
                        break;
                    case '3_mt_file':
                        $filename = $form_params['group_id'].'_m_3_img.'.$extension;
                        break;
                    case '4_mt_file':
                        $filename = $form_params['group_id'].'_m_4_img.'.$extension;
                        break;
                    case '5_mt_file':
                        $filename = $form_params['group_id'].'_m_5_img.'.$extension;
                        break;
                    case '6_mt_file':
                        $filename = $form_params['group_id'].'_m_6_img.'.$extension;
                        break;
//                    case '7_mt_file':
//                        $filename = $form_params['group_id'].'_m_7_img.'.$extension;
//                        break;
//                    case '8_mt_file':
//                        $filename = $form_params['group_id'].'_m_8_img.'.$extension;
//                        break;
                }

                if (empty($filename)) {
                    continue;
                }

                if ($storage->exists($filesPath.$filename)) {
                    $storage->delete($filesPath.$filename);
                }
                if (in_array($extension, $extension_array) && $storage->putFileAs($filesPath, $request->file($file), $filename)) {
                    $form_params[$name] = $storage->url($filesPath.$filename);
                } else {
                    $form_params[$name] = '';
                }
            }
        }


        $updateGroupSettingsService = App::make(UpdateGroupSettingsService::class);
        $updateGroupSettingsService->execute($userDeviceGroup->id, $form_params);

        $updateBillingInfoService = App::make(UpdateBillingInfoService::class);
        $updateBillingInfoService->execute($userDeviceGroup->id);

        $updateBillingProductsService = App::make(UpdateBillingProductsService::class);
        $updateBillingProductsService->execute($userDeviceGroup->id, $groupSettings);

        session()->put('gs_edit_success', true);
        echo route('arra-group-settings.edit')."?saved=1";
        //die();

//        GBD::setBillingCompanyDetails($form_params['group_id']);
//        GroupSettings::saveApiData($result,$old_data);
//
//        $_SESSION['gs_edit_success'] = true;
//        echo  route("gsm-e", ['id' => $form_params['group_id']]);

//        $old_data = GroupSettings::getGroupDetails($form_params['group_id']);
//        //dd('333');
//        $result = GroupSettings::saveData($form_params);
//        if($result){
//            GBD::setBillingCompanyDetails($form_params['group_id']);
//            GroupSettings::saveApiData($result,$old_data);
//
//            $_SESSION['gs_edit_success'] = true;
//            echo  route("gsm-e", ['id' => $form_params['group_id']]);
//        }
        //echo '';
    }

    public function addEditByodDevice(Request $request)
    {
        $userDeviceGroup = auth()->user()->deviceGroups()->first();
        $output = [
            'status' => '400',
            'message' => 'Invalid Parameters',
            'input' => 'bd_mac_description'
        ];

        if (isset($request->bd_group_id) &&
            isset($request->bd_mac_address) &&
            isset($request->bd_mac_speed) &&
            isset($request->bd_mac_description) &&
            isset($request->bd_parental_control)
        ) {

            if ( ArraByodDeviceRepository::checkIfMacAddressBelongsToGroup($request->bd_mac_address, $userDeviceGroup->id)) {

                if (isset($request->p_dns) && trim(strip_tags($request->p_dns))) {
                    $p_dns = trim(strip_tags($request->p_dns));
                } else {
                    $p_dns = '';
                }

                if (isset($request->s_dns) && trim(strip_tags($request->s_dns))) {
                    $s_dns = trim(strip_tags($request->s_dns));
                } else {
                    $s_dns = '';
                }

                if ($request->bd_parental_control) {
                    if(!$s_dns || !$p_dns){
                        $output['message'] = 'To use the Parental Control option, you must complete the two DNS.';
                        return $output;
                    }
                }

                if (isset($request->bd_device_id) && (int) $request->bd_device_id) { // EDIT DEVICE
                    DB::beginTransaction();
                    try {
                        $byodDevice = ArraDeviceGroupByodDevice::where('id', $request->bd_device_id)->where('device_group_id',$userDeviceGroup->id)->first();
                        if (empty($byodDevice)) {
                            $output['message'] = 'This device not exist or not belongs to this group!';
                            $output['status'] = 400;
                            \Response::json($output);
                        }
                        $updateByodDeviceService = App::make(UpdateByodDeviceService::class);
                        $updateByodDeviceService->execute(
                            (int) $request->bd_device_id,
                            (int) trim(strip_tags($request->bd_mac_speed)),
                            trim(strip_tags($request->bd_mac_description)),
                            trim(strip_tags($request->bd_parental_control)),
                            $p_dns,
                            $s_dns
                        );
                        DB::commit();
                        $output['message'] = 'Devices edited successfully.';
                        $output['success'] = true;
                        $output['status'] = 200;
                        \Response::json($output);
                    } catch (\Throwable $e) {
                        DB::rollBack();
                        $output['message'] = $e->getMessage();
                        $output['status'] = 400;
                        \Response::json($output);
                    }

                } else { // ADD DEVICE
                    $byodDevice = ArraDeviceGroupByodDevice::where('mac_address', $request->bd_mac_address)->first();
                    if (!empty($byodDevice)) {
                        $output['message'] = 'This Mac Address already exists!';
                        $output['status'] = 400;
                        $output['input'] = 'bd_mac_address';
                        \Response::json($output);
                    }

                    DB::beginTransaction();
                    try {
                        $createByodDeviceService = App::make(CreateByodDeviceService::class);
                        $createByodDeviceService->execute(
                            $userDeviceGroup->id,
                            $request->bd_mac_address,
                            (int) trim(strip_tags($request->bd_mac_speed)),
                            trim(strip_tags($request->bd_mac_description)),
                            trim(strip_tags($request->bd_parental_control)),
                            $p_dns,
                            $s_dns
                        );
                        DB::commit();
                        $output['message'] = 'Devices added successfully.';
                        $output['success'] = true;
                        $output['status'] = 200;
                        \Response::json($output);
                    } catch (\Throwable $e) {
                        DB::rollBack();
                        $output['message'] = $e->getMessage();
                        $output['status'] = 400;
                        return \Response::json($output);
                    }
                }
            } else {
                $output['message'] = 'This Mac Address does not belong to the group.';
                $output['input'] = 'bd_mac_address';
            }

        }
        return \Response::json($output);
    }

    public function getByodDevices(Request $request)
    {
        $userDeviceGroup = auth()->user()->deviceGroups()->first();
        $page = $request->page ?? 1;
        $per_page = $request->bd_counter ?? 10;
        $search = $request->bd_searchquery ?? '';
        $output = [
            'items_list' => ArraByodDeviceRepository::getDevicesByGroup($userDeviceGroup->id, $per_page, $page, $search),
            'items_total' => ArraByodDeviceRepository::countDevicesByGroup($userDeviceGroup->id, $search)
        ];

        return \Response::json($output);
    }

    public function deleteByodDevice(Request $request)
    {
        if (empty($request->db_device_id)) {
            return \Response::json([
                'message' => 'Invalid Parameters',
                'status' => 400
            ]);
        }

        $userDeviceGroup = auth()->user()->deviceGroups()->first();
        $byodDevice = ArraDeviceGroupByodDevice::where('id', $request->db_device_id)->where('device_group_id', $userDeviceGroup->id)->first();
        if (empty($byodDevice)) {
            return \Response::json([
                'message' => 'This device not exist or not belongs to this group!',
                'status' => 400
            ]);
        }

        try {
            $deleteByodDeviceService = App::make(DeleteByodDeviceService::class);
            $deleteByodDeviceService->execute($byodDevice->id);

            $byodDevice->delete();
            return \Response::json([
                'message' => 'Device deleted successfully.',
                'status' => 200
            ]);
        } catch (\Throwable $e) {
            return \Response::json([
                'message' => $e->getMessage(),
                'status' => 400
            ]);
        }
    }

    public function getGroupConfigVersion(Request $request)
    {
        if (config('app.debug')) {
            \Debugbar::disable();
        }
        if (!$request->has('mac') || !$request->filled('mac')) {
            return '';
        }

        $macAddress = $request->input('mac');

        $device = ArraByodDeviceRepository::getDeviceByMacAddress($macAddress);

        if (!$device) {
            return '';
        }

        $deviceGroup = $device->groups->first();

        if (!$deviceGroup) {
            return '';
        }

        $configFileContent = ArraGroupSettingsRepository::getGroupConfigFileContent($deviceGroup->id, $request->has('rev'));
        if (empty($configFileContent)) {
            return '';
        }

        return response(hash('sha256', $configFileContent))->header("Access-Control-Allow-Origin",  "*");

        //echo hash_file('sha256', $configFileUrl);
        //return '';
    }

    public function getGroupConfig(Request $request)
    {
        if (config('app.debug')) {
            \Debugbar::disable();
        }

        if (!$request->has('mac') || !$request->filled('mac')) {
            return '';
        }

        $macAddress = $request->input('mac');

        $device = ArraByodDeviceRepository::getDeviceByMacAddress($macAddress);

        if (!$device) {
            return '';
        }

        $deviceGroup = $device->groups->first();

        if (!$deviceGroup) {
            return '';
        }

        $configFileContent = ArraGroupSettingsRepository::getGroupConfigFileContent($deviceGroup->id, $request->has('rev'));
        if (empty($configFileContent)) {
            return '';
        }

        $configFileContent = str_replace("\n", "", $configFileContent);

        return response($configFileContent)->header("Access-Control-Allow-Origin",  "*");

//        if ($request->has('rev')) {
//            return response($file)->header("Access-Control-Allow-Origin",  "*");
////            header('Content-type: application/json');
////            echo $file;
//        } else {
//            echo $file;
//        }
    }

    public function getGroupSettingsForCP(Request $request)
    {
        if (!$request->has('mac') || !$request->filled('mac')) {
            return response()->json([
                'success' => false,
                'message' => 'mac parameter is required.'
            ])->header("Access-Control-Allow-Origin",  "*");
        }

        $device = ArraByodDeviceRepository::getDeviceByMacAddress($request->input('mac'));

        if (empty($device)) {
            return response()->json([
                'success' => false,
                'message' => 'Device not found.'
            ])->header("Access-Control-Allow-Origin",  "*");
        }

        $deviceGroupID = $device->groups->first()?->id;

        if (empty($deviceGroupID)) {
            return response()->json([
                'success' => false,
                'message' => 'Device group not found.'
            ])->header("Access-Control-Allow-Origin",  "*");
        }

        $groupSettings = ArraGroupSettings::where('device_group_id', $deviceGroupID)->first();

        if (empty($groupSettings)) {
            return response()->json([
                'success' => false,
                'message' => 'Group settings not found.'
            ])->header("Access-Control-Allow-Origin",  "*");
        }

        if (!Schema::hasTable('arra_device_group_billing_settings')) {
            return response()->json([
                'success' => false,
                'message' => 'Arra billing plugin is not active.'
            ])->header("Access-Control-Allow-Origin",  "*");
        }

        $arraBilling = DB::table('arra_device_group_billing_settings')->where('device_group_id', $deviceGroupID)->first();
        if (empty($arraBilling)) {
            return response()->json([
                'success' => false,
                'message' => 'Billing settings not found.'
            ])->header("Access-Control-Allow-Origin",  "*");
        }

        $monthlyType = 0;
        if ($groupSettings->business_model == 'monthly') {
            if (!empty($groupSettings->business_model_value['1_mt']['1_type']) && $groupSettings->business_model_value['1_mt']['1_type'] == 'user') {
                $monthlyType = 1;
            }
        }


        return response()->json([
            'success' => true,
            'data' => [
                'billing_url' => $arraBilling->billing_url,
                'products_type' => $groupSettings->business_model,
                'monthly_type' => $monthlyType
            ]
        ])->header("Access-Control-Allow-Origin",  "*");
    }

    public function testLog()
    {
        Log::error('Test ERROR!');
        Log::channel('arra-import-factory-device')->error("Test ERROR! arra-import-factory-device");

    }
}
