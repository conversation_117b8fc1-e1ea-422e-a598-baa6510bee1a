<?php

namespace Arra\GroupSettings\Services\RadiusDesk\ByodDevice;

use Arra\GroupSettings\Models\ArraDeviceGroupByodDevice;
use Arra\GroupSettings\Services\RadiusDesk\Integration\RadiusDeskConnector;
use Arra\GroupSettings\Services\RadiusDesk\Integration\RadiusDeskDtoRequest;
use Illuminate\Support\Facades\Schema;

class RadiusDeskAddDeviceService
{
    public function __construct(private readonly RadiusDeskConnector $radiusDeskConnector)
    {

    }

    /**
     * @throws \Throwable
     */
    public function execute($byodDeviceID): bool
    {
        $byodDevice = ArraDeviceGroupByodDevice::where('id', $byodDeviceID)->firstOrFail();

        if (!Schema::hasTable('arra_device_group_billing_settings')) {
            throw new \Exception('Failed to get group billing settings!');
        }
        $groupBillingSettings = \DB::table('arra_device_group_billing_settings')->where('device_group_id', $byodDevice->device_group_id)->first();
        if (empty($groupBillingSettings)) {
            throw new \Exception('Failed to get group billing settings!');
        }

        $macAddress = trim(str_replace(':', '-', str_replace(" ","",$byodDevice->mac_address)));
        $rDProfileName = "AllowClientsDevice-Speed Limit {$byodDevice->speed}MB Group ".$byodDevice->device_group_id;
        $RDRequest = new RadiusDeskDtoRequest(
            // endpoint: 'cake3/rd_cake/arraDevices/addDynamic.json', // OLD LINK
            endpoint: 'cake4/rd_cake/arra/devices/add.json',
            params: [
                'profile' => $rDProfileName,
                'permanent_user_id' => $groupBillingSettings->radius_desk_permanent_user_id,
                'name' => $macAddress,
                'data_cap_type' => 'hard',
                'time_cap_time' => 'hard',
                'active' => 1,
                'speed' => $byodDevice->speed,
                'always_active' => 1,
                'from_date' => date('m/d/Y'),
                'description' => $byodDevice->description,
                'parental_control' => $byodDevice->parental_control,
                'dns_primary' => $byodDevice->dns_primary,
                'dns_secondary' => $byodDevice->dns_secondary,
                'dns_profile_name' => "group_".$byodDevice->device_group_id."_dns"
            ]
        );

        $response = $this->radiusDeskConnector->execute($RDRequest);

        if (!empty($response['success'])) {
            return true;
        } else {
            return false;
        }
    }
}
