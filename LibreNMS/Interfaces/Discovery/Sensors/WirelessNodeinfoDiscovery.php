<?php
/**
 * WirelessNodeInfoDiscovery.php
 *
 * Discover NodeInfo Sensors as boolean
 */
namespace LibreNMS\Interfaces\Discovery\Sensors;

interface WirelessNodeinfoDiscovery
{
    /**
     * Discover wireless NodeInfo.  This is boolean. Type is NodeInfo.
     * Returns an array of LibreNMS\Device\Sensor objects that have been discovered
     *
     * @return array Sensors
     */
    public function discoverWirelessNodeinfo();
}
