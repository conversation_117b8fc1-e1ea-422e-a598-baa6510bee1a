mib: ZYXEL-IES5000-MIB
modules:
    mempools:
        data:
            -
                oid: ZYXEL-IES5000-MIB::memoryUsageTable
                percent_used: ZYXEL-IES5000-MIB::memoryCurValue
                descr: ZYXEL-IES5000-MIB::memoryDescr
                warn_percent: ZYXEL-IES5000-MIB::memoryHighThresh
    os:
        serial: .*******.4.1.890.********.6.2.1.3.0
        version:
            - .*******.4.1.890.********.6.3.1.4.0.1
            - .*******.4.1.890.********.6.3.1.4.0.6

    processors:
        data:
            -
                oid: cpuUtilizationTable
                value: cpuCurValue
                num_oid: '.*******.4.1.890.********.11.3.4.1.2.{{ $index }}'
                snmp_flags: ['-OeQUsb', '-Pu'] # workaround for underscores in mib
                descr: '{{ $index }} {{ $cpuDescr }}'
                #high_limit: cpuHighThresh
    sensors:
        fanspeed:
            data:
                -
                    oid: fanRpmTable
                    value: fanRpmCurValue
                    num_oid: '.*******.4.1.890.********.11.3.1.1.2.{{ $index }}'
                    snmp_flags: ['-OeQUs', '-Pu'] # workaround for underscores in mib
                    descr: '{{ $fanRpmDescr }} Speed'
                    low_limit: fanRpmLowThresh
                    high_limit: fanRpmHighThresh
        voltage:
            data:
                -
                    oid: voltageTable
                    value: voltageCurValue
                    num_oid: '.*******.4.1.890.********.11.3.2.1.2.{{ $index }}'
                    snmp_flags: ['-OeQUs', '-Pu'] # workaround for underscores in mib
                    descr: '{{ $voltageDescr }} {{ $voltageNominalValue }}mA'
                    divisor: 1000
                    low_limit: voltageLowThresh
                    high_limit: voltageHighThresh
        temperature:
            data:
                -
                    oid: temperatureTable
                    value: temperatureCurValue
                    num_oid: '.*******.4.1.890.********.11.3.3.1.2.{{ $index }}'
                    snmp_flags: ['-OeQUs', '-Pu'] # workaround for underscores in mib
                    descr: '{{ $temperatureDescr }}'
                    low_limit: temperatureLowThresh
                    high_limit: temperatureHighThresh
        state:
            data:
                -
                    oid: chassisTable
                    value: chassisStatus
                    num_oid: '.*******.4.1.890.********.6.2.1.5.{{ $index }}'
                    snmp_flags: ['-LE 3', '-OeQUs', '-Pu'] # workaround for underscores in mib
                    descr: 'Chassis {{ $chassisNumber }} Status'
                    state_name: chassisStatus
                    states:
                        - { descr: empty, graph: 0, value: 1, generic: 3 }
                        - { descr: up, graph: 0, value: 2, generic: 0 }
                        - { descr: down, graph: 0, value: 3, generic: 2 }
                        - { descr: testing, graph: 0, value: 4, generic: 1 }
                -
                    oid: slotTable
                    value: slotModuleStatus
                    num_oid: '.*******.4.1.890.********.*******.{{ $index }}'
                    snmp_flags: ['-LE 3', '-OeQUs', '-Pu'] # workaround for underscores in mib
                    descr: '{{ $index }} {{ $slotModuleDescr }} {{ $slotModuleFWVersion }} Status'
                    state_name: slotModuleStatus
                    skip_values: 1
                    states:
                        - { descr: empty, graph: 0, value: 1, generic: 3 }
                        - { descr: up, graph: 0, value: 2, generic: 0 }
                        - { descr: down, graph: 0, value: 3, generic: 2 }
                        - { descr: testing, graph: 0, value: 4, generic: 1 }
                        - { descr: standby, graph: 0, value: 5, generic: 3 }
                -
                    oid: systemStatus
                    value: systemStatus
                    num_oid: '.*******.4.1.890.********.11.1.1.{{ $index }}'
                    snmp_flags: ['-LE 3','-OeQUs', '-Pu'] # workaround for underscores in mib
                    descr: 'System Status'
                    state_name: systemStatus
                    states:
                        - { value: 1, generic: 0, graph: 0, descr: moduleNoDefect }
                        - { value: 2, generic: 2, graph: 0, descr: moduleOverHeat }
                        - { value: 4, generic: 2, graph: 0, descr: moduleFanRpmLow }
                        - { value: 8, generic: 2, graph: 0, descr: moduleVoltageLow }
                        - { value: 16, generic: 2, graph: 0, descr: moduleThermalSensorFailure }
                        - { value: 32, generic: 2, graph: 0, descr: modulePullOut }
                        - { value: 64, generic: 2, graph: 0, descr: powerDC48VAFailure }
                        - { value: 128, generic: 2, graph: 0, descr: powerDC48VBFailure }
                        - { value: 256, generic: 2, graph: 0, descr: extAlarmInputTrigger }
                        - { value: 512, generic: 2, graph: 0, descr: moduleDown }
                        - { value: 1024, generic: 2, graph: 0, descr: mscSwitchOverOK }
                        - { value: 2048, generic: 2, graph: 0, descr: networkTopologyChange }
                        - { value: 4096, generic: 2, graph: 0, descr: macSpoof }
                        - { value: 8192, generic: 2, graph: 0, descr: cpuHigh }
                        - { value: 16384, generic: 2, graph: 0, descr: memoryUsageHigh }
#                        - { value: 32768, generic: 2, graph: 0, descr: packetBufferUsageHigh }  # state value larger than smallint 32767
                -
                    oid: ledAlarmStatus
                    value: ledAlarmStatus
                    num_oid: '.*******.4.1.890.********.11.1.2.1.{{ $index }}'
                    snmp_flags: ['-LE 3','-OeQUs', '-Pu'] # workaround for underscores in mib
                    descr: 'Alarm LED Status'
                    state_name: alarmLedStatus
                    states:
                        - { value: 1, generic: 2, graph: 0, descr: on }
                        - { value: 2, generic: 0, graph: 0, descr: off }
