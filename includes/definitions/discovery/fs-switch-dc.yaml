mib: FS-ENTITY-MIB:FS-SYSTEM-MIB:FS-MEMORY-MIB:FS-PROCESS-MIB:FS-FIBER-MIB
modules:
    os:
        hardware: FS-ENTITY-MIB::fsDeviceInfoDescr.1
        serial: FS-ENTITY-MIB::fsDeviceSerialNumber.1
        version: FS-ENTITY-MIB::fsDeviceSWVersion.1
    mempools:
        data:
            -
                oid: FS-MEMORY-MIB::fsNodeMemoryPoolTable
                total: FS-MEMORY-MIB::fsNodeMemoryPoolSize
                used: FS-MEMORY-MIB::fsNodeMemoryPoolUsed
                warn_percent: FS-MEMORY-MIB::fsNodeMemoryPoolWarning
                precision: 1024
                descr: "{{ $FS-MEMORY-MIB::fsNodeMemoryPoolName }}"
    processors:
        data:
            -
                oid: fsNodeCPUTotalTable
                value: fsNodeCPUTotal5min
                num_oid: '.*******.4.1.52642.********.36.1.2.1.5.{{ $index }}'
                descr: '{{ $fsNodeCPUTotalName }}'
                warn_percent: fsNodeCPUTotalCritical
    sensors:
        dbm:
            data:
                -
                    oid: fsFiberTable
                    value: fsFiberRXpower
                    num_oid: '.*******.4.1.52642.********.*********.76.{{ $index }}'
                    descr: "{{ $fsFiberPortDescr }} RX"
                    divisor: 100
                    low_warn_limit: fsFiberRXpowerLowWarnThreshold
                    warn_limit: fsFiberRXpowerHighWarnThreshold
                    low_limit: fsFiberRXpowerLowAlarmThreshold
                    high_limit: fsFiberRXpowerHighAlarmThreshold
                    index: 'rx-{{ $index }}'
                    skip_values:
                        -
                            oid: fsFiberDDMSupportStatus
                            op: '='
                            value: 2
                        -
                            oid: fsFiberRXpower
                            op: '='
                            value: -10000
                -
                    oid: fsFiberTable
                    value: fsFiberTXpower
                    num_oid: '.*******.4.1.52642.********.*********.81.{{ $index }}'
                    descr: "{{ $fsFiberPortDescr }} TX"
                    divisor: 100
                    low_warn_limit: fsFiberTXpowerLowWarnThreshold
                    warn_limit: fsFiberTXpowerHighWarnThreshold
                    low_limit: fsFiberTXpowerLowAlarmThreshold
                    high_limit: fsFiberTXpowerHighAlarmThreshold
                    index: 'tx-{{ $index }}'
                    skip_values:
                        -
                            oid: fsFiberDDMSupportStatus
                            op: '='
                            value: 2
                        -
                            oid: fsFiberTXpower
                            op: '='
                            value: -10000
        fanspeed:
            data:
                -
                    oid: fsSystemFanStatusTable
                    value: fsSystemFanStatusSpeed
                    num_oid: '.*******.4.1.52642.********.********.6.{{ $index }}'
                    descr: 'Fan {{ $subindex0 }}/{{ $subindex1 }}'
        temperature:
            data:
                -
                    oid: fsSystemMultipleTemperatureTable
                    value: fsSystemMultipleTemperatureCurrent
                    num_oid: '.*******.4.1.52642.********.********.5.{{ $index }}'
                    warn_limit: fsSystemMultipleTemperatureWarning
                    high_limit: fsSystemMultipleTemperatureCritical
                    descr: "{{ $fsSystemMultipleTemperatureName }} {{ $subindex0 }}/{{ $subindex1 }}"
                -
                    oid: fsFiberTable
                    value: fsFiberTemp
                    num_oid: '.*******.4.1.52642.********.*********.17.{{ $index }}'
                    descr: "{{ $fsFiberPortDescr }}"
                    index: 'temp-{{ $index }}'
                    group: Transceivers
                    skip_values:
                        -
                            oid: fsFiberDDMSupportStatus
                            op: '='
                            value: 2
                        -
                            oid: fsFiberTemp
                            op: '='
                            value: 0
        voltage:
            data:
                -
                    oid: fsSystemElectricalInformationTable
                    value: fsSystemElectricalInformationInVoltage
                    num_oid: '.*******.4.1.52642.********.********.11.{{ $index }}'
                    descr: 'Power supply {{ $subindex0 }}/{{ $subindex1 }}'
        power:
            data:
                -
                    oid: fsSystemElectricalInformationTable
                    value: fsSystemElectricalInformationOutPower
                    num_oid: '.*******.4.1.52642.********.********.15.{{ $index }}'
                    descr: 'Power supply {{ $subindex0 }}/{{ $subindex1 }}'
        state:
            data:
                -
                    oid: fsSystemFanStatusTable
                    value: fsSystemFanStatus
                    num_oid: '.*******.4.1.52642.********.********.4.{{ $index }}'
                    descr: 'Fan {{ $subindex0 }}/{{ $subindex1 }}'
                    state_name: fsSystemFanStatus
                    index: 'fan-{{ $index }}'
                    group: Fans
                    states:
                        - { value: 1, generic: 3, graph: 0, descr: 'noexist' },
                        - { value: 2, generic: 2, graph: 0, descr: 'existnopower' },
                        - { value: 3, generic: 1, graph: 0, descr: 'existreadypower' },
                        - { value: 4, generic: 0, graph: 0, descr: 'normal' },
                        - { value: 5, generic: 2, graph: 0, descr: 'powerbutabnormal' },
                        - { value: 6, generic: 3, graph: 0, descr: 'unknown' },
                -
                    oid: fsFiberTable
                    value: fsFiberTempStatus
                    num_oid: '.*******.4.1.52642.********.*********.18.{{ $index }}'
                    descr: '{{ $fsFiberPortDescr }} Temperature'
                    state_name: fsFiberTempStatus
                    index: 'tempstat-{{ $index }}'
                    group: Transceivers
                    skip_values:
                        - 1
                    states:
                        - { value: 2, generic: 0, graph: 0, descr: 'ok' },
                        - { value: 3, generic: 1, graph: 0, descr: 'warning' },
                        - { value: 4, generic: 2, graph: 0, descr: 'alarm' },
                -
                    oid: fsSystemElectricalInformationTable
                    value: fsSystemElectricalInformationStatus
                    num_oid: '.*******.4.1.52642.********.********.3.{{ $index }}'
                    descr: 'Power supply {{ $subindex0 }}/{{ $subindex1 }}'
                    state_name: fsSystemElectricalInformationStatus
                    index: 'psu-{{ $index }}'
                    group: 'Power Supplies'
                    states:
                        - { value: 1, generic: 3, graph: 0, descr: 'noexist' },
                        - { value: 2, generic: 2, graph: 0, descr: 'existnopower' },
                        - { value: 3, generic: 1, graph: 0, descr: 'existreadypower' },
                        - { value: 4, generic: 0, graph: 0, descr: 'normal' },
                        - { value: 5, generic: 2, graph: 0, descr: 'powerbutabnormal' },
                        - { value: 6, generic: 3, graph: 0, descr: 'unknown' },
