mib: RAISECOM-COMMON-MANAGEMENT-MIB:RAISECOM-FANMONITOR-MIB:RAISECOM-POWERMONITOR-MIB:RAISECOM-SYSTEM-MIB
modules:
    mempools:
        data:
            -
                total: RAISECOM-SYSTEM-MIB::raisecomMemoryTotal
                free: RAISECOM-SYSTEM-MIB::raisecomMemoryAvailable
    os:
        sysDescr_regex: '/^ROAP  Version (?<version>[^,]+)/'
        hardware: .1.3.6.1.4.1.8886.6.1.1.1.2.0
        serial: .1.3.6.1.4.1.8886.6.1.1.1.14.0
        version: .1.3.6.1.4.1.8886.6.1.1.1.1.0
    processors:
        data:
            -
                oid: raisecomTotalCPUUtilization.oneMin
                value: raisecomTotalCPUUtilization
                num_oid: '.1.3.6.1.4.1.8886.1.1.1.3.1.2.1.2.{{ $index }}'
                snmp_flags: ['-OeQUsb', '-Pu'] # workaround for underscores in mib
                descr: 'Processor'
    sensors:
        fanspeed:
            data:
                -
                    oid: raisecomFanMonitorStateTable
                    value: raisecomFanSpeedValue
                    num_oid: '.1.3.6.1.4.1.8886.1.1.5.2.2.1.2.{{ $index }}'
                    snmp_flags: ['-OeQUs', '-Pu'] # workaround for underscores in mib
                    descr: 'Fan {{ $index }} Speed'
                    low_limit: 0
                    high_limit: 10000
        voltage:
            data:
                -
                    oid: raisecomVoltTable
                    value: raisecomVoltValue
                    num_oid: '.1.3.6.1.4.1.8886.1.1.4.3.1.1.3.{{ $index }}'
                    snmp_flags: ['-OeQUs', '-Pu'] # workaround for underscores in mib
                    descr: 'Voltage {{ $index }} ({{ $raisecomVoltReference }}mV)'
                    divisor: 1000
                    low_limit: raisecomVoltThresholdLow
                    high_limit: raisecomVoltThresholdHigh
        state:
            data:
                -
                    oid: raisecomConfigLoadState
                    value: raisecomConfigLoadState
                    num_oid: '.1.3.6.1.4.1.8886.1.2.1.3.{{ $index }}'
                    snmp_flags: ['-OeQUs', '-Pu'] # workaround for underscores in mib
                    descr: 'Config {{ $index }} Load  State'
                    states:
                        - { descr: ready, graph: 0, value: 1, generic: 1 }
                        - { descr: running, graph: 0, value: 2, generic: 1 }
                        - { descr: successful, graph: 0, value: 3, generic: 0 }
                        - { descr: failed, graph: 0, value: 4, generic: 2 }
                -
                    oid: raisecomFanMonitorStateTable
                    value: raisecomFanWorkState
                    num_oid: '.1.3.6.1.4.1.8886.1.1.5.2.2.1.3.{{ $index }}'
                    snmp_flags: ['-OeQUs', '-Pu'] # workaround for underscores in mib
                    descr: 'Fan {{ $index }} State'
                    states:
                        - { descr: normal, graph: 0, value: 1, generic: 0 }
                        - { descr: abnormal, graph: 0, value: 2, generic: 2 }
                -
                    oid: raisecomPowerMonitorStateTable
                    value: raisecomPowerStatus
                    snmp_flags: ['-OeQUs', '-Pu'] # workaround for underscores in mib
                    num_oid: '.1.3.6.1.4.1.8886.1.24.2.1.1.6.{{ $index }}'
                    descr: 'Power Supply {{ $index }} State'
                    states:
                        - { descr: offline, graph: 0, value: 1, generic: 2 }
                        - { descr: online, graph: 0, value: 2, generic: 0 }
                        - { descr: power-on, graph: 0, value: 3, generic: 0 }
