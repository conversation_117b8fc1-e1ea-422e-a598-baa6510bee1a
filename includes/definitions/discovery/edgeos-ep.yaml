mib: UBNT-EdgeMAX-MIB
modules:
    os:
        hardware: UBNT-EdgeMAX-MIB::ubntModel.0
        version: UBNT-EdgeMAX-MIB::ubntVersion.0
    processors:
        data:
            -
                oid: ubntHostCpuLoad
                num_oid: '.*******.4.1.41112.*******.{{ $index }}'
                precision: 100
    sensors:
        count:
            data:
                -
                    oid: ubntPsuBaysNumber
                    num_oid: '.*******.4.1.41112.*******.{{ $index }}'
                    descr: Number of PSU's
                -
                    oid: ubntPsuBatteryQuantity
                    num_oid: '.*******.4.1.41112.*******.1.8.{{ $index }}'
                    descr: 'Number of batteries PSU {{ $index }}'
        voltage:
            data:
                -
                    oid: ubntPowerOutVoltage
                    divisor: 1000
                    num_oid: '.*******.4.1.41112.*******.1.2.{{ $index }}'
                    descr: Output Voltage
                -
                    oid: ubntPsuVoltage
                    divisor: 1000
                    num_oid: '.*******.4.1.41112.*******.1.5.{{ $index }}'
                    index: 'ubntPsuVoltage.{{ $index }}'
                    descr: 'Input Voltage PSU {{ $index }}'
        charge:
            data:
                -
                    oid: ubntPsuBatteryChargeLevel
                    num_oid: '.*******.4.1.41112.*******.1.9.{{ $index }}'
                    index: 'ubntPsuBatteryChargeLevel.{{ $index }}'
                    descr: 'Charge percentage PSU {{ $index }}'
        runtime:
            data:
                -
                    oid: ubntPsuBatteryTimeRemaining
                    divisor: 6000
                    num_oid: '.*******.4.1.41112.*******.1.10.{{ $index }}'
                    index: 'ubntPsuBatteryTimeRemaining.{{ $index }}'
                    descr: 'Battery runtime remaining PSU {{ $index }}'
        current:
            data:
                -
                    oid: ubntPowerOutCurrent
                    divisor: 1000
                    num_oid: '.*******.4.1.41112.*******.1.3.{{ $index }}'
                    descr: Output Current
        power:
            data:
                -
                    oid: ubntPowerOutPower
                    divisor: 1000
                    num_oid: '.*******.4.1.41112.*******.1.4.{{ $index }}'
                    descr: Output power
        state:
            data:
                -
                    oid: ubntPsuOperStatus
                    num_oid: '.*******.4.1.41112.*******.1.4.{{ $index }}'
                    low_limit: 0
                    descr: 'PSU {{ $index }}'
                    states:
                        - { descr: Offline, graph: 0, value: 0, generic: 2 }
                        - { descr: Online, graph: 0, value: 1, generic: 0 }
                -
                    oid: ubntPsuCharging
                    value: ubntPsuCharging
                    num_oid: '.*******.4.1.41112.*******.1.7.{{ $index }}'
                    descr: 'Charge status PSU {{ $index }}'
                    index: '{{ $index }}'
                    states:
                        - { descr: unknown, graph: 0, value: 0, generic: 1 }
                        - { descr: Charging, graph: 0, value: 1, generic: 0 }
                        - { descr: Charging Off, graph: 0, value: 2, generic: 0 }
                -
                    oid: ubntPsuStatus
                    value: ubntPsuStatus
                    num_oid: '.*******.4.1.41112.*******.1.3.{{ $index }}'
                    descr: 'PSU status {{ $index }}'
                    index: '{{ $index }}'
                    states:
                        - { descr: unknown, graph: 0, value: 0, generic: 1 }
                        - { descr: On, graph: 0, value: 1, generic: 0 }
                        - { descr: Off, graph: 0, value: 2, generic: 2 }
                        - { descr: Standby, graph: 0, value: 3, generic: 0 }
                -
                    oid: ubntPsuType
                    value: ubntPsuType
                    num_oid: '.*******.4.1.41112.*******.1.2.{{ $index }}'
                    descr: 'PSU type {{ $index }}'
                    index: '{{ $index }}'
                    states:
                        - { descr: unknown, graph: 0, value: 0, generic: 1 }
                        - { descr: AC, graph: 0, value: 1, generic: 0 }
                        - { descr: DC, graph: 0, value: 2, generic: 0 }
                        - { descr: POE, graph: 0, value: 3, generic: 0 }
                -
                    oid: ubntPsuBatteryReplaceIndicator
                    value: ubntPsuBatteryReplaceIndicator
                    num_oid: '.*******.4.1.41112.*******.1.11.{{ $index }}'
                    descr: 'Battery indicator PSU {{ $index }}'
                    index: '{{ $index }}'
                    states:
                        - { descr: unknown, graph: 0, value: 0, generic: 1 }
                        - { descr: OK, graph: 0, value: 1, generic: 0 }
                        - { descr: REPLACE, graph: 0, value: 2, generic: 1 }
        temperature:
            data:
                -
                    oid: ubntThermTemperature
                    divisor: 1000
                    num_oid: '.*******.4.1.41112.1.5.4.2.1.3.{{ $index }}'
                    index: 'ubntThermTemperature.{{ $index }}'
                    descr: 'Temperatures {{ $index }}'
                    low_limit: 0
                    low_warn_limit: 5
                    warn_limit: 35
                    high_limit: 40
