mib: HUAWEI-WLAN-CONFIGURATION-MIB:HUAWEI-WAN-MIB:HUAWEI-ENTITY-EXTENT-MIB:HUAWEI-ENERGYMNGT-MIB:HUAWEI-STACK-MIB:HUAWEI-POE-MIB
modules:
    os:
        sysDescr_regex: '/\((?<hardware>[^)]+) (?<version>V[0-9]{3}R[0-9]{3}[0-9A-Z]+)/'
        hardware:
            - HUAWEI-ENTITY-EXTENT-MIB::hwEntityExtentMIB.6.5.0
            - HUAWEI-DEVICE-EXT-MIB::hwProductName.0
            - HUAWEI-MIB::hwDatacomm.**********.5.1
            - HUAWEI-MIB::mlsr.********.3.0
            - HUAWEI-ENTITY-EXTENT-MIB::hwEntityBoardName.9
        serial:
            - HUAWEI-DEVICE-EXT-MIB::hwDeviceEsn.0
            - ENTITY-MIB::entPhysicalSerialNum.9
    processors:
        data:
            -
                oid: hwEntityStateEntry
                value: hwEntityCpuUsage
                num_oid: '.*******.4.1.2011.*********.*******.{{ $index }}'
                descr: '{{ $entPhysicalName }} Processor'
                skip_values:
                    -
                        oid: hwEntityOperStatus
                        op: '!='
                        value: 3
                    -
                        oid: hwEntityCpuUsageThreshold
                        op: '='
                        value: 0
    sensors:
        pre-cache:
            data:
                -
                    oid:
                        - entPhysicalName
                        - hwEntityStateTable
                        - hwEntityAdminStatus
                        - hwPwrStatusTable
                        - hwStackPortTable
                        - hw3GTable
        temperature:
            data:
                -
                    oid: hwOpticalModuleInfoTable
                    value: hwEntityOpticalTemperature
                    num_oid: '.*******.4.1.2011.*********.*******.{{ $index }}'
                    descr: '{{ $entPhysicalName }}'
                    index: '{{ $index }}'
                    skip_values:
                        -
                            oid: hwEntityOpticalMode
                            op: '='
                            value: '1'
                        -
                            oid: hwEntityOpticalTemperature
                            op: '<'
                            value: '-50'
                        -
                            oid: hwEntityOpticalTemperature
                            op: '>='
                            value: '2147483646'
                -
                    oid: hwEntityStateTable
                    value: hwEntityTemperature
                    num_oid: '.*******.4.1.2011.*********.********.{{ $index }}'
                    descr: '{{ $entPhysicalName }}'
                    index: '{{ $index }}'
                    skip_values:
                        -
                            oid: hwEntityTemperature
                            op: '='
                            value: '0'
                        -
                            oid: hwEntityTemperature
                            op: '>='
                            value: '2147483646'
                -
                    oid: hwTemperatureThresholdTable
                    value: hwEntityTempValue
                    index: '{{ $index }}'
                    num_oid: '.*******.4.1.2011.*********.*******.{{ $index }}'
                    descr: 'TempSensor {{ $subindex0 }}.{{ $subindex1 }}.{{ $subindex2 }}.{{ $subindex3 }}'
                    group: 'Slot-{{ $subindex0 }}'
                    warn_limit: hwEntityTempMinorAlmThreshold
                    high_limit: hwEntityTempMajorAlmThreshold
                    divisor: 1
        count:
            data:
                -
                    oid: hw3GTable
                    value: hw3GBER
                    num_oid: '.*******.4.1.2011.**********.********.{{ $index }}'
                    descr: 'Bit Error Rate'
                    index: '3GBER-{{ $index }}'
                    group: 'Modem {{ $hw3GDeviceNumber }}, {{ $hw3GIMEI }}'
                    skip_values:
                        -
                            oid: hw3GDeviceNumber
                            op: '='
                            value: ''
                        -
                            oid: hw3GIMEI
                            op: '='
                            value: ''
                -
                    oid: hw3GTable
                    value: hw3GUpBand
                    num_oid: '.*******.4.1.2011.**********.********.{{ $index }}'
                    descr: 'Upstream Bandwidth'
                    index: '3gup-{{ $index }}'
                    group: 'Modem {{ $hw3GDeviceNumber }}, {{ $hw3GIMEI }}'
                    skip_values:
                        -
                            oid: hw3GDeviceNumber
                            op: '='
                            value: ''
                        -
                            oid: hw3GIMEI
                            op: '='
                            value: ''
                -
                    oid: hw3GTable
                    value: hw3GDownBand
                    num_oid: '.*******.4.1.2011.**********.********.{{ $index }}'
                    descr: 'Downstream Bandwidth'
                    index: '3gdown-{{ $index }}'
                    group: 'Modem {{ $hw3GDeviceNumber }}, {{ $hw3GIMEI }}'
                    skip_values:
                        -
                            oid: hw3GDeviceNumber
                            op: '='
                            value: ''
                        -
                            oid: hw3GIMEI
                            op: '='
                            value: ''
                -
                    oid: hw3GTable
                    value: hw3GBytesRateSent
                    num_oid: '.*******.4.1.2011.**********.********.{{ $index }}'
                    descr: 'Upstream Cur. Speed (bytes/s)'
                    index: '3gupr-{{ $index }}'
                    group: 'Modem {{ $hw3GDeviceNumber }}, {{ $hw3GIMEI }}'
                    skip_values:
                        -
                            oid: hw3GDeviceNumber
                            op: '='
                            value: ''
                        -
                            oid: hw3GIMEI
                            op: '='
                            value: ''
                -
                    oid: hw3GTable
                    value: hw3GBytesRateReceived
                    num_oid: '.*******.4.1.2011.**********.********.{{ $index }}'
                    descr: 'Downstream Cur. Speed (bytes/s)'
                    index: '3gdownr-{{ $index }}'
                    group: 'Modem {{ $hw3GDeviceNumber }}, {{ $hw3GIMEI }}'
                    skip_values:
                        -
                            oid: hw3GDeviceNumber
                            op: '='
                            value: ''
                        -
                            oid: hw3GIMEI
                            op: '='
                            value: ''
        runtime:
            data:
                -
                    oid: hw3GTable
                    value: hw3GUpTime
                    num_oid: '.*******.4.1.2011.**********.********.{{ $index }}'
                    descr: 'Uptime'
                    divisor: 6000
                    index: '3guptime-{{ $index }}'
                    group: 'Modem {{ $hw3GDeviceNumber }}, {{ $hw3GIMEI }}'
                    skip_values:
                        -
                            oid: hw3GDeviceNumber
                            op: '='
                            value: ''
                        -
                            oid: hw3GIMEI
                            op: '='
                            value: ''
        dbm:
            data:
                -
                    oid: hw3GTable
                    value: hw3GCurrentRSSI
                    num_oid: '.*******.4.1.2011.**********.********.{{ $index }}'
                    descr: 'RSSI'
                    index: 'rssi-{{ $index }}'
                    group: 'Modem {{ $hw3GDeviceNumber }}, {{ $hw3GIMEI }}'
                    skip_values:
                        -
                            oid: hw3GDeviceNumber
                            op: '='
                            value: ''
                        -
                            oid: hw3GIMEI
                            op: '='
                            value: ''
                -
                    oid: hw3GTable
                    value: hw3GCurrentLteRSRP
                    num_oid: '.*******.4.1.2011.**********.********.{{ $index }}'
                    descr: 'LTE RSRP'
                    index: 'rsrp-{{ $index }}'
                    group: 'Modem {{ $hw3GDeviceNumber }}, {{ $hw3GIMEI }}'
                    skip_values:
                        -
                            oid: hw3GDeviceNumber
                            op: '='
                            value: ''
                        -
                            oid: hw3GIMEI
                            op: '='
                            value: ''
                -
                    oid: hw3GTable
                    value: hw3GCurrentLteRSRQ
                    num_oid: '.*******.4.1.2011.**********.********.{{ $index }}'
                    descr: 'LTE RSRQ'
                    index: 'rsrq-{{ $index }}'
                    group: 'Modem {{ $hw3GDeviceNumber }}, {{ $hw3GIMEI }}'
                    skip_values:
                        -
                            oid: hw3GDeviceNumber
                            op: '='
                            value: ''
                        -
                            oid: hw3GIMEI
                            op: '='
                            value: ''
                -
                    oid: hwOpticalModuleInfoTable
                    value: hwEntityOpticalRxPower
                    num_oid: '.*******.4.1.2011.*********.*******.{{ $index }}'
                    descr: '{{ $entPhysicalName }} Rx'
                    entPhysicalIndex: '{{ $index }}'
                    entPhysicalIndex_measured: ports
                    index: 'rx-{{ $index }}'
                    user_func: 'uw_to_dbm'
                    group: '{{ $entPhysicalName }}'
                    low_limit: hwEntityOpticalRxLowWarnThreshold
                    high_limit: hwEntityOpticalRxHighWarnThreshold
                    skip_values:
                        -
                            oid: hwEntityOpticalRxPower
                            op: '<='
                            value: 0
                        -
                            oid: hwEntityAdminStatus
                            op: '='
                            value: 12
                -
                    oid: hwOpticalModuleInfoTable
                    value: hwEntityOpticalRxPower
                    num_oid: '.*******.4.1.2011.*********.*******.{{ $index }}'
                    descr: '{{ $entPhysicalName }} Rx'
                    entPhysicalIndex: '{{ $index }}'
                    entPhysicalIndex_measured: ports
                    index: 'rx-{{ $index }}'
                    divisor: 100
                    group: '{{ $entPhysicalName }}'
                    skip_values:
                        -
                            oid: hwEntityOpticalRxPower
                            op: '>'
                            value: 0
                        -
                            oid: hwEntityAdminStatus
                            op: '='
                            value: 12
                        -
                            oid: hwEntityOpticalLaneRxPower
                            op: 'exists'
                            value: true
                        -
                            oid: hwEntityOpticalBiasCurrent
                            op: '='
                            value: 0
                -
                    oid: hwOpticalModuleInfoTable
                    value: hwEntityOpticalTxPower
                    num_oid: '.*******.4.1.2011.*********.*******.{{ $index }}'
                    descr: '{{ $entPhysicalName }} Tx'
                    entPhysicalIndex: '{{ $index }}'
                    entPhysicalIndex_measured: ports
                    index: 'tx-{{ $index }}'
                    user_func: 'uw_to_dbm'
                    group: '{{ $entPhysicalName }}'
                    skip_values:
                        -
                            oid: hwEntityOpticalTxPower
                            op: '<='
                            value: 0
                        -
                            oid: hwEntityAdminStatus
                            op: '='
                            value: 12
                -
                    oid: hwOpticalModuleInfoTable
                    value: hwEntityOpticalTxPower
                    num_oid: '.*******.4.1.2011.*********.*******.{{ $index }}'
                    descr: '{{ $entPhysicalName }} Tx'
                    entPhysicalIndex: '{{ $index }}'
                    entPhysicalIndex_measured: ports
                    index: 'tx-{{ $index }}'
                    divisor: 100
                    group: '{{ $entPhysicalName }}'
                    skip_values:
                        -
                            oid: hwEntityOpticalTxPower
                            op: '>'
                            value: 0
                        -
                            oid: hwEntityAdminStatus
                            op: '='
                            value: 12
                        -
                            oid: hwEntityOpticalLaneTxPower
                            op: 'exists'
                            value: true
                        -
                            oid: hwEntityOpticalBiasCurrent
                            op: '='
                            value: 0
                -
                    oid: hwOpticalModuleInfoTable
                    value: hwEntityOpticalLaneRxPower
                    num_oid: '.*******.4.1.2011.*********.********.{{ $index }}'
                    descr: '{{ $entPhysicalName }} Rx'
                    entPhysicalIndex: '{{ $index }}'
                    entPhysicalIndex_measured: ports
                    index: 'lane-rx-{{ $index }}'
                    divisor: 100
                    group: '{{ $entPhysicalName }}'
                    high_limit: hwEntityOpticalRxHighThreshold
                    low_limit: hwEntityOpticalRxLowThreshold
                    skip_values:
                        -
                            oid: hwEntityOpticalRxPower
                            op: '>'
                            value: 0
                        -
                            oid: hwEntityAdminStatus
                            op: '='
                            value: 12
                -
                    oid: hwOpticalModuleInfoTable
                    value: hwEntityOpticalLaneTxPower
                    num_oid: '.*******.4.1.2011.*********.********.{{ $index }}'
                    descr: '{{ $entPhysicalName }} Tx'
                    entPhysicalIndex: '{{ $index }}'
                    entPhysicalIndex_measured: ports
                    index: 'lane-tx-{{ $index }}'
                    group: '{{ $entPhysicalName }}'
                    high_limit: hwEntityOpticalTxHighThreshold
                    low_limit: hwEntityOpticalTxLowThreshold
                    skip_values:
                        -
                            oid: hwEntityOpticalTxPower
                            op: '>'
                            value: 0
                        -
                            oid: hwEntityAdminStatus
                            op: '='
                            value: 12
                    divisor: 100
        voltage:
            data:
                -
                    oid: hwOpticalModuleInfoTable
                    value: hwEntityOpticalVoltage
                    num_oid: '.*******.4.1.2011.*********.*******.{{ $index }}'
                    descr: '{{ $entPhysicalName }}'
                    index: '{{ $index }}'
                    divisor: 1000
                    skip_values:
                        -
                            oid: hwEntityOpticalVoltage
                            op: '='
                            value: '-1'
                        -
                            oid: hwEntityOpticalMode
                            op: '='
                            value: '1'
                -
                    oid: hwEntityStateTable
                    value: hwEntityVoltage
                    num_oid: '.*******.4.1.2011.*********.********.{{ $index }}'
                    descr: '{{ $entPhysicalName }}'
                    index: '{{ $index }}'
                    divisor: 1000
                    skip_values:
                        -
                            oid: hwEntityVoltage
                            op: '='
                            value: '0'
                        -
                            oid: hwEntityVoltage
                            op: '>='
                            value: '2147483646'
                -
                    oid: hwPwrStatusTable
                    value: hwEntityPwrVoltage
                    num_oid: '.*******.4.1.2011.*********.********.{{ $index }}'
                    descr: 'PWR{{ $hwEntityPwrSlot }}'
                    index: '{{ $index }}'
                    divisor: 1000
                -
                    oid: hwVoltageInfoTable
                    value: hwEntityVolCurValue
                    index: '{{ $index }}'
                    num_oid: '.*******.4.1.2011.*********.*******.{{ $index }}'
                    descr: 'VoltSensor {{ $subindex0 }}.{{ $subindex1 }}.{{ $subindex2 }}.{{ $subindex3 }}'
                    group: 'Slot-{{ $subindex0 }}'
                    low_limit: hwEntityVolLowAlmFatal
                    low_warn_limit: hwEntityVolLowAlmMajor
                    warn_limit: hwEntityVolHighAlmMajor
                    high_limit: hwEntityVolHighAlmFatal
                    divisor: 1000
        current:
            data:
                -
                    oid: hwOpticalModuleInfoTable
                    value: hwEntityOpticalBiasCurrent
                    num_oid: '.*******.4.1.2011.*********.*******.{{ $index }}'
                    descr: '{{ $entPhysicalName }}'
                    index: '{{ $index }}'
                    divisor: 1000000
                    skip_values:
                        -
                            oid: hwEntityOpticalBiasCurrent
                            op: '='
                            value: '-1'
                        -
                            oid: hwEntityOpticalMode
                            op: '='
                            value: '1'
                        -
                            oid: hwEntityOpticalBiasCurrent
                            op: '<'
                            value: '1000'
                -
                    oid: hwOpticalModuleInfoTable
                    value: hwEntityOpticalBiasCurrent
                    num_oid: '.*******.4.1.2011.*********.*******.{{ $index }}'
                    descr: '{{ $entPhysicalName }}'
                    index: '{{ $index }}'
                    divisor: 1000
                    skip_values:
                        -
                            oid: hwEntityOpticalBiasCurrent
                            op: '='
                            value: '-1'
                        -
                            oid: hwEntityOpticalMode
                            op: '='
                            value: '1'
                        -
                            oid: hwEntityOpticalBiasCurrent
                            op: '>='
                            value: '1000'
                -
                    oid: hwPwrStatusTable
                    value: hwEntityPwrCurrent
                    num_oid: '.*******.4.1.2011.*********.********.{{ $index }}'
                    descr: 'PWR{{ $hwEntityPwrSlot }}'
                    index: '{{ $index }}'
                    divisor: 1000
        power:
            data:
                -
                    oid: hwAveragePower
                    num_oid: '.*******.4.1.2011.*********.{{ $index }}'
                    descr: 'Average Power Consumption'
                    index: 0
                    divisor: 1000
                -
                    oid: hwCurrentPower
                    num_oid: '.*******.4.1.2011.*********.{{ $index }}'
                    descr: 'Current Power Consumption'
                    index: 1
                    divisor: 1000
                -
                    oid: hwBoardPowerMngtTable
                    value: hwBoardCurrentPower
                    num_oid: '.*******.4.1.2011.*********.1.4.{{ $index }}'
                    descr: '{{ $entPhysicalName }}'
                    index: '{{ $index }}'
                    divisor: 1000
                    skip_values:
                        -
                            oid: hwBoardCurrentPower
                            op: '='
                            value: '-1'
                        -
                            oid: entPhysicalName
                            op: '='
                            value: ''
                -
                    oid: hwBoardPowerMngtTable
                    value: hwBoardCurrentPower
                    num_oid: '.*******.4.1.2011.*********.1.4.{{ $index }}'
                    descr: '{{ $hwBoardType }} {{ $hwBoardName }}'
                    index: '{{ $index }}'
                    divisor: 1000
                    skip_values:
                        -
                            oid: hwBoardCurrentPower
                            op: '='
                            value: '-1'
                        -
                            oid: entPhysicalName
                            op: '!='
                            value: ''
        percent:
            data:
                -
                    oid: hwFanStatusTable
                    value: hwEntityFanSpeed
                    num_oid: '.*******.4.1.2011.*********.********.{{ $index }}'
                    descr: 'Slot {{ $subindex0 }} Fan {{ $subindex1 }} Speed'
        state:
            data:
                -
                    oid: hwWlanClusterSynConfig
                    value: hwWlanClusterSynConfigScheduleEnable
                    num_oid: '.*******.4.1.2011.**********.83.5.4.{{ $index }}'
                    descr: 'Configuration Sync'
                    group: Redundancy
                    state_name: hwWlanClusterSynConfigScheduleEnable
                    states:
                        - { descr: disable, graph: 1, value: 1, generic: 3 }
                        - { descr: enable, graph: 1, value: 2, generic: 0 }
                -
                    oid: hwWlanClusterACListInfoTable
                    value: hwWlanClusterACStatus
                    num_oid: '.*******.4.1.2011.**********.83.6.1.7.{{ $index }}'
                    descr: 'Current Redundancy Status'
                    group: Redundancy
                    state_name: hwWlanClusterACStatus
                    states:
                        - { descr: down, graph: 1, value: 1, generic: 2 }
                        - { descr: up, graph: 1, value: 2, generic: 0 }
                        - { descr: pskmismatch, graph: 1, value: 3, generic: 2 }
                        - { descr: vermismatch, graph: 1, value: 4, generic: 2 }
                        - { descr: cfgmismatch, graph: 1, value: 5, generic: 2 }
                        - { descr: devmismatch, graph: 1, value: 6, generic: 2 }
                        - { descr: initial, graph: 1, value: 7, generic: 3 }
                -
                    oid: hwWlanClusterACListInfoTable
                    value: hwWlanClusterACRole
                    num_oid: '.*******.4.1.2011.**********.83.6.1.4.{{ $index }}'
                    descr: 'Peer Redundancy Role'
                    group: Redundancy
                    state_name: hwWlanClusterACRole
                    states:
                        - { descr: Master, graph: 1, value: 1, generic: 0 }
                        - { descr: Slave, graph: 1, value: 2, generic: 0 }
                        - { descr: Backup, graph: 1, value: 3, generic: 0 }
                -
                    oid: hwFanStatusTable
                    value: hwEntityFanState
                    num_oid: '.*******.4.1.2011.*********.1.10.1.7.{{ $index }}'
                    descr: 'Slot {{ $subindex0 }} Fan {{ $subindex1 }} Status'
                    group: Fans
                    state_name: hwEntityFanState
                    states:
                        - { descr: Normal, graph: 1, value: 1, generic: 0 }
                        - { descr: Abnormal, graph: 1, value: 2, generic: 2 }
                -
                    oid: hwStackMemberInfoTable
                    value: hwMemberStackRole
                    num_oid: '.*******.4.1.2011.5.25.183.1.20.1.3.{{ $index }}'
                    descr: 'Unit {{ $index }} {{ $hwMemberStackDeviceType }} Status'
                    group: Stack
                    state_name: hwMemberStackRole
                    states:
                        - { descr: Master, graph: 1, value: 1, generic: 0 }
                        - { descr: Standby, graph: 1, value: 2, generic: 0 }
                        - { descr: Slave, graph: 1, value: 3, generic: 0 }
                -
                    oid: hw3GTable
                    value: hw3GModemStatus
                    num_oid: '.*******.4.1.2011.**********.********.{{ $index }}'
                    descr: 'Current Modem Status'
                    group: 'Modem {{ $hw3GDeviceNumber }}, {{ $hw3GIMEI }}'
                    state_name: hw3GModemStatus
                    states:
                        - { descr: onLine, graph: 1, value: 1, generic: 0 }
                        - { descr: offLine, graph: 1, value: 4, generic: 2 }
                    skip_values:
                        -
                            oid: hw3GDeviceNumber
                            op: '='
                            value: ''
                        -
                            oid: hw3GIMEI
                            op: '='
                            value: ''
                -
                    oid: hw3GTable
                    value: hw3GCurrentNetworkConnection
                    index: 'netConn-{{ $index }}'
                    num_oid: '.*******.4.1.2011.**********.********.{{ $index }}'
                    descr: 'Current Network'
                    group: 'Modem {{ $hw3GDeviceNumber }}, {{ $hw3GIMEI }}'
                    state_name: hw3GCurrentNetworkConnection
                    states:
                        - { descr: '3G', graph: 1, value: 2, generic: 0 }
                        - { descr: 'LTE', graph: 1, value: 3, generic: 0 }
                        - { descr: 'Unknown', graph: 1, value: 7, generic: 3 }
                    skip_values:
                        -
                            oid: hw3GDeviceNumber
                            op: '='
                            value: ''
                        -
                            oid: hw3GIMEI
                            op: '='
                            value: ''
                -
                    oid: hw3GTable
                    value: hw3GConnectionStatus
                    index: 'netConnStatus-{{ $index }}'
                    num_oid: '.*******.4.1.2011.**********.********.{{ $index }}'
                    descr: 'Current Network Status'
                    group: 'Modem {{ $hw3GDeviceNumber }}, {{ $hw3GIMEI }}'
                    state_name: hw3GConnectionStatus
                    states:
                        - { descr: 'noService', graph: 1, value: 0, generic: 2 }
                        - { descr: 'emergency', graph: 1, value: 1, generic: 2 }
                        - { descr: 'serviceAvailable', graph: 1, value: 2, generic: 0 }
                        - { descr: 'regionEmergency', graph: 1, value: 3, generic: 2 }
                        - { descr: 'savingStatus', graph: 1, value: 4, generic: 3 }
                    skip_values:
                        -
                            oid: hw3GDeviceNumber
                            op: '='
                            value: ''
                        -
                            oid: hw3GIMEI
                            op: '='
                            value: ''
                -
                    oid: hwVoltageInfoTable
                    value: hwEntityVolStatus
                    index: '{{ $index }}'
                    num_oid: '.*******.4.1.2011.*********.*******.{{ $index }}'
                    group: 'Slot-{{ $subindex0 }}'
                    descr: 'VoltStatus {{ $subindex0 }}.{{ $subindex1 }}.{{ $subindex2 }}.{{ $subindex3 }}'
                    states:
                        - { descr: 'Normal', graph: 1, value: 1, generic: 0 }
                        - { descr: 'Minor', graph: 1, value: 2, generic: 1 }
                        - { descr: 'Major', graph: 1, value: 3, generic: 2 }
                        - { descr: 'Fatal', graph: 1, value: 4, generic: 2 }
                -
                    oid: hwTemperatureThresholdTable
                    value: hwEntityTempStatus
                    index: '{{ $index }}'
                    num_oid: '.*******.4.1.2011.*********.1.8.1.5.{{ $index }}'
                    group: 'Slot-{{ $subindex0 }}'
                    descr: 'TempStatus {{ $subindex0 }}.{{ $subindex1 }}.{{ $subindex2 }}.{{ $subindex3 }}'
                    states:
                        - { descr: 'Normal', graph: 1, value: 1, generic: 0 }
                        - { descr: 'Minor', graph: 1, value: 2, generic: 1 }
                        - { descr: 'Major', graph: 1, value: 3, generic: 2 }
                        - { descr: 'Fatal', graph: 1, value: 4, generic: 2 }