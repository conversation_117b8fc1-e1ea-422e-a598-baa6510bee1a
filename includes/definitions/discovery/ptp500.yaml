mib: CAMBIUM-PTP500-V2-MIB
modules:
    os:
        sysDescr_regex: '/5\d(?<hardware>.*?)( Wireless|,).*Version\s+(?<version>[\d-]+)/'
        hardware: CAMBIUM-PTP500-V2-MIB::masterSlaveMode.0
        hardware_template: '{{ $hardware }} {{ CAMBIUM-PTP500-V2-MIB::masterSlaveMode.0 }}'
    sensors:
        state:
            data:
                -
                    oid: transmitModulationMode
                    num_oid: '.1.3.6.1.4.1.17713.5.12.9.{{ $index }}'
                    index: 'transmitModulationMode.{{ $index }}'
                    descr: Transmit Modulation Mode
                    states:
                        - { value: 1, generic: 0, graph: 1, descr: modBpsk50percent }
                        - { value: 2, generic: 0, graph: 1, descr: modQpsk50percentSingle }
                        - { value: 3, generic: 0, graph: 1, descr: modQpsk75percentSingle }
                        - { value: 4, generic: 0, graph: 1, descr: mod16qam50percentSingle }
                        - { value: 5, generic: 0, graph: 1, descr: mod16qam75percentSingle }
                        - { value: 6, generic: 0, graph: 1, descr: mod64qam67percentSingle }
                        - { value: 7, generic: 0, graph: 1, descr: mod64qam83percentSingle }
                        - { value: 8, generic: 0, graph: 1, descr: modReserved1 }
                        - { value: 9, generic: 0, graph: 1, descr: modQpsk50percentDual }
                        - { value: 10, generic: 0, graph: 1, descr: modQpsk75percentDual }
                        - { value: 11, generic: 0, graph: 1, descr: mod16qam50percentDual }
                        - { value: 12, generic: 0, graph: 1, descr: mod16qam75percentDual }
                        - { value: 13, generic: 0, graph: 1, descr: mod64qam67percentDual }
                        - { value: 14, generic: 0, graph: 1, descr: mod64qam83percentDual }
                        - { value: 15, generic: 0, graph: 1, descr: modReserved2 }
                -
                    oid: receiveModulationMode
                    num_oid: '.1.3.6.1.4.1.17713.5.12.8.{{ $index }}'
                    index: 'receiveModulationMode.{{ $index }}'
                    descr: Receive Modulation Mode
                    states:
                        - { value: 1, generic: 0, graph: 1, descr: modBpsk50percent }
                        - { value: 2, generic: 0, graph: 1, descr: modQpsk50percentSingle }
                        - { value: 3, generic: 0, graph: 1, descr: modQpsk75percentSingle }
                        - { value: 4, generic: 0, graph: 1, descr: mod16qam50percentSingle }
                        - { value: 5, generic: 0, graph: 1, descr: mod16qam75percentSingle }
                        - { value: 6, generic: 0, graph: 1, descr: mod64qam67percentSingle }
                        - { value: 7, generic: 0, graph: 1, descr: mod64qam83percentSingle }
                        - { value: 8, generic: 0, graph: 1, descr: modReserved1 }
                        - { value: 9, generic: 0, graph: 1, descr: modQpsk50percentDual }
                        - { value: 10, generic: 0, graph: 1, descr: modQpsk75percentDual }
                        - { value: 11, generic: 0, graph: 1, descr: mod16qam50percentDual }
                        - { value: 12, generic: 0, graph: 1, descr: mod16qam75percentDual }
                        - { value: 13, generic: 0, graph: 1, descr: mod64qam67percentDual }
                        - { value: 14, generic: 0, graph: 1, descr: mod64qam83percentDual }
                        - { value: 15, generic: 0, graph: 1, descr: modReserved2 }
