os: acs
group: cisco
text: 'Cisco ACS'
ifname: true
type: server
icon: cisco
over:
    - { graph: device_bits, text: 'Device Traffic' }
    - { graph: device_processor, text: 'CPU Usage' }
    - { graph: device_mempool, text: 'Memory Usage' }
poller_modules:
    cisco-ace-serverfarms: true
    cisco-ace-loadbalancer: true
    cisco-cef: true
    cisco-mac-accounting: true
    cisco-remote-access-monitor: true
    slas: true
    cisco-ipsec-flow-monitor: true
    cipsec-tunnels: true
    cisco-otv: true
discovery_modules:
    cisco-cef: true
    slas: true
    cisco-mac-accounting: true
    cisco-otv: true
    cisco-pw: true
    vrf: true
    cisco-vrf-lite: true
discovery:
    -
        sysObjectID:
            - .*******.*******.1117
    -
        sysDescr:
            - 'Cisco Secure Access Control System'
