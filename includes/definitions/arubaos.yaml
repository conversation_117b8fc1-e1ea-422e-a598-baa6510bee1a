os: arubaos
text: ArubaOS
type: wireless
icon: aruba
over:
    - { graph: device_wireless_ap-count, text: 'AP Count' }
    - { graph: device_wireless_clients, text: 'Client Count' }
    - { graph: device_arubacontroller_numaps, text: 'Number of APs (Legacy)' }
    - { graph: device_arubacontroller_numclients, text: 'Number of Clients (Legacy)' }
poller_modules:
    aruba-controller: true
discovery:
    -
        sysObjectID:
            - .1.3.6.1.4.1.14823. #ArubaOS
            - .1.3.6.1.4.1.6486.800.1.1.2.2.2. #AOS-W
        sysObjectID_except:
            - .1.3.6.1.4.1.14823.1.2 #Aruba apProducts (Aruba Instant)
            - .1.3.6.1.4.1.14823.1.6 #ClearPass
    -
        sysDescr:
            - ArubaOS
        sysObjectID_except:
            - .1.3.6.1.4.1.14823.1.2 #Aruba apProducts (Aruba Instant)
