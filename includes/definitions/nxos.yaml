os: nxos
group: cisco
text: 'Cisco NX-OS'
type: network
icon: cisco
over:
    - { graph: device_bits, text: 'Device Traffic' }
    - { graph: device_processor, text: 'CPU Usage' }
    - { graph: device_mempool, text: 'Memory Usage' }
poller_modules:
    cisco-ace-serverfarms: true
    cisco-ace-loadbalancer: true
    cisco-cef: true
    cisco-mac-accounting: true
    cisco-remote-access-monitor: true
    slas: true
    cisco-ipsec-flow-monitor: true
    cipsec-tunnels: true
    cisco-otv: true
    ipmi: false
    cisco-vpdn: true
discovery_modules:
    cisco-cef: true
    slas: true
    cisco-mac-accounting: true
    cisco-otv: true
    cisco-pw: true
    vrf: true
    cisco-vrf-lite: true
discovery:
    -
        sysDescr:
            - NX-OS(tm)
            - Cisco NX-OS
        sysDescr_except:
            - Cisco NX-OS(tm) ucs # exclude UCS, and indentify as "UCOS" OS
        sysObjectID_except:
            - .*******.********.3.1.3.1062 # exclude UCS, and indentify as "UCOS" OS
