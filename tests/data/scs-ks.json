{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.9839", "sysDescr": "CAREL cpCO controller", "sysContact": "<private>", "version": null, "hardware": null, "features": null, "location": "<private>", "os": "scs-ks", "type": "environment", "serial": null, "icon": "carel.png"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "ETH", "ifName": "ETH", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "ETH", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "USB", "ifName": "USB", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "USB", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "ETH", "ifName": "ETH", "portName": null, "ifIndex": 1, "ifSpeed": 100000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "ETH", "ifPhysAddress": "000000000001", "ifLastChange": 120517535, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 85065021, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 67907408, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 509368580, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 2749288297, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 349, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 578617, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 6877290, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "USB", "ifName": "USB", "portName": null, "ifIndex": 2, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "USB", "ifPhysAddress": "000000000000", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "humidity", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.********", "sensor_index": "0", "sensor_type": "scs-ks", "sensor_descr": "Room humidity", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 28.6, "sensor_limit": 70, "sensor_limit_warn": null, "sensor_limit_low": 30, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.*******", "sensor_index": "0", "sensor_type": "status-compressor.0", "sensor_descr": "Compressor alarm", "group": "Alarm", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "status-compressor.0"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.*******", "sensor_index": "0", "sensor_type": "status-condenser-fan.0", "sensor_descr": "Condenser Fan alarm", "group": "Alarm", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "status-condenser-fan.0"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.*******", "sensor_index": "0", "sensor_type": "status-evaporator-fan.0", "sensor_descr": "Evaporator Fan alarm", "group": "Alarm", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "status-evaporator-fan.0"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.*******", "sensor_index": "0", "sensor_type": "status-exhaust-air-sensor.0", "sensor_descr": "Exhaust air sensor alarm", "group": "Alarm", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "status-exhaust-air-sensor.0"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.********", "sensor_index": "0", "sensor_type": "status-filter-monitoring.0", "sensor_descr": "Filter monitoring alarm", "group": "Alarm", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "status-filter-monitoring.0"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.*******", "sensor_index": "0", "sensor_type": "status-heater-thermal-protection.0", "sensor_descr": "Heater thermal protection alarm", "group": "Alarm", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "status-heater-thermal-protection.0"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.********", "sensor_index": "0", "sensor_type": "status-max-exhaust-air-temperature.0", "sensor_descr": "Maximum exhaust air temperature alarm", "group": "Alarm", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "status-max-exhaust-air-temperature.0"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.********", "sensor_index": "0", "sensor_type": "status-min-exhaust-air-temperature.0", "sensor_descr": "Minimum exhaust air temperature alarm", "group": "Alarm", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "status-min-exhaust-air-temperature.0"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.********", "sensor_index": "0", "sensor_type": "status-min-supply-air-temperature.0", "sensor_descr": "Minimum supply air temperature alarm", "group": "Alarm", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "status-min-supply-air-temperature.0"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.*******", "sensor_index": "0", "sensor_type": "status-outside-sensor.0", "sensor_descr": "Outside temperature sensor alarm", "group": "Alarm", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "status-outside-sensor.0"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.*******", "sensor_index": "0", "sensor_type": "status-room-humidity-sensor.0", "sensor_descr": "Humidity room sensor alarm", "group": "Alarm", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "status-room-humidity-sensor.0"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.*******", "sensor_index": "0", "sensor_type": "status-room-temperature-sensor.0", "sensor_descr": "Temperature room sensor alarm", "group": "Alarm", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "status-room-temperature-sensor.0"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.*******", "sensor_index": "0", "sensor_type": "status-supply-air-sensor.0", "sensor_descr": "Supply air sensor alarm", "group": "Alarm", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "status-supply-air-sensor.0"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.********", "sensor_index": "0", "sensor_type": "status-water-sensor.0", "sensor_descr": "Water sensor alarm", "group": "Alarm", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "status-water-sensor.0"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.********", "sensor_index": "exhaust.0", "sensor_type": "scs-ks", "sensor_descr": "Exhaust air temperature", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 21.2, "sensor_limit": 41.2, "sensor_limit_warn": null, "sensor_limit_low": 11.2, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.********", "sensor_index": "outside.0", "sensor_type": "scs-ks", "sensor_descr": "Outside air temperature", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 8.7, "sensor_limit": 28.7, "sensor_limit_warn": null, "sensor_limit_low": -1.3, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.********", "sensor_index": "room.0", "sensor_type": "scs-ks", "sensor_descr": "Room temperature", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 19.6, "sensor_limit": 39.6, "sensor_limit_warn": null, "sensor_limit_low": 9.6, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.9839.********", "sensor_index": "supply.0", "sensor_type": "scs-ks", "sensor_descr": "Supply air temperature", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 20.9, "sensor_limit": 40.9, "sensor_limit_warn": null, "sensor_limit_low": 10.9, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "status-compressor.0", "state_descr": "null", "state_draw_graph": 0, "state_value": -1, "state_generic_value": 3}, {"state_name": "status-compressor.0", "state_descr": "OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "status-compressor.0", "state_descr": "Error", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "status-condenser-fan.0", "state_descr": "null", "state_draw_graph": 0, "state_value": -1, "state_generic_value": 3}, {"state_name": "status-condenser-fan.0", "state_descr": "OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "status-condenser-fan.0", "state_descr": "Error", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "status-evaporator-fan.0", "state_descr": "null", "state_draw_graph": 0, "state_value": -1, "state_generic_value": 3}, {"state_name": "status-evaporator-fan.0", "state_descr": "OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "status-evaporator-fan.0", "state_descr": "Error", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "status-exhaust-air-sensor.0", "state_descr": "null", "state_draw_graph": 0, "state_value": -1, "state_generic_value": 3}, {"state_name": "status-exhaust-air-sensor.0", "state_descr": "OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "status-exhaust-air-sensor.0", "state_descr": "Error", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "status-filter-monitoring.0", "state_descr": "null", "state_draw_graph": 0, "state_value": -1, "state_generic_value": 3}, {"state_name": "status-filter-monitoring.0", "state_descr": "OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "status-filter-monitoring.0", "state_descr": "Error", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "status-heater-thermal-protection.0", "state_descr": "null", "state_draw_graph": 0, "state_value": -1, "state_generic_value": 3}, {"state_name": "status-heater-thermal-protection.0", "state_descr": "OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "status-heater-thermal-protection.0", "state_descr": "Error", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "status-max-exhaust-air-temperature.0", "state_descr": "null", "state_draw_graph": 0, "state_value": -1, "state_generic_value": 3}, {"state_name": "status-max-exhaust-air-temperature.0", "state_descr": "OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "status-max-exhaust-air-temperature.0", "state_descr": "Error", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "status-min-exhaust-air-temperature.0", "state_descr": "null", "state_draw_graph": 0, "state_value": -1, "state_generic_value": 3}, {"state_name": "status-min-exhaust-air-temperature.0", "state_descr": "OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "status-min-exhaust-air-temperature.0", "state_descr": "Error", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "status-min-supply-air-temperature.0", "state_descr": "null", "state_draw_graph": 0, "state_value": -1, "state_generic_value": 3}, {"state_name": "status-min-supply-air-temperature.0", "state_descr": "OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "status-min-supply-air-temperature.0", "state_descr": "Error", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "status-outside-sensor.0", "state_descr": "null", "state_draw_graph": 0, "state_value": -1, "state_generic_value": 3}, {"state_name": "status-outside-sensor.0", "state_descr": "OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "status-outside-sensor.0", "state_descr": "Error", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "status-room-humidity-sensor.0", "state_descr": "null", "state_draw_graph": 0, "state_value": -1, "state_generic_value": 3}, {"state_name": "status-room-humidity-sensor.0", "state_descr": "OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "status-room-humidity-sensor.0", "state_descr": "Error", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "status-room-temperature-sensor.0", "state_descr": "null", "state_draw_graph": 0, "state_value": -1, "state_generic_value": 3}, {"state_name": "status-room-temperature-sensor.0", "state_descr": "OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "status-room-temperature-sensor.0", "state_descr": "Error", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "status-supply-air-sensor.0", "state_descr": "null", "state_draw_graph": 0, "state_value": -1, "state_generic_value": 3}, {"state_name": "status-supply-air-sensor.0", "state_descr": "OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "status-supply-air-sensor.0", "state_descr": "Error", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "status-water-sensor.0", "state_descr": "null", "state_draw_graph": 0, "state_value": -1, "state_generic_value": 3}, {"state_name": "status-water-sensor.0", "state_descr": "OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "status-water-sensor.0", "state_descr": "Error", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}]}, "poller": "matches discovery"}}