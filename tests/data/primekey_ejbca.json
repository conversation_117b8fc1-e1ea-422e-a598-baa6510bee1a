{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.8072.3.2.10", "sysDescr": "Linux vgw 4.19.46 #1 SMP Thu Sep 19 02:32:09 CEST 2019 x86_64", "sysContact": "<private>", "version": "PrimeKeyAppliance.3.4.3", "hardware": null, "features": null, "location": "<private>", "os": "primekey", "type": "appliance", "serial": "CS700886", "icon": "primekey.svg"}]}, "poller": "matches discovery"}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.***************.51.1", "sensor_index": "pkAClusterActiveNodes", "sensor_type": "primekey", "sensor_descr": "Nodes Active", "group": "Database", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.***************.49.1", "sensor_index": "pkAClusterLocalNodeID", "sensor_type": "primekey", "sensor_descr": "Node ID", "group": "Database", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.***************.50.1", "sensor_index": "pkAClusterSize", "sensor_type": "primekey", "sensor_descr": "Node Count", "group": "Database", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.100.52.1", "sensor_index": "pkASfpLoad15m", "sensor_type": "primekey", "sensor_descr": "15m CPU load", "group": "CPU", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0.37, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.100.50.1", "sensor_index": "pkASfpLoad1m", "sensor_type": "primekey", "sensor_descr": "1m CPU load", "group": "CPU", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0.36, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.100.51.1", "sensor_index": "pkASfpLoad5m", "sensor_type": "primekey", "sensor_descr": "5m CPU load", "group": "CPU", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0.34, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.100.54.1", "sensor_index": "pkASfpRaidActiveDevices", "sensor_type": "primekey", "sensor_descr": "RAID Active", "group": "RAID", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.100.52.1", "sensor_index": "pkASfpRaidTotalDevices", "sensor_type": "primekey", "sensor_descr": "RAID Devices", "group": "RAID", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.49.1", "sensor_index": "pkASfpCpuFan", "sensor_type": "primekey", "sensor_descr": "CPU Fan", "group": "CPU", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1000, "sensor_limit": 1800, "sensor_limit_warn": null, "sensor_limit_low": 800, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.50.1", "sensor_index": "pkASfpSysFan1", "sensor_type": "primekey", "sensor_descr": "System Fan 1", "group": "System", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2300, "sensor_limit": 4140, "sensor_limit_warn": null, "sensor_limit_low": 1840, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.51.1", "sensor_index": "pkASfpSysFan2", "sensor_type": "primekey", "sensor_descr": "System Fan 2", "group": "System", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2200, "sensor_limit": 3960, "sensor_limit_warn": null, "sensor_limit_low": 1760, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.52.1", "sensor_index": "pkASfpSysFan3", "sensor_type": "primekey", "sensor_descr": "System Fan 3", "group": "System", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2200, "sensor_limit": 3960, "sensor_limit_warn": null, "sensor_limit_low": 1760, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "percent", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.49.1", "sensor_index": "pkAVdbUsagePercent", "sensor_type": "primekey", "sensor_descr": "DB Usage %", "group": "Database", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 53, "sensor_limit": 100, "sensor_limit_warn": 80, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.*************.56.1", "sensor_index": "pkAHsmBatteryExtStatus", "sensor_type": "BatteryExt", "sensor_descr": "Battery Ext", "group": "HSM", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "BatteryExt"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.*************.53.1", "sensor_index": "pkAHsmBatteryIntStatus", "sensor_type": "BatteryInt", "sensor_descr": "Battery Int", "group": "HSM", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "BatteryInt"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.***************.52.1", "sensor_index": "pkAClusterLocalGaleraState", "sensor_type": "DbEnum", "sensor_descr": "DB Enum", "group": "Database", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DbEnum"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.50.1", "sensor_index": "pkAVdbStatus", "sensor_type": "DbStorage", "sensor_descr": "DB Storage", "group": "Database", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DbStorage"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.53.1", "sensor_index": "pkASfpCpuFanStatus", "sensor_type": "FansCpu", "sensor_descr": "Fan CPU", "group": "Fans", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "FansCpu"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.54.1", "sensor_index": "pkASfpSysFansStatus", "sensor_type": "FansSystem", "sensor_descr": "Fans System", "group": "Fans", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "FansSystem"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.***************.50.1", "sensor_index": "pkAEJBCAHealth", "sensor_type": "HealthEjbca", "sensor_descr": "Health EJBCA", "group": "Health of VMs", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "HealthEjbca"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.***************.50.1", "sensor_index": "pkASignServerHealth", "sensor_type": "HealthSignserver", "sensor_descr": "Health SignServer", "group": "Health of VMs", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "HealthSignserver"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.***********", "sensor_index": "pkASfpVmStatus", "sensor_type": "HealthVMs", "sensor_descr": "Health VMs", "group": "Health of VMs", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "HealthVMs"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.*************.50.1", "sensor_index": "pkAHsmStatusEnum", "sensor_type": "HsmEnum", "sensor_descr": "HSM Enum", "group": "HSM", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "HsmEnum"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.*************.51.1", "sensor_index": "pkAHsmStatusBool", "sensor_type": "HsmHealthy", "sensor_descr": "HSM Healthy", "group": "HSM", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "HsmHealthy"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.100.49.1", "sensor_index": "pkASfpRaidStatus", "sensor_type": "RaidHealth", "sensor_descr": "RAID Health", "group": "RAID", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "RaidHealth"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.************.1", "sensor_index": "pkASfpCpuTemp", "sensor_type": "primekey", "sensor_descr": "CPU Temp", "group": "CPU", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 29, "sensor_limit": 49, "sensor_limit_warn": null, "sensor_limit_low": 19, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.22408.*******.*************.52.1", "sensor_index": "pkAHsmBatteryInt", "sensor_type": "primekey", "sensor_descr": "Int Battery", "group": "HSM", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3.115, "sensor_limit": 3.58225, "sensor_limit_warn": null, "sensor_limit_low": 2.64775, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": "\\LibreNMS\\Util\\Number::cast", "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "BatteryExt", "state_descr": "Ok or Absent", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "BatteryExt", "state_descr": "Low or Fail", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 2}, {"state_name": "BatteryInt", "state_descr": "OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "BatteryInt", "state_descr": "Low or Fail", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 2}, {"state_name": "DbEnum", "state_descr": "Undefined", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "DbEnum", "state_descr": "<PERSON><PERSON>", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 1}, {"state_name": "DbEnum", "state_descr": "Don<PERSON>", "state_draw_graph": 2, "state_value": 2, "state_generic_value": 0}, {"state_name": "DbEnum", "state_descr": "Joined", "state_draw_graph": 3, "state_value": 3, "state_generic_value": 0}, {"state_name": "DbEnum", "state_descr": "Synced", "state_draw_graph": 4, "state_value": 4, "state_generic_value": 0}, {"state_name": "DbEnum", "state_descr": "Error", "state_draw_graph": 5, "state_value": 5, "state_generic_value": 2}, {"state_name": "DbEnum", "state_descr": "Max", "state_draw_graph": 6, "state_value": 6, "state_generic_value": 2}, {"state_name": "DbStorage", "state_descr": "< 80% full", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "DbStorage", "state_descr": "> 80% full", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 2}, {"state_name": "FansCpu", "state_descr": "OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "FansCpu", "state_descr": "Fail", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 2}, {"state_name": "FansSystem", "state_descr": "All OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "FansSystem", "state_descr": "Fail", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 1}, {"state_name": "HealthEjbca", "state_descr": "All OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "HealthEjbca", "state_descr": "Not Running or Unhealthy", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "HealthSignserver", "state_descr": "All OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "HealthSignserver", "state_descr": "Not Running or Unhealthy", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "HealthVMs", "state_descr": "All OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "HealthVMs", "state_descr": "Some Inactive", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 2}, {"state_name": "HsmEnum", "state_descr": "STATUS_is_OPER", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "HsmEnum", "state_descr": "STATUS_is_MAINT", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 1}, {"state_name": "HsmEnum", "state_descr": "STATUS_is_BOOT", "state_draw_graph": 2, "state_value": 2, "state_generic_value": 1}, {"state_name": "HsmEnum", "state_descr": "STATUS_is_ALARM", "state_draw_graph": 3, "state_value": 3, "state_generic_value": 2}, {"state_name": "HsmEnum", "state_descr": "STATUS_is_EXTERNALERASE", "state_draw_graph": 4, "state_value": 4, "state_generic_value": 2}, {"state_name": "HsmEnum", "state_descr": "STATUS_is_OTHER", "state_draw_graph": 6, "state_value": 5, "state_generic_value": 3}, {"state_name": "HsmHealthy", "state_descr": "OK", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "HsmHealthy", "state_descr": "Fail", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 2}, {"state_name": "RaidHealth", "state_descr": "Clean or Active", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "RaidHealth", "state_descr": "Degraded", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 2}]}, "poller": "matches discovery"}}