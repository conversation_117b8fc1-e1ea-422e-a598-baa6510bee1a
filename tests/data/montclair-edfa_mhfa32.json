{"os": {"discovery": {"devices": [{"sysName": null, "sysObjectID": ".*******.4.1.1385", "sysDescr": "4-10-g036b52a", "sysContact": "<private>", "version": "2.1", "hardware": "MHFA-S-37-32-AA-1-1", "features": null, "location": "<private>", "os": "montclair-edfa", "type": "network", "serial": "0001011016", "icon": "montclair.svg"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "st", "ifName": "st", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "0", "ifAlias": "st", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "st", "ifName": "st", "portName": null, "ifIndex": 1, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "0", "ifAlias": "st", "ifPhysAddress": "001274420b64", "ifLastChange": 1096, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1385.*********.0", "sensor_index": "amplifierPortAmount.0", "sensor_type": "montclair-edfa", "sensor_descr": "Ports", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1385.12.6.0", "sensor_index": "deviceFanAmount.0", "sensor_type": "montclair-edfa", "sensor_descr": "Fan Count", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1385.12.3.0", "sensor_index": "devicePowerSupplyAmount.0", "sensor_type": "montclair-edfa", "sensor_descr": "Power Supplies", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1385.********.0", "sensor_index": "amplifierBackReflectionPower.0", "sensor_type": "montclair-edfa", "sensor_descr": "Amplifier Reflection", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -24.4, "sensor_limit": -15, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1385.*********.0", "sensor_index": "amplifierGain.0", "sensor_type": "montclair-edfa", "sensor_descr": "<PERSON><PERSON>", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 27.5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1385.********.0", "sensor_index": "amplifierInputPower.0", "sensor_type": "montclair-edfa", "sensor_descr": "Input Power", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 6, "sensor_limit": 10, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1385.*********.0", "sensor_index": "amplifierOutputPower.0", "sensor_type": "montclair-edfa", "sensor_descr": "Output Power", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 33.5, "sensor_limit": 40, "sensor_limit_warn": null, "sensor_limit_low": 28, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1385.*********.0", "sensor_index": "amplifierPortGain.0", "sensor_type": "montclair-edfa", "sensor_descr": "Port Gain", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 12.5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1385.*********.0", "sensor_index": "amplifierPortOutputPower.0", "sensor_type": "montclair-edfa", "sensor_descr": "Port Output Power", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 18.5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "percent", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1385.********.0", "sensor_index": "amplifierPrePumpCurrent.0", "sensor_type": "montclair-edfa", "sensor_descr": "Pre-Pump Current", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 80, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "percent", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1385.********.0", "sensor_index": "amplifierPumpCurrent1.0", "sensor_type": "montclair-edfa", "sensor_descr": "Pump Current 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 63, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "percent", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1385.*********.0", "sensor_index": "amplifierPumpCurrent2.0", "sensor_type": "montclair-edfa", "sensor_descr": "Pump Current 2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 112, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1385.********.0", "sensor_index": "amplifierMode.0", "sensor_type": "amplifierMode", "sensor_descr": "Amp Mode", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "amplifierMode"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1385.********.0", "sensor_index": "amplifierEnabled.0", "sensor_type": "amplifierStatus", "sensor_descr": "Amplifier", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "amplifierStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1385.12.8.0", "sensor_index": "deviceFanMode.0", "sensor_type": "deviceFanMode", "sensor_descr": "Fan Mode", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "deviceFanMode"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1385.12.7.0", "sensor_index": "deviceFanStatus.0", "sensor_type": "deviceFanStatus", "sensor_descr": "Fan Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "deviceFanStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1385.12.4.0", "sensor_index": "devicePowerSupply1Status.0", "sensor_type": "devicePowerSupply1Status", "sensor_descr": "Power Supply 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 7, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "devicePowerSupply1Status"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1385.12.5.0", "sensor_index": "devicePowerSupply2Status.0", "sensor_type": "devicePowerSupply2Status", "sensor_descr": "Power Supply 2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 7, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "devicePowerSupply2Status"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1385.*********.0", "sensor_index": "amplifierPrePumpTemperature.0", "sensor_type": "montclair-edfa", "sensor_descr": "Amp Pre-Pump Temperature", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 24.9, "sensor_limit": 70, "sensor_limit_warn": null, "sensor_limit_low": -33, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1385.*********.0", "sensor_index": "amplifierRadiatorTemperature.0", "sensor_type": "montclair-edfa", "sensor_descr": "Amp Radiator Temperature", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 70, "sensor_limit_warn": null, "sensor_limit_low": -33, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "amplifierMode", "state_descr": "ACC", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "amplifierMode", "state_descr": "APC", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "amplifierMode", "state_descr": "AGC", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "amplifierStatus", "state_descr": "Disabled", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 2}, {"state_name": "amplifierStatus", "state_descr": "Enabled", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "deviceFanMode", "state_descr": "Auto", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "deviceFanMode", "state_descr": "Manual", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "deviceFanMode", "state_descr": "Low", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "deviceFanMode", "state_descr": "Linear", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 0}, {"state_name": "deviceFanMode", "state_descr": "High", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "deviceFanStatus", "state_descr": "Normal", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "deviceFanStatus", "state_descr": "Unknown", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "deviceFanStatus", "state_descr": "Critical", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 2}, {"state_name": "deviceFanStatus", "state_descr": "Warning", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 2}, {"state_name": "deviceFanStatus", "state_descr": "Normal", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 0}, {"state_name": "devicePowerSupply1Status", "state_descr": "Empty", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "devicePowerSupply1Status", "state_descr": "Critical", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 2}, {"state_name": "devicePowerSupply1Status", "state_descr": "Normal", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 0}, {"state_name": "devicePowerSupply2Status", "state_descr": "Empty", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "devicePowerSupply2Status", "state_descr": "Critical", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 2}, {"state_name": "devicePowerSupply2Status", "state_descr": "Normal", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 0}]}, "poller": "matches discovery"}}