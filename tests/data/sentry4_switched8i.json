{"os": {"discovery": {"devices": [{"sysName": null, "sysObjectID": ".*******.4.1.1718.4", "sysDescr": "Sentry Smart PDU", "sysContact": null, "version": null, "hardware": null, "features": null, "location": null, "os": "sentry4", "type": "power", "serial": null, "icon": "servertech.png"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lo", "ifName": "lo", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "softwareLoopback", "ifAlias": "lo", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "eth0", "ifName": "eth0", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "eth0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lo", "ifName": "lo", "portName": null, "ifIndex": 1, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "softwareLoopback", "ifAlias": "lo", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "eth0", "ifName": "eth0", "portName": null, "ifIndex": 2, "ifSpeed": 100000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "eth0", "ifPhysAddress": "000a9c605685", "ifLastChange": 1650, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 1752868, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 1747024, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 946960256, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 295508752, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 11997864, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 5808, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4LineCurrent.1.1.1", "sensor_type": "sentry4", "sensor_descr": "Line, AA:L, Current", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 4.03, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4LineCurrent.2.1.1", "sensor_type": "sentry4", "sensor_descr": "Line, BA:L, Current", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 4.27, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4PhaseCurrent.1.1.1", "sensor_type": "sentry4", "sensor_descr": "Phase, AA:L-L/N, Current", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 4.03, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4PhaseCurrent.2.1.1", "sensor_type": "sentry4", "sensor_descr": "Phase, BA:L-L/N, Current", "group": null, "sensor_divisor": 100, "sensor_multiplier": 1, "sensor_current": 4.29, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.********", "sensor_index": "st4InputCord.1.1", "sensor_type": "sentry4", "sensor_descr": "Line, Master_Cord_A, Frequency", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 60, "sensor_limit": 63, "sensor_limit_warn": null, "sensor_limit_low": 57, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.********", "sensor_index": "st4InputCord.2.1", "sensor_type": "sentry4", "sensor_descr": "Line, Link1_Cord_A, Frequency", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 60, "sensor_limit": 63, "sensor_limit_warn": null, "sensor_limit_low": 57, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "humidity", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.********.*******", "sensor_index": "st4HumidSensorValue.5.1", "sensor_type": "sentry4", "sensor_descr": "Humid_Sensor_E1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 37, "sensor_limit": 95, "sensor_limit_warn": 90, "sensor_limit_low": 5, "sensor_limit_low_warn": 10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "humidity", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.********.*******", "sensor_index": "st4HumidSensorValue.5.2", "sensor_type": "sentry4", "sensor_descr": "Humid_Sensor_E2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 95, "sensor_limit_warn": 90, "sensor_limit_low": 5, "sensor_limit_low_warn": 10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******", "sensor_index": "st4InputCord.1.1", "sensor_type": "sentry4", "sensor_descr": "<PERSON><PERSON>, Master_Cord_A, Active Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 806, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******", "sensor_index": "st4InputCord.2.1", "sensor_type": "sentry4", "sensor_descr": "<PERSON><PERSON>, <PERSON><PERSON>_Cord_A, Active Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 856, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4PhaseActivePower.1.1.1", "sensor_type": "sentry4", "sensor_descr": "Phase, AA:L-L/N, Active Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 806, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4PhaseActivePower.2.1.1", "sensor_type": "sentry4", "sensor_descr": "Phase, BA:L-L/N, Active Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 856, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4BranchState.1.1.1", "sensor_type": "st4BranchState", "sensor_descr": "Branch AA:BR1 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4BranchState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.2", "sensor_index": "st4BranchState.1.1.2", "sensor_type": "st4BranchState", "sensor_descr": "Branch AA:BR2 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4BranchState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4BranchState.2.1.1", "sensor_type": "st4BranchState", "sensor_descr": "Branch BA:BR1 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4BranchState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.2", "sensor_index": "st4BranchState.2.1.2", "sensor_type": "st4BranchState", "sensor_descr": "Branch BA:BR2 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4BranchState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4BranchStatus.1.1.1", "sensor_type": "st4BranchStatus", "sensor_descr": "Branch AA:BR1 Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4BranchStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.2", "sensor_index": "st4BranchStatus.1.1.2", "sensor_type": "st4BranchStatus", "sensor_descr": "Branch AA:BR2 Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4BranchStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4BranchStatus.2.1.1", "sensor_type": "st4BranchStatus", "sensor_descr": "Branch BA:BR1 Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4BranchStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.2", "sensor_index": "st4BranchStatus.2.1.2", "sensor_type": "st4BranchStatus", "sensor_descr": "Branch BA:BR2 Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4BranchStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******", "sensor_index": "st4InputCordActivePowerStatus.1.1", "sensor_type": "st4InputCordActivePowerStatus", "sensor_descr": "Input Cord Active Power Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4InputCordActivePowerStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******", "sensor_index": "st4InputCordActivePowerStatus.2.1", "sensor_type": "st4InputCordActivePowerStatus", "sensor_descr": "Input Cord Active Power Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4InputCordActivePowerStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******", "sensor_index": "st4InputCordApparentPowerStatus.1.1", "sensor_type": "st4InputCordApparentPowerStatus", "sensor_descr": "Input Cord Apparent Power Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4InputCordApparentPowerStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******", "sensor_index": "st4InputCordApparentPowerStatus.2.1", "sensor_type": "st4InputCordApparentPowerStatus", "sensor_descr": "Input Cord Apparent Power Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4InputCordApparentPowerStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******", "sensor_index": "st4InputCordPowerFactorStatus.1.1", "sensor_type": "st4InputCordPowerFactorStatus", "sensor_descr": "Input Cord Power Factor Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4InputCordPowerFactorStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******", "sensor_index": "st4InputCordPowerFactorStatus.2.1", "sensor_type": "st4InputCordPowerFactorStatus", "sensor_descr": "Input Cord Power Factor Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4InputCordPowerFactorStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******", "sensor_index": "st4InputCordState.1.1", "sensor_type": "st4InputCordState", "sensor_descr": "Input Cord State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4InputCordState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******", "sensor_index": "st4InputCordState.2.1", "sensor_type": "st4InputCordState", "sensor_descr": "Input Cord State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4InputCordState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******", "sensor_index": "st4InputCordStatus.1.1", "sensor_type": "st4InputCordStatus", "sensor_descr": "Input Cord Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4InputCordStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******", "sensor_index": "st4InputCordStatus.2.1", "sensor_type": "st4InputCordStatus", "sensor_descr": "Input Cord Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4InputCordStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4LineState.1.1.1", "sensor_type": "st4LineState", "sensor_descr": "Line AA:L State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4LineState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4LineState.2.1.1", "sensor_type": "st4LineState", "sensor_descr": "Line BA:L State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4LineState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4LineStatus.1.1.1", "sensor_type": "st4LineStatus", "sensor_descr": "Line AA:L Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4LineStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4LineStatus.2.1.1", "sensor_type": "st4LineStatus", "sensor_descr": "Line BA:L Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4LineStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4OcpStatus.1.1.1", "sensor_type": "st4OcpStatus", "sensor_descr": "Over-current Protector AA:BR1 Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4OcpStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.2", "sensor_index": "st4OcpStatus.1.1.2", "sensor_type": "st4OcpStatus", "sensor_descr": "Over-current Protector AA:BR2 Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4OcpStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4OcpStatus.2.1.1", "sensor_type": "st4OcpStatus", "sensor_descr": "Over-current Protector BA:BR1 Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4OcpStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.2", "sensor_index": "st4OcpStatus.2.1.2", "sensor_type": "st4OcpStatus", "sensor_descr": "Over-current Protector BA:BR2 Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4OcpStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.1.1*******", "sensor_index": "st4PhasePowerFactorStatus.1.1.1", "sensor_type": "st4PhasePowerFactorStatus", "sensor_descr": "Phase AA:L-L/N Power Factor Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4PhasePowerFactorStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.1.1*******", "sensor_index": "st4PhasePowerFactorStatus.2.1.1", "sensor_type": "st4PhasePowerFactorStatus", "sensor_descr": "Phase BA:L-L/N Power Factor Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4PhasePowerFactorStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4PhaseState.1.1.1", "sensor_type": "st4PhaseState", "sensor_descr": "Phase AA:L-L/N State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4PhaseState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4PhaseState.2.1.1", "sensor_type": "st4PhaseState", "sensor_descr": "Phase BA:L-L/N State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4PhaseState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4PhaseStatus.1.1.1", "sensor_type": "st4PhaseStatus", "sensor_descr": "Phase AA:L-L/N Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4PhaseStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4PhaseStatus.2.1.1", "sensor_type": "st4PhaseStatus", "sensor_descr": "Phase BA:L-L/N Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4PhaseStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4PhaseVoltageStatus.1.1.1", "sensor_type": "st4PhaseVoltageStatus", "sensor_descr": "Phase AA:L-L/N Voltage Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4PhaseVoltageStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4PhaseVoltageStatus.2.1.1", "sensor_type": "st4PhaseVoltageStatus", "sensor_descr": "Phase BA:L-L/N Voltage Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4PhaseVoltageStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.1.1.1", "sensor_index": "st4UnitStatus.1", "sensor_type": "st4UnitStatus", "sensor_descr": "Unit Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4UnitStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.1.1.2", "sensor_index": "st4UnitStatus.2", "sensor_type": "st4UnitStatus", "sensor_descr": "Unit Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4UnitStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.1.1.5", "sensor_index": "st4UnitStatus.5", "sensor_type": "st4UnitStatus", "sensor_descr": "Unit Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "st4UnitStatus"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******", "sensor_index": "st4TempSensorValue5.1", "sensor_type": "sentry4", "sensor_descr": "Temp_Sensor_E1", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 23.9, "sensor_limit": 50, "sensor_limit_warn": 45, "sensor_limit_low": 1, "sensor_limit_low_warn": 5, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******", "sensor_index": "st4TempSensorValue5.2", "sensor_type": "sentry4", "sensor_descr": "Temp_Sensor_E2", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 27.1, "sensor_limit": 50, "sensor_limit_warn": 45, "sensor_limit_low": 1, "sensor_limit_low_warn": 5, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4PhaseVoltage.1.1.1", "sensor_type": "sentry4", "sensor_descr": "Phase, AA:L-L/N, Voltage", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 211.7, "sensor_limit": 243.455, "sensor_limit_warn": null, "sensor_limit_low": 179.945, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1718.*******.*******.1", "sensor_index": "st4PhaseVoltage.2.1.1", "sensor_type": "sentry4", "sensor_descr": "Phase, BA:L-L/N, Voltage", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 209.8, "sensor_limit": 241.27, "sensor_limit_warn": null, "sensor_limit_low": 178.33, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "st4BranchState", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "st4BranchState", "state_descr": "on", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "st4BranchState", "state_descr": "off", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 3}, {"state_name": "st4BranchStatus", "state_descr": "normal", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 0}, {"state_name": "st4BranchStatus", "state_descr": "disabled", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "st4BranchStatus", "state_descr": "purged", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "st4BranchStatus", "state_descr": "reading", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "st4BranchStatus", "state_descr": "settle", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 1}, {"state_name": "st4BranchStatus", "state_descr": "notFound", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 3}, {"state_name": "st4BranchStatus", "state_descr": "lost", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 1}, {"state_name": "st4BranchStatus", "state_descr": "readError", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 1}, {"state_name": "st4BranchStatus", "state_descr": "noComm", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 1}, {"state_name": "st4BranchStatus", "state_descr": "pwr<PERSON><PERSON>r", "state_draw_graph": 1, "state_value": 11, "state_generic_value": 2}, {"state_name": "st4BranchStatus", "state_descr": "breakerTripped", "state_draw_graph": 1, "state_value": 12, "state_generic_value": 2}, {"state_name": "st4BranchStatus", "state_descr": "fuseBlown", "state_draw_graph": 1, "state_value": 13, "state_generic_value": 2}, {"state_name": "st4BranchStatus", "state_descr": "lowAlarm", "state_draw_graph": 1, "state_value": 14, "state_generic_value": 2}, {"state_name": "st4BranchStatus", "state_descr": "lowWarning", "state_draw_graph": 1, "state_value": 15, "state_generic_value": 1}, {"state_name": "st4BranchStatus", "state_descr": "highWarning", "state_draw_graph": 1, "state_value": 16, "state_generic_value": 1}, {"state_name": "st4BranchStatus", "state_descr": "highAlarm", "state_draw_graph": 1, "state_value": 17, "state_generic_value": 2}, {"state_name": "st4BranchStatus", "state_descr": "alarm", "state_draw_graph": 1, "state_value": 18, "state_generic_value": 2}, {"state_name": "st4BranchStatus", "state_descr": "underLimit", "state_draw_graph": 1, "state_value": 19, "state_generic_value": 1}, {"state_name": "st4BranchStatus", "state_descr": "overLimit", "state_draw_graph": 1, "state_value": 20, "state_generic_value": 1}, {"state_name": "st4BranchStatus", "state_descr": "nvmFail", "state_draw_graph": 1, "state_value": 21, "state_generic_value": 2}, {"state_name": "st4BranchStatus", "state_descr": "profileError", "state_draw_graph": 1, "state_value": 22, "state_generic_value": 2}, {"state_name": "st4BranchStatus", "state_descr": "conflict", "state_draw_graph": 1, "state_value": 23, "state_generic_value": 2}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "normal", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 0}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "disabled", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "purged", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "reading", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "settle", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 1}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "notFound", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 3}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "lost", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 1}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "readError", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 1}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "noComm", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 1}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "pwr<PERSON><PERSON>r", "state_draw_graph": 1, "state_value": 11, "state_generic_value": 2}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "breakerTripped", "state_draw_graph": 1, "state_value": 12, "state_generic_value": 2}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "fuseBlown", "state_draw_graph": 1, "state_value": 13, "state_generic_value": 2}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "lowAlarm", "state_draw_graph": 1, "state_value": 14, "state_generic_value": 2}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "lowWarning", "state_draw_graph": 1, "state_value": 15, "state_generic_value": 1}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "highWarning", "state_draw_graph": 1, "state_value": 16, "state_generic_value": 1}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "highAlarm", "state_draw_graph": 1, "state_value": 17, "state_generic_value": 2}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "alarm", "state_draw_graph": 1, "state_value": 18, "state_generic_value": 2}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "underLimit", "state_draw_graph": 1, "state_value": 19, "state_generic_value": 1}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "overLimit", "state_draw_graph": 1, "state_value": 20, "state_generic_value": 1}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "nvmFail", "state_draw_graph": 1, "state_value": 21, "state_generic_value": 2}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "profileError", "state_draw_graph": 1, "state_value": 22, "state_generic_value": 2}, {"state_name": "st4InputCordActivePowerStatus", "state_descr": "conflict", "state_draw_graph": 1, "state_value": 23, "state_generic_value": 2}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "normal", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 0}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "disabled", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "purged", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "reading", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "settle", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 1}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "notFound", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 3}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "lost", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 1}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "readError", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 1}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "noComm", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 1}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "pwr<PERSON><PERSON>r", "state_draw_graph": 1, "state_value": 11, "state_generic_value": 2}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "breakerTripped", "state_draw_graph": 1, "state_value": 12, "state_generic_value": 2}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "fuseBlown", "state_draw_graph": 1, "state_value": 13, "state_generic_value": 2}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "lowAlarm", "state_draw_graph": 1, "state_value": 14, "state_generic_value": 2}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "lowWarning", "state_draw_graph": 1, "state_value": 15, "state_generic_value": 1}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "highWarning", "state_draw_graph": 1, "state_value": 16, "state_generic_value": 1}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "highAlarm", "state_draw_graph": 1, "state_value": 17, "state_generic_value": 2}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "alarm", "state_draw_graph": 1, "state_value": 18, "state_generic_value": 2}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "underLimit", "state_draw_graph": 1, "state_value": 19, "state_generic_value": 1}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "overLimit", "state_draw_graph": 1, "state_value": 20, "state_generic_value": 1}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "nvmFail", "state_draw_graph": 1, "state_value": 21, "state_generic_value": 2}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "profileError", "state_draw_graph": 1, "state_value": 22, "state_generic_value": 2}, {"state_name": "st4InputCordApparentPowerStatus", "state_descr": "conflict", "state_draw_graph": 1, "state_value": 23, "state_generic_value": 2}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "normal", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 0}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "disabled", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "purged", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "reading", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "settle", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 1}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "notFound", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 3}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "lost", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 1}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "readError", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 1}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "noComm", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 1}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "pwr<PERSON><PERSON>r", "state_draw_graph": 1, "state_value": 11, "state_generic_value": 2}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "breakerTripped", "state_draw_graph": 1, "state_value": 12, "state_generic_value": 2}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "fuseBlown", "state_draw_graph": 1, "state_value": 13, "state_generic_value": 2}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "lowAlarm", "state_draw_graph": 1, "state_value": 14, "state_generic_value": 2}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "lowWarning", "state_draw_graph": 1, "state_value": 15, "state_generic_value": 1}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "highWarning", "state_draw_graph": 1, "state_value": 16, "state_generic_value": 1}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "highAlarm", "state_draw_graph": 1, "state_value": 17, "state_generic_value": 2}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "alarm", "state_draw_graph": 1, "state_value": 18, "state_generic_value": 2}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "underLimit", "state_draw_graph": 1, "state_value": 19, "state_generic_value": 1}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "overLimit", "state_draw_graph": 1, "state_value": 20, "state_generic_value": 1}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "nvmFail", "state_draw_graph": 1, "state_value": 21, "state_generic_value": 2}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "profileError", "state_draw_graph": 1, "state_value": 22, "state_generic_value": 2}, {"state_name": "st4InputCordPowerFactorStatus", "state_descr": "conflict", "state_draw_graph": 1, "state_value": 23, "state_generic_value": 2}, {"state_name": "st4InputCordState", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "st4InputCordState", "state_descr": "on", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "st4InputCordState", "state_descr": "off", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 3}, {"state_name": "st4InputCordStatus", "state_descr": "normal", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 0}, {"state_name": "st4InputCordStatus", "state_descr": "disabled", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "st4InputCordStatus", "state_descr": "purged", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "st4InputCordStatus", "state_descr": "reading", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "st4InputCordStatus", "state_descr": "settle", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 1}, {"state_name": "st4InputCordStatus", "state_descr": "notFound", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 3}, {"state_name": "st4InputCordStatus", "state_descr": "lost", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 1}, {"state_name": "st4InputCordStatus", "state_descr": "readError", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 1}, {"state_name": "st4InputCordStatus", "state_descr": "noComm", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 1}, {"state_name": "st4InputCordStatus", "state_descr": "pwr<PERSON><PERSON>r", "state_draw_graph": 1, "state_value": 11, "state_generic_value": 2}, {"state_name": "st4InputCordStatus", "state_descr": "breakerTripped", "state_draw_graph": 1, "state_value": 12, "state_generic_value": 2}, {"state_name": "st4InputCordStatus", "state_descr": "fuseBlown", "state_draw_graph": 1, "state_value": 13, "state_generic_value": 2}, {"state_name": "st4InputCordStatus", "state_descr": "lowAlarm", "state_draw_graph": 1, "state_value": 14, "state_generic_value": 2}, {"state_name": "st4InputCordStatus", "state_descr": "lowWarning", "state_draw_graph": 1, "state_value": 15, "state_generic_value": 1}, {"state_name": "st4InputCordStatus", "state_descr": "highWarning", "state_draw_graph": 1, "state_value": 16, "state_generic_value": 1}, {"state_name": "st4InputCordStatus", "state_descr": "highAlarm", "state_draw_graph": 1, "state_value": 17, "state_generic_value": 2}, {"state_name": "st4InputCordStatus", "state_descr": "alarm", "state_draw_graph": 1, "state_value": 18, "state_generic_value": 2}, {"state_name": "st4InputCordStatus", "state_descr": "underLimit", "state_draw_graph": 1, "state_value": 19, "state_generic_value": 1}, {"state_name": "st4InputCordStatus", "state_descr": "overLimit", "state_draw_graph": 1, "state_value": 20, "state_generic_value": 1}, {"state_name": "st4InputCordStatus", "state_descr": "nvmFail", "state_draw_graph": 1, "state_value": 21, "state_generic_value": 2}, {"state_name": "st4InputCordStatus", "state_descr": "profileError", "state_draw_graph": 1, "state_value": 22, "state_generic_value": 2}, {"state_name": "st4InputCordStatus", "state_descr": "conflict", "state_draw_graph": 1, "state_value": 23, "state_generic_value": 2}, {"state_name": "st4LineState", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "st4LineState", "state_descr": "on", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "st4LineState", "state_descr": "off", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 3}, {"state_name": "st4LineStatus", "state_descr": "normal", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 0}, {"state_name": "st4LineStatus", "state_descr": "disabled", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "st4LineStatus", "state_descr": "purged", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "st4LineStatus", "state_descr": "reading", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "st4LineStatus", "state_descr": "settle", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 1}, {"state_name": "st4LineStatus", "state_descr": "notFound", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 3}, {"state_name": "st4LineStatus", "state_descr": "lost", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 1}, {"state_name": "st4LineStatus", "state_descr": "readError", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 1}, {"state_name": "st4LineStatus", "state_descr": "noComm", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 1}, {"state_name": "st4LineStatus", "state_descr": "pwr<PERSON><PERSON>r", "state_draw_graph": 1, "state_value": 11, "state_generic_value": 2}, {"state_name": "st4LineStatus", "state_descr": "breakerTripped", "state_draw_graph": 1, "state_value": 12, "state_generic_value": 2}, {"state_name": "st4LineStatus", "state_descr": "fuseBlown", "state_draw_graph": 1, "state_value": 13, "state_generic_value": 2}, {"state_name": "st4LineStatus", "state_descr": "lowAlarm", "state_draw_graph": 1, "state_value": 14, "state_generic_value": 2}, {"state_name": "st4LineStatus", "state_descr": "lowWarning", "state_draw_graph": 1, "state_value": 15, "state_generic_value": 1}, {"state_name": "st4LineStatus", "state_descr": "highWarning", "state_draw_graph": 1, "state_value": 16, "state_generic_value": 1}, {"state_name": "st4LineStatus", "state_descr": "highAlarm", "state_draw_graph": 1, "state_value": 17, "state_generic_value": 2}, {"state_name": "st4LineStatus", "state_descr": "alarm", "state_draw_graph": 1, "state_value": 18, "state_generic_value": 2}, {"state_name": "st4LineStatus", "state_descr": "underLimit", "state_draw_graph": 1, "state_value": 19, "state_generic_value": 1}, {"state_name": "st4LineStatus", "state_descr": "overLimit", "state_draw_graph": 1, "state_value": 20, "state_generic_value": 1}, {"state_name": "st4LineStatus", "state_descr": "nvmFail", "state_draw_graph": 1, "state_value": 21, "state_generic_value": 2}, {"state_name": "st4LineStatus", "state_descr": "profileError", "state_draw_graph": 1, "state_value": 22, "state_generic_value": 2}, {"state_name": "st4LineStatus", "state_descr": "conflict", "state_draw_graph": 1, "state_value": 23, "state_generic_value": 2}, {"state_name": "st4OcpStatus", "state_descr": "normal", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 0}, {"state_name": "st4OcpStatus", "state_descr": "disabled", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "st4OcpStatus", "state_descr": "purged", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "st4OcpStatus", "state_descr": "reading", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "st4OcpStatus", "state_descr": "settle", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 1}, {"state_name": "st4OcpStatus", "state_descr": "notFound", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 3}, {"state_name": "st4OcpStatus", "state_descr": "lost", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 1}, {"state_name": "st4OcpStatus", "state_descr": "readError", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 1}, {"state_name": "st4OcpStatus", "state_descr": "noComm", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 1}, {"state_name": "st4OcpStatus", "state_descr": "pwr<PERSON><PERSON>r", "state_draw_graph": 1, "state_value": 11, "state_generic_value": 2}, {"state_name": "st4OcpStatus", "state_descr": "breakerTripped", "state_draw_graph": 1, "state_value": 12, "state_generic_value": 2}, {"state_name": "st4OcpStatus", "state_descr": "fuseBlown", "state_draw_graph": 1, "state_value": 13, "state_generic_value": 2}, {"state_name": "st4OcpStatus", "state_descr": "lowAlarm", "state_draw_graph": 1, "state_value": 14, "state_generic_value": 2}, {"state_name": "st4OcpStatus", "state_descr": "lowWarning", "state_draw_graph": 1, "state_value": 15, "state_generic_value": 1}, {"state_name": "st4OcpStatus", "state_descr": "highWarning", "state_draw_graph": 1, "state_value": 16, "state_generic_value": 1}, {"state_name": "st4OcpStatus", "state_descr": "highAlarm", "state_draw_graph": 1, "state_value": 17, "state_generic_value": 2}, {"state_name": "st4OcpStatus", "state_descr": "alarm", "state_draw_graph": 1, "state_value": 18, "state_generic_value": 2}, {"state_name": "st4OcpStatus", "state_descr": "underLimit", "state_draw_graph": 1, "state_value": 19, "state_generic_value": 1}, {"state_name": "st4OcpStatus", "state_descr": "overLimit", "state_draw_graph": 1, "state_value": 20, "state_generic_value": 1}, {"state_name": "st4OcpStatus", "state_descr": "nvmFail", "state_draw_graph": 1, "state_value": 21, "state_generic_value": 2}, {"state_name": "st4OcpStatus", "state_descr": "profileError", "state_draw_graph": 1, "state_value": 22, "state_generic_value": 2}, {"state_name": "st4OcpStatus", "state_descr": "conflict", "state_draw_graph": 1, "state_value": 23, "state_generic_value": 2}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "normal", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 0}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "disabled", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "purged", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "reading", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "settle", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 1}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "notFound", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 3}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "lost", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 1}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "readError", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 1}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "noComm", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 1}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "pwr<PERSON><PERSON>r", "state_draw_graph": 1, "state_value": 11, "state_generic_value": 2}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "breakerTripped", "state_draw_graph": 1, "state_value": 12, "state_generic_value": 2}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "fuseBlown", "state_draw_graph": 1, "state_value": 13, "state_generic_value": 2}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "lowAlarm", "state_draw_graph": 1, "state_value": 14, "state_generic_value": 2}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "lowWarning", "state_draw_graph": 1, "state_value": 15, "state_generic_value": 1}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "highWarning", "state_draw_graph": 1, "state_value": 16, "state_generic_value": 1}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "highAlarm", "state_draw_graph": 1, "state_value": 17, "state_generic_value": 2}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "alarm", "state_draw_graph": 1, "state_value": 18, "state_generic_value": 2}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "underLimit", "state_draw_graph": 1, "state_value": 19, "state_generic_value": 1}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "overLimit", "state_draw_graph": 1, "state_value": 20, "state_generic_value": 1}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "nvmFail", "state_draw_graph": 1, "state_value": 21, "state_generic_value": 2}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "profileError", "state_draw_graph": 1, "state_value": 22, "state_generic_value": 2}, {"state_name": "st4PhasePowerFactorStatus", "state_descr": "conflict", "state_draw_graph": 1, "state_value": 23, "state_generic_value": 2}, {"state_name": "st4PhaseState", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "st4PhaseState", "state_descr": "on", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "st4PhaseState", "state_descr": "off", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 3}, {"state_name": "st4PhaseStatus", "state_descr": "normal", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 0}, {"state_name": "st4PhaseStatus", "state_descr": "disabled", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "st4PhaseStatus", "state_descr": "purged", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "st4PhaseStatus", "state_descr": "reading", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "st4PhaseStatus", "state_descr": "settle", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 1}, {"state_name": "st4PhaseStatus", "state_descr": "notFound", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 3}, {"state_name": "st4PhaseStatus", "state_descr": "lost", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 1}, {"state_name": "st4PhaseStatus", "state_descr": "readError", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 1}, {"state_name": "st4PhaseStatus", "state_descr": "noComm", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 1}, {"state_name": "st4PhaseStatus", "state_descr": "pwr<PERSON><PERSON>r", "state_draw_graph": 1, "state_value": 11, "state_generic_value": 2}, {"state_name": "st4PhaseStatus", "state_descr": "breakerTripped", "state_draw_graph": 1, "state_value": 12, "state_generic_value": 2}, {"state_name": "st4PhaseStatus", "state_descr": "fuseBlown", "state_draw_graph": 1, "state_value": 13, "state_generic_value": 2}, {"state_name": "st4PhaseStatus", "state_descr": "lowAlarm", "state_draw_graph": 1, "state_value": 14, "state_generic_value": 2}, {"state_name": "st4PhaseStatus", "state_descr": "lowWarning", "state_draw_graph": 1, "state_value": 15, "state_generic_value": 1}, {"state_name": "st4PhaseStatus", "state_descr": "highWarning", "state_draw_graph": 1, "state_value": 16, "state_generic_value": 1}, {"state_name": "st4PhaseStatus", "state_descr": "highAlarm", "state_draw_graph": 1, "state_value": 17, "state_generic_value": 2}, {"state_name": "st4PhaseStatus", "state_descr": "alarm", "state_draw_graph": 1, "state_value": 18, "state_generic_value": 2}, {"state_name": "st4PhaseStatus", "state_descr": "underLimit", "state_draw_graph": 1, "state_value": 19, "state_generic_value": 1}, {"state_name": "st4PhaseStatus", "state_descr": "overLimit", "state_draw_graph": 1, "state_value": 20, "state_generic_value": 1}, {"state_name": "st4PhaseStatus", "state_descr": "nvmFail", "state_draw_graph": 1, "state_value": 21, "state_generic_value": 2}, {"state_name": "st4PhaseStatus", "state_descr": "profileError", "state_draw_graph": 1, "state_value": 22, "state_generic_value": 2}, {"state_name": "st4PhaseStatus", "state_descr": "conflict", "state_draw_graph": 1, "state_value": 23, "state_generic_value": 2}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "normal", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 0}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "disabled", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "purged", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "reading", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "settle", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 1}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "notFound", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 3}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "lost", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 1}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "readError", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 1}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "noComm", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 1}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "pwr<PERSON><PERSON>r", "state_draw_graph": 1, "state_value": 11, "state_generic_value": 2}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "breakerTripped", "state_draw_graph": 1, "state_value": 12, "state_generic_value": 2}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "fuseBlown", "state_draw_graph": 1, "state_value": 13, "state_generic_value": 2}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "lowAlarm", "state_draw_graph": 1, "state_value": 14, "state_generic_value": 2}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "lowWarning", "state_draw_graph": 1, "state_value": 15, "state_generic_value": 1}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "highWarning", "state_draw_graph": 1, "state_value": 16, "state_generic_value": 1}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "highAlarm", "state_draw_graph": 1, "state_value": 17, "state_generic_value": 2}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "alarm", "state_draw_graph": 1, "state_value": 18, "state_generic_value": 2}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "underLimit", "state_draw_graph": 1, "state_value": 19, "state_generic_value": 1}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "overLimit", "state_draw_graph": 1, "state_value": 20, "state_generic_value": 1}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "nvmFail", "state_draw_graph": 1, "state_value": 21, "state_generic_value": 2}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "profileError", "state_draw_graph": 1, "state_value": 22, "state_generic_value": 2}, {"state_name": "st4PhaseVoltageStatus", "state_descr": "conflict", "state_draw_graph": 1, "state_value": 23, "state_generic_value": 2}, {"state_name": "st4UnitStatus", "state_descr": "normal", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 0}, {"state_name": "st4UnitStatus", "state_descr": "disabled", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "st4UnitStatus", "state_descr": "purged", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "st4UnitStatus", "state_descr": "reading", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "st4UnitStatus", "state_descr": "settle", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 1}, {"state_name": "st4UnitStatus", "state_descr": "notFound", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 3}, {"state_name": "st4UnitStatus", "state_descr": "lost", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 1}, {"state_name": "st4UnitStatus", "state_descr": "readError", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 1}, {"state_name": "st4UnitStatus", "state_descr": "noComm", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 1}, {"state_name": "st4UnitStatus", "state_descr": "pwr<PERSON><PERSON>r", "state_draw_graph": 1, "state_value": 11, "state_generic_value": 2}, {"state_name": "st4UnitStatus", "state_descr": "breakerTripped", "state_draw_graph": 1, "state_value": 12, "state_generic_value": 2}, {"state_name": "st4UnitStatus", "state_descr": "fuseBlown", "state_draw_graph": 1, "state_value": 13, "state_generic_value": 2}, {"state_name": "st4UnitStatus", "state_descr": "lowAlarm", "state_draw_graph": 1, "state_value": 14, "state_generic_value": 2}, {"state_name": "st4UnitStatus", "state_descr": "lowWarning", "state_draw_graph": 1, "state_value": 15, "state_generic_value": 1}, {"state_name": "st4UnitStatus", "state_descr": "highWarning", "state_draw_graph": 1, "state_value": 16, "state_generic_value": 1}, {"state_name": "st4UnitStatus", "state_descr": "highAlarm", "state_draw_graph": 1, "state_value": 17, "state_generic_value": 2}, {"state_name": "st4UnitStatus", "state_descr": "alarm", "state_draw_graph": 1, "state_value": 18, "state_generic_value": 2}, {"state_name": "st4UnitStatus", "state_descr": "underLimit", "state_draw_graph": 1, "state_value": 19, "state_generic_value": 1}, {"state_name": "st4UnitStatus", "state_descr": "overLimit", "state_draw_graph": 1, "state_value": 20, "state_generic_value": 1}, {"state_name": "st4UnitStatus", "state_descr": "nvmFail", "state_draw_graph": 1, "state_value": 21, "state_generic_value": 2}, {"state_name": "st4UnitStatus", "state_descr": "profileError", "state_draw_graph": 1, "state_value": 22, "state_generic_value": 2}, {"state_name": "st4UnitStatus", "state_descr": "conflict", "state_draw_graph": 1, "state_value": 23, "state_generic_value": 2}]}, "poller": "matches discovery"}}