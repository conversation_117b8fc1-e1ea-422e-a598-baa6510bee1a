{"os": {"discovery": {"devices": [{"sysName": null, "sysObjectID": null, "sysDescr": null, "sysContact": null, "version": "000292", "hardware": "DSR-4410MD", "features": null, "location": null, "os": "arris-dsr4410md", "type": "network", "serial": "<private>", "icon": "arris.svg"}]}, "poller": "matches discovery"}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.1166.1.621.11.1.0", "sensor_index": "0", "sensor_type": "acquisitionState", "sensor_descr": "Acquisition State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "acquisitionState"}], "state_indexes": [{"state_name": "acquisitionState", "state_descr": "null", "state_draw_graph": 0, "state_value": -1, "state_generic_value": 3}, {"state_name": "acquisitionState", "state_descr": "Locked", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 0}, {"state_name": "acquisitionState", "state_descr": "Un-Locked", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 2}]}, "poller": "matches discovery"}, "wireless": {"discovery": {"wireless_sensors": [{"sensor_deleted": 0, "sensor_class": "quality", "sensor_index": "0", "sensor_type": "arris-dsr4410md", "sensor_descr": "Receive Quality", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 88, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.1166.1.621.11.8.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "rssi", "sensor_index": "0", "sensor_type": "arris-dsr4410md", "sensor_descr": "Receive Signal Level", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": -47.5, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.1166.1.621.11.9.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "snr", "sensor_index": "0", "sensor_type": "arris-dsr4410md", "sensor_descr": "Receive SNR", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 18.2, "sensor_prev": null, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.1166.1.621.********\"]", "rrd_type": "GAUGE"}]}, "poller": {"wireless_sensors": [{"sensor_deleted": 0, "sensor_class": "quality", "sensor_index": "0", "sensor_type": "arris-dsr4410md", "sensor_descr": "Receive Quality", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 88, "sensor_prev": 88, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.1166.1.621.11.8.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "rssi", "sensor_index": "0", "sensor_type": "arris-dsr4410md", "sensor_descr": "Receive Signal Level", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": -47.5, "sensor_prev": -47.5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.1166.1.621.11.9.0\"]", "rrd_type": "GAUGE"}, {"sensor_deleted": 0, "sensor_class": "snr", "sensor_index": "0", "sensor_type": "arris-dsr4410md", "sensor_descr": "Receive SNR", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_aggregator": "sum", "sensor_current": 18.2, "sensor_prev": 18.2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_oids": "[\".*******.4.1.1166.1.621.********\"]", "rrd_type": "GAUGE"}]}}}